@extends('layouts.app')

@section('title', '仪表板')

@section('page-title', '数据看板')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        <button type="button" class="btn btn-success" onclick="exportReport()">
            <i class="fas fa-download me-1"></i>导出报告
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- 顶部统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                监控任务
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="taskCount">0</div>
                            <div class="text-xs text-muted">个活跃任务</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                监控商品
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="productCount">0</div>
                            <div class="text-xs text-muted">个商品在监控</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                价格异常
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="priceAlertCount">0</div>
                            <div class="text-xs text-muted">个价格异常警报</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                数据更新
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayUpdateCount">0</div>
                            <div class="text-xs text-muted">今日更新次数</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sync-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧图表 -->
        <div class="col-lg-8">
            <!-- 价格趋势图 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        价格趋势分析
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="priceTrendChart" height="100"></canvas>
                </div>
            </div>

            <!-- 促销活动分布 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        促销活动分布
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="promotionChart" height="80"></canvas>
                </div>
            </div>

            <!-- 商品类目分布 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        商品类目分布
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" height="80"></canvas>
                </div>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div class="col-lg-4">
            <!-- 快速操作 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('search') }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            商品搜索
                        </a>
                        <a href="#" class="btn btn-success" onclick="showAddTaskModal()">
                            <i class="fas fa-plus me-2"></i>
                            新增监控任务
                        </a>
                        <a href="#" class="btn btn-info">
                            <i class="fas fa-download me-2"></i>
                            导出数据
                        </a>
                        <a href="#" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最新警报 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bell me-2"></i>
                        最新警报
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @forelse($recentAlerts ?? [] as $alert)
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ $alert['title'] ?? '价格异常警报' }}</h6>
                                <small>{{ $alert['created_at'] ? $alert['created_at']->diffForHumans() : '刚刚' }}</small>
                            </div>
                            <p class="mb-1">{{ $alert['description'] ?? '监控数据异常' }}</p>
                            <small class="text-muted">{{ $alert['type'] ?? '系统消息' }}</small>
                        </div>
                        @empty
                        <div class="list-group-item text-center text-muted">
                            <i class="fas fa-check-circle me-2"></i>
                            暂无新警报
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-success">正常</h4>
                                <small class="text-muted">数据采集</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">在线</h4>
                            <small class="text-muted">API服务</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <small class="text-muted">最后更新：</small>
                            <span class="text-dark">{{ now()->format('Y-m-d H:i:s') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增监控任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="mb-3">
                        <label for="productName" class="form-label">商品名称</label>
                        <input type="text" class="form-control" id="productName" required>
                    </div>
                    <div class="mb-3">
                        <label for="productUrl" class="form-label">商品链接</label>
                        <input type="url" class="form-control" id="productUrl" required>
                    </div>
                    <div class="mb-3">
                        <label for="monitorFrequency" class="form-label">监控频率</label>
                        <select class="form-select" id="monitorFrequency">
                            <option value="hourly">每小时</option>
                            <option value="daily" selected>每天</option>
                            <option value="weekly">每周</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddTask()">添加任务</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* 统计卡片样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 1rem;
}

.chart-container canvas {
    max-height: 100%;
}

/* 卡片悬停效果 */
.card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
}

/* 数字动画效果 */
.animated-number {
    transition: all 0.3s ease;
}

.animated-number.updating {
    color: #4e73df;
    transform: scale(1.1);
}

/* 图表标题样式 */
.chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 1rem;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
}

/* 加载状态 */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.chart-loading .spinner-border {
    margin-right: 0.5rem;
}
</style>
@endpush

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
<script src="{{ asset('js/api-client.js') }}"></script>
<script src="{{ asset('js/dashboard.js') }}"></script>
<script>
// 页面特定的功能 - 避免重复声明
if (typeof priceTrendChart === 'undefined') {
    var priceTrendChart, promotionChart, categoryChart;
}

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadDashboardData();
});

// 初始化图表
function initializeCharts() {
    // 价格趋势图
    const priceTrendCtx = document.getElementById('priceTrendChart').getContext('2d');
    priceTrendChart = new Chart(priceTrendCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '平均价格偏差率',
                data: [],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true,
                pointBackgroundColor: '#4e73df',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            }, {
                label: '竞品平均价格',
                data: [],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                fill: false,
                pointBackgroundColor: '#1cc88a',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    titleColor: '#333',
                    bodyColor: '#333',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '价格偏差率 (%)',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // 促销分布图
    const promotionCtx = document.getElementById('promotionChart').getContext('2d');
    promotionChart = new Chart(promotionCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b',
                    '#858796'
                ],
                borderWidth: 2,
                borderColor: '#ffffff',
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    titleColor: '#333',
                    bodyColor: '#333',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });

    // 类目分布图
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    categoryChart = new Chart(categoryCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '商品数量',
                data: [],
                backgroundColor: [
                    'rgba(78, 115, 223, 0.8)',
                    'rgba(28, 200, 138, 0.8)',
                    'rgba(54, 185, 204, 0.8)',
                    'rgba(246, 194, 62, 0.8)',
                    'rgba(231, 74, 59, 0.8)',
                    'rgba(133, 135, 150, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)'
                ],
                borderColor: [
                    'rgba(78, 115, 223, 1)',
                    'rgba(28, 200, 138, 1)',
                    'rgba(54, 185, 204, 1)',
                    'rgba(246, 194, 62, 1)',
                    'rgba(231, 74, 59, 1)',
                    'rgba(133, 135, 150, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)'
                ],
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    titleColor: '#333',
                    bodyColor: '#333',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.y} 个商品`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0,
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '商品数量',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 加载仪表板数据
function loadDashboardData() {
    // 模拟API调用
    setTimeout(() => {
        // 更新价格趋势数据
        const priceTrendData = {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [
                [12.5, 15.2, 8.7, 11.3, 9.8, 13.1],
                [18.3, 16.7, 14.2, 15.8, 17.1, 16.4]
            ]
        };

        updatePriceTrendChart(priceTrendData);

        // 更新促销分布数据
        const promotionData = {
            labels: ['满减', '折扣', '买赠', '拼团', '秒杀', '其他'],
            data: [156, 89, 67, 45, 34, 23]
        };

        updatePromotionChart(promotionData);

        // 更新类目分布数据
        const categoryData = {
            labels: ['数码电器', '服装鞋包', '家居用品', '美妆护肤', '食品饮料', '运动户外', '汽车用品', '母婴用品'],
            data: [456, 389, 267, 234, 189, 156, 123, 89]
        };

        updateCategoryChart(categoryData);

        // 更新统计卡片
        updateStatCards({
            taskCount: 128,
            productCount: 2751,
            priceAlertCount: 15,
            todayUpdateCount: 1247
        });

    }, 1000);
}

// 更新价格趋势图表
function updatePriceTrendChart(data) {
    priceTrendChart.data.labels = data.labels;
    priceTrendChart.data.datasets[0].data = data.datasets[0];
    priceTrendChart.data.datasets[1].data = data.datasets[1];
    priceTrendChart.update('active');
}

// 更新促销分布图表
function updatePromotionChart(data) {
    promotionChart.data.labels = data.labels;
    promotionChart.data.datasets[0].data = data.data;
    promotionChart.update('active');
}

// 更新类目分布图表
function updateCategoryChart(data) {
    categoryChart.data.labels = data.labels;
    categoryChart.data.datasets[0].data = data.data;
    categoryChart.update('active');
}

// 更新统计卡片
function updateStatCards(stats) {
    // 使用动画效果更新数字
    animateNumber('taskCount', stats.taskCount);
    animateNumber('productCount', stats.productCount);
    animateNumber('priceAlertCount', stats.priceAlertCount);
    animateNumber('todayUpdateCount', stats.todayUpdateCount);
}

// 数字动画效果
function animateNumber(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const startValue = parseInt(element.textContent) || 0;
    const duration = 1000;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);

        element.textContent = currentValue.toLocaleString();

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 刷新仪表板
function refreshDashboard() {
    // 显示加载状态
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
    refreshBtn.disabled = true;

    // 重新加载数据
    loadDashboardData();

    // 恢复按钮状态
    setTimeout(() => {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }, 2000);
}

// 导出报告
function exportReport() {
    console.log('导出报告功能');
    // 实际项目中调用导出API
    alert('报告导出功能开发中...');
}

// 显示添加任务模态框
function showAddTaskModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTaskModal'));
    modal.show();
}

// 提交新任务
function submitAddTask() {
    const form = document.getElementById('addTaskForm');
    const formData = new FormData(form);

    // 简单验证
    if (!formData.get('productName') || !formData.get('productUrl')) {
        alert('请填写完整的商品信息');
        return;
    }

    // 模拟提交
    console.log('提交新任务:', Object.fromEntries(formData));
    alert('任务添加成功！');

    const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
    modal.hide();

    // 清空表单
    form.reset();
}
</script>
@endsection
<?php

namespace App\Http\Controllers;

use App\Models\DataSource;
use App\Models\ImportLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class DataSourceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * 显示数据源管理首页
     */
    public function index(Request $request)
    {
        $query = auth()->user()->dataSources()->with(['importLog', 'product']);

        // 过滤条件
        if ($request->has('platform') && $request->platform !== '') {
            $query->where('platform', $request->platform);
        }

        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        if ($request->has('search') && $request->search !== '') {
            $query->where(function($q) use ($request) {
                $q->where('source_id', 'like', '%' . $request->search . '%')
                  ->orWhere('source_url', 'like', '%' . $request->search . '%')
                  ->orWhereHas('product', function($pq) use ($request) {
                      $pq->where('title', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $dataSources = $query->latest()->paginate(20);

        // 统计信息
        $stats = $this->getStats();

        return view('data-sources.index', compact('dataSources', 'stats'));
    }

    /**
     * 显示手动添加页面
     */
    public function create()
    {
        return view('data-sources.create');
    }

    /**
     * 手动添加单个数据源
     */
    public function store(Request $request)
    {
        $request->validate([
            'input_data' => 'required|string',
            'auto_monitor' => 'boolean'
        ]);

        $inputData = trim($request->input_data);
        $autoMonitor = $request->boolean('auto_monitor');

        // 创建导入日志
        $importLog = ImportLog::create([
            'user_id' => auth()->id(),
            'import_type' => 'manual',
            'data_type' => $this->detectDataType($inputData),
            'source_data' => $inputData,
            'total_items' => 1,
            'status' => 'processing'
        ]);

        $importLog->markAsStarted();

        try {
            $result = $this->processSingleInput($inputData, $importLog, $autoMonitor);

            if ($result['success']) {
                $importLog->markAsCompleted([
                    'total' => 1,
                    'success' => 1,
                    'failed' => 0,
                    'duplicate' => 0
                ]);

                return redirect()->route('data-sources.index')
                    ->with('success', '数据源添加成功！');
            } else {
                $importLog->markAsFailed($result['error']);
                return back()->withInput()
                    ->with('error', '添加失败：' . $result['error']);
            }
        } catch (\Exception $e) {
            $importLog->markAsFailed($e->getMessage());
            Log::error('手动添加数据源失败', [
                'user_id' => auth()->id(),
                'input_data' => $inputData,
                'error' => $e->getMessage()
            ]);

            return back()->withInput()
                ->with('error', '添加失败：' . $e->getMessage());
        }
    }

    /**
     * 显示批量导入页面
     */
    public function batchCreate()
    {
        return view('data-sources.batch-create');
    }

    /**
     * 批量导入数据源
     */
    public function batchStore(Request $request)
    {
        $request->validate([
            'batch_data' => 'required|string',
            'auto_monitor' => 'boolean'
        ]);

        $batchData = trim($request->batch_data);
        $autoMonitor = $request->boolean('auto_monitor');

        // 分割输入数据
        $lines = array_filter(array_map('trim', explode("\n", $batchData)));
        
        if (empty($lines)) {
            return back()->withInput()
                ->with('error', '请输入有效的数据！');
        }

        // 创建导入日志
        $importLog = ImportLog::create([
            'user_id' => auth()->id(),
            'import_type' => 'batch',
            'data_type' => 'mixed',
            'source_data' => $batchData,
            'total_items' => count($lines),
            'status' => 'processing'
        ]);

        $importLog->markAsStarted();

        $results = [
            'success' => 0,
            'failed' => 0,
            'duplicate' => 0,
            'details' => []
        ];

        try {
            DB::beginTransaction();

            foreach ($lines as $index => $line) {
                $result = $this->processSingleInput($line, $importLog, $autoMonitor);
                
                if ($result['success']) {
                    $results['success']++;
                } elseif ($result['duplicate']) {
                    $results['duplicate']++;
                } else {
                    $results['failed']++;
                }

                $results['details'][] = [
                    'line' => $index + 1,
                    'input' => $line,
                    'success' => $result['success'],
                    'duplicate' => $result['duplicate'] ?? false,
                    'error' => $result['error'] ?? null
                ];
            }

            $importLog->update([
                'success_count' => $results['success'],
                'failed_count' => $results['failed'],
                'duplicate_count' => $results['duplicate']
            ]);

            $importLog->markAsCompleted([
                'total' => count($lines),
                'success' => $results['success'],
                'failed' => $results['failed'],
                'duplicate' => $results['duplicate']
            ], $results['details']);

            DB::commit();

            $message = "批量导入完成！成功：{$results['success']}，失败：{$results['failed']}，重复：{$results['duplicate']}";
            return redirect()->route('data-sources.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            $importLog->markAsFailed($e->getMessage());
            
            Log::error('批量导入数据源失败', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'results' => $results
            ]);

            return back()->withInput()
                ->with('error', '批量导入失败：' . $e->getMessage());
        }
    }

    /**
     * 显示导入历史
     */
    public function importHistory(Request $request)
    {
        $query = auth()->user()->importLogs()->with(['dataSources']);

        if ($request->has('type') && $request->type !== '') {
            $query->where('import_type', $request->type);
        }

        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        $importLogs = $query->latest()->paginate(15);

        return view('data-sources.import-history', compact('importLogs'));
    }

    /**
     * 查看导入详情
     */
    public function importDetail($id)
    {
        $importLog = auth()->user()->importLogs()
            ->with(['dataSources.product'])
            ->findOrFail($id);

        return view('data-sources.import-detail', compact('importLog'));
    }

    /**
     * 撤销导入
     */
    public function undoImport($id)
    {
        $importLog = auth()->user()->importLogs()->findOrFail($id);

        if (!$importLog->canUndo()) {
            return back()->with('error', '该导入记录无法撤销！');
        }

        try {
            DB::beginTransaction();
            
            $result = $importLog->undo();
            
            if ($result) {
                DB::commit();
                return back()->with('success', '导入已成功撤销！');
            } else {
                DB::rollBack();
                return back()->with('error', '撤销失败！');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('撤销导入失败', [
                'import_log_id' => $id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', '撤销失败：' . $e->getMessage());
        }
    }

    /**
     * 删除数据源
     */
    public function destroy($id)
    {
        $dataSource = auth()->user()->dataSources()->findOrFail($id);
        
        try {
            $dataSource->update(['is_active' => false]);
            return back()->with('success', '数据源已删除！');
        } catch (\Exception $e) {
            return back()->with('error', '删除失败：' . $e->getMessage());
        }
    }

    /**
     * 重新处理失败的数据源
     */
    public function retry($id)
    {
        $dataSource = auth()->user()->dataSources()->findOrFail($id);

        if (!$dataSource->canRetry()) {
            return back()->with('error', '该数据源无法重试！');
        }

        try {
            $dataSource->markAsProcessing();
            
            // 这里应该调用实际的数据抓取服务
            // 目前先模拟成功
            $dataSource->markAsCompleted([
                'title' => '模拟商品标题',
                'price' => 99.99,
                'platform' => $dataSource->platform
            ]);

            return back()->with('success', '重新处理成功！');
        } catch (\Exception $e) {
            $dataSource->markAsFailed($e->getMessage());
            return back()->with('error', '重新处理失败：' . $e->getMessage());
        }
    }

    /**
     * 处理单个输入数据
     */
    protected function processSingleInput($input, ImportLog $importLog, $autoMonitor = false)
    {
        try {
            // 判断是URL还是ID
            if (filter_var($input, FILTER_VALIDATE_URL)) {
                $parsed = DataSource::parseUrl($input);
                
                if (!$parsed) {
                    return ['success' => false, 'error' => '无法解析URL或不支持的平台'];
                }
            } else {
                // 尝试作为商品ID处理
                return ['success' => false, 'error' => '纯商品ID导入功能待开发'];
            }

            // 检查是否重复
            $existing = auth()->user()->dataSources()
                ->where('platform', $parsed['platform'])
                ->where('source_id', $parsed['source_id'])
                ->where('is_active', true)
                ->first();

            if ($existing) {
                return ['success' => false, 'duplicate' => true, 'error' => '数据源已存在'];
            }

            // 创建数据源记录
            $dataSource = DataSource::create([
                'user_id' => auth()->id(),
                'import_log_id' => $importLog->id,
                'source_type' => $parsed['source_type'],
                'platform' => $parsed['platform'],
                'source_url' => $parsed['source_url'],
                'source_id' => $parsed['source_id'],
                'normalized_url' => $parsed['normalized_url'],
                'auto_monitor' => $autoMonitor,
                'status' => 'pending'
            ]);

            // 这里应该调用实际的数据抓取服务
            // 目前先标记为完成状态
            $dataSource->markAsCompleted([
                'title' => '模拟商品标题 - ' . $parsed['source_id'],
                'price' => rand(10, 1000),
                'platform' => $parsed['platform']
            ]);

            return ['success' => true, 'data_source' => $dataSource];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 检测数据类型
     */
    protected function detectDataType($input)
    {
        if (filter_var($input, FILTER_VALIDATE_URL)) {
            return 'url';
        } elseif (preg_match('/^\d+$/', $input)) {
            return 'id';
        } else {
            return 'mixed';
        }
    }

    /**
     * 获取统计信息
     */
    protected function getStats()
    {
        $userId = auth()->id();
        
        return [
            'total' => DataSource::where('user_id', $userId)->where('is_active', true)->count(),
            'pending' => DataSource::where('user_id', $userId)->where('status', 'pending')->count(),
            'completed' => DataSource::where('user_id', $userId)->where('status', 'completed')->count(),
            'failed' => DataSource::where('user_id', $userId)->where('status', 'failed')->count(),
            'platforms' => DataSource::where('user_id', $userId)
                ->where('is_active', true)
                ->groupBy('platform')
                ->selectRaw('platform, count(*) as count')
                ->get()
                ->pluck('count', 'platform')
                ->toArray()
        ];
    }

    /**
     * 下载Excel模板
     */
    public function downloadTemplate()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置标题
            $sheet->setTitle('数据源导入模板');

            // 设置表头
            $headers = [
                'A1' => '商品URL或ID',
                'B1' => '平台',
                'C1' => '备注',
                'D1' => '自动监控'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
                $sheet->getStyle($cell)->getFont()->setBold(true);
                $sheet->getStyle($cell)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('FFE0E0E0');
            }

            // 设置列宽
            $sheet->getColumnDimension('A')->setWidth(50);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(30);
            $sheet->getColumnDimension('D')->setWidth(15);

            // 添加示例数据
            $examples = [
                [
                    'https://detail.tmall.com/item.htm?id=123456789',
                    '天猫',
                    '示例商品链接',
                    '是'
                ],
                [
                    'https://item.taobao.com/item.htm?id=987654321',
                    '淘宝',
                    '另一个示例',
                    '否'
                ],
                [
                    '123456789',
                    '京东',
                    '商品ID示例',
                    '是'
                ]
            ];

            $row = 2;
            foreach ($examples as $example) {
                $sheet->setCellValue('A' . $row, $example[0]);
                $sheet->setCellValue('B' . $row, $example[1]);
                $sheet->setCellValue('C' . $row, $example[2]);
                $sheet->setCellValue('D' . $row, $example[3]);
                $row++;
            }

            // 添加说明
            $sheet->setCellValue('A6', '说明：');
            $sheet->getStyle('A6')->getFont()->setBold(true);
            
            $instructions = [
                'A7' => '1. 第一列填写完整的商品URL或商品ID',
                'A8' => '2. 第二列填写平台名称（可选，系统会自动识别）',
                'A9' => '3. 第三列填写备注信息（可选）',
                'A10' => '4. 第四列填写是否自动监控（是/否，可选，默认否）',
                'A11' => '5. 支持的平台：淘宝、天猫、京东、拼多多',
                'A12' => '6. 请删除示例数据后再导入您的数据'
            ];

            foreach ($instructions as $cell => $text) {
                $sheet->setCellValue($cell, $text);
                $sheet->getStyle($cell)->getFont()->setSize(10);
            }

            // 创建写入器
            $writer = new Xlsx($spreadsheet);
            
            // 设置文件名
            $filename = '数据源导入模板_' . date('Y-m-d') . '.xlsx';
            
            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            
            // 输出到浏览器
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('下载Excel模板失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return back()->with('error', '模板下载失败：' . $e->getMessage());
        }
    }

    /**
     * 显示Excel导入页面
     */
    public function excelCreate()
    {
        return view('data-sources.excel-create');
    }

    /**
     * 处理Excel文件导入
     */
    public function excelStore(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB
            'auto_monitor' => 'boolean'
        ]);

        $file = $request->file('excel_file');
        $autoMonitor = $request->boolean('auto_monitor');

        try {
            // 保存上传的文件
            $path = $file->store('imports', 'local');
            $fullPath = storage_path('app/' . $path);

            // 读取Excel文件
            $spreadsheet = IOFactory::load($fullPath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // 移除表头
            if (!empty($rows)) {
                array_shift($rows);
            }

            // 过滤空行
            $validRows = array_filter($rows, function($row) {
                return !empty(trim($row[0] ?? ''));
            });

            if (empty($validRows)) {
                Storage::delete($path);
                return back()->with('error', 'Excel文件中没有找到有效数据！');
            }

            // 创建导入日志
            $importLog = ImportLog::create([
                'user_id' => auth()->id(),
                'import_type' => 'excel',
                'data_type' => 'excel',
                'source_data' => $file->getClientOriginalName(),
                'total_items' => count($validRows),
                'status' => 'processing'
            ]);

            $importLog->markAsStarted();

            $results = [
                'success' => 0,
                'failed' => 0,
                'duplicate' => 0,
                'details' => []
            ];

            DB::beginTransaction();

            foreach ($validRows as $index => $row) {
                $url = trim($row[0] ?? '');
                $platform = trim($row[1] ?? '');
                $remark = trim($row[2] ?? '');
                $shouldAutoMonitor = $this->parseBoolean($row[3] ?? '') ?? $autoMonitor;

                if (empty($url)) {
                    $results['failed']++;
                    $results['details'][] = [
                        'row' => $index + 2, // +2 因为有表头且数组从0开始
                        'input' => $url,
                        'success' => false,
                        'error' => '商品URL或ID不能为空'
                    ];
                    continue;
                }

                $result = $this->processSingleInput($url, $importLog, $shouldAutoMonitor);
                
                // 如果有备注，更新数据源的备注字段
                if ($result['success'] && !empty($remark) && isset($result['data_source'])) {
                    $result['data_source']->update(['metadata' => ['remark' => $remark]]);
                }

                if ($result['success']) {
                    $results['success']++;
                } elseif ($result['duplicate'] ?? false) {
                    $results['duplicate']++;
                } else {
                    $results['failed']++;
                }

                $results['details'][] = [
                    'row' => $index + 2,
                    'input' => $url,
                    'platform' => $platform,
                    'remark' => $remark,
                    'auto_monitor' => $shouldAutoMonitor,
                    'success' => $result['success'],
                    'duplicate' => $result['duplicate'] ?? false,
                    'error' => $result['error'] ?? null
                ];
            }

            $importLog->update([
                'success_count' => $results['success'],
                'failed_count' => $results['failed'],
                'duplicate_count' => $results['duplicate']
            ]);

            $importLog->markAsCompleted([
                'total' => count($validRows),
                'success' => $results['success'],
                'failed' => $results['failed'],
                'duplicate' => $results['duplicate']
            ], $results['details']);

            DB::commit();

            // 删除临时文件
            Storage::delete($path);

            if ($results['failed'] > 0) {
                return redirect()->route('data-sources.import-detail', $importLog->id)
                    ->with('warning', sprintf(
                        'Excel导入完成！成功：%d，失败：%d，重复：%d。请查看详情了解失败原因。',
                        $results['success'],
                        $results['failed'],
                        $results['duplicate']
                    ));
            } else {
                return redirect()->route('data-sources.index')
                    ->with('success', sprintf(
                        'Excel导入成功！共导入 %d 条数据，重复 %d 条。',
                        $results['success'],
                        $results['duplicate']
                    ));
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // 清理文件
            if (isset($path)) {
                Storage::delete($path);
            }

            if (isset($importLog)) {
                $importLog->markAsFailed($e->getMessage());
            }

            Log::error('Excel导入失败', [
                'user_id' => auth()->id(),
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Excel导入失败：' . $e->getMessage());
        }
    }

    /**
     * 解析布尔值字符串
     */
    protected function parseBoolean($value)
    {
        $value = strtolower(trim($value));
        
        if (in_array($value, ['是', 'yes', 'true', '1', 'y'])) {
            return true;
        } elseif (in_array($value, ['否', 'no', 'false', '0', 'n'])) {
            return false;
        }
        
        return null;
    }
}

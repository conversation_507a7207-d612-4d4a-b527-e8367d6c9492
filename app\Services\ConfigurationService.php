<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

/**
 * 配置管理服务
 * 
 * 提供统一的配置访问、验证和管理功能
 */
class ConfigurationService
{
    /**
     * 配置缓存前缀
     */
    const CACHE_PREFIX = 'config:';
    
    /**
     * 配置缓存TTL（秒）
     */
    const CACHE_TTL = 3600;
    
    /**
     * 敏感配置键（不会被记录到日志）
     */
    const SENSITIVE_KEYS = [
        'token', 'password', 'secret', 'key', 'api_key', 
        'access_token', 'refresh_token', 'webhook'
    ];

    /**
     * 获取配置值
     *
     * @param string $key 配置键（支持点号分隔）
     * @param mixed $default 默认值
     * @param bool $useCache 是否使用缓存
     * @return mixed
     */
    public function get(string $key, $default = null, bool $useCache = true)
    {
        try {
            if ($useCache) {
                $cacheKey = self::CACHE_PREFIX . $key;
                return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($key, $default) {
                    return $this->getConfigValue($key, $default);
                });
            }
            
            return $this->getConfigValue($key, $default);
        } catch (Exception $e) {
            Log::error('配置获取失败', [
                'key' => $key,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $default;
        }
    }

    /**
     * 设置配置值（运行时）
     *
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @return void
     */
    public function set(string $key, $value): void
    {
        try {
            Config::set($key, $value);
            
            // 清除相关缓存
            $this->clearCache($key);
            
            // 记录配置更改（排除敏感信息）
            if (!$this->isSensitiveKey($key)) {
                Log::info('配置已更新', [
                    'key' => $key,
                    'value' => $value
                ]);
            } else {
                Log::info('敏感配置已更新', ['key' => $key]);
            }
        } catch (Exception $e) {
            Log::error('配置设置失败', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * 获取数据收集配置
     *
     * @param string|null $platform 平台名称
     * @return array
     */
    public function getDataCollectionConfig(?string $platform = null): array
    {
        $config = $this->get('datacollection', []);
        
        if ($platform) {
            return $config['platforms'][$platform] ?? [];
        }
        
        return $config;
    }

    /**
     * 获取平台API配置
     *
     * @param string $platform 平台名称
     * @return array
     */
    public function getPlatformConfig(string $platform): array
    {
        return $this->get("datacollection.platforms.{$platform}", []);
    }

    /**
     * 获取队列配置
     *
     * @return array
     */
    public function getQueueConfig(): array
    {
        return [
            'connection' => $this->get('queue.default'),
            'workers' => $this->get('datacollection.queue.workers', 5),
            'max_tries' => $this->get('datacollection.queue.max_tries', 3),
            'timeout' => $this->get('datacollection.queue.timeout', 300),
            'retry_after' => $this->get('datacollection.queue.retry_after', 90),
            'queues' => $this->get('datacollection.queue.queues', [])
        ];
    }

    /**
     * 获取HTTP客户端配置
     *
     * @return array
     */
    public function getHttpConfig(): array
    {
        return $this->get('datacollection.http', []);
    }

    /**
     * 获取监控配置
     *
     * @return array
     */
    public function getMonitoringConfig(): array
    {
        return $this->get('datacollection.monitoring', []);
    }

    /**
     * 获取缓存配置
     *
     * @return array
     */
    public function getCacheConfig(): array
    {
        return $this->get('datacollection.cache', []);
    }

    /**
     * 获取安全配置
     *
     * @return array
     */
    public function getSecurityConfig(): array
    {
        return $this->get('datacollection.security', []);
    }

    /**
     * 验证配置完整性
     *
     * @return array 验证结果
     */
    public function validateConfiguration(): array
    {
        $results = [
            'valid' => true,
            'errors' => [],
            'warnings' => [],
            'missing' => []
        ];

        // 验证必需的配置项
        $requiredConfigs = [
            'app.key' => 'APP_KEY',
            'database.connections.mysql.host' => 'DB_HOST',
            'database.connections.mysql.database' => 'DB_DATABASE',
            'queue.default' => 'QUEUE_CONNECTION',
            'cache.default' => 'CACHE_DRIVER'
        ];

        foreach ($requiredConfigs as $configKey => $envKey) {
            $value = $this->get($configKey);
            if (empty($value)) {
                $results['missing'][] = $envKey;
                $results['errors'][] = "缺少必需的配置: {$envKey}";
                $results['valid'] = false;
            }
        }

        // 验证数据收集配置
        $this->validateDataCollectionConfig($results);

        // 验证平台配置
        $this->validatePlatformConfigs($results);

        // 验证队列配置
        $this->validateQueueConfig($results);

        return $results;
    }

    /**
     * 获取所有配置信息（用于调试）
     *
     * @param bool $includeSensitive 是否包含敏感信息
     * @return array
     */
    public function getAllConfigs(bool $includeSensitive = false): array
    {
        $configs = [
            'app' => $this->getAppConfig(),
            'database' => $this->getDatabaseConfig($includeSensitive),
            'queue' => $this->getQueueConfig(),
            'cache' => $this->getCacheConfig(),
            'datacollection' => $this->getDataCollectionConfig(),
            'monitoring' => $this->getMonitoringConfig(),
            'security' => $this->getSecurityConfig()
        ];

        if (!$includeSensitive) {
            $configs = $this->filterSensitiveData($configs);
        }

        return $configs;
    }

    /**
     * 重新加载配置
     *
     * @return void
     */
    public function reload(): void
    {
        // 清除所有配置缓存
        $this->clearAllCache();
        
        Log::info('配置已重新加载');
    }

    /**
     * 检查配置是否存在
     *
     * @param string $key 配置键
     * @return bool
     */
    public function has(string $key): bool
    {
        return Config::has($key);
    }

    /**
     * 获取环境变量
     *
     * @param string $key 环境变量名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function env(string $key, $default = null)
    {
        return env($key, $default);
    }

    /**
     * 获取配置值（内部方法）
     *
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    private function getConfigValue(string $key, $default = null)
    {
        return Config::get($key, $default);
    }

    /**
     * 验证数据收集配置
     *
     * @param array $results 验证结果数组
     * @return void
     */
    private function validateDataCollectionConfig(array &$results): void
    {
        $config = $this->getDataCollectionConfig();
        
        if (empty($config)) {
            $results['errors'][] = '数据收集配置缺失';
            $results['valid'] = false;
            return;
        }

        // 验证HTTP配置
        $httpConfig = $config['http'] ?? [];
        if (empty($httpConfig['timeout']) || $httpConfig['timeout'] < 1) {
            $results['warnings'][] = 'HTTP超时时间配置可能不合理';
        }

        // 验证收集配置
        $collectionConfig = $config['collection'] ?? [];
        if (empty($collectionConfig['batch_size']) || $collectionConfig['batch_size'] < 1) {
            $results['warnings'][] = '批处理大小配置可能不合理';
        }
    }

    /**
     * 验证平台配置
     *
     * @param array $results 验证结果数组
     * @return void
     */
    private function validatePlatformConfigs(array &$results): void
    {
        $platforms = $this->get('datacollection.platforms', []);
        
        foreach ($platforms as $platform => $config) {
            if (empty($config['base_url'])) {
                $results['warnings'][] = "平台 {$platform} 缺少 base_url 配置";
            }
            
            if (empty($config['endpoints'])) {
                $results['warnings'][] = "平台 {$platform} 缺少接口端点配置";
            }
        }
    }

    /**
     * 验证队列配置
     *
     * @param array $results 验证结果数组
     * @return void
     */
    private function validateQueueConfig(array &$results): void
    {
        $queueConnection = $this->get('queue.default');
        
        if ($queueConnection === 'sync' && app()->environment('production')) {
            $results['warnings'][] = '生产环境使用同步队列可能影响性能';
        }
        
        $workers = $this->get('datacollection.queue.workers', 0);
        if ($workers < 1) {
            $results['warnings'][] = '队列工作进程数量配置过少';
        }
    }

    /**
     * 获取应用配置
     *
     * @return array
     */
    private function getAppConfig(): array
    {
        return [
            'name' => $this->get('app.name'),
            'env' => $this->get('app.env'),
            'debug' => $this->get('app.debug'),
            'url' => $this->get('app.url'),
            'timezone' => $this->get('app.timezone'),
            'locale' => $this->get('app.locale')
        ];
    }

    /**
     * 获取数据库配置
     *
     * @param bool $includeSensitive 是否包含敏感信息
     * @return array
     */
    private function getDatabaseConfig(bool $includeSensitive = false): array
    {
        $config = [
            'default' => $this->get('database.default'),
            'connections' => []
        ];

        $connections = $this->get('database.connections', []);
        foreach ($connections as $name => $connection) {
            $config['connections'][$name] = [
                'driver' => $connection['driver'] ?? '',
                'host' => $connection['host'] ?? '',
                'port' => $connection['port'] ?? '',
                'database' => $connection['database'] ?? ''
            ];

            if ($includeSensitive) {
                $config['connections'][$name]['username'] = $connection['username'] ?? '';
                $config['connections'][$name]['password'] = $connection['password'] ?? '';
            }
        }

        return $config;
    }

    /**
     * 过滤敏感数据
     *
     * @param array $data 数据数组
     * @return array
     */
    private function filterSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->filterSensitiveData($value);
            } elseif ($this->isSensitiveKey($key)) {
                $data[$key] = '***';
            }
        }

        return $data;
    }

    /**
     * 检查是否为敏感配置键
     *
     * @param string $key 配置键
     * @return bool
     */
    private function isSensitiveKey(string $key): bool
    {
        $key = Str::lower($key);
        
        foreach (self::SENSITIVE_KEYS as $sensitiveKey) {
            if (Str::contains($key, $sensitiveKey)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清除配置缓存
     *
     * @param string $key 配置键
     * @return void
     */
    private function clearCache(string $key): void
    {
        $cacheKey = self::CACHE_PREFIX . $key;
        Cache::forget($cacheKey);
    }

    /**
     * 清除所有配置缓存
     *
     * @return void
     */
    private function clearAllCache(): void
    {
        $pattern = self::CACHE_PREFIX . '*';
        
        // 注意：这里使用Redis的方式，如果使用其他缓存驱动需要调整
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            $redis = Cache::getRedis();
            $keys = $redis->keys($pattern);
            if (!empty($keys)) {
                $redis->del($keys);
            }
        }
    }
} 
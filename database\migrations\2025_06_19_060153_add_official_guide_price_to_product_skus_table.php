<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            // 添加官方指导价字段
            $table->decimal('official_guide_price', 10, 2)->nullable()->after('discount_price')
                  ->comment('官方指导价 - 用于渠道价格偏差率计算');
            
            // 添加官方指导价设置时间
            $table->timestamp('official_guide_price_set_at')->nullable()->after('official_guide_price')
                  ->comment('官方指导价设置时间');
            
            // 添加官方指导价数据源
            $table->string('official_guide_price_source', 50)->nullable()->after('official_guide_price_set_at')
                  ->comment('官方指导价数据源(manual/api/import)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_skus', function (Blueprint $table) {
            $table->dropColumn(['official_guide_price', 'official_guide_price_set_at', 'official_guide_price_source']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 先删除现有的外键约束
            $table->dropForeign(['task_id']);
            
            // 将task_id字段改为可空
            $table->foreignId('task_id')->nullable()->change()->comment('关联的监控任务ID(可选)');
            
            // 重新添加外键约束，但允许null值
            $table->foreign('task_id')->references('id')->on('monitor_tasks')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['task_id']);
            
            // 将task_id字段改为不可空
            $table->foreignId('task_id')->nullable(false)->change()->comment('关联的监控任务ID');
            
            // 重新添加外键约束
            $table->foreign('task_id')->references('id')->on('monitor_tasks')->onDelete('cascade');
        });
    }
}; 
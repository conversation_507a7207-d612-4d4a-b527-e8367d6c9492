<?php

namespace Tests\Feature;

use App\Models\ApiConfiguration;
use App\Models\Role;
use App\Models\User;
use App\Services\SecureConfigurationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Crypt;
use Tests\TestCase;

class SecureConfigurationTest extends TestCase
{
    use RefreshDatabase;

    protected SecureConfigurationService $secureConfigService;
    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->secureConfigService = new SecureConfigurationService();
        
        // 创建管理员角色和用户
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->roles()->attach($adminRole->id);
    }

    public function test_can_store_secure_configuration()
    {
        $configData = [
            'name' => 'Secure Test API',
            'slug' => 'secure-test-api',
            'platform_type' => 'taobao',
            'base_url' => 'https://api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => [
                'api_key' => 'test-secret-key-123',
                'secret_key' => 'super-secret-value',
                'username' => 'test-user', // 非敏感字段
            ],
            'is_active' => true,
        ];

        $configuration = $this->secureConfigService->storeSecureConfiguration($configData);

        $this->assertInstanceOf(ApiConfiguration::class, $configuration);
        $this->assertEquals('Secure Test API', $configuration->name);
        
        // 验证敏感字段被加密
        $storedCredentials = $configuration->auth_credentials;
        $this->assertNotEquals('test-secret-key-123', $storedCredentials['api_key']);
        $this->assertNotEquals('super-secret-value', $storedCredentials['secret_key']);
        $this->assertEquals('test-user', $storedCredentials['username']); // 非敏感字段不加密
    }

    public function test_can_decrypt_configuration()
    {
        $configData = [
            'name' => 'Decrypt Test API',
            'slug' => 'decrypt-test-api',
            'platform_type' => 'jd',
            'base_url' => 'https://api.jd.com',
            'auth_type' => 'api_key',
            'auth_credentials' => [
                'api_key' => 'original-secret-key',
                'access_token' => 'original-access-token',
            ],
            'is_active' => true,
        ];

        $configuration = $this->secureConfigService->storeSecureConfiguration($configData);
        $configuration->refresh(); // 确保从数据库获取最新数据
        $decryptedData = $this->secureConfigService->getDecryptedConfiguration($configuration);

        $this->assertArrayHasKey('auth_credentials', $decryptedData);
        if (isset($decryptedData['auth_credentials']['api_key'])) {
            $this->assertEquals('original-secret-key', $decryptedData['auth_credentials']['api_key']);
        }
        if (isset($decryptedData['auth_credentials']['access_token'])) {
            $this->assertEquals('original-access-token', $decryptedData['auth_credentials']['access_token']);
        }
    }

    public function test_can_update_secure_configuration()
    {
        $configuration = ApiConfiguration::create([
            'name' => 'Update Test API',
            'slug' => 'update-test-api',
            'platform_type' => 'taobao',
            'base_url' => 'https://api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'old-key'],
            'is_active' => true,
        ]);

        $updateData = [
            'auth_credentials' => [
                'api_key' => 'new-secret-key',
                'secret_key' => 'new-secret-value',
            ],
        ];

        $updatedConfig = $this->secureConfigService->updateSecureConfiguration($configuration, $updateData);
        $updatedConfig->refresh(); // 确保从数据库获取最新数据
        $decryptedData = $this->secureConfigService->getDecryptedConfiguration($updatedConfig);

        $this->assertEquals('new-secret-key', $decryptedData['auth_credentials']['api_key']);
        $this->assertEquals('new-secret-value', $decryptedData['auth_credentials']['secret_key']);
    }

    public function test_security_validation()
    {
        $insecureConfigData = [
            'base_url' => 'http://api.example.com', // HTTP instead of HTTPS
            'auth_credentials' => [
                'api_key' => '123', // Too short
            ],
        ];

        $validation = $this->secureConfigService->validateSecurity($insecureConfigData);

        $this->assertFalse($validation['is_secure']);
        $this->assertNotEmpty($validation['issues']);
        $this->assertNotEmpty($validation['recommendations']);
        
        // 检查具体的安全问题
        $issues = implode(' ', $validation['issues']);
        $this->assertStringContainsString('HTTPS', $issues);
        $this->assertStringContainsString('过短', $issues);
    }

    public function test_environment_variable_integration()
    {
        // 模拟环境变量
        putenv('API_CONFIG_TAOBAO_API_KEY=env-api-key-value');
        putenv('API_CONFIG_TAOBAO_BASE_URL=https://env.api.com');

        $configuration = ApiConfiguration::create([
            'name' => 'Env Test API',
            'slug' => 'env-test-api',
            'platform_type' => 'taobao',
            'base_url' => 'https://db.api.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'db-api-key'],
            'is_active' => true,
        ]);

        // 测试环境变量优先级
        $effectiveCredentials = $this->secureConfigService->getEffectiveCredentials($configuration);
        $effectiveBaseUrl = $this->secureConfigService->getEffectiveValue($configuration, 'base_url');

        $this->assertEquals('env-api-key-value', $effectiveCredentials['api_key']);
        $this->assertEquals('https://env.api.com', $effectiveBaseUrl);

        // 清理环境变量
        putenv('API_CONFIG_TAOBAO_API_KEY');
        putenv('API_CONFIG_TAOBAO_BASE_URL');
    }

    public function test_model_security_methods()
    {
        $configuration = ApiConfiguration::create([
            'name' => 'Model Test API',
            'slug' => 'model-test-api',
            'platform_type' => 'taobao',
            'base_url' => 'https://api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'is_active' => true,
        ]);

        // 测试安全验证方法
        $validation = $configuration->validateSecurity();
        $this->assertArrayHasKey('is_secure', $validation);
        $this->assertArrayHasKey('issues', $validation);
        $this->assertArrayHasKey('recommendations', $validation);

        // 测试环境变量检查
        $this->assertFalse($configuration->hasEnvironmentOverrides());
        $this->assertEmpty($configuration->getEnvironmentOverrides());

        // 模拟环境变量
        putenv('API_CONFIG_TAOBAO_API_KEY=env-value');
        
        $this->assertTrue($configuration->hasEnvironmentOverrides());
        $overrides = $configuration->getEnvironmentOverrides();
        $this->assertArrayHasKey('API_CONFIG_TAOBAO_API_KEY', $overrides);
        $this->assertEquals('***masked***', $overrides['API_CONFIG_TAOBAO_API_KEY']);

        // 清理
        putenv('API_CONFIG_TAOBAO_API_KEY');
    }

    public function test_sensitive_metadata_encryption()
    {
        $configData = [
            'name' => 'Metadata Test API',
            'slug' => 'metadata-test-api',
            'platform_type' => 'jd',
            'base_url' => 'https://api.jd.com',
            'auth_type' => 'oauth',
            'auth_credentials' => ['client_id' => 'test-client'],
            'metadata' => [
                'webhook_secret' => 'secret-webhook-key',
                'private_key' => 'private-key-content',
                'description' => 'This is not sensitive',
                'nested' => [
                    'api_key' => 'nested-secret-key',
                    'public_info' => 'This is public',
                ],
            ],
            'is_active' => true,
        ];

        $configuration = $this->secureConfigService->storeSecureConfiguration($configData);
        $configuration->refresh(); // 确保从数据库获取最新数据

        // 验证敏感元数据被加密
        $storedMetadata = $configuration->metadata;
        $this->assertNotEquals('secret-webhook-key', $storedMetadata['webhook_secret']);
        $this->assertNotEquals('private-key-content', $storedMetadata['private_key']);
        $this->assertEquals('This is not sensitive', $storedMetadata['description']);
        
        // 验证嵌套敏感数据被加密
        $this->assertNotEquals('nested-secret-key', $storedMetadata['nested']['api_key']);
        $this->assertEquals('This is public', $storedMetadata['nested']['public_info']);

        // 验证解密功能
        $decryptedData = $this->secureConfigService->getDecryptedConfiguration($configuration);
        $this->assertEquals('secret-webhook-key', $decryptedData['metadata']['webhook_secret']);
        $this->assertEquals('private-key-content', $decryptedData['metadata']['private_key']);
        $this->assertEquals('nested-secret-key', $decryptedData['metadata']['nested']['api_key']);
    }

    public function test_console_commands_exist()
    {
        // 测试命令是否注册
        $this->artisan('api-config:generate-env --template')
            ->assertExitCode(0);

        $this->artisan('api-config:validate-security')
            ->assertExitCode(0);
    }

    protected function tearDown(): void
    {
        // 清理可能的环境变量
        putenv('API_CONFIG_TAOBAO_API_KEY');
        putenv('API_CONFIG_TAOBAO_BASE_URL');
        putenv('API_CONFIG_TAOBAO_SECRET_KEY');
        
        parent::tearDown();
    }
}

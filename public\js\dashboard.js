/**
 * 仪表板JavaScript功能
 */

// 全局变量
let priceChart = null;
let categoryChart = null;
let trendChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// 初始化仪表板
async function initializeDashboard() {
    try {
        showLoading(true);
        
        // 并行加载数据
        await Promise.all([
            loadStatistics(),
            loadCharts()
        ]);
        
        // 设置定时刷新
        setInterval(loadStatistics, 30000); // 每30秒刷新统计数据
        setInterval(loadCharts, 60000); // 每分钟刷新图表
        
    } catch (error) {
        console.error('初始化仪表板失败:', error);
        showAlert('加载仪表板数据失败，请刷新页面重试', 'danger');
    } finally {
        showLoading(false);
    }
}

// 加载统计数据
async function loadStatistics() {
    try {
        const response = await apiService.getDashboardStats();
        
        if (response.success) {
            updateStatistics(response.data);
        } else {
            throw new Error(response.message || '获取统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        
        // 如果是首次加载失败，显示错误信息
        if (!document.getElementById('taskCount').textContent || document.getElementById('taskCount').textContent === '0') {
            showAlert('获取统计数据失败，显示模拟数据', 'warning');
            // 使用模拟数据作为后备
            updateStatistics({
                taskCount: 156,
                productCount: 1234,
                priceAlertCount: 23,
                todayUpdateCount: 89
            });
        }
    }
}

// 更新统计数据显示
function updateStatistics(data) {
    // 使用动画效果更新数字
    animateNumber('taskCount', data.taskCount || 0);
    animateNumber('productCount', data.productCount || 0);
    animateNumber('priceAlertCount', data.priceAlertCount || 0);
    animateNumber('todayUpdateCount', data.todayUpdateCount || 0);
}

// 数字动画效果
function animateNumber(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const currentValue = parseInt(element.textContent) || 0;
    const increment = Math.ceil((targetValue - currentValue) / 20);
    
    if (increment === 0) return;
    
    element.classList.add('updating');
    
    const timer = setInterval(() => {
        const current = parseInt(element.textContent) || 0;
        const next = current + increment;
        
        if ((increment > 0 && next >= targetValue) || (increment < 0 && next <= targetValue)) {
            element.textContent = targetValue;
            clearInterval(timer);
            element.classList.remove('updating');
        } else {
            element.textContent = next;
        }
    }, 50);
}

// 加载图表数据
async function loadCharts() {
    try {
        // 并行加载所有图表数据
        const [priceData, categoryData, trendData] = await Promise.all([
            loadChartData('price-trend'),
            loadChartData('category-distribution'),
            loadChartData('monitoring-trend')
        ]);
        
        // 初始化图表
        initializePriceChart(priceData);
        initializeCategoryChart(categoryData);
        initializeTrendChart(trendData);
        
    } catch (error) {
        console.error('加载图表数据失败:', error);
        showAlert('图表数据加载失败，显示模拟数据', 'warning');
        
        // 使用模拟数据
        initializeChartsWithMockData();
    }
}

// 加载单个图表数据
async function loadChartData(chartType) {
    try {
        const response = await apiService.getChartData(chartType);
        return response.success ? response.data : null;
    } catch (error) {
        console.error(`加载${chartType}图表数据失败:`, error);
        return null;
    }
}

// 初始化价格趋势图表
function initializePriceChart(data) {
    const ctx = document.getElementById('priceChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (priceChart) {
        priceChart.destroy();
    }
    
    const chartData = data || getMockPriceData();
    
    priceChart = new Chart(ctx.getContext('2d'), {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: '平均价格',
                data: chartData.prices,
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '价格趋势分析'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 初始化类目分布图表
function initializeCategoryChart(data) {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (categoryChart) {
        categoryChart.destroy();
    }
    
    const chartData = data || getMockCategoryData();
    
    categoryChart = new Chart(ctx.getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.values,
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b',
                    '#858796'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '商品类目分布'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 初始化监控趋势图表
function initializeTrendChart(data) {
    const ctx = document.getElementById('trendChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (trendChart) {
        trendChart.destroy();
    }
    
    const chartData = data || getMockTrendData();
    
    trendChart = new Chart(ctx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: '监控任务数',
                data: chartData.tasks,
                backgroundColor: '#1cc88a',
                borderColor: '#1cc88a',
                borderWidth: 1
            }, {
                label: '异常数量',
                data: chartData.alerts,
                backgroundColor: '#e74a3b',
                borderColor: '#e74a3b',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '监控趋势统计'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 使用模拟数据初始化图表
function initializeChartsWithMockData() {
    initializePriceChart(null);
    initializeCategoryChart(null);
    initializeTrendChart(null);
}

// 模拟数据生成函数
function getMockPriceData() {
    const labels = [];
    const prices = [];
    const now = new Date();
    
    for (let i = 29; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        prices.push(Math.random() * 100 + 50);
    }
    
    return { labels, prices };
}

function getMockCategoryData() {
    return {
        labels: ['电子产品', '服装鞋帽', '家居用品', '食品饮料', '美妆护肤', '其他'],
        values: [35, 25, 15, 12, 8, 5]
    };
}

function getMockTrendData() {
    const labels = [];
    const tasks = [];
    const alerts = [];
    
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { weekday: 'short' }));
        tasks.push(Math.floor(Math.random() * 50) + 20);
        alerts.push(Math.floor(Math.random() * 10) + 1);
    }
    
    return { labels, tasks, alerts };
}

// 工具函数
function showAlert(message, type = 'info') {
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function showLoading(show) {
    const existingSpinner = document.querySelector('.dashboard-loading');
    
    if (show && !existingSpinner) {
        const spinner = document.createElement('div');
        spinner.className = 'dashboard-loading position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        spinner.style.backgroundColor = 'rgba(255,255,255,0.8)';
        spinner.style.zIndex = '9998';
        spinner.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="text-muted">正在加载仪表板数据...</div>
            </div>
        `;
        document.body.appendChild(spinner);
    } else if (!show && existingSpinner) {
        existingSpinner.remove();
    }
}

// 刷新所有数据
function refreshDashboard() {
    showLoading(true);
    Promise.all([
        loadStatistics(),
        loadCharts()
    ]).finally(() => {
        showLoading(false);
        showAlert('仪表板数据已刷新', 'success');
    });
}

// 导出刷新函数供HTML调用
window.refreshDashboard = refreshDashboard;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->string('event')->comment('事件类型');
            $table->string('auditable_type')->nullable()->comment('操作对象类型');
            $table->unsignedBigInteger('auditable_id')->nullable()->comment('操作对象ID');
            $table->json('old_values')->nullable()->comment('修改前的值');
            $table->json('new_values')->nullable()->comment('修改后的值');
            $table->string('url')->nullable()->comment('请求URL');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('user_agent')->nullable()->comment('用户代理');
            $table->json('metadata')->nullable()->comment('额外元数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'created_at']);
            $table->index(['event', 'created_at']);
            $table->index(['auditable_type', 'auditable_id']);
            $table->index('created_at');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};

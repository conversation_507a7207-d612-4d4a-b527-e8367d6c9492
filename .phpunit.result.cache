{"version": 1, "defects": {"Tests\\Feature\\SimpleOfficialGuidePriceTest::service_instance_can_be_created": 8, "Tests\\Feature\\SimpleOfficialGuidePriceTest::can_set_guide_price_for_single_sku": 8, "Tests\\Feature\\SimpleOfficialGuidePriceTest::validates_guide_price_input": 8, "Tests\\Feature\\SimpleOfficialGuidePriceTest::can_get_guide_price_stats": 8, "Tests\\Feature\\SimpleOfficialGuidePriceTest::can_clear_guide_price": 8, "Tests\\Feature\\SimpleOfficialGuidePriceTest::can_batch_set_guide_prices": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_set_guide_price_for_single_sku": 8, "Tests\\Feature\\OfficialGuidePriceTest::validates_guide_price_input": 7, "Tests\\Feature\\OfficialGuidePriceTest::can_batch_set_guide_prices": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_max_method": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_avg_method": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_percentile_method": 8, "Tests\\Feature\\OfficialGuidePriceTest::auto_calculation_fails_without_price_history": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_get_skus_needing_guide_price": 8, "Tests\\Feature\\OfficialGuidePriceTest::can_get_guide_price_stats": 7, "Tests\\Feature\\OfficialGuidePriceTest::can_import_from_csv_content": 7, "Tests\\Feature\\OfficialGuidePriceTest::handles_csv_import_errors": 8, "Tests\\Feature\\OfficialGuidePriceTest::csv_import_handles_missing_file": 8, "Tests\\Feature\\OfficialGuidePriceTest::triggers_deviation_recalculation_when_guide_price_set": 8, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_analyze_with_promotion_data": 7, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_analyze_without_promotion_data": 8, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_average_promotion_duration_with_continuous_periods": 8, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_competitors_with_keywords": 7, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_with_exclude_words": 8, "Tests\\Unit\\CompetitorSearchServiceTest::test_category_path_matching": 7, "Tests\\Unit\\CompetitorSearchServiceTest::test_and_or_operator_logic": 8, "Tests\\Unit\\CompetitorSearchServiceTest::test_find_similar_competitors": 7, "Tests\\Unit\\CompetitorSearchServiceTest::test_relevance_scoring": 7, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_suggestions": 8, "Tests\\Unit\\CompetitorSearchServiceTest::test_price_and_rating_filters": 8, "Tests\\Unit\\PriceComparisonServiceTest::it_calculates_price_deviation_rate_correctly": 8, "Tests\\Feature\\CompetitorMetricsApiTest::it_can_fetch_competitor_metrics": 8, "Tests\\Feature\\Feature\\CompetitorMetricsApiTest::test_example": 8, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_overall_similarity_correctly": 8, "Tests\\Unit\\ProductSimilarityServiceTest::it_handles_batch_similarity_calculation": 7, "Tests\\Unit\\ProductSimilarityServiceTest::it_returns_algorithm_configuration": 7, "Tests\\Unit\\ProductSimilarityServiceTest::it_handles_edge_cases_gracefully": 8, "Tests\\Feature\\ApiRateLimitTest::test_can_check_initial_rate_limits": 8, "Tests\\Feature\\ApiRateLimitTest::test_can_record_request_and_check_limits": 7, "Tests\\Feature\\ApiRateLimitTest::test_rate_limit_exceeded": 7, "Tests\\Feature\\ApiRateLimitTest::test_can_reset_rate_limits": 8, "Tests\\Feature\\ApiRateLimitTest::test_can_get_usage_stats": 7, "Tests\\Feature\\ApiRateLimitTest::test_health_check_successful": 8, "Tests\\Feature\\ApiRateLimitTest::test_health_check_failed": 8, "Tests\\Feature\\ApiRateLimitTest::test_bulk_health_check": 8, "Tests\\Feature\\ApiRateLimitTest::test_controller_health_check_with_rate_limit": 8, "Tests\\Feature\\ApiRateLimitTest::test_controller_rate_limit_status": 8, "Tests\\Feature\\ApiRateLimitTest::test_controller_reset_rate_limits": 8, "Tests\\Feature\\ApiConfigurationTest::test_can_create_api_configuration": 8, "Tests\\Feature\\ApiConfigurationTest::test_api_configuration_validates_required_fields": 7, "Tests\\Feature\\ApiConfigurationTest::test_can_toggle_active_status": 8, "Tests\\Feature\\ApiConfigurationTest::test_rate_limit_service_works": 7, "Tests\\Feature\\ApiConfigurationTest::test_health_monitor_service_works": 7, "Tests\\Feature\\ApiConfigurationTest::test_can_get_client_config": 7, "Tests\\Feature\\ApiConfigurationTest::test_non_admin_cannot_manage_configurations": 7, "Tests\\Feature\\ApiConfigurationTest::test_health_check_respects_rate_limits": 7, "Tests\\Feature\\ApiConfigurationTest::test_bulk_health_check_works": 7, "Tests\\Feature\\ApiConfigurationTest::test_rate_limit_service_works_with_mock": 7, "Tests\\Feature\\ApiVersionManagementTest::test_can_create_new_version": 8, "Tests\\Feature\\ApiVersionManagementTest::test_can_upgrade_to_version": 7, "Tests\\Feature\\ApiVersionManagementTest::test_can_deprecate_version": 7, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_compatibility_info": 8, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_migration_path": 7, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_version_statistics": 8, "Tests\\Feature\\ApiVersionManagementTest::test_api_configuration_version_methods": 7, "Tests\\Feature\\ApiVersionManagementTest::test_controller_create_version": 7, "Tests\\Feature\\ApiVersionManagementTest::test_controller_upgrade_version": 8, "Tests\\Feature\\ApiVersionManagementTest::test_controller_get_migration_path": 7, "Tests\\Feature\\ApiVersionManagementTest::test_controller_get_version_statistics": 7, "Tests\\Feature\\SecureConfigurationTest::test_can_decrypt_configuration": 8, "Tests\\Feature\\SecureConfigurationTest::test_can_update_secure_configuration": 8, "Tests\\Feature\\SecureConfigurationTest::test_model_security_methods": 8, "Tests\\Feature\\SecureConfigurationTest::test_sensitive_metadata_encryption": 8, "Tests\\Feature\\SystemConfigurationTest::test_encrypted_configuration": 7, "Tests\\Feature\\SystemConfigurationTest::test_cache_clearing": 7, "Tests\\Feature\\SystemConfigurationTest::test_controller_get_public_configurations": 7, "Tests\\Feature\\LoggingAndMonitoringTest::test_audit_log_scopes": 7, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_dashboard": 7, "Tests\\Feature\\LoggingAndMonitoringTest::test_requires_admin_role_for_monitoring": 7, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_test_page_loads": 7, "Tests\\Feature\\ResponsiveDesignTest::test_layout_uses_local_assets": 7, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_table_structure": 7, "Tests\\Feature\\ResponsiveDesignTest::test_accessibility_features": 7, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_breakpoints_display": 7, "Tests\\Feature\\ResponsiveDesignTest::test_assets_are_accessible": 7}, "times": {"Tests\\Unit\\ExampleTest::test_that_true_is_true": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::service_can_be_instantiated": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::service_has_required_constants": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::validates_price_input": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::validates_source_input": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::percentile_calculation_works_correctly": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::average_calculation_works_correctly": 0, "Tests\\Unit\\OfficialGuidePriceServiceTest::max_calculation_works_correctly": 0, "Tests\\Feature\\OfficialGuidePriceTest::can_set_guide_price_for_single_sku": 0.018, "Tests\\Feature\\OfficialGuidePriceTest::validates_guide_price_input": 0.003, "Tests\\Feature\\OfficialGuidePriceTest::can_batch_set_guide_prices": 0.01, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_max_method": 0.009, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_avg_method": 0.008, "Tests\\Feature\\OfficialGuidePriceTest::can_calculate_auto_guide_price_with_percentile_method": 0.009, "Tests\\Feature\\OfficialGuidePriceTest::auto_calculation_fails_without_price_history": 0.002, "Tests\\Feature\\OfficialGuidePriceTest::can_get_skus_needing_guide_price": 0.006, "Tests\\Feature\\OfficialGuidePriceTest::can_get_guide_price_stats": 0.008, "Tests\\Feature\\OfficialGuidePriceTest::can_import_from_csv_content": 0.008, "Tests\\Feature\\OfficialGuidePriceTest::handles_csv_import_errors": 0.005, "Tests\\Feature\\OfficialGuidePriceTest::csv_import_handles_missing_file": 0.001, "Tests\\Feature\\OfficialGuidePriceTest::triggers_deviation_recalculation_when_guide_price_set": 0.007, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_competitors_with_keywords": 0.051, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_with_exclude_words": 0.006, "Tests\\Unit\\CompetitorSearchServiceTest::test_category_path_matching": 0.075, "Tests\\Unit\\CompetitorSearchServiceTest::test_and_or_operator_logic": 0.008, "Tests\\Unit\\CompetitorSearchServiceTest::test_find_similar_competitors": 0.007, "Tests\\Unit\\CompetitorSearchServiceTest::test_relevance_scoring": 0.006, "Tests\\Unit\\CompetitorSearchServiceTest::test_search_suggestions": 0.006, "Tests\\Unit\\CompetitorSearchServiceTest::test_price_and_rating_filters": 0.005, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_analyze_with_promotion_data": 0.016, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_analyze_without_promotion_data": 0.003, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_average_promotion_duration_with_continuous_periods": 0.006, "Tests\\Unit\\CompetitorPromotionAnalysisServiceTest::test_it_can_summarize_promotion_strategy": 0.007, "Tests\\Unit\\PriceComparisonServiceTest::it_calculates_price_deviation_rate_correctly": 0.011, "Tests\\Unit\\PriceComparisonServiceTest::it_analyzes_min_max_price_deviation_range": 0.01, "Tests\\Unit\\PriceComparisonServiceTest::it_calculates_single_product_discount_rate": 0.006, "Tests\\Unit\\PriceComparisonServiceTest::it_calculates_overall_promotion_intensity": 0.009, "Tests\\Feature\\Feature\\CompetitorMetricsApiTest::test_example": 0.673, "Tests\\Feature\\CompetitorMetricsApiTest::it_can_fetch_competitor_metrics": 0.06, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_title_similarity_correctly": 0.009, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_category_similarity_correctly": 0.001, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_price_similarity_correctly": 0.001, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_shop_type_similarity_correctly": 0.001, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_overall_similarity_correctly": 0.043, "Tests\\Unit\\ProductSimilarityServiceTest::it_handles_batch_similarity_calculation": 0.028, "Tests\\Unit\\ProductSimilarityServiceTest::it_returns_algorithm_configuration": 0.001, "Tests\\Unit\\ProductSimilarityServiceTest::it_handles_edge_cases_gracefully": 0.007, "Tests\\Unit\\ProductSimilarityServiceTest::it_handles_unicode_characters_in_titles": 0.001, "Tests\\Unit\\ProductSimilarityServiceTest::it_calculates_cosine_similarity_correctly": 0.001, "Tests\\Feature\\ApiRateLimitTest::test_can_check_initial_rate_limits": 0.013, "Tests\\Feature\\ApiRateLimitTest::test_can_record_request_and_check_limits": 0.005, "Tests\\Feature\\ApiRateLimitTest::test_rate_limit_exceeded": 0.037, "Tests\\Feature\\ApiRateLimitTest::test_can_reset_rate_limits": 0.022, "Tests\\Feature\\ApiRateLimitTest::test_can_get_usage_stats": 0.006, "Tests\\Feature\\ApiRateLimitTest::test_health_check_successful": 0.02, "Tests\\Feature\\ApiRateLimitTest::test_health_check_failed": 0.004, "Tests\\Feature\\ApiRateLimitTest::test_bulk_health_check": 0.005, "Tests\\Feature\\ApiRateLimitTest::test_controller_health_check_with_rate_limit": 0.067, "Tests\\Feature\\ApiRateLimitTest::test_controller_rate_limit_status": 0.011, "Tests\\Feature\\ApiRateLimitTest::test_controller_reset_rate_limits": 0.017, "Tests\\Feature\\ApiConfigurationTest::test_can_create_api_configuration": 0.07, "Tests\\Feature\\ApiConfigurationTest::test_api_configuration_validates_required_fields": 0.01, "Tests\\Feature\\ApiConfigurationTest::test_can_toggle_active_status": 0.011, "Tests\\Feature\\ApiConfigurationTest::test_rate_limit_service_works": 0.014, "Tests\\Feature\\ApiConfigurationTest::test_health_monitor_service_works": 0.022, "Tests\\Feature\\ApiConfigurationTest::test_can_get_client_config": 0.012, "Tests\\Feature\\ApiConfigurationTest::test_non_admin_cannot_manage_configurations": 0.013, "Tests\\Feature\\ApiConfigurationTest::test_health_check_respects_rate_limits": 1.34, "Tests\\Feature\\ApiConfigurationTest::test_bulk_health_check_works": 0.013, "Tests\\Feature\\ApiConfigurationTest::test_rate_limit_service_works_with_mock": 0.019, "Tests\\Feature\\PlatformAdapterTest::test_platform_adapter_factory_supports_multiple_platforms": 0.013, "Tests\\Feature\\PlatformAdapterTest::test_can_create_taobao_adapter": 0.014, "Tests\\Feature\\PlatformAdapterTest::test_can_create_jd_adapter": 0.001, "Tests\\Feature\\PlatformAdapterTest::test_adapter_has_required_methods": 0.002, "Tests\\Feature\\PlatformAdapterTest::test_adapter_supported_features": 0.335, "Tests\\Feature\\PlatformAdapterTest::test_adapter_api_version": 0.003, "Tests\\Feature\\PlatformAdapterTest::test_adapter_statistics": 0.001, "Tests\\Feature\\PlatformAdapterTest::test_adapter_health_check": 0.13, "Tests\\Feature\\PlatformAdapterTest::test_can_create_adapter_from_api_configuration": 0.012, "Tests\\Feature\\PlatformAdapterTest::test_adapter_manager_service": 2.701, "Tests\\Feature\\PlatformAdapterTest::test_adapter_manager_batch_tasks": 1.045, "Tests\\Feature\\PlatformAdapterTest::test_platform_config_retrieval": 0.001, "Tests\\Feature\\PlatformAdapterTest::test_unsupported_platform_throws_exception": 0.003, "Tests\\Feature\\PlatformAdapterTest::test_jd_adapter_specific_features": 0.001, "Tests\\Feature\\ApiVersionManagementTest::test_can_create_new_version": 0.018, "Tests\\Feature\\ApiVersionManagementTest::test_cannot_create_duplicate_version": 0.004, "Tests\\Feature\\ApiVersionManagementTest::test_can_upgrade_to_version": 0.017, "Tests\\Feature\\ApiVersionManagementTest::test_can_deprecate_version": 0.005, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_compatibility_info": 0.009, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_migration_path": 0.015, "Tests\\Feature\\ApiVersionManagementTest::test_can_get_version_statistics": 0.008, "Tests\\Feature\\ApiVersionManagementTest::test_api_configuration_version_methods": 0.009, "Tests\\Feature\\ApiVersionManagementTest::test_controller_get_version_info": 0.043, "Tests\\Feature\\ApiVersionManagementTest::test_controller_create_version": 0.021, "Tests\\Feature\\ApiVersionManagementTest::test_controller_upgrade_version": 0.01, "Tests\\Feature\\ApiVersionManagementTest::test_controller_deprecate_version": 0.009, "Tests\\Feature\\ApiVersionManagementTest::test_controller_get_migration_path": 0.011, "Tests\\Feature\\ApiVersionManagementTest::test_controller_get_version_statistics": 0.007, "Tests\\Feature\\SecureConfigurationTest::test_can_store_secure_configuration": 0.022, "Tests\\Feature\\SecureConfigurationTest::test_can_decrypt_configuration": 0.016, "Tests\\Feature\\SecureConfigurationTest::test_can_update_secure_configuration": 0.004, "Tests\\Feature\\SecureConfigurationTest::test_security_validation": 0.003, "Tests\\Feature\\SecureConfigurationTest::test_environment_variable_integration": 0.002, "Tests\\Feature\\SecureConfigurationTest::test_model_security_methods": 0.004, "Tests\\Feature\\SecureConfigurationTest::test_sensitive_metadata_encryption": 0.004, "Tests\\Feature\\SecureConfigurationTest::test_console_commands_exist": 0.009, "Tests\\Feature\\SystemConfigurationTest::test_can_create_system_configuration": 0.01, "Tests\\Feature\\SystemConfigurationTest::test_can_get_configuration_value": 0.004, "Tests\\Feature\\SystemConfigurationTest::test_can_set_configuration_value": 0.004, "Tests\\Feature\\SystemConfigurationTest::test_can_get_configurations_by_category": 0.003, "Tests\\Feature\\SystemConfigurationTest::test_can_get_public_configurations": 0.002, "Tests\\Feature\\SystemConfigurationTest::test_encrypted_configuration": 0.006, "Tests\\Feature\\SystemConfigurationTest::test_configuration_type_casting": 0.003, "Tests\\Feature\\SystemConfigurationTest::test_configuration_caching": 0.002, "Tests\\Feature\\SystemConfigurationTest::test_cache_clearing": 0.004, "Tests\\Feature\\SystemConfigurationTest::test_controller_get_by_category": 0.039, "Tests\\Feature\\SystemConfigurationTest::test_controller_get_public_configurations": 0.004, "Tests\\Feature\\SystemConfigurationTest::test_controller_update_configuration": 0.014, "Tests\\Feature\\SystemConfigurationTest::test_controller_batch_update": 0.022, "Tests\\Feature\\SystemConfigurationTest::test_controller_reset_to_default": 0.007, "Tests\\Feature\\SystemConfigurationTest::test_controller_export_configurations": 0.008, "Tests\\Feature\\SystemConfigurationTest::test_validation_rules": 0.007, "Tests\\Feature\\SystemConfigurationTest::test_requires_admin_role": 0.005, "Tests\\Feature\\LoggingAndMonitoringTest::test_can_create_audit_log": 0.011, "Tests\\Feature\\LoggingAndMonitoringTest::test_audit_log_static_methods": 0.01, "Tests\\Feature\\LoggingAndMonitoringTest::test_audit_log_scopes": 0.03, "Tests\\Feature\\LoggingAndMonitoringTest::test_audit_log_helper_methods": 0.002, "Tests\\Feature\\LoggingAndMonitoringTest::test_performance_monitor_service": 0.06, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_dashboard": 0.392, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_system_overview": 0.048, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_response_time_stats": 0.005, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_audit_logs": 0.011, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_health_check": 0.015, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_clear_cache": 0.006, "Tests\\Feature\\LoggingAndMonitoringTest::test_monitoring_controller_user_activity_stats": 0.007, "Tests\\Feature\\LoggingAndMonitoringTest::test_performance_monitor_caching": 0.044, "Tests\\Feature\\LoggingAndMonitoringTest::test_requires_admin_role_for_monitoring": 0.27, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_test_page_loads": 0.362, "Tests\\Feature\\ResponsiveDesignTest::test_layout_contains_responsive_meta_tag": 0.272, "Tests\\Feature\\ResponsiveDesignTest::test_layout_uses_local_assets": 0.277, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_css_classes_present": 0.283, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_table_structure": 0.282, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_form_layout": 0.273, "Tests\\Feature\\ResponsiveDesignTest::test_mobile_friendly_button_sizes": 0.271, "Tests\\Feature\\ResponsiveDesignTest::test_css_media_queries_present": 0.28, "Tests\\Feature\\ResponsiveDesignTest::test_touch_device_optimizations": 0.27, "Tests\\Feature\\ResponsiveDesignTest::test_accessibility_features": 0.283, "Tests\\Feature\\ResponsiveDesignTest::test_sidebar_responsive_behavior": 0.279, "Tests\\Feature\\ResponsiveDesignTest::test_navbar_responsive_elements": 0.278, "Tests\\Feature\\ResponsiveDesignTest::test_card_responsive_design": 0.277, "Tests\\Feature\\ResponsiveDesignTest::test_font_size_responsive_scaling": 0.276, "Tests\\Feature\\ResponsiveDesignTest::test_javascript_device_detection": 0.287, "Tests\\Feature\\ResponsiveDesignTest::test_high_dpi_screen_support": 0.279, "Tests\\Feature\\ResponsiveDesignTest::test_dark_mode_support": 0.279, "Tests\\Feature\\ResponsiveDesignTest::test_responsive_breakpoints_display": 0.285, "Tests\\Feature\\ResponsiveDesignTest::test_mobile_table_transformation": 0.276, "Tests\\Feature\\ResponsiveDesignTest::test_loading_performance_optimizations": 0.289, "Tests\\Feature\\ResponsiveDesignTest::test_assets_are_accessible": 0.259}}
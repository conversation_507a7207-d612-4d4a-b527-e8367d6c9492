@extends('layouts.app')

@section('title', '数据源管理')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">数据源管理</li>
@endsection

@section('page-title', '数据源管理')

@section('page-actions')
    <div class="btn-group">
        <a href="{{ route('data-sources.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>添加数据源
        </a>
        <a href="{{ route('data-sources.excel-create') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>Excel导入
        </a>
        <a href="{{ route('data-sources.import-history') }}" class="btn btn-info">
            <i class="fas fa-history me-1"></i>导入历史
        </a>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <!-- 数据源列表 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-database me-2"></i>数据源列表
                </h6>
            </div>
            <div class="card-body">
                <!-- 搜索和筛选 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索数据源...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchDataSources()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="platformFilter" onchange="filterByPlatform()">
                            <option value="">全部平台</option>
                            <option value="淘宝">淘宝</option>
                            <option value="京东">京东</option>
                            <option value="天猫">天猫</option>
                            <option value="拼多多">拼多多</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" onchange="filterByStatus()">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                            <option value="error">错误</option>
                        </select>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>数据源名称</th>
                                <th>平台</th>
                                <th>类型</th>
                                <th>商品数量</th>
                                <th>状态</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataSourcesTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示 <span id="showingStart">1</span> 到 <span id="showingEnd">10</span> 条，共 <span id="totalRecords">0</span> 条记录
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                            <!-- 分页将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您选择了 <span id="selectedCount">0</span> 个数据源，请选择要执行的操作：</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="batchActivate()">
                        <i class="fas fa-play me-2"></i>批量激活
                    </button>
                    <button type="button" class="btn btn-warning" onclick="batchDeactivate()">
                        <i class="fas fa-pause me-2"></i>批量停用
                    </button>
                    <button type="button" class="btn btn-info" onclick="batchUpdate()">
                        <i class="fas fa-sync me-2"></i>批量更新
                    </button>
                    <button type="button" class="btn btn-danger" onclick="batchDelete()">
                        <i class="fas fa-trash me-2"></i>批量删除
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ asset('js/api-client.js') }}"></script>
<script src="{{ asset('js/data-sources.js') }}"></script>
<script>
// 页面特定功能
let currentPage = 1;
let itemsPerPage = 10;
let selectedItems = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDataSources();
});

// 加载数据源列表
function loadDataSources() {
    // 模拟数据
    const mockDataSources = [
        {
            id: 1,
            name: '淘宝热销商品',
            platform: '淘宝',
            type: 'API',
            product_count: 1250,
            status: 'active',
            last_updated: '2024-01-15 10:30:00'
        },
        {
            id: 2,
            name: '京东数码产品',
            platform: '京东',
            type: 'Excel',
            product_count: 890,
            status: 'active',
            last_updated: '2024-01-15 09:45:00'
        },
        {
            id: 3,
            name: '天猫美妆类目',
            platform: '天猫',
            type: 'API',
            product_count: 567,
            status: 'inactive',
            last_updated: '2024-01-14 16:20:00'
        }
    ];

    renderDataSources(mockDataSources);
    updatePagination(1, 10, mockDataSources.length);
}

// 渲染数据源列表
function renderDataSources(dataSources) {
    const tbody = document.getElementById('dataSourcesTableBody');
    
    if (dataSources.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <div>暂无数据源</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = dataSources.map(source => `
        <tr>
            <td>
                <input type="checkbox" class="item-checkbox" value="${source.id}" onchange="updateSelectedItems()">
            </td>
            <td>
                <div class="fw-bold">${source.name}</div>
                <small class="text-muted">ID: ${source.id}</small>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(source.platform)}">${source.platform}</span>
            </td>
            <td>
                <span class="badge bg-${getTypeColor(source.type)}">${source.type}</span>
            </td>
            <td>${source.product_count.toLocaleString()}</td>
            <td>
                <span class="badge bg-${getStatusColor(source.status)}">${getStatusText(source.status)}</span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(source.last_updated)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewDataSource(${source.id})" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="editDataSource(${source.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="syncDataSource(${source.id})" title="同步">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteDataSource(${source.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 工具函数
function getPlatformColor(platform) {
    const colors = {
        '淘宝': 'warning',
        '京东': 'danger',
        '天猫': 'primary',
        '拼多多': 'success'
    };
    return colors[platform] || 'secondary';
}

function getTypeColor(type) {
    const colors = {
        'API': 'info',
        'Excel': 'success',
        'CSV': 'warning'
    };
    return colors[type] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'inactive': 'secondary',
        'error': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '活跃',
        'inactive': '非活跃',
        'error': '错误'
    };
    return texts[status] || status;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 操作函数
function searchDataSources() {
    console.log('搜索数据源');
}

function filterByPlatform() {
    console.log('按平台筛选');
}

function filterByStatus() {
    console.log('按状态筛选');
}

function viewDataSource(id) {
    console.log('查看数据源:', id);
}

function editDataSource(id) {
    console.log('编辑数据源:', id);
}

function syncDataSource(id) {
    console.log('同步数据源:', id);
}

function deleteDataSource(id) {
    if (confirm('确定要删除这个数据源吗？')) {
        console.log('删除数据源:', id);
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.item-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedItems();
}

function updateSelectedItems() {
    const checkboxes = document.querySelectorAll('.item-checkbox:checked');
    selectedItems = Array.from(checkboxes).map(cb => cb.value);
    
    if (selectedItems.length > 0) {
        showBatchActions();
    } else {
        hideBatchActions();
    }
}

function showBatchActions() {
    // 显示批量操作按钮或工具栏
    console.log('显示批量操作');
}

function hideBatchActions() {
    // 隐藏批量操作按钮或工具栏
    console.log('隐藏批量操作');
}

function updatePagination(current, perPage, total) {
    // 更新分页信息
    document.getElementById('showingStart').textContent = ((current - 1) * perPage + 1);
    document.getElementById('showingEnd').textContent = Math.min(current * perPage, total);
    document.getElementById('totalRecords').textContent = total;
}
</script>
@endsection

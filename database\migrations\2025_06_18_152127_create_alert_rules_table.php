<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alert_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('创建规则的用户ID');
            $table->foreignId('task_id')->constrained('monitor_tasks')->onDelete('cascade')->comment('关联的监控任务ID');
            $table->string('rule_name')->comment('规则名称');
            $table->text('description')->nullable()->comment('规则描述');
            $table->enum('type', ['price_drop', 'price_rise', 'stock_change', 'custom'])->comment('告警类型');
            $table->decimal('threshold', 10, 2)->nullable()->comment('阈值');
            $table->decimal('official_price', 10, 2)->nullable()->comment('官方价格参考');
            $table->decimal('percentage_threshold', 5, 2)->nullable()->comment('百分比阈值');
            $table->json('conditions')->nullable()->comment('复杂条件配置');
            $table->enum('comparison', ['greater_than', 'less_than', 'equal', 'greater_equal', 'less_equal'])->default('less_than')->comment('比较操作符');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->enum('notification_method', ['email', 'sms', 'push', 'all'])->default('email')->comment('通知方式');
            $table->json('notification_settings')->nullable()->comment('通知设置');
            $table->unsignedInteger('trigger_count')->default(0)->comment('触发次数');
            $table->timestamp('last_triggered_at')->nullable()->comment('最后触发时间');
            $table->unsignedInteger('cooldown_minutes')->default(60)->comment('冷却时间(分钟)');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'is_active']);
            $table->index(['task_id', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index(['threshold', 'comparison']);
            $table->index('last_triggered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alert_rules');
    }
};

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class QueueConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:config 
                            {action? : 配置操作 (show|setup|test|optimize)}
                            {--driver= : 队列驱动 (database|redis|sync)}
                            {--connection= : 数据库连接名称}
                            {--table=jobs : 队列表名}
                            {--redis-connection=default : Redis连接名称}
                            {--batch-size=1000 : 批处理大小}
                            {--retry-after=90 : 重试间隔（秒）}
                            {--block-for=null : Redis阻塞时间}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '配置Laravel队列驱动和连接参数';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action') ?? 'show';

        switch ($action) {
            case 'show':
                return $this->showCurrentConfig();
            case 'setup':
                return $this->setupQueue();
            case 'test':
                return $this->testConnection();
            case 'optimize':
                return $this->optimizeConfig();
            default:
                $this->error("未知操作: {$action}");
                $this->line('可用操作: show, setup, test, optimize');
                return 1;
        }
    }

    /**
     * 显示当前队列配置
     */
    protected function showCurrentConfig()
    {
        $this->info('=== 当前队列配置 ===');

        // 基本配置
        $config = [
            ['配置项', '当前值'],
            ['默认队列驱动', config('queue.default')],
            ['失败队列驱动', config('queue.failed.driver')],
            ['批处理大小', config('queue.batching.batch_size', 1000)],
        ];

        $this->table(['配置项', '当前值'], array_slice($config, 1));

        // 显示各驱动配置
        $this->showDriverConfigs();

        // 显示队列表状态（如果使用数据库驱动）
        if (config('queue.default') === 'database') {
            $this->showDatabaseQueueStatus();
        }

        // 显示Redis状态（如果使用Redis驱动）
        if (config('queue.default') === 'redis') {
            $this->showRedisQueueStatus();
        }

        return 0;
    }

    /**
     * 设置队列配置
     */
    protected function setupQueue()
    {
        $this->info('=== 队列配置向导 ===');

        // 选择队列驱动
        $driver = $this->option('driver') ?? $this->choice(
            '选择队列驱动',
            ['database', 'redis', 'sync'],
            0
        );

        switch ($driver) {
            case 'database':
                return $this->setupDatabaseQueue();
            case 'redis':
                return $this->setupRedisQueue();
            case 'sync':
                return $this->setupSyncQueue();
            default:
                $this->error("不支持的驱动: {$driver}");
                return 1;
        }
    }

    /**
     * 测试队列连接
     */
    protected function testConnection()
    {
        $this->info('=== 测试队列连接 ===');

        $driver = config('queue.default');
        $this->line("当前驱动: {$driver}");

        switch ($driver) {
            case 'database':
                return $this->testDatabaseConnection();
            case 'redis':
                return $this->testRedisConnection();
            case 'sync':
                $this->info('同步驱动无需连接测试');
                return 0;
            default:
                $this->error("不支持的驱动: {$driver}");
                return 1;
        }
    }

    /**
     * 优化队列配置
     */
    protected function optimizeConfig()
    {
        $this->info('=== 队列配置优化建议 ===');

        $driver = config('queue.default');
        
        switch ($driver) {
            case 'database':
                $this->optimizeDatabaseConfig();
                break;
            case 'redis':
                $this->optimizeRedisConfig();
                break;
            case 'sync':
                $this->warn('同步驱动不适合生产环境，建议使用database或redis驱动');
                break;
        }

        return 0;
    }

    /**
     * 显示驱动配置
     */
    protected function showDriverConfigs()
    {
        $this->newLine();
        $this->info('=== 驱动配置详情 ===');

        // 数据库驱动配置
        $dbConfig = config('queue.connections.database');
        if ($dbConfig) {
            $this->line('数据库驱动配置:');
            $this->table(['参数', '值'], [
                ['连接', $dbConfig['connection'] ?? 'default'],
                ['表名', $dbConfig['table'] ?? 'jobs'],
                ['队列', $dbConfig['queue'] ?? 'default'],
                ['重试间隔', $dbConfig['retry_after'] ?? 90],
            ]);
        }

        // Redis驱动配置
        $redisConfig = config('queue.connections.redis');
        if ($redisConfig) {
            $this->newLine();
            $this->line('Redis驱动配置:');
            $this->table(['参数', '值'], [
                ['连接', $redisConfig['connection'] ?? 'default'],
                ['队列', $redisConfig['queue'] ?? 'default'],
                ['重试间隔', $redisConfig['retry_after'] ?? 90],
                ['阻塞时间', $redisConfig['block_for'] ?? 'null'],
            ]);
        }
    }

    /**
     * 显示数据库队列状态
     */
    protected function showDatabaseQueueStatus()
    {
        $this->newLine();
        $this->info('=== 数据库队列状态 ===');

        try {
            $tableName = config('queue.connections.database.table', 'jobs');
            
            // 检查表是否存在
            if (!$this->tableExists($tableName)) {
                $this->error("队列表 '{$tableName}' 不存在");
                $this->line('运行: php artisan queue:table && php artisan migrate');
                return;
            }

            // 获取队列统计
            $stats = [
                ['总任务数', DB::table($tableName)->count()],
                ['可用任务', DB::table($tableName)->where('available_at', '<=', time())->count()],
                ['延迟任务', DB::table($tableName)->where('available_at', '>', time())->count()],
            ];

            $this->table(['统计项', '数量'], $stats);

            // 按队列分组统计
            $queueStats = DB::table($tableName)
                ->select('queue', DB::raw('count(*) as count'))
                ->groupBy('queue')
                ->get();

            if ($queueStats->isNotEmpty()) {
                $this->newLine();
                $this->line('按队列统计:');
                $queueData = [];
                foreach ($queueStats as $stat) {
                    $queueData[] = [$stat->queue, $stat->count];
                }
                $this->table(['队列名', '任务数'], $queueData);
            }

        } catch (\Exception $e) {
            $this->error('获取数据库队列状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示Redis队列状态
     */
    protected function showRedisQueueStatus()
    {
        $this->newLine();
        $this->info('=== Redis队列状态 ===');

        try {
            $redis = Redis::connection(config('queue.connections.redis.connection', 'default'));
            
            // Redis基本信息
            $info = $redis->info();
            $this->table(['Redis信息', '值'], [
                ['版本', $info['redis_version'] ?? 'N/A'],
                ['内存使用', $info['used_memory_human'] ?? 'N/A'],
                ['连接数', $info['connected_clients'] ?? 'N/A'],
                ['键总数', $info['db0']['keys'] ?? 0],
            ]);

            // 队列键统计
            $queueKeys = $redis->keys('queues:*');
            if (!empty($queueKeys)) {
                $this->newLine();
                $this->line('队列键统计:');
                $keyData = [];
                foreach ($queueKeys as $key) {
                    $length = $redis->llen($key);
                    $keyData[] = [$key, $length];
                }
                $this->table(['队列键', '长度'], $keyData);
            }

        } catch (\Exception $e) {
            $this->error('获取Redis队列状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置数据库队列
     */
    protected function setupDatabaseQueue()
    {
        $this->info('配置数据库队列驱动...');

        $connection = $this->option('connection') ?? $this->ask('数据库连接名称', 'mysql');
        $table = $this->option('table') ?? $this->ask('队列表名', 'jobs');
        $retryAfter = $this->option('retry-after') ?? $this->ask('重试间隔（秒）', '90');

        // 生成队列配置
        $config = [
            'QUEUE_CONNECTION' => 'database',
            'QUEUE_TABLE' => $table,
            'QUEUE_RETRY_AFTER' => $retryAfter,
        ];

        $this->updateEnvFile($config);

        // 检查是否需要创建队列表
        if (!$this->tableExists($table)) {
            if ($this->confirm('队列表不存在，是否创建？')) {
                $this->call('queue:table', ['--table' => $table]);
                $this->call('migrate');
            }
        }

        $this->info('数据库队列配置完成');
        return 0;
    }

    /**
     * 设置Redis队列
     */
    protected function setupRedisQueue()
    {
        $this->info('配置Redis队列驱动...');

        $connection = $this->option('redis-connection') ?? $this->ask('Redis连接名称', 'default');
        $retryAfter = $this->option('retry-after') ?? $this->ask('重试间隔（秒）', '90');
        $blockFor = $this->option('block-for') ?? $this->ask('阻塞时间（秒，null为不阻塞）', 'null');

        // 生成队列配置
        $config = [
            'QUEUE_CONNECTION' => 'redis',
            'QUEUE_RETRY_AFTER' => $retryAfter,
        ];

        if ($blockFor !== 'null') {
            $config['REDIS_QUEUE_BLOCK_FOR'] = $blockFor;
        }

        $this->updateEnvFile($config);

        $this->info('Redis队列配置完成');
        return 0;
    }

    /**
     * 设置同步队列
     */
    protected function setupSyncQueue()
    {
        $this->info('配置同步队列驱动...');
        
        $config = [
            'QUEUE_CONNECTION' => 'sync',
        ];

        $this->updateEnvFile($config);

        $this->warn('同步驱动仅适用于开发环境，生产环境建议使用database或redis');
        $this->info('同步队列配置完成');
        return 0;
    }

    /**
     * 测试数据库连接
     */
    protected function testDatabaseConnection()
    {
        try {
            $connection = config('queue.connections.database.connection', 'mysql');
            $table = config('queue.connections.database.table', 'jobs');

            $this->line("测试数据库连接: {$connection}");
            
            // 测试连接
            DB::connection($connection)->getPdo();
            $this->info('✓ 数据库连接正常');

            // 检查队列表
            if ($this->tableExists($table)) {
                $this->info("✓ 队列表 '{$table}' 存在");
                
                // 测试表访问
                $count = DB::table($table)->count();
                $this->info("✓ 表访问正常，当前任务数: {$count}");
            } else {
                $this->warn("✗ 队列表 '{$table}' 不存在");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('✗ 数据库连接失败: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * 测试Redis连接
     */
    protected function testRedisConnection()
    {
        try {
            $connection = config('queue.connections.redis.connection', 'default');
            $this->line("测试Redis连接: {$connection}");

            $redis = Redis::connection($connection);
            
            // 测试连接
            $redis->ping();
            $this->info('✓ Redis连接正常');

            // 测试读写
            $testKey = 'queue_test_' . time();
            $redis->set($testKey, 'test', 'EX', 10);
            $value = $redis->get($testKey);
            $redis->del($testKey);

            if ($value === 'test') {
                $this->info('✓ Redis读写正常');
            } else {
                $this->warn('✗ Redis读写异常');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error('✗ Redis连接失败: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * 优化数据库配置
     */
    protected function optimizeDatabaseConfig()
    {
        $this->line('数据库队列优化建议:');
        
        $suggestions = [
            '1. 为队列表添加索引以提高查询性能',
            '2. 定期清理已完成的任务记录',
            '3. 考虑使用分区表处理大量任务',
            '4. 调整retry_after参数避免任务重复执行',
            '5. 监控队列表大小，防止磁盘空间不足',
        ];

        foreach ($suggestions as $suggestion) {
            $this->line("  • {$suggestion}");
        }

        // 检查队列表索引
        $this->checkQueueTableIndexes();
    }

    /**
     * 优化Redis配置
     */
    protected function optimizeRedisConfig()
    {
        $this->line('Redis队列优化建议:');
        
        $suggestions = [
            '1. 配置Redis持久化以防止数据丢失',
            '2. 设置合适的maxmemory和淘汰策略',
            '3. 使用Redis Cluster提高可用性',
            '4. 监控Redis内存使用情况',
            '5. 配置block_for参数减少CPU使用',
        ];

        foreach ($suggestions as $suggestion) {
            $this->line("  • {$suggestion}");
        }

        // 检查Redis配置
        $this->checkRedisConfig();
    }

    /**
     * 检查表是否存在
     */
    protected function tableExists($table)
    {
        try {
            return DB::getSchemaBuilder()->hasTable($table);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 更新.env文件
     */
    protected function updateEnvFile(array $config)
    {
        $envFile = base_path('.env');
        
        if (!File::exists($envFile)) {
            $this->error('.env文件不存在');
            return;
        }

        $content = File::get($envFile);

        foreach ($config as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $content)) {
                $content = preg_replace($pattern, $replacement, $content);
            } else {
                $content .= "\n{$replacement}";
            }
        }

        File::put($envFile, $content);
        $this->line('已更新.env文件');
    }

    /**
     * 检查队列表索引
     */
    protected function checkQueueTableIndexes()
    {
        try {
            $table = config('queue.connections.database.table', 'jobs');
            
            // 这里可以添加检查索引的逻辑
            $this->newLine();
            $this->line('建议为队列表添加以下索引:');
            $this->line("  CREATE INDEX idx_queue_available ON {$table} (queue, available_at);");
            $this->line("  CREATE INDEX idx_created_at ON {$table} (created_at);");

        } catch (\Exception $e) {
            $this->warn('无法检查队列表索引: ' . $e->getMessage());
        }
    }

    /**
     * 检查Redis配置
     */
    protected function checkRedisConfig()
    {
        try {
            $redis = Redis::connection();
            $config = $redis->config('get', '*');
            
            $this->newLine();
            $this->line('当前Redis配置检查:');
            
            $maxmemory = $config['maxmemory'] ?? '0';
            if ($maxmemory === '0') {
                $this->warn('  • 未设置maxmemory限制');
            } else {
                $this->info("  • maxmemory: {$maxmemory}");
            }

            $policy = $config['maxmemory-policy'] ?? 'noeviction';
            $this->line("  • 淘汰策略: {$policy}");

        } catch (\Exception $e) {
            $this->warn('无法检查Redis配置: ' . $e->getMessage());
        }
    }
} 
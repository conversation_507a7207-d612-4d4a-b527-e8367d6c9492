<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->unique()->comment('角色名称');
            $table->string('display_name', 100)->comment('角色显示名称');
            $table->text('description')->nullable()->comment('角色描述');
            $table->enum('level', ['admin', 'advanced', 'normal', 'readonly'])->comment('角色级别');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->json('permissions_config')->nullable()->comment('权限配置');
            $table->timestamps();
            
            // 索引
            $table->index(['name', 'is_active']);
            $table->index('level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
}; 
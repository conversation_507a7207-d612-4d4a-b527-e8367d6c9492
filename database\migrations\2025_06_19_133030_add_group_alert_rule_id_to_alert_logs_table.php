<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alert_logs', function (Blueprint $table) {
            // 添加group_alert_rule_id字段，用于支持分组告警规则
            $table->foreignId('group_alert_rule_id')
                ->nullable()
                ->after('rule_id')
                ->constrained('alert_rules')
                ->onDelete('set null')
                ->comment('分组告警规则ID，用于批量告警管理');
            
            // 添加索引以提高查询性能
            $table->index(['group_alert_rule_id', 'created_at'], 'idx_alert_logs_group_rule_created');
            $table->index(['group_alert_rule_id', 'status'], 'idx_alert_logs_group_rule_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alert_logs', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_alert_logs_group_rule_created');
            $table->dropIndex('idx_alert_logs_group_rule_status');
            
            // 删除外键约束和字段
            $table->dropForeign(['group_alert_rule_id']);
            $table->dropColumn('group_alert_rule_id');
        });
    }
};

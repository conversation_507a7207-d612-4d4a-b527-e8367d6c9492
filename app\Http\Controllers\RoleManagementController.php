<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RoleManagementController extends Controller
{
    /**
     * 显示角色管理主页
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = Role::with(['permissions', 'users']);

        // 搜索过滤
        if ($search = $request->get('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 级别过滤
        if ($levelFilter = $request->get('level')) {
            $query->where('level', $levelFilter);
        }

        // 状态过滤
        if ($statusFilter = $request->get('status')) {
            $isActive = $statusFilter === 'active';
            $query->where('is_active', $isActive);
        }

        $roles = $query->paginate(15);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['roles' => $roles],
            ]);
        }

        return view('admin.roles.index', compact('roles'));
    }

    /**
     * 显示创建角色表单
     */
    public function create(): View|JsonResponse
    {
        $permissions = Permission::where('is_active', true)
                                ->orderBy('module')
                                ->orderBy('name')
                                ->get()
                                ->groupBy('module');

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['permissions' => $permissions],
            ]);
        }

        return view('admin.roles.create', compact('permissions'));
    }

    /**
     * 存储新角色
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:roles,name',
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'level' => 'required|in:admin,advanced,normal,readonly',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ], [
            'name.required' => '角色名称不能为空',
            'name.unique' => '角色名称已存在',
            'display_name.required' => '角色显示名称不能为空',
            'level.required' => '请选择角色级别',
            'level.in' => '无效的角色级别',
            'permissions.*.exists' => '选择的权限不存在',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // 创建角色
            $role = Role::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'level' => $request->level,
                'is_active' => true,
            ]);

            // 分配权限
            if ($request->has('permissions') && is_array($request->permissions)) {
                $permissions = Permission::whereIn('id', $request->permissions)
                                        ->where('is_active', true)
                                        ->get();
                $role->permissions()->sync($permissions->pluck('id'));
            }

            DB::commit();

            $message = "角色 {$role->display_name} 创建成功";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'data' => ['role' => $role->load(['permissions'])],
                ]);
            }

            return redirect()->route('admin.roles.show', $role)
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '角色创建失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '角色创建失败：' . $e->getMessage())->withInput();
        }
    }

    /**
     * 显示角色详情
     */
    public function show(Role $role): View|JsonResponse
    {
        $role->load(['permissions', 'users' => function($q) {
            $q->limit(10);
        }]);

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['role' => $role],
            ]);
        }

        return view('admin.roles.show', compact('role'));
    }

    /**
     * 显示编辑角色表单
     */
    public function edit(Role $role): View|JsonResponse
    {
        $permissions = Permission::where('is_active', true)
                                ->orderBy('module')
                                ->orderBy('name')
                                ->get()
                                ->groupBy('module');

        $rolePermissions = $role->permissions()->pluck('id')->toArray();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'role' => $role,
                    'permissions' => $permissions,
                    'rolePermissions' => $rolePermissions,
                ],
            ]);
        }

        return view('admin.roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * 更新角色
     */
    public function update(Request $request, Role $role): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'level' => 'required|in:admin,advanced,normal,readonly',
            'is_active' => 'boolean',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ], [
            'name.required' => '角色名称不能为空',
            'name.unique' => '角色名称已存在',
            'display_name.required' => '角色显示名称不能为空',
            'level.required' => '请选择角色级别',
            'level.in' => '无效的角色级别',
            'permissions.*.exists' => '选择的权限不存在',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // 更新角色基本信息
            $role->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'level' => $request->level,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // 同步权限
            if ($request->has('permissions')) {
                $permissions = Permission::whereIn('id', $request->permissions ?: [])
                                        ->where('is_active', true)
                                        ->pluck('id');
                $role->permissions()->sync($permissions);
            } else {
                $role->permissions()->sync([]);
            }

            DB::commit();

            $message = "角色 {$role->display_name} 更新成功";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'data' => ['role' => $role->fresh(['permissions'])],
                ]);
            }

            return redirect()->route('admin.roles.show', $role)
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '角色更新失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '角色更新失败：' . $e->getMessage())->withInput();
        }
    }

    /**
     * 删除角色
     */
    public function destroy(Request $request, Role $role): JsonResponse|RedirectResponse
    {
        try {
            // 检查角色是否有关联用户
            $userCount = $role->users()->count();
            if ($userCount > 0) {
                $message = "无法删除角色 {$role->display_name}，还有 {$userCount} 个用户使用此角色";
                
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message,
                    ], 422);
                }

                return back()->with('error', $message);
            }

            DB::beginTransaction();

            // 移除角色的所有权限关联
            $role->permissions()->detach();
            
            // 删除角色
            $roleName = $role->display_name;
            $role->delete();

            DB::commit();

            $message = "角色 {$roleName} 删除成功";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                ]);
            }

            return redirect()->route('admin.roles.index')
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '角色删除失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '角色删除失败：' . $e->getMessage());
        }
    }

    /**
     * 批量操作角色
     */
    public function batchAction(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'role_ids' => 'required|array',
            'role_ids.*' => 'exists:roles,id',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            $roles = Role::whereIn('id', $request->role_ids)->get();
            $affectedCount = 0;

            foreach ($roles as $role) {
                switch ($request->action) {
                    case 'activate':
                        $role->update(['is_active' => true]);
                        $affectedCount++;
                        break;

                    case 'deactivate':
                        $role->update(['is_active' => false]);
                        $affectedCount++;
                        break;

                    case 'delete':
                        // 检查是否有关联用户
                        if ($role->users()->count() === 0) {
                            $role->permissions()->detach();
                            $role->delete();
                            $affectedCount++;
                        }
                        break;
                }
            }

            DB::commit();

            $message = "批量操作完成，影响 {$affectedCount} 个角色";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'affected_count' => $affectedCount,
                ]);
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '批量操作失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '批量操作失败：' . $e->getMessage());
        }
    }

    /**
     * 角色权限管理页面
     */
    public function permissions(Role $role): View|JsonResponse
    {
        $allPermissions = Permission::where('is_active', true)
                                   ->orderBy('module')
                                   ->orderBy('name')
                                   ->get()
                                   ->groupBy('module');

        $rolePermissions = $role->permissions()->pluck('id')->toArray();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'role' => $role,
                    'allPermissions' => $allPermissions,
                    'rolePermissions' => $rolePermissions,
                ],
            ]);
        }

        return view('admin.roles.permissions', compact('role', 'allPermissions', 'rolePermissions'));
    }

    /**
     * 更新角色权限
     */
    public function updatePermissions(Request $request, Role $role): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ], [
            'permissions.*.exists' => '选择的权限不存在',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            // 同步权限
            $permissions = Permission::whereIn('id', $request->permissions ?: [])
                                    ->where('is_active', true)
                                    ->pluck('id');

            $role->permissions()->sync($permissions);

            DB::commit();

            $message = "角色 {$role->display_name} 的权限更新成功";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'data' => ['role' => $role->fresh(['permissions'])],
                ]);
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '权限更新失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '权限更新失败：' . $e->getMessage());
        }
    }
} 
<?php

namespace Database\Factories;

use App\Models\MonitorTask;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductSku;
use App\Models\TaskGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MonitorTask>
 */
class MonitorTaskFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MonitorTask::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'sku_id' => ProductSku::factory(),
            'task_group_id' => TaskGroup::factory(),
            'task_name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph,
            'monitor_type' => 'sku',
            'frequency' => '1hour',
            'status' => 'active',
            'is_enabled' => true,
            'is_public' => false,
            'monitor_settings' => json_encode(['timeout' => 60]),
        ];
    }
} 
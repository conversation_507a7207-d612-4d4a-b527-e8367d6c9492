<?php

namespace App\Console\Commands;

use App\Models\ApiConfiguration;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

/**
 * 生成API配置环境变量模板命令
 */
class GenerateApiConfigEnv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api-config:generate-env 
                            {--output=.env.api : Output file path}
                            {--platform= : Specific platform to generate}
                            {--template : Generate template only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate environment variable template for API configurations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $outputFile = $this->option('output');
        $platform = $this->option('platform');
        $templateOnly = $this->option('template');

        $this->info('生成API配置环境变量模板...');

        $envContent = $this->generateEnvContent($platform, $templateOnly);

        // 写入文件
        File::put($outputFile, $envContent);

        $this->info("环境变量模板已生成到: {$outputFile}");
        
        if (!$templateOnly) {
            $this->warn('注意: 请填写实际的API密钥和敏感信息');
            $this->warn('建议: 将此文件添加到 .gitignore 以避免泄露敏感信息');
        }

        $this->displaySecurityTips();
    }

    /**
     * 生成环境变量内容
     *
     * @param string|null $platform 指定平台
     * @param bool $templateOnly 仅生成模板
     * @return string 环境变量内容
     */
    private function generateEnvContent(?string $platform, bool $templateOnly): string
    {
        $content = [];
        $content[] = '# API配置环境变量';
        $content[] = '# 生成时间: ' . now()->format('Y-m-d H:i:s');
        $content[] = '';

        if ($templateOnly) {
            $content[] = $this->generateTemplate();
        } else {
            $content[] = $this->generateFromDatabase($platform);
        }

        return implode("\n", $content);
    }

    /**
     * 生成模板内容
     *
     * @return string 模板内容
     */
    private function generateTemplate(): string
    {
        $template = [];
        
        $template[] = '# 通用API配置模板';
        $template[] = '';
        
        // 常见平台模板
        $platforms = ['TAOBAO', 'JD', 'TMALL', 'SHOPIFY', 'MAGENTO'];
        
        foreach ($platforms as $platform) {
            $template[] = "# {$platform} API配置";
            $template[] = "API_CONFIG_{$platform}_BASE_URL=https://api.{strtolower($platform)}.com";
            $template[] = "API_CONFIG_{$platform}_API_KEY=your_api_key_here";
            $template[] = "API_CONFIG_{$platform}_SECRET_KEY=your_secret_key_here";
            $template[] = "API_CONFIG_{$platform}_ACCESS_TOKEN=your_access_token_here";
            $template[] = "API_CONFIG_{$platform}_REFRESH_TOKEN=your_refresh_token_here";
            $template[] = '';
        }

        $template[] = '# 通用配置';
        $template[] = 'API_CONFIG_DEFAULT_TIMEOUT=30';
        $template[] = 'API_CONFIG_DEFAULT_RETRY_ATTEMPTS=3';
        $template[] = 'API_CONFIG_ENABLE_LOGGING=true';
        $template[] = 'API_CONFIG_LOG_LEVEL=info';
        $template[] = '';

        return implode("\n", $template);
    }

    /**
     * 从数据库生成配置
     *
     * @param string|null $platform 指定平台
     * @return string 配置内容
     */
    private function generateFromDatabase(?string $platform): string
    {
        $content = [];
        
        $query = ApiConfiguration::query();
        if ($platform) {
            $query->where('platform_type', $platform);
        }
        
        $configurations = $query->get();

        if ($configurations->isEmpty()) {
            $content[] = '# 未找到API配置，请先在管理界面创建配置';
            return implode("\n", $content);
        }

        foreach ($configurations as $config) {
            $platformUpper = strtoupper($config->platform_type);
            $content[] = "# {$config->name} ({$config->platform_type})";
            
            // 基础配置
            $content[] = "API_CONFIG_{$platformUpper}_BASE_URL={$config->base_url}";
            
            // 认证凭据（使用占位符）
            $authCredentials = $config->auth_credentials ?? [];
            foreach ($authCredentials as $key => $value) {
                $keyUpper = strtoupper($key);
                $placeholder = $this->generatePlaceholder($key, $value);
                $content[] = "API_CONFIG_{$platformUpper}_{$keyUpper}={$placeholder}";
            }
            
            // 其他配置
            if ($config->rate_limit_per_minute) {
                $content[] = "API_CONFIG_{$platformUpper}_RATE_LIMIT_PER_MINUTE={$config->rate_limit_per_minute}";
            }
            if ($config->rate_limit_per_hour) {
                $content[] = "API_CONFIG_{$platformUpper}_RATE_LIMIT_PER_HOUR={$config->rate_limit_per_hour}";
            }
            if ($config->rate_limit_per_day) {
                $content[] = "API_CONFIG_{$platformUpper}_RATE_LIMIT_PER_DAY={$config->rate_limit_per_day}";
            }
            
            $content[] = '';
        }

        return implode("\n", $content);
    }

    /**
     * 生成占位符
     *
     * @param string $key 键名
     * @param mixed $value 值
     * @return string 占位符
     */
    private function generatePlaceholder(string $key, $value): string
    {
        $sensitiveFields = ['api_key', 'secret_key', 'access_token', 'refresh_token', 'password', 'client_secret'];
        
        if (in_array($key, $sensitiveFields)) {
            return 'your_' . $key . '_here';
        }
        
        // 对于非敏感字段，可以显示实际值或部分值
        if (is_string($value) && strlen($value) > 10) {
            return substr($value, 0, 4) . '****' . substr($value, -4);
        }
        
        return $value;
    }

    /**
     * 显示安全提示
     */
    private function displaySecurityTips(): void
    {
        $this->info('');
        $this->info('🔒 安全提示:');
        $this->line('1. 将 .env.api 文件添加到 .gitignore');
        $this->line('2. 使用强密码和复杂的API密钥');
        $this->line('3. 定期轮换API密钥和访问令牌');
        $this->line('4. 在生产环境中使用环境变量而非配置文件');
        $this->line('5. 启用API访问日志监控异常活动');
        $this->line('6. 设置适当的文件权限 (chmod 600)');
        $this->info('');
    }
}

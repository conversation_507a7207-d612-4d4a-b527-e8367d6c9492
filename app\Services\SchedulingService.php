<?php

namespace App\Services;

use App\Models\MonitorTask;
use App\Jobs\DataCollectionJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Console\Scheduling\Schedule;
use Cron\CronExpression;
use Exception;
use Carbon\Carbon;

/**
 * 调度服务 - 管理数据收集任务的调度
 * 
 * 支持功能：
 * - 可配置的收集频率（小时、天、自定义cron）
 * - 动态调度任务管理
 * - 与Laravel Queue集成
 * - 调度状态监控
 */
class SchedulingService
{
    protected $config;
    
    /**
     * 预定义的调度频率
     */
    const FREQUENCIES = [
        'every_minute' => '* * * * *',
        'every_5_minutes' => '*/5 * * * *',
        'every_15_minutes' => '*/15 * * * *',
        'every_30_minutes' => '*/30 * * * *',
        'hourly' => '0 * * * *',
        'every_2_hours' => '0 */2 * * *',
        'every_4_hours' => '0 */4 * * *',
        'every_6_hours' => '0 */6 * * *',
        'every_12_hours' => '0 */12 * * *',
        'daily' => '0 0 * * *',
        'weekly' => '0 0 * * 0',
        'monthly' => '0 0 1 * *',
    ];

    /**
     * 调度状态
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_PAUSED = 'paused';
    const STATUS_DISABLED = 'disabled';

    /**
     * 缓存键前缀
     */
    const CACHE_PREFIX = 'scheduling:';

    public function __construct()
    {
        $this->config = config('datacollection.scheduling', []);
    }

    /**
     * 注册所有监控任务的调度
     * 
     * @param Schedule $schedule Laravel调度器实例
     */
    public function registerScheduledTasks(Schedule $schedule)
    {
        Log::info('开始注册监控任务调度');
        
        // 获取所有启用的监控任务
        $monitorTasks = MonitorTask::where('is_enabled', true)
            ->where('status', 'active')
            ->get();

        foreach ($monitorTasks as $task) {
            $this->registerTaskSchedule($schedule, $task);
        }

        // 注册调度状态检查任务
        $this->registerHealthCheckSchedule($schedule);
        
        Log::info('监控任务调度注册完成', ['总任务数' => $monitorTasks->count()]);
    }

    /**
     * 为单个监控任务注册调度
     * 
     * @param Schedule $schedule
     * @param MonitorTask $task
     */
    protected function registerTaskSchedule(Schedule $schedule, MonitorTask $task)
    {
        $frequency = $this->getTaskFrequency($task);
        $cronExpression = $this->getCronExpression($frequency);
        
        if (!$cronExpression) {
            Log::warning('无效的调度频率', [
                'task_id' => $task->id,
                'frequency' => $frequency
            ]);
            return;
        }

        // 创建调度任务
        $scheduledTask = $schedule->call(function () use ($task) {
            $this->dispatchDataCollectionJob($task);
        })->cron($cronExpression);

        // 设置任务标识和描述
        $scheduledTask->name("data_collection_task_{$task->id}")
                     ->description("数据收集任务: {$task->task_name}")
                     ->withoutOverlapping(5) // 防止任务重叠，超时5分钟
                     ->runInBackground()
                     ->appendOutputTo(storage_path('logs/scheduler.log'));

        // 如果启用了队列，使用队列处理
        if ($this->shouldUseQueue($task)) {
            $scheduledTask->onOneServer(); // 避免多服务器重复执行
        }

        Log::info('监控任务调度已注册', [
            'task_id' => $task->id,
            'task_name' => $task->task_name,
            'frequency' => $frequency,
            'cron' => $cronExpression
        ]);
    }

    /**
     * 分发数据收集作业
     * 
     * @param MonitorTask $task
     */
    protected function dispatchDataCollectionJob(MonitorTask $task)
    {
        try {
            Log::info('开始分发数据收集作业', [
                'task_id' => $task->id,
                'task_name' => $task->task_name
            ]);

            // 检查任务是否仍然启用
            $task->refresh();
            if (!$task->is_enabled || $task->status !== 'active') {
                Log::info('任务已禁用，跳过执行', ['task_id' => $task->id]);
                return;
            }

            // 检查上次执行时间，避免重复执行
            if ($this->isRecentlyExecuted($task)) {
                Log::info('任务最近已执行，跳过本次执行', ['task_id' => $task->id]);
                return;
            }

            // 更新最后执行时间
            $this->updateLastExecutionTime($task);

            // 分发到队列
            DataCollectionJob::dispatch($task)
                ->onQueue($this->getTaskQueue($task))
                ->delay($this->getTaskDelay($task));

            Log::info('数据收集作业分发成功', [
                'task_id' => $task->id,
                'queue' => $this->getTaskQueue($task)
            ]);

        } catch (\Exception $e) {
            Log::error('分发数据收集作业失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取任务的调度频率
     * 
     * @param MonitorTask $task
     * @return string
     */
    protected function getTaskFrequency(MonitorTask $task)
    {
        // 优先使用任务自身配置
        if (!empty($task->collection_frequency)) {
            return $task->collection_frequency;
        }

        // 使用默认配置
        return $this->config['default_frequency'] ?? 'hourly';
    }

    /**
     * 将频率转换为cron表达式
     * 
     * @param string $frequency
     * @return string|null
     */
    protected function getCronExpression($frequency)
    {
        // 如果是预定义频率
        if (isset(self::FREQUENCIES[$frequency])) {
            return self::FREQUENCIES[$frequency];
        }

        // 如果是cron表达式（5个或6个字段）
        if ($this->isValidCronExpression($frequency)) {
            return $frequency;
        }

        return null;
    }

    /**
     * 验证cron表达式
     * 
     * @param string $expression
     * @return bool
     */
    protected function isValidCronExpression($expression)
    {
        $parts = explode(' ', trim($expression));
        
        // 标准cron: 5个字段 (分 时 日 月 周)
        // Laravel cron: 支持6个字段 (秒 分 时 日 月 周)
        if (count($parts) !== 5 && count($parts) !== 6) {
            return false;
        }

        // 基本格式检查
        foreach ($parts as $part) {
            if (!preg_match('/^[\d\*\-\/\,]+$/', $part)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查任务是否最近已执行
     * 
     * @param MonitorTask $task
     * @return bool
     */
    protected function isRecentlyExecuted(MonitorTask $task)
    {
        $cacheKey = "task_last_execution_{$task->id}";
        $lastExecution = Cache::get($cacheKey);
        
        if (!$lastExecution) {
            return false;
        }

        $frequency = $this->getTaskFrequency($task);
        $minInterval = $this->getMinExecutionInterval($frequency);
        
        return Carbon::parse($lastExecution)->addSeconds($minInterval)->isFuture();
    }

    /**
     * 获取最小执行间隔（秒）
     * 
     * @param string $frequency
     * @return int
     */
    protected function getMinExecutionInterval($frequency)
    {
        $intervals = [
            'every_minute' => 60,
            'every_5_minutes' => 300,
            'every_15_minutes' => 900,
            'every_30_minutes' => 1800,
            'hourly' => 3600,
            'every_2_hours' => 7200,
            'every_4_hours' => 14400,
            'every_6_hours' => 21600,
            'every_12_hours' => 43200,
            'daily' => 86400,
            'weekly' => 604800,
            'monthly' => 2592000,
        ];

        return $intervals[$frequency] ?? 3600; // 默认1小时
    }

    /**
     * 更新最后执行时间
     * 
     * @param MonitorTask $task
     */
    protected function updateLastExecutionTime(MonitorTask $task)
    {
        $cacheKey = "task_last_execution_{$task->id}";
        $expirationTime = $this->getMinExecutionInterval($this->getTaskFrequency($task)) * 2;
        
        Cache::put($cacheKey, now(), $expirationTime);
    }

    /**
     * 确定是否应该使用队列
     * 
     * @param MonitorTask $task
     * @return bool
     */
    protected function shouldUseQueue(MonitorTask $task)
    {
        return $this->config['use_queue'] ?? true;
    }

    /**
     * 获取任务的队列名称
     * 
     * @param MonitorTask $task
     * @return string
     */
    protected function getTaskQueue(MonitorTask $task)
    {
        if (!empty($task->queue_name)) {
            return $task->queue_name;
        }

        // 根据优先级分配队列
        $priority = $task->priority ?? 'medium';
        return $this->config['queues'][$priority] ?? 'default';
    }

    /**
     * 获取任务延迟时间
     * 
     * @param MonitorTask $task
     * @return int
     */
    protected function getTaskDelay(MonitorTask $task)
    {
        return $task->delay_seconds ?? 0;
    }

    /**
     * 注册调度健康检查
     * 
     * @param Schedule $schedule
     */
    protected function registerHealthCheckSchedule(Schedule $schedule)
    {
        $schedule->call(function () {
            $this->performHealthCheck();
        })->everyFiveMinutes()
          ->name('scheduling_health_check')
          ->description('调度系统健康检查')
          ->withoutOverlapping();
    }

    /**
     * 执行调度健康检查
     */
    protected function performHealthCheck()
    {
        Log::info('开始调度系统健康检查');

        $stats = $this->getSchedulingStats();
        
        // 记录统计信息
        Cache::put('scheduling_stats', $stats, 300); // 缓存5分钟

        // 检查是否有长时间未执行的任务
        $this->checkStaleSchedules();

        Log::info('调度系统健康检查完成', $stats);
    }

    /**
     * 获取调度统计信息
     * 
     * @return array
     */
    public function getSchedulingStats()
    {
        $activeTasks = MonitorTask::where('is_enabled', true)
            ->where('status', 'active')
            ->count();

        $totalTasks = MonitorTask::count();

        return [
            'active_scheduled_tasks' => $activeTasks,
            'total_tasks' => $totalTasks,
            'last_health_check' => now()->toISOString(),
            'scheduler_status' => 'running'
        ];
    }

    /**
     * 检查过期的调度任务
     */
    protected function checkStaleSchedules()
    {
        // 这里可以添加检查逻辑，比如检查是否有任务长时间未执行
        // 可以发送警告或自动重启等
    }

    /**
     * 获取可用的调度频率选项
     * 
     * @return array
     */
    public static function getAvailableFrequencies()
    {
        return [
            'every_minute' => '每分钟',
            'every_5_minutes' => '每5分钟',
            'every_15_minutes' => '每15分钟',
            'every_30_minutes' => '每30分钟',
            'hourly' => '每小时',
            'every_2_hours' => '每2小时',
            'every_4_hours' => '每4小时',
            'every_6_hours' => '每6小时',
            'every_12_hours' => '每12小时',
            'daily' => '每天',
            'weekly' => '每周',
            'monthly' => '每月',
            'custom' => '自定义cron表达式'
        ];
    }

    /**
     * 手动触发监控任务
     * 
     * @param int $taskId
     * @return bool
     */
    public function triggerTask($taskId)
    {
        try {
            $task = MonitorTask::findOrFail($taskId);
            
            if (!$task->is_enabled) {
                throw new \Exception('任务已禁用');
            }

            $this->dispatchDataCollectionJob($task);
            
            Log::info('手动触发监控任务成功', ['task_id' => $taskId]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('手动触发监控任务失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 暂停监控任务调度
     * 
     * @param int $taskId
     * @return bool
     */
    public function pauseTask($taskId)
    {
        try {
            $task = MonitorTask::findOrFail($taskId);
            $task->update(['is_enabled' => false]);
            
            Log::info('监控任务调度已暂停', ['task_id' => $taskId]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('暂停监控任务调度失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 恢复监控任务调度
     * 
     * @param int $taskId
     * @return bool
     */
    public function resumeTask($taskId)
    {
        try {
            $task = MonitorTask::findOrFail($taskId);
            $task->update(['is_enabled' => true]);
            
            Log::info('监控任务调度已恢复', ['task_id' => $taskId]);
            return true;
            
        } catch (\Exception $e) {
            Log::error('恢复监控任务调度失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 
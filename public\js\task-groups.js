/**
 * 任务分组管理JavaScript
 */

// 全局变量
let groups = [];
let unassignedTasks = [];
let currentEditingGroup = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTaskGroups();
    setupEventListeners();
    loadStatistics();
});

/**
 * 初始化任务分组数据
 */
async function initializeTaskGroups() {
    try {
        showLoading(true);
        
        // 并行加载数据
        const [groupsResponse, unassignedResponse] = await Promise.all([
            apiService.getTaskGroups(),
            apiService.getUnassignedTasks()
        ]);

        if (groupsResponse.success) {
            groups = groupsResponse.data || [];
        } else {
            groups = [];
        }

        if (unassignedResponse.success) {
            unassignedTasks = unassignedResponse.data || [];
        } else {
            unassignedTasks = [];
        }
        
        renderGroups();
        renderUnassignedTasks();
        
    } catch (error) {
        console.error('加载任务分组数据失败:', error);
        showAlert('加载数据失败，请刷新页面重试', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 创建分组表单
    const createForm = document.getElementById('createGroupForm');
    if (createForm) {
        createForm.addEventListener('submit', handleCreateGroup);
    }
    
    // 编辑分组表单
    const editForm = document.getElementById('editGroupForm');
    if (editForm) {
        editForm.addEventListener('submit', handleEditGroup);
    }
    
    // 颜色预览
    const groupColor = document.getElementById('groupColor');
    if (groupColor) {
        groupColor.addEventListener('change', function() {
            updateColorPreview(this.value, '.color-preview');
        });
    }
    
    const editGroupColor = document.getElementById('editGroupColor');
    if (editGroupColor) {
        editGroupColor.addEventListener('change', function() {
            updateColorPreview(this.value, '.edit-color-preview');
        });
    }
}

/**
 * 渲染分组列表
 */
function renderGroups() {
    const container = document.getElementById('groupsList');
    if (!container) return;
    
    if (groups.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center text-muted py-5">
                    <i class="fas fa-layer-group fa-3x mb-3"></i>
                    <p>还没有创建任何分组</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGroupModal">
                        <i class="fas fa-plus me-2"></i>创建第一个分组
                    </button>
                </div>
            </div>
        `;
        return;
    }
    
    container.innerHTML = groups.map(group => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card group-card" 
                 data-group-id="${group.id}"
                 ondrop="handleDrop(event, ${group.id})" 
                 ondragover="handleDragOver(event)"
                 ondragleave="handleDragLeave(event)">
                <div class="card-header group-header" style="border-left-color: ${group.color || '#007bff'}">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" style="color: ${group.color || '#007bff'}">
                            <i class="fas fa-folder me-2"></i>
                            ${group.name}
                        </h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editGroup(${group.id})">
                                    <i class="fas fa-edit me-2"></i>编辑
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="viewGroupReport(${group.id})">
                                    <i class="fas fa-chart-bar me-2"></i>查看报告
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteGroup(${group.id})">
                                    <i class="fas fa-trash me-2"></i>删除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    ${group.description ? `<small class="text-muted">${group.description}</small>` : ''}
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="badge bg-primary">
                            ${group.monitor_tasks_count || 0} 个任务
                        </span>
                        <span class="badge bg-success">
                            ${group.is_active ? '活跃' : '暂停'}
                        </span>
                    </div>
                    <div class="tasks-container" style="min-height: 100px;">
                        ${renderGroupTasks(group.monitor_tasks || [])}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * 渲染分组内的任务
 */
function renderGroupTasks(tasks) {
    if (!tasks || tasks.length === 0) {
        return '<p class="text-muted text-center">拖拽任务到此处</p>';
    }
    
    return tasks.map(task => `
        <div class="task-item card mb-2" 
             draggable="true" 
             data-task-id="${task.id}"
             ondragstart="handleTaskDragStart(event, ${task.id})">
            <div class="card-body p-2">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="fw-bold">${task.task_name}</small>
                    <span class="badge bg-${getStatusColor(task.status)}">${task.status}</span>
                </div>
                <small class="text-muted">${task.platform}</small>
            </div>
        </div>
    `).join('');
}

/**
 * 渲染未分组任务
 */
function renderUnassignedTasks() {
    const container = document.getElementById('unassignedTasks');
    if (!container) return;
    
    if (unassignedTasks.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="unassigned-area p-4 text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <p class="text-muted mb-0">所有任务都已分组！</p>
                </div>
            </div>
        `;
        return;
    }
    
    container.innerHTML = unassignedTasks.map(task => `
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="task-item card" 
                 draggable="true" 
                 data-task-id="${task.id}"
                 ondragstart="handleTaskDragStart(event, ${task.id})">
                <div class="card-body">
                    <h6 class="card-title">${task.task_name}</h6>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-globe me-1"></i>${task.platform}<br>
                            <i class="fas fa-clock me-1"></i>${task.frequency}<br>
                            <span class="badge bg-${getStatusColor(task.status)}">${task.status}</span>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * 拖拽处理函数
 */
let draggedTaskId = null;

function handleTaskDragStart(event, taskId) {
    draggedTaskId = taskId;
    event.target.classList.add('dragging');
    event.dataTransfer.effectAllowed = 'move';
}

function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
    event.currentTarget.classList.add('drag-over');
}

function handleDragLeave(event) {
    event.currentTarget.classList.remove('drag-over');
}

async function handleDrop(event, groupId) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
    
    if (!draggedTaskId) return;
    
    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const response = await fetch(`/api/task-groups/${groupId}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
            },
            body: JSON.stringify({
                task_ids: [draggedTaskId]
            })
        });
        
        if (response.ok) {
            showAlert('任务分配成功！', 'success');
            initializeTaskGroups(); // 重新加载数据
            loadStatistics();
        } else {
            throw new Error('分配失败');
        }
    } catch (error) {
        console.error('任务分配失败:', error);
        showAlert('任务分配失败，请重试', 'danger');
    } finally {
        // 清理拖拽状态
        document.querySelectorAll('.task-item.dragging').forEach(el => {
            el.classList.remove('dragging');
        });
        draggedTaskId = null;
    }
}

/**
 * 创建分组
 */
async function handleCreateGroup(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await apiService.createTaskGroup(data);

        if (response.success) {
            showAlert('分组创建成功！', 'success');
            const createModal = document.getElementById('createGroupModal');
            if (createModal && bootstrap.Modal.getInstance(createModal)) {
                bootstrap.Modal.getInstance(createModal).hide();
            }
            document.getElementById('createGroupForm').reset();
            initializeTaskGroups();
            loadStatistics();
        } else {
            throw new Error(response.message || '创建失败');
        }
    } catch (error) {
        console.error('创建分组失败:', error);
        showAlert('创建分组失败：' + error.message, 'danger');
    }
}

/**
 * 编辑分组
 */
function editGroup(groupId) {
    const group = groups.find(g => g.id === groupId);
    if (!group) return;
    
    currentEditingGroup = group;
    
    // 填充表单
    document.getElementById('editGroupId').value = group.id;
    document.getElementById('editGroupName').value = group.name;
    document.getElementById('editGroupDescription').value = group.description || '';
    document.getElementById('editGroupColor').value = group.color || '#007bff';
    document.getElementById('editSortOrder').value = group.sort_order || 0;
    
    updateColorPreview(group.color || '#007bff', '.edit-color-preview');
    
    // 显示模态框
    const editModal = document.getElementById('editGroupModal');
    if (editModal) {
        new bootstrap.Modal(editModal).show();
    }
}

async function handleEditGroup(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    const groupId = data.id;
    
    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const response = await fetch(`/api/task-groups/${groupId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showAlert('分组更新成功！', 'success');
            const editModal = document.getElementById('editGroupModal');
            if (editModal && bootstrap.Modal.getInstance(editModal)) {
                bootstrap.Modal.getInstance(editModal).hide();
            }
            initializeTaskGroups();
            loadStatistics();
        } else {
            throw new Error('更新失败');
        }
    } catch (error) {
        console.error('更新分组失败:', error);
        showAlert('更新分组失败，请重试', 'danger');
    }
}

/**
 * 删除分组
 */
async function deleteGroup(groupId) {
    const group = groups.find(g => g.id === groupId);
    if (!group) return;
    
    if (!confirm(`确定要删除分组"${group.name}"吗？\n\n注意：分组内的任务将变为未分组状态。`)) {
        return;
    }
    
    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const response = await fetch(`/api/task-groups/${groupId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
            }
        });
        
        if (response.ok) {
            showAlert('分组删除成功！', 'success');
            initializeTaskGroups();
            loadStatistics();
        } else {
            throw new Error('删除失败');
        }
    } catch (error) {
        console.error('删除分组失败:', error);
        showAlert('删除分组失败，请重试', 'danger');
    }
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        const response = await fetch('/api/task-groups/distribution-analysis');
        if (response.ok) {
            const result = await response.json();
            const stats = result.data || {};
            
            const totalGroupsEl = document.getElementById('totalGroups');
            if (totalGroupsEl) totalGroupsEl.textContent = stats.total_groups || 0;
            
            const groupedTasksEl = document.getElementById('groupedTasks');
            if (groupedTasksEl) groupedTasksEl.textContent = stats.grouped_tasks || 0;
            
            const ungroupedTasksEl = document.getElementById('ungroupedTasks');
            if (ungroupedTasksEl) ungroupedTasksEl.textContent = stats.ungrouped_tasks || 0;
            
            const groupingRateEl = document.getElementById('groupingRate');
            if (groupingRateEl) groupingRateEl.textContent = `${stats.grouping_percentage || 0}%`;
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

/**
 * 查看分组报告
 */
function viewGroupReport(groupId) {
    // 跳转到报告页面
    window.location.href = `/task-groups/${groupId}/report`;
}

/**
 * 显示分布分析
 */
async function showDistributionAnalysis() {
    try {
        const response = await fetch('/api/task-groups/distribution-analysis');
        if (response.ok) {
            const result = await response.json();
            const analysis = result.data || {};
            
            // 创建分析模态框内容
            const modalContent = `
                <div class="modal fade" id="distributionModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">分布分析报告</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-4">
                                    <div class="col-md-3 text-center">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <h4>${analysis.total_groups || 0}</h4>
                                                <small>总分组数</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="card bg-success text-white">
                                            <div class="card-body">
                                                <h4>${analysis.grouped_tasks || 0}</h4>
                                                <small>已分组任务</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body">
                                                <h4>${analysis.ungrouped_tasks || 0}</h4>
                                                <small>未分组任务</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="card bg-info text-white">
                                            <div class="card-body">
                                                <h4>${analysis.grouping_percentage || 0}%</h4>
                                                <small>分组率</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>按平台分布</h6>
                                        <div class="list-group">
                                            ${Object.entries(analysis.platform_distribution || {}).map(([platform, count]) => `
                                                <div class="list-group-item d-flex justify-content-between">
                                                    <span>${platform}</span>
                                                    <span class="badge bg-primary">${count}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>按状态分布</h6>
                                        <div class="list-group">
                                            ${Object.entries(analysis.status_distribution || {}).map(([status, count]) => `
                                                <div class="list-group-item d-flex justify-content-between">
                                                    <span>${status}</span>
                                                    <span class="badge bg-${getStatusColor(status)}">${count}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                                
                                ${analysis.recommendations && analysis.recommendations.length > 0 ? `
                                    <div class="mt-4">
                                        <h6>分组建议</h6>
                                        <div class="alert alert-info">
                                            <ul class="mb-0">
                                                ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                            </ul>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="showAutoGroupModal()">智能分组</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面并显示
            const existingModal = document.getElementById('distributionModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            document.body.insertAdjacentHTML('beforeend', modalContent);
            const modal = new bootstrap.Modal(document.getElementById('distributionModal'));
            modal.show();
            
        } else {
            throw new Error('获取分布分析失败');
        }
    } catch (error) {
        console.error('显示分布分析失败:', error);
        showAlert('获取分布分析失败，请重试', 'danger');
    }
}

/**
 * 显示智能分组模态框
 */
function showAutoGroupModal() {
    // 关闭分布分析模态框
    const distributionModal = document.getElementById('distributionModal');
    if (distributionModal && bootstrap.Modal.getInstance(distributionModal)) {
        bootstrap.Modal.getInstance(distributionModal).hide();
    }
    
    // 显示智能分组模态框
    const autoGroupModal = document.getElementById('autoGroupModal');
    if (autoGroupModal) {
        // 重置内容
        const criteriaContainer = document.getElementById('autoGroupCriteria');
        if (criteriaContainer) {
            criteriaContainer.innerHTML = '';
            addGroupCriterion(); // 添加第一个条件
        }
        
        new bootstrap.Modal(autoGroupModal).show();
    }
}

/**
 * 添加分组条件
 */
function addGroupCriterion() {
    const container = document.getElementById('autoGroupCriteria');
    if (!container) return;
    
    const criterionIndex = container.children.length;
    const criterionHtml = `
        <div class="criterion-item" data-index="${criterionIndex}">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h6>分组条件 ${criterionIndex + 1}</h6>
                ${criterionIndex > 0 ? `<button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCriterion(${criterionIndex})">删除</button>` : ''}
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">分组名称</label>
                    <input type="text" class="form-control" name="criteria[${criterionIndex}][group_name]" required>
                </div>
                <div class="col-md-6">
                    <label class="form-label">分组颜色</label>
                    <input type="color" class="form-control form-control-color" name="criteria[${criterionIndex}][color]" value="#007bff">
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">分组描述</label>
                <textarea class="form-control" name="criteria[${criterionIndex}][description]" rows="2"></textarea>
            </div>
            
            <div class="mb-3">
                <label class="form-label">分组条件</label>
                <div class="conditions-container" id="conditions_${criterionIndex}">
                    <div class="condition-item row mb-2">
                        <div class="col-md-3">
                            <select class="form-select" name="criteria[${criterionIndex}][conditions][0][field]" required>
                                <option value="">选择字段</option>
                                <option value="platform">平台</option>
                                <option value="status">状态</option>
                                <option value="monitor_type">监控类型</option>
                                <option value="frequency">频率</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="criteria[${criterionIndex}][conditions][0][operator]" required>
                                <option value="=">等于</option>
                                <option value="!=">不等于</option>
                                <option value="like">包含</option>
                                <option value="in">属于</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <input type="text" class="form-control" name="criteria[${criterionIndex}][conditions][0][value]" placeholder="条件值" required>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCondition(${criterionIndex})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', criterionHtml);
}

/**
 * 移除分组条件
 */
function removeCriterion(index) {
    const criterion = document.querySelector(`[data-index="${index}"]`);
    if (criterion) {
        criterion.remove();
    }
}

/**
 * 添加条件
 */
function addCondition(criterionIndex) {
    const container = document.getElementById(`conditions_${criterionIndex}`);
    if (!container) return;
    
    const conditionIndex = container.children.length;
    const conditionHtml = `
        <div class="condition-item row mb-2">
            <div class="col-md-3">
                <select class="form-select" name="criteria[${criterionIndex}][conditions][${conditionIndex}][field]" required>
                    <option value="">选择字段</option>
                    <option value="platform">平台</option>
                    <option value="status">状态</option>
                    <option value="monitor_type">监控类型</option>
                    <option value="frequency">频率</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="criteria[${criterionIndex}][conditions][${conditionIndex}][operator]" required>
                    <option value="=">等于</option>
                    <option value="!=">不等于</option>
                    <option value="like">包含</option>
                    <option value="in">属于</option>
                </select>
            </div>
            <div class="col-md-5">
                <input type="text" class="form-control" name="criteria[${criterionIndex}][conditions][${conditionIndex}][value]" placeholder="条件值" required>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCondition(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', conditionHtml);
}

/**
 * 移除条件
 */
function removeCondition(button) {
    const conditionItem = button.closest('.condition-item');
    if (conditionItem) {
        conditionItem.remove();
    }
}

/**
 * 执行智能分组
 */
async function executeAutoGrouping() {
    try {
        // 收集表单数据
        const formData = new FormData();
        const criteria = [];
        
        document.querySelectorAll('.criterion-item').forEach((criterion, index) => {
            const criterionData = {
                group_name: criterion.querySelector(`[name="criteria[${index}][group_name]"]`)?.value,
                description: criterion.querySelector(`[name="criteria[${index}][description]"]`)?.value,
                color: criterion.querySelector(`[name="criteria[${index}][color]"]`)?.value,
                conditions: []
            };
            
            criterion.querySelectorAll('.condition-item').forEach((condition, condIndex) => {
                const field = condition.querySelector(`[name="criteria[${index}][conditions][${condIndex}][field]"]`)?.value;
                const operator = condition.querySelector(`[name="criteria[${index}][conditions][${condIndex}][operator]"]`)?.value;
                const value = condition.querySelector(`[name="criteria[${index}][conditions][${condIndex}][value]"]`)?.value;
                
                if (field && operator && value) {
                    criterionData.conditions.push({ field, operator, value });
                }
            });
            
            if (criterionData.group_name && criterionData.conditions.length > 0) {
                criteria.push(criterionData);
            }
        });
        
        if (criteria.length === 0) {
            showAlert('请至少配置一个有效的分组条件', 'warning');
            return;
        }
        
        showLoading(true);
        
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const response = await fetch('/api/task-groups/auto-group', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
            },
            body: JSON.stringify({ criteria })
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert(`智能分组完成！创建了 ${result.data.length} 个分组`, 'success');
            
            // 关闭模态框
            const autoGroupModal = document.getElementById('autoGroupModal');
            if (autoGroupModal && bootstrap.Modal.getInstance(autoGroupModal)) {
                bootstrap.Modal.getInstance(autoGroupModal).hide();
            }
            
            // 刷新数据
            initializeTaskGroups();
            loadStatistics();
        } else {
            const error = await response.json();
            throw new Error(error.message || '智能分组失败');
        }
        
    } catch (error) {
        console.error('智能分组失败:', error);
        showAlert('智能分组失败：' + error.message, 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * 工具函数
 */
function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'paused': 'warning', 
        'stopped': 'secondary',
        'error': 'danger'
    };
    return colors[status] || 'secondary';
}

function updateColorPreview(color, selector) {
    const preview = document.querySelector(selector);
    if (preview) {
        preview.style.backgroundColor = color;
    }
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alertContainer';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

function showLoading(show) {
    const existingSpinner = document.querySelector('.loading-overlay');
    
    if (show && !existingSpinner) {
        const spinner = document.createElement('div');
        spinner.className = 'loading-overlay position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        spinner.style.backgroundColor = 'rgba(0,0,0,0.5)';
        spinner.style.zIndex = '9998';
        spinner.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        `;
        document.body.appendChild(spinner);
    } else if (!show && existingSpinner) {
        existingSpinner.remove();
    }
} 
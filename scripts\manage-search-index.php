<?php

/**
 * MeiliSearch 索引管理脚本
 * 用于创建和管理产品搜索索引
 */

require_once __DIR__ . '/../vendor/autoload.php';

use MeiliSearch\Client;

// 配置
$host = 'http://127.0.0.1:7700';
$masterKey = 'your-master-key-here'; // 请修改为实际的主密钥

try {
    // 创建客户端
    $client = new Client($host, $masterKey);
    
    // 检查连接
    echo "正在连接到 MeiliSearch...\n";
    $health = $client->health();
    echo "连接成功！状态: " . $health['status'] . "\n";
    
    // 创建产品索引
    echo "创建产品索引...\n";
    $index = $client->index('products');
    
    // 配置可搜索属性
    $searchableAttributes = [
        'title',
        'description', 
        'category_path',
        'shop_name',
        'brand'
    ];
    
    $index->updateSearchableAttributes($searchableAttributes);
    echo "已设置可搜索属性: " . implode(', ', $searchableAttributes) . "\n";
    
    // 配置可过滤属性
    $filterableAttributes = [
        'source_platform',
        'category_path',
        'min_price',
        'max_price',
        'total_sales',
        'rating',
        'shop_id',
        'shop_name'
    ];
    
    $index->updateFilterableAttributes($filterableAttributes);
    echo "已设置可过滤属性: " . implode(', ', $filterableAttributes) . "\n";
    
    // 配置可排序属性
    $sortableAttributes = [
        'min_price',
        'max_price',
        'total_sales',
        'rating',
        'created_at',
        'updated_at'
    ];
    
    $index->updateSortableAttributes($sortableAttributes);
    echo "已设置可排序属性: " . implode(', ', $sortableAttributes) . "\n";
    
    // 配置同义词（可选）
    $synonyms = [
        'phone' => ['手机', 'mobile', 'smartphone'],
        'laptop' => ['笔记本', 'notebook', '电脑'],
        'tablet' => ['平板', 'pad']
    ];
    
    $index->updateSynonyms($synonyms);
    echo "已设置同义词\n";
    
    // 配置停用词（可选）
    $stopWords = ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个'];
    $index->updateStopWords($stopWords);
    echo "已设置停用词\n";
    
    echo "索引配置完成！\n";
    echo "现在可以使用 Laravel Scout 进行产品搜索了。\n";
    echo "\n使用方法:\n";
    echo "1. 在 Laravel 中运行: php artisan scout:import \"App\\Models\\Product\"\n";
    echo "2. 或者使用模型方法: Product::all()->searchable()\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "请检查 MeiliSearch 是否正在运行，以及配置是否正确。\n";
    exit(1);
}

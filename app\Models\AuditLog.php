<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event',
        'auditable_type',
        'auditable_id',
        'old_values',
        'new_values',
        'url',
        'ip_address',
        'user_agent',
        'metadata',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
    ];

    // 事件类型常量
    const EVENT_CREATED = 'created';
    const EVENT_UPDATED = 'updated';
    const EVENT_DELETED = 'deleted';
    const EVENT_LOGIN = 'login';
    const EVENT_LOGOUT = 'logout';
    const EVENT_PASSWORD_CHANGED = 'password_changed';
    const EVENT_ROLE_ASSIGNED = 'role_assigned';
    const EVENT_ROLE_REMOVED = 'role_removed';
    const EVENT_API_CALL = 'api_call';
    const EVENT_DATA_COLLECTION = 'data_collection';
    const EVENT_SYSTEM_CONFIG_CHANGED = 'system_config_changed';

    // 关联关系
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function auditable(): MorphTo
    {
        return $this->morphTo();
    }

    // 查询作用域
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByEvent(Builder $query, string $event): Builder
    {
        return $query->where('event', $event);
    }

    public function scopeByModel(Builder $query, string $modelType, ?int $modelId = null): Builder
    {
        $query->where('auditable_type', $modelType);

        if ($modelId) {
            $query->where('auditable_id', $modelId);
        }

        return $query;
    }

    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('created_at', today());
    }

    // 静态方法用于记录审计日志
    public static function logEvent(
        string $event,
        ?Model $auditable = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $metadata = null
    ): self {
        return self::create([
            'user_id' => auth()->id(),
            'event' => $event,
            'auditable_type' => $auditable ? get_class($auditable) : null,
            'auditable_id' => $auditable?->id,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'url' => request()->fullUrl(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => $metadata,
        ]);
    }

    public static function logLogin(User $user, array $metadata = []): self
    {
        return self::create([
            'user_id' => $user->id,
            'event' => self::EVENT_LOGIN,
            'url' => request()->fullUrl(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => array_merge([
                'login_time' => now()->toISOString(),
            ], $metadata),
        ]);
    }

    public static function logLogout(User $user, array $metadata = []): self
    {
        return self::create([
            'user_id' => $user->id,
            'event' => self::EVENT_LOGOUT,
            'url' => request()->fullUrl(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => array_merge([
                'logout_time' => now()->toISOString(),
            ], $metadata),
        ]);
    }

    public static function logApiCall(string $endpoint, array $metadata = []): self
    {
        return self::create([
            'user_id' => auth()->id(),
            'event' => self::EVENT_API_CALL,
            'url' => request()->fullUrl(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => array_merge([
                'endpoint' => $endpoint,
                'method' => request()->method(),
                'response_time' => $metadata['response_time'] ?? null,
                'status_code' => $metadata['status_code'] ?? null,
            ], $metadata),
        ]);
    }

    public static function logDataCollection(string $platform, array $metadata = []): self
    {
        return self::create([
            'user_id' => auth()->id(),
            'event' => self::EVENT_DATA_COLLECTION,
            'metadata' => array_merge([
                'platform' => $platform,
                'collection_time' => now()->toISOString(),
            ], $metadata),
        ]);
    }

    // 辅助方法
    public function getEventDisplayName(): string
    {
        $eventNames = [
            self::EVENT_CREATED => '创建',
            self::EVENT_UPDATED => '更新',
            self::EVENT_DELETED => '删除',
            self::EVENT_LOGIN => '登录',
            self::EVENT_LOGOUT => '登出',
            self::EVENT_PASSWORD_CHANGED => '密码修改',
            self::EVENT_ROLE_ASSIGNED => '角色分配',
            self::EVENT_ROLE_REMOVED => '角色移除',
            self::EVENT_API_CALL => 'API调用',
            self::EVENT_DATA_COLLECTION => '数据收集',
            self::EVENT_SYSTEM_CONFIG_CHANGED => '系统配置修改',
        ];

        return $eventNames[$this->event] ?? $this->event;
    }

    public function getModelDisplayName(): string
    {
        if (!$this->auditable_type) {
            return '';
        }

        $modelNames = [
            User::class => '用户',
            ApiConfiguration::class => 'API配置',
            SystemConfiguration::class => '系统配置',
            // 可以添加更多模型映射
        ];

        return $modelNames[$this->auditable_type] ?? class_basename($this->auditable_type);
    }

    public function hasValueChanges(): bool
    {
        return !empty($this->old_values) || !empty($this->new_values);
    }

    public function getChangedFields(): array
    {
        if (!$this->hasValueChanges()) {
            return [];
        }

        $oldValues = $this->old_values ?? [];
        $newValues = $this->new_values ?? [];

        return array_keys(array_merge($oldValues, $newValues));
    }
}

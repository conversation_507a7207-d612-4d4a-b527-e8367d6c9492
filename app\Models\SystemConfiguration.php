<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Cache;

class SystemConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'category',
        'value',
        'type',
        'label',
        'description',
        'validation_rules',
        'options',
        'is_encrypted',
        'is_public',
        'requires_restart',
        'sort_order',
    ];

    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'is_encrypted' => 'boolean',
        'is_public' => 'boolean',
        'requires_restart' => 'boolean',
    ];

    // 缓存键前缀
    const CACHE_PREFIX = 'system_config_';
    const CACHE_TTL = 3600; // 1小时

    // 配置分类
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_PERFORMANCE = 'performance';
    const CATEGORY_EMAIL = 'email';
    const CATEGORY_MONITORING = 'monitoring';
    const CATEGORY_SECURITY = 'security';

    // 数据类型
    const TYPE_STRING = 'string';
    const TYPE_INTEGER = 'integer';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_JSON = 'json';
    const TYPE_FILE = 'file';
    const TYPE_SELECT = 'select';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_PASSWORD = 'password';

    // Accessors and Mutators
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && !empty($value)) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return $value; // 如果解密失败，返回原值
            }
        }

        // 根据类型转换值
        return $this->castValue($value, $this->type);
    }

    public function setValueAttribute($value)
    {
        if ($this->attributes['is_encrypted'] ?? false && !empty($value)) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    // Scopes
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('category')->orderBy('sort_order')->orderBy('label');
    }

    // Static methods for configuration management
    public static function getValue(string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . $key;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($key, $default) {
            $config = self::where('key', $key)->first();
            return $config ? $config->value : $default;
        });
    }

    public static function setValue(string $key, $value): bool
    {
        $config = self::where('key', $key)->first();

        if ($config) {
            $config->update(['value' => $value]);
        } else {
            $config = self::create([
                'key' => $key,
                'value' => $value,
                'type' => self::TYPE_STRING,
                'label' => $key,
                'category' => self::CATEGORY_GENERAL,
            ]);
        }

        // 清除缓存
        Cache::forget(self::CACHE_PREFIX . $key);

        return true;
    }

    public static function getByCategory(string $category): array
    {
        $cacheKey = self::CACHE_PREFIX . 'category_' . $category;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($category) {
            return self::byCategory($category)
                ->ordered()
                ->get()
                ->keyBy('key')
                ->map(fn($config) => $config->value)
                ->toArray();
        });
    }

    public static function getAllPublic(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'public';

        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return self::public()
                ->ordered()
                ->get()
                ->keyBy('key')
                ->map(fn($config) => $config->value)
                ->toArray();
        });
    }

    public static function clearCache(?string $key = null): void
    {
        if ($key) {
            Cache::forget(self::CACHE_PREFIX . $key);
        } else {
            // 清除所有配置缓存
            $keys = self::pluck('key');
            foreach ($keys as $configKey) {
                Cache::forget(self::CACHE_PREFIX . $configKey);
            }

            // 清除分类缓存
            $categories = self::distinct('category')->pluck('category');
            foreach ($categories as $category) {
                Cache::forget(self::CACHE_PREFIX . 'category_' . $category);
            }

            Cache::forget(self::CACHE_PREFIX . 'public');
        }
    }

    // Helper methods
    private function castValue($value, string $type)
    {
        switch ($type) {
            case self::TYPE_INTEGER:
                return (int) $value;
            case self::TYPE_BOOLEAN:
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case self::TYPE_JSON:
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    public function getFormattedValue()
    {
        switch ($this->type) {
            case self::TYPE_BOOLEAN:
                return $this->value ? '是' : '否';
            case self::TYPE_JSON:
                return json_encode($this->value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case self::TYPE_PASSWORD:
                return '***';
            default:
                return $this->value;
        }
    }

    public function isRequired(): bool
    {
        $rules = $this->validation_rules ?? [];
        return in_array('required', $rules);
    }
}

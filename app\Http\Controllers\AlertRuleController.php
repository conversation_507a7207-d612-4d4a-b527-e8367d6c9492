<?php

namespace App\Http\Controllers;

use App\Models\AlertRule;
use App\Models\Product;
use App\Models\ProductSku;
use App\Models\MonitorTask;
use App\Services\AlertRuleValidationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class AlertRuleController extends Controller
{
    protected AlertRuleValidationService $validationService;

    public function __construct(AlertRuleValidationService $validationService = null)
    {
        $this->validationService = $validationService ?? new AlertRuleValidationService();
    }
    /**
     * 获取所有警报规则列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = AlertRule::with(['user', 'product', 'productSku', 'monitorTask'])
                ->where('user_id', Auth::id());

            // 状态过滤
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // 类型过滤
            if ($request->has('type')) {
                $query->where('type', $request->input('type'));
            }

            // 范围过滤
            if ($request->has('scope')) {
                $query->where('scope', $request->input('scope'));
            }

            // 分页
            $perPage = min($request->input('per_page', 15), 100);
            $rules = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $rules,
                'message' => '警报规则列表获取成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取警报规则列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建新的警报规则
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // 使用验证服务进行全面验证
            $requestData = $request->all();
            $requestData['user_id'] = Auth::id();
            $validatedData = $this->validationService->validateAlertRule($requestData);

            DB::beginTransaction();

            $alertRule = AlertRule::create($validatedData);

            DB::commit();

            // 重新加载关联数据
            $alertRule->load(['user', 'product', 'productSku', 'monitorTask']);

            return response()->json([
                'success' => true,
                'data' => $alertRule,
                'message' => '警报规则创建成功'
            ], 201);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '数据验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '创建警报规则失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示指定的警报规则
     */
    public function show(AlertRule $alertRule): JsonResponse
    {
        try {
            // 检查权限
            if ($alertRule->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权访问此警报规则'
                ], 403);
            }

            $alertRule->load(['user', 'product', 'productSku', 'monitorTask', 'alertLogs' => function($query) {
                $query->latest()->take(10);
            }]);

            return response()->json([
                'success' => true,
                'data' => $alertRule,
                'message' => '警报规则详情获取成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取警报规则详情失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新指定的警报规则
     */
    public function update(Request $request, AlertRule $alertRule): JsonResponse
    {
        try {
            // 检查权限
            if ($alertRule->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权修改此警报规则'
                ], 403);
            }

            // 使用验证服务进行全面验证
            $requestData = $request->all();
            $validatedData = $this->validationService->validateAlertRule($requestData, $alertRule->id);

            DB::beginTransaction();

            $alertRule->update($validatedData);

            DB::commit();

            // 重新加载关联数据
            $alertRule->load(['user', 'product', 'productSku', 'monitorTask']);

            return response()->json([
                'success' => true,
                'data' => $alertRule,
                'message' => '警报规则更新成功'
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '数据验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '更新警报规则失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除指定的警报规则
     */
    public function destroy(AlertRule $alertRule): JsonResponse
    {
        try {
            // 检查权限
            if ($alertRule->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权删除此警报规则'
                ], 403);
            }

            DB::beginTransaction();

            $alertRule->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '警报规则删除成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '删除警报规则失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量删除警报规则
     */
    public function batchDestroy(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'ids' => 'required|array|min:1',
                'ids.*' => 'required|integer|exists:alert_rules,id'
            ]);

            $ids = $request->input('ids');
            
            DB::beginTransaction();

            // 只删除属于当前用户的规则
            $deletedCount = AlertRule::whereIn('id', $ids)
                ->where('user_id', Auth::id())
                ->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "成功删除 {$deletedCount} 条警报规则"
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '数据验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 启用/禁用警报规则
     */
    public function toggleStatus(AlertRule $alertRule): JsonResponse
    {
        try {
            // 检查权限
            if ($alertRule->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => '无权修改此警报规则'
                ], 403);
            }

            $alertRule->update(['is_active' => !$alertRule->is_active]);

            $status = $alertRule->is_active ? '启用' : '禁用';

            return response()->json([
                'success' => true,
                'data' => $alertRule,
                'message' => "警报规则已{$status}"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '状态切换失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取警报规则统计信息
     */
    public function stats(): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            $stats = [
                'total' => AlertRule::where('user_id', $userId)->count(),
                'active' => AlertRule::where('user_id', $userId)->where('is_active', true)->count(),
                'inactive' => AlertRule::where('user_id', $userId)->where('is_active', false)->count(),
                'by_type' => AlertRule::where('user_id', $userId)
                    ->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'triggered_today' => AlertRule::where('user_id', $userId)
                    ->whereDate('last_triggered_at', today())
                    ->count(),
                'total_triggers' => AlertRule::where('user_id', $userId)
                    ->sum('trigger_count')
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => '统计信息获取成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计信息失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取可用的选项列表
     */
    public function options(): JsonResponse
    {
        try {
            $options = [
                'alert_types' => AlertRule::getAlertTypes(),
                'comparison_options' => AlertRule::getComparisonOptions(),
                'scope_options' => AlertRule::getScopeOptions(),
                'notification_methods' => AlertRule::getNotificationMethods(),
                'products' => Product::select('id', 'name', 'brand')->get(),
                'monitor_tasks' => MonitorTask::select('id', 'task_name', 'platform')->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $options,
                'message' => '选项列表获取成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取选项列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示警报规则列表页面
     */
    public function indexPage(Request $request)
    {
        try {
            // 获取统计信息
            $userId = Auth::id();
            $stats = [
                'total' => AlertRule::where('user_id', $userId)->count(),
                'active' => AlertRule::where('user_id', $userId)->where('is_active', true)->count(),
                'triggered_today' => AlertRule::where('user_id', $userId)
                    ->whereDate('last_triggered_at', today())
                    ->count(),
                'inactive' => AlertRule::where('user_id', $userId)->where('is_active', false)->count(),
            ];

            // 构建查询
            $query = AlertRule::with(['product', 'productSku', 'monitorTask'])
                ->where('user_id', $userId);

            // 应用过滤器
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }

            if ($request->filled('status')) {
                $query->where('is_active', $request->boolean('status'));
            }

            if ($request->filled('priority')) {
                $query->where('priority', $request->input('priority'));
            }

            // 分页
            $rules = $query->orderBy('created_at', 'desc')->paginate(15);

            return view('alert-rules.index', compact('rules', 'stats'));
        } catch (\Exception $e) {
            return back()->withError('获取警报规则列表失败：' . $e->getMessage());
        }
    }

    /**
     * 显示创建警报规则页面
     */
    public function createPage()
    {
        try {
            $products = Product::select('id', 'name', 'brand')->get();
            $monitorTasks = MonitorTask::select('id', 'task_name', 'platform')->get();
            
            return view('alert-rules.create', compact('products', 'monitorTasks'));
        } catch (\Exception $e) {
            return back()->withError('无法加载创建页面：' . $e->getMessage());
        }
    }

    /**
     * 显示编辑警报规则页面
     */
    public function editPage(AlertRule $alertRule)
    {
        try {
            // 检查权限
            if ($alertRule->user_id !== Auth::id()) {
                return abort(403, '无权访问此警报规则');
            }

            $products = Product::select('id', 'name', 'brand')->get();
            $monitorTasks = MonitorTask::select('id', 'task_name', 'platform')->get();
            
            return view('alert-rules.edit', compact('alertRule', 'products', 'monitorTasks'));
        } catch (\Exception $e) {
            return back()->withError('无法加载编辑页面：' . $e->getMessage());
        }
    }

    /**
     * 处理批量操作
     */
    public function bulkAction(Request $request)
    {
        try {
            $request->validate([
                'action' => 'required|in:enable,disable,delete',
                'rule_ids' => 'required|array|min:1',
                'rule_ids.*' => 'required|integer|exists:alert_rules,id'
            ]);

            $action = $request->input('action');
            $ruleIds = $request->input('rule_ids');
            
            DB::beginTransaction();

            $query = AlertRule::whereIn('id', $ruleIds)->where('user_id', Auth::id());
            
            switch ($action) {
                case 'enable':
                    $affectedCount = $query->update(['is_active' => true]);
                    $message = "成功启用 {$affectedCount} 条警报规则";
                    break;
                case 'disable':
                    $affectedCount = $query->update(['is_active' => false]);
                    $message = "成功禁用 {$affectedCount} 条警报规则";
                    break;
                case 'delete':
                    $affectedCount = $query->delete();
                    $message = "成功删除 {$affectedCount} 条警报规则";
                    break;
                default:
                    throw new \InvalidArgumentException('无效的操作类型');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '数据验证失败',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '批量操作失败：' . $e->getMessage()
            ], 500);
        }
    }

} 
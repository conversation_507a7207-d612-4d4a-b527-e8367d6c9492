/**
 * 性能监控和优化模块
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.observers = {};
        this.init();
    }

    init() {
        this.setupPerformanceObserver();
        this.setupResourceMonitoring();
        this.setupUserInteractionTracking();
        this.setupMemoryMonitoring();
        this.setupNetworkMonitoring();
        this.startPerformanceReporting();
    }

    // ==================== 性能观察器 ====================
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // 监控导航性能
            this.observers.navigation = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordNavigationMetrics(entry);
                }
            });
            this.observers.navigation.observe({ entryTypes: ['navigation'] });

            // 监控资源加载性能
            this.observers.resource = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordResourceMetrics(entry);
                }
            });
            this.observers.resource.observe({ entryTypes: ['resource'] });

            // 监控用户交互性能
            if ('first-input' in PerformanceObserver.supportedEntryTypes) {
                this.observers.firstInput = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordFirstInputDelay(entry);
                    }
                });
                this.observers.firstInput.observe({ entryTypes: ['first-input'] });
            }

            // 监控布局偏移
            if ('layout-shift' in PerformanceObserver.supportedEntryTypes) {
                this.observers.layoutShift = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordLayoutShift(entry);
                    }
                });
                this.observers.layoutShift.observe({ entryTypes: ['layout-shift'] });
            }

            // 监控最大内容绘制
            if ('largest-contentful-paint' in PerformanceObserver.supportedEntryTypes) {
                this.observers.lcp = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordLargestContentfulPaint(entry);
                    }
                });
                this.observers.lcp.observe({ entryTypes: ['largest-contentful-paint'] });
            }
        }
    }

    // ==================== 指标记录 ====================
    recordNavigationMetrics(entry) {
        this.metrics.navigation = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            domInteractive: entry.domInteractive - entry.navigationStart,
            firstPaint: this.getFirstPaint(),
            firstContentfulPaint: this.getFirstContentfulPaint(),
            timeToInteractive: this.calculateTTI(entry)
        };
    }

    recordResourceMetrics(entry) {
        if (!this.metrics.resources) {
            this.metrics.resources = [];
        }

        this.metrics.resources.push({
            name: entry.name,
            type: entry.initiatorType,
            size: entry.transferSize,
            duration: entry.duration,
            startTime: entry.startTime
        });
    }

    recordFirstInputDelay(entry) {
        this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
    }

    recordLayoutShift(entry) {
        if (!this.metrics.cumulativeLayoutShift) {
            this.metrics.cumulativeLayoutShift = 0;
        }
        this.metrics.cumulativeLayoutShift += entry.value;
    }

    recordLargestContentfulPaint(entry) {
        this.metrics.largestContentfulPaint = entry.startTime;
    }

    // ==================== 辅助方法 ====================
    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : null;
    }

    getFirstContentfulPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        return fcp ? fcp.startTime : null;
    }

    calculateTTI(navigationEntry) {
        // 简化的TTI计算
        return navigationEntry.domInteractive - navigationEntry.navigationStart;
    }

    // ==================== 资源监控 ====================
    setupResourceMonitoring() {
        // 监控图片加载失败
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IMG') {
                this.recordResourceError('image', e.target.src);
            }
        }, true);

        // 监控脚本加载失败
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'SCRIPT') {
                this.recordResourceError('script', e.target.src);
            }
        }, true);

        // 监控样式表加载失败
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'LINK' && e.target.rel === 'stylesheet') {
                this.recordResourceError('stylesheet', e.target.href);
            }
        }, true);
    }

    recordResourceError(type, url) {
        if (!this.metrics.resourceErrors) {
            this.metrics.resourceErrors = [];
        }

        this.metrics.resourceErrors.push({
            type: type,
            url: url,
            timestamp: Date.now()
        });
    }

    // ==================== 用户交互跟踪 ====================
    setupUserInteractionTracking() {
        let clickCount = 0;
        let scrollCount = 0;
        let keyboardCount = 0;

        document.addEventListener('click', () => {
            clickCount++;
        });

        document.addEventListener('scroll', this.throttle(() => {
            scrollCount++;
        }, 100));

        document.addEventListener('keydown', () => {
            keyboardCount++;
        });

        // 每分钟记录一次交互统计
        setInterval(() => {
            this.metrics.userInteractions = {
                clicks: clickCount,
                scrolls: scrollCount,
                keystrokes: keyboardCount,
                timestamp: Date.now()
            };
            
            // 重置计数器
            clickCount = 0;
            scrollCount = 0;
            keyboardCount = 0;
        }, 60000);
    }

    // ==================== 内存监控 ====================
    setupMemoryMonitoring() {
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memory = {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };
            }, 30000); // 每30秒记录一次
        }
    }

    // ==================== 网络监控 ====================
    setupNetworkMonitoring() {
        if ('connection' in navigator) {
            this.metrics.connection = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            };

            navigator.connection.addEventListener('change', () => {
                this.metrics.connection = {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt,
                    saveData: navigator.connection.saveData,
                    timestamp: Date.now()
                };
            });
        }
    }

    // ==================== 性能报告 ====================
    startPerformanceReporting() {
        // 页面加载完成后发送初始报告
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.sendPerformanceReport('page-load');
            }, 1000);
        });

        // 页面卸载前发送最终报告
        window.addEventListener('beforeunload', () => {
            this.sendPerformanceReport('page-unload');
        });

        // 定期发送性能报告
        setInterval(() => {
            this.sendPerformanceReport('periodic');
        }, 300000); // 每5分钟
    }

    sendPerformanceReport(type) {
        const report = {
            type: type,
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: Date.now(),
            metrics: this.metrics
        };

        // 发送到性能监控端点
        if ('sendBeacon' in navigator) {
            navigator.sendBeacon('/api/performance-metrics', JSON.stringify(report));
        } else {
            // 降级到普通请求
            fetch('/api/performance-metrics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            }).catch(error => {
                console.warn('Failed to send performance report:', error);
            });
        }
    }

    // ==================== 性能优化建议 ====================
    analyzePerformance() {
        const suggestions = [];

        // 检查首次内容绘制时间
        if (this.metrics.navigation && this.metrics.navigation.firstContentfulPaint > 2500) {
            suggestions.push({
                type: 'warning',
                message: '首次内容绘制时间较长，建议优化关键资源加载'
            });
        }

        // 检查累积布局偏移
        if (this.metrics.cumulativeLayoutShift > 0.1) {
            suggestions.push({
                type: 'warning',
                message: '页面布局偏移较大，建议为图片和广告预留空间'
            });
        }

        // 检查首次输入延迟
        if (this.metrics.firstInputDelay > 100) {
            suggestions.push({
                type: 'warning',
                message: '首次输入延迟较长，建议优化JavaScript执行'
            });
        }

        // 检查内存使用
        if (this.metrics.memory && this.metrics.memory.usedJSHeapSize > 50 * 1024 * 1024) {
            suggestions.push({
                type: 'warning',
                message: 'JavaScript内存使用较高，建议检查内存泄漏'
            });
        }

        return suggestions;
    }

    // ==================== 工具方法 ====================
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // ==================== 公共API ====================
    getMetrics() {
        return this.metrics;
    }

    getSuggestions() {
        return this.analyzePerformance();
    }

    // 手动触发性能报告
    reportNow() {
        this.sendPerformanceReport('manual');
    }
}

// 初始化性能监控
document.addEventListener('DOMContentLoaded', () => {
    window.performanceMonitor = new PerformanceMonitor();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}

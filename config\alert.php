<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 警报规则配置
    |--------------------------------------------------------------------------
    |
    | 这里定义了警报规则系统的各种配置选项
    |
    */

    // 用户规则数量限制
    'max_rules_per_user' => env('ALERT_MAX_RULES_PER_USER', 50),

    // 单个产品的规则数量限制
    'max_rules_per_product' => env('ALERT_MAX_RULES_PER_PRODUCT', 10),

    // 单个SKU的规则数量限制
    'max_rules_per_sku' => env('ALERT_MAX_RULES_PER_SKU', 5),

    // 默认冷却时间（分钟）
    'default_cooldown_minutes' => env('ALERT_DEFAULT_COOLDOWN_MINUTES', 60),

    // 最小冷却时间（分钟）
    'min_cooldown_minutes' => env('ALERT_MIN_COOLDOWN_MINUTES', 5),

    // 最大冷却时间（分钟）
    'max_cooldown_minutes' => env('ALERT_MAX_COOLDOWN_MINUTES', 10080), // 7天

    // 阈值配置
    'thresholds' => [
        'price' => [
            'min' => 0,
            'max' => 999999.99,
        ],
        'percentage' => [
            'min' => 0,
            'max' => 100,
        ],
        'inventory' => [
            'min' => 0,
            'max' => 999999,
        ],
        'data_update_hours' => [
            'min' => 1,
            'max' => 168, // 7天
        ],
    ],

    // 支持的渠道
    'allowed_channels' => [
        'taobao' => '淘宝',
        'tmall' => '天猫',
        'jd' => '京东',
        'pdd' => '拼多多',
        'vip' => '唯品会',
        'suning' => '苏宁',
        'amazon' => '亚马逊',
        'official' => '官方商城',
    ],

    // 通知配置
    'notifications' => [
        'email' => [
            'enabled' => env('ALERT_EMAIL_ENABLED', true),
            'max_recipients' => env('ALERT_EMAIL_MAX_RECIPIENTS', 10),
        ],
        'sms' => [
            'enabled' => env('ALERT_SMS_ENABLED', true),
            'max_recipients' => env('ALERT_SMS_MAX_RECIPIENTS', 5),
        ],
        'push' => [
            'enabled' => env('ALERT_PUSH_ENABLED', true),
        ],
    ],

    // 验证规则配置
    'validation' => [
        'check_duplicates' => env('ALERT_CHECK_DUPLICATES', true),
        'check_conflicts' => env('ALERT_CHECK_CONFLICTS', true),
        'check_resource_limits' => env('ALERT_CHECK_RESOURCE_LIMITS', true),
        'verify_related_resources' => env('ALERT_VERIFY_RELATED_RESOURCES', true),
    ],

    // 警报类型特定配置
    'types' => [
        'promotion_price_deviation' => [
            'default_threshold' => 10.0,
            'min_threshold' => 0.1,
            'max_threshold' => 100.0,
        ],
        'channel_price_deviation' => [
            'default_threshold' => 5.0,
            'min_threshold' => 0.1,
            'max_threshold' => 100.0,
        ],
        'price_drop' => [
            'default_threshold' => 10.0,
            'min_threshold' => 0.01,
        ],
        'price_rise' => [
            'default_threshold' => 10.0,
            'min_threshold' => 0.01,
        ],
        'inventory_anomaly' => [
            'default_threshold' => 0,
            'min_threshold' => 0,
        ],
        'stock_change' => [
            'default_threshold' => 10,
            'min_threshold' => 1,
        ],
        'data_update_anomaly' => [
            'default_threshold' => 24,
            'min_threshold' => 1,
            'max_threshold' => 168,
        ],
    ],
]; 
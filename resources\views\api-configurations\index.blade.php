@extends('layouts.app')

@section('title', 'API配置管理')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">API配置管理</h1>
            <p class="mb-0 text-muted">管理第三方API配置、认证信息、速率限制和健康监控</p>
        </div>
        <div class="btn-group">
            <a href="{{ route('api-configurations.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新建配置
            </a>
            <button type="button" class="btn btn-outline-secondary" id="bulkHealthCheckBtn">
                <i class="fas fa-heartbeat"></i> 批量健康检查
            </button>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('api-configurations.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">搜索</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="名称、描述或平台类型">
                </div>
                <div class="col-md-2">
                    <label for="platform_type" class="form-label">平台类型</label>
                    <select class="form-select" id="platform_type" name="platform_type">
                        <option value="">全部</option>
                        <option value="shopify" {{ request('platform_type') == 'shopify' ? 'selected' : '' }}>Shopify</option>
                        <option value="magento" {{ request('platform_type') == 'magento' ? 'selected' : '' }}>Magento</option>
                        <option value="woocommerce" {{ request('platform_type') == 'woocommerce' ? 'selected' : '' }}>WooCommerce</option>
                        <option value="opencart" {{ request('platform_type') == 'opencart' ? 'selected' : '' }}>OpenCart</option>
                        <option value="prestashop" {{ request('platform_type') == 'prestashop' ? 'selected' : '' }}>PrestaShop</option>
                        <option value="custom" {{ request('platform_type') == 'custom' ? 'selected' : '' }}>自定义</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="is_active" class="form-label">状态</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="">全部</option>
                        <option value="1" {{ request('is_active') == '1' ? 'selected' : '' }}>启用</option>
                        <option value="0" {{ request('is_active') == '0' ? 'selected' : '' }}>禁用</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="health_status" class="form-label">健康状态</label>
                    <select class="form-select" id="health_status" name="health_status">
                        <option value="">全部</option>
                        <option value="healthy" {{ request('health_status') == 'healthy' ? 'selected' : '' }}>健康</option>
                        <option value="unhealthy" {{ request('health_status') == 'unhealthy' ? 'selected' : '' }}>异常</option>
                        <option value="unknown" {{ request('health_status') == 'unknown' ? 'selected' : '' }}>未知</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ route('api-configurations.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                总配置数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $configurations->total() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                启用配置</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $configurations->where('is_active', true)->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                健康配置</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $configurations->where('health_status', 'healthy')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heartbeat fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                异常配置</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $configurations->where('health_status', 'unhealthy')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">API配置列表</h6>
        </div>
        <div class="card-body">
            @if($configurations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>平台类型</th>
                                <th>基础URL</th>
                                <th>认证类型</th>
                                <th>版本</th>
                                <th>状态</th>
                                <th>健康状态</th>
                                <th>使用统计</th>
                                <th>最后使用</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($configurations as $config)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $config->name }}</strong>
                                            @if($config->is_deprecated)
                                                <span class="badge badge-warning ml-1">已废弃</span>
                                            @endif
                                        </div>
                                        @if($config->description)
                                            <small class="text-muted">{{ Str::limit($config->description, 50) }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ ucfirst($config->platform_type) }}</span>
                                    </td>
                                    <td>
                                        <code>{{ Str::limit($config->base_url, 30) }}</code>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ ucfirst(str_replace('_', ' ', $config->auth_type)) }}</span>
                                    </td>
                                    <td>{{ $config->version }}</td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input toggle-active" 
                                                   id="active{{ $config->id }}" 
                                                   data-id="{{ $config->id }}"
                                                   {{ $config->is_active ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="active{{ $config->id }}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $healthClass = match($config->health_status) {
                                                'healthy' => 'success',
                                                'unhealthy' => 'danger',
                                                default => 'secondary'
                                            };
                                            $healthIcon = match($config->health_status) {
                                                'healthy' => 'check-circle',
                                                'unhealthy' => 'times-circle',
                                                default => 'question-circle'
                                            };
                                        @endphp
                                        <span class="badge badge-{{ $healthClass }}">
                                            <i class="fas fa-{{ $healthIcon }}"></i>
                                            {{ ucfirst($config->health_status) }}
                                        </span>
                                        @if($config->last_health_check_at)
                                            <br><small class="text-muted">
                                                {{ $config->last_health_check_at->diffForHumans() }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <small>
                                            请求: {{ number_format($config->request_count) }}<br>
                                            错误: {{ number_format($config->error_count) }}
                                            @if($config->request_count > 0)
                                                <br>成功率: {{ number_format((($config->request_count - $config->error_count) / $config->request_count) * 100, 1) }}%
                                            @endif
                                        </small>
                                    </td>
                                    <td>
                                        @if($config->last_used_at)
                                            <small>{{ $config->last_used_at->diffForHumans() }}</small>
                                        @else
                                            <small class="text-muted">从未使用</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('api-configurations.show', $config) }}" 
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($config->health_check_endpoint)
                                                <button type="button" class="btn btn-outline-success health-check" 
                                                        data-id="{{ $config->id }}" title="健康检查">
                                                    <i class="fas fa-heartbeat"></i>
                                                </button>
                                            @endif
                                            <button type="button" class="btn btn-outline-warning rate-limits" 
                                                    data-id="{{ $config->id }}" title="速率限制">
                                                <i class="fas fa-tachometer-alt"></i>
                                            </button>
                                            <a href="{{ route('api-configurations.edit', $config) }}" 
                                               class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger delete-config" 
                                                    data-id="{{ $config->id }}" 
                                                    data-name="{{ $config->name }}" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="mt-3">
                    {{ $configurations->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-cogs fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">暂无API配置</h5>
                    <p class="text-muted">点击上方"新建配置"按钮创建您的第一个API配置。</p>
                    <a href="{{ route('api-configurations.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 新建配置
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>您确定要删除API配置 "<span id="deleteConfigName"></span>" 吗？</p>
                <p class="text-danger"><small>此操作不可撤销。</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 速率限制模态框 -->
<div class="modal fade" id="rateLimitModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tachometer-alt"></i> 速率限制状态
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // 切换启用状态
    $('.toggle-active').change(function() {
        const id = $(this).data('id');
        const isChecked = $(this).is(':checked');
        
        $.ajax({
            url: `/api-configurations/${id}/toggle-active`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success(response.message || 'Status updated successfully');
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || 'Failed to update status');
                // 恢复开关状态
                $(this).prop('checked', !isChecked);
            }
        });
    });

    // 健康检查
    $('.health-check').click(function() {
        const id = $(this).data('id');
        const $btn = $(this);
        const originalHtml = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);
        
        $.ajax({
            url: `/api-configurations/${id}/health-check`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success('健康检查完成: ' + response.status);
                setTimeout(() => location.reload(), 1000);
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || 'Health check failed');
            },
            complete: function() {
                $btn.html(originalHtml).prop('disabled', false);
            }
        });
    });

    // 批量健康检查
    $('#bulkHealthCheckBtn').click(function() {
        const $btn = $(this);
        const originalHtml = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin"></i> 检查中...').prop('disabled', true);
        
        $.ajax({
            url: '/api-configurations/bulk-health-check',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success(`批量健康检查完成，检查了 ${response.total_checked} 个配置`);
                setTimeout(() => location.reload(), 1000);
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || 'Bulk health check failed');
            },
            complete: function() {
                $btn.html(originalHtml).prop('disabled', false);
            }
        });
    });

    // 删除配置
    $('.delete-config').click(function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        
        $('#deleteConfigName').text(name);
        $('#deleteForm').attr('action', `/api-configurations/${id}`);
        $('#deleteModal').modal('show');
    });

    // 速率限制
    $('.rate-limits').click(function() {
        const id = $(this).data('id');
        
        $.ajax({
            url: `/api-configurations/${id}/rate-limits`,
            method: 'GET',
            success: function(response) {
                $('#rateLimitModal .modal-body').html(`
                    <div class="row">
                        <div class="col-md-6">
                            <h6>配置限制</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tr>
                                        <td>每分钟:</td>
                                        <td>${response.configuration.rate_limit_per_minute || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td>每小时:</td>
                                        <td>${response.configuration.rate_limit_per_hour || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td>每天:</td>
                                        <td>${response.configuration.rate_limit_per_day || 'N/A'}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>当前使用情况</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tr>
                                        <td>每分钟剩余:</td>
                                        <td class="${response.limits.per_minute.remaining < 10 ? 'text-danger' : 'text-success'}">
                                            ${response.limits.per_minute.remaining}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>每小时剩余:</td>
                                        <td class="${response.limits.per_hour.remaining < 50 ? 'text-warning' : 'text-success'}">
                                            ${response.limits.per_hour.remaining}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>每天剩余:</td>
                                        <td class="${response.limits.per_day.remaining < 100 ? 'text-warning' : 'text-success'}">
                                            ${response.limits.per_day.remaining}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert ${response.can_make_request ? 'alert-success' : 'alert-danger'}">
                                ${response.can_make_request ? '✓ 可以发送请求' : '✗ 速率限制已达上限'}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-warning btn-sm" onclick="resetRateLimits(${id})">
                                <i class="fas fa-undo"></i> 重置限制
                            </button>
                        </div>
                    </div>
                `);
                $('#rateLimitModal').modal('show');
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || 'Failed to get rate limit status');
            }
        });
    });
});

// 重置速率限制
function resetRateLimits(id) {
    if (confirm('确定要重置此配置的速率限制吗？')) {
        $.ajax({
            url: `/api-configurations/${id}/reset-limits`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                toastr.success(response.message || 'Rate limits reset successfully');
                $('#rateLimitModal').modal('hide');
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                toastr.error(response.message || 'Failed to reset rate limits');
            }
        });
    }
}
</script>
@endpush

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 电商市场动态监测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            background: #f8f9fa;
            min-height: calc(100vh - 200px);
            border-radius: 10px;
        }
        .nav-link {
            color: #6c757d;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: #667eea;
            color: white;
        }
        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }
        .password-requirements {
            font-size: 0.875rem;
        }
        .requirement {
            color: #dc3545;
        }
        .requirement.met {
            color: #198754;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="bi bi-graph-up"></i> 电商监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">
                    <i class="bi bi-speedometer2"></i> 管理后台
                </a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <img src="{{ auth()->user()->avatar_url }}" alt="头像" class="rounded-circle" width="24" height="24">
                        {{ auth()->user()->display_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('profile.index') }}">个人中心</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="{{ route('profile.index') }}">
                            <i class="bi bi-house me-2"></i>个人中心
                        </a>
                        <a class="nav-link" href="{{ route('profile.edit') }}">
                            <i class="bi bi-person-gear me-2"></i>个人资料
                        </a>
                        <a class="nav-link active" href="{{ route('profile.change-password') }}">
                            <i class="bi bi-shield-lock me-2"></i>修改密码
                        </a>
                        <a class="nav-link" href="{{ route('profile.preferences') }}">
                            <i class="bi bi-gear me-2"></i>偏好设置
                        </a>
                        <a class="nav-link" href="{{ route('profile.activity-log') }}">
                            <i class="bi bi-clock-history me-2"></i>活动日志
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="bi bi-shield-lock me-2"></i>修改密码
                        </h4>
                    </div>
                    <div class="card-body">
                        @if($errors->any())
                            <div class="alert alert-danger">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>请修正以下错误：</h6>
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('profile.change-password.update') }}" method="POST" id="changePasswordForm">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                <div class="col-md-8">
                                    <!-- 当前密码 -->
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">
                                            <i class="bi bi-lock me-1"></i>当前密码 <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="current_password" 
                                                   name="current_password" required>
                                            <button class="btn btn-outline-secondary" type="button" id="toggleCurrentPassword">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 新密码 -->
                                    <div class="mb-3">
                                        <label for="password" class="form-label">
                                            <i class="bi bi-key me-1"></i>新密码 <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" 
                                                   name="password" required>
                                            <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <div class="password-strength bg-light" id="passwordStrength"></div>
                                    </div>

                                    <!-- 确认新密码 -->
                                    <div class="mb-3">
                                        <label for="password_confirmation" class="form-label">
                                            <i class="bi bi-key-fill me-1"></i>确认新密码 <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password_confirmation" 
                                                   name="password_confirmation" required>
                                            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <div class="form-text" id="passwordMatch"></div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                            <i class="bi bi-check-lg me-1"></i>更新密码
                                        </button>
                                        <a href="{{ route('profile.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-left me-1"></i>返回
                                        </a>
                                    </div>
                                </div>

                                <!-- 密码要求说明 -->
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="bi bi-info-circle me-1"></i>密码要求
                                            </h6>
                                            <div class="password-requirements">
                                                <div class="requirement" id="length">
                                                    <i class="bi bi-x-circle me-1"></i>至少8个字符
                                                </div>
                                                <div class="requirement" id="lowercase">
                                                    <i class="bi bi-x-circle me-1"></i>包含小写字母
                                                </div>
                                                <div class="requirement" id="uppercase">
                                                    <i class="bi bi-x-circle me-1"></i>包含大写字母
                                                </div>
                                                <div class="requirement" id="number">
                                                    <i class="bi bi-x-circle me-1"></i>包含数字
                                                </div>
                                                <div class="requirement" id="special">
                                                    <i class="bi bi-x-circle me-1"></i>包含特殊字符
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card bg-light mt-3">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="bi bi-shield-check me-1"></i>安全提示
                                            </h6>
                                            <ul class="small mb-0">
                                                <li>使用强密码保护您的账户</li>
                                                <li>定期更换密码</li>
                                                <li>不要使用相同密码</li>
                                                <li>不要与他人分享密码</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 密码显示/隐藏切换
        function setupPasswordToggle(inputId, buttonId) {
            const input = document.getElementById(inputId);
            const button = document.getElementById(buttonId);
            
            button.addEventListener('click', function() {
                const type = input.getAttribute('type');
                if (type === 'password') {
                    input.setAttribute('type', 'text');
                    button.innerHTML = '<i class="bi bi-eye-slash"></i>';
                } else {
                    input.setAttribute('type', 'password');
                    button.innerHTML = '<i class="bi bi-eye"></i>';
                }
            });
        }

        setupPasswordToggle('current_password', 'toggleCurrentPassword');
        setupPasswordToggle('password', 'toggleNewPassword');
        setupPasswordToggle('password_confirmation', 'toggleConfirmPassword');

        // 密码强度检测
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('password_confirmation');
        const strengthBar = document.getElementById('passwordStrength');
        const matchIndicator = document.getElementById('passwordMatch');
        const submitBtn = document.getElementById('submitBtn');

        // 密码要求元素
        const requirements = {
            length: document.getElementById('length'),
            lowercase: document.getElementById('lowercase'),
            uppercase: document.getElementById('uppercase'),
            number: document.getElementById('number'),
            special: document.getElementById('special')
        };

        function checkPasswordRequirement(requirement, test, text) {
            if (test) {
                requirement.classList.add('met');
                requirement.innerHTML = `<i class="bi bi-check-circle me-1"></i>${text}`;
            } else {
                requirement.classList.remove('met');
                requirement.innerHTML = `<i class="bi bi-x-circle me-1"></i>${text}`;
            }
            return test;
        }

        function updatePasswordStrength() {
            const password = passwordInput.value;
            let score = 0;
            let metRequirements = 0;

            // 检查各项要求
            if (checkPasswordRequirement(requirements.length, password.length >= 8, '至少8个字符')) {
                score += 20;
                metRequirements++;
            }
            if (checkPasswordRequirement(requirements.lowercase, /[a-z]/.test(password), '包含小写字母')) {
                score += 20;
                metRequirements++;
            }
            if (checkPasswordRequirement(requirements.uppercase, /[A-Z]/.test(password), '包含大写字母')) {
                score += 20;
                metRequirements++;
            }
            if (checkPasswordRequirement(requirements.number, /\d/.test(password), '包含数字')) {
                score += 20;
                metRequirements++;
            }
            if (checkPasswordRequirement(requirements.special, /[!@#$%^&*(),.?":{}|<>]/.test(password), '包含特殊字符')) {
                score += 20;
                metRequirements++;
            }

            // 更新强度条
            strengthBar.style.width = score + '%';
            if (score < 40) {
                strengthBar.className = 'password-strength bg-danger';
            } else if (score < 80) {
                strengthBar.className = 'password-strength bg-warning';
            } else {
                strengthBar.className = 'password-strength bg-success';
            }

            checkFormValidity();
        }

        function checkPasswordMatch() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (confirmPassword === '') {
                matchIndicator.innerHTML = '';
                return false;
            }

            if (password === confirmPassword) {
                matchIndicator.innerHTML = '<i class="bi bi-check-circle text-success me-1"></i>密码匹配';
                matchIndicator.className = 'form-text text-success';
                return true;
            } else {
                matchIndicator.innerHTML = '<i class="bi bi-x-circle text-danger me-1"></i>密码不匹配';
                matchIndicator.className = 'form-text text-danger';
                return false;
            }
        }

        function checkFormValidity() {
            const password = passwordInput.value;
            const currentPassword = document.getElementById('current_password').value;
            const confirmPassword = confirmPasswordInput.value;
            
            const passwordValid = password.length >= 8 && 
                                 /[a-z]/.test(password) && 
                                 /[A-Z]/.test(password) && 
                                 /\d/.test(password) && 
                                 /[!@#$%^&*(),.?":{}|<>]/.test(password);
            
            const passwordsMatch = password === confirmPassword && confirmPassword !== '';
            const currentPasswordProvided = currentPassword !== '';

            submitBtn.disabled = !(passwordValid && passwordsMatch && currentPasswordProvided);
        }

        passwordInput.addEventListener('input', updatePasswordStrength);
        confirmPasswordInput.addEventListener('input', function() {
            checkPasswordMatch();
            checkFormValidity();
        });
        document.getElementById('current_password').addEventListener('input', checkFormValidity);
    </script>
</body>
</html> 
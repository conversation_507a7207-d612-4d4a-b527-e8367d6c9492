<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductSku;
use App\Models\CompetitorMetric;
use App\Services\MarketAnalysisService;
use App\Services\CompetitorPromotionAnalysisService;
use App\Services\PriceComparisonService;
use Carbon\Carbon;

class CalculateCompetitorMetrics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metrics:calculate-competitor {--date=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate and store competitor metrics for a given date.';

    protected $marketAnalysisService;
    protected $promotionAnalysisService;
    protected $priceComparisonService;

    public function __construct(
        MarketAnalysisService $marketAnalysisService,
        CompetitorPromotionAnalysisService $promotionAnalysisService,
        PriceComparisonService $priceComparisonService
    ) {
        parent::__construct();
        $this->marketAnalysisService = $marketAnalysisService;
        $this->promotionAnalysisService = $promotionAnalysisService;
        $this->priceComparisonService = $priceComparisonService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting competitor metrics calculation...');

        $date = $this->option('date') ? Carbon::parse($this->option('date')) : Carbon::today();
        
        $competitorSkus = ProductSku::where('is_own', false)->get();
        $ownSkus = ProductSku::where('is_own', true)->get();

        if ($competitorSkus->isEmpty() || $ownSkus->isEmpty()) {
            $this->warn('No competitor or own SKUs found. Exiting.');
            return 1;
        }

        $this->withProgressBar($competitorSkus, function ($competitorSku) use ($ownSkus, $date) {
            // 为简单起见，我们随机选择一个自有SKU进行比较
            $ownSku = $ownSkus->random();

            // 1. 市场分析 (假设已在别处计算并可获取)
            // ...

            // 2. 促销策略分析
            $promoAnalysis = $this->promotionAnalysisService->summarizeStrategy($competitorSku->id, $date->copy()->subMonth(), $date);
            
            // 3. 价格偏离分析
            $deviationRate = $this->priceComparisonService->calculatePriceDeviationRate($competitorSku, $ownSku, $date);

            // 存储数据
            CompetitorMetric::updateOrCreate(
                [
                    'product_id' => $competitorSku->product_id,
                    'sku_id' => $competitorSku->id,
                    'own_sku_id' => $ownSku->id,
                    'date' => $date->toDateString(),
                ],
                [
                    'promotion_types' => $promoAnalysis['raw_data']['promotion_types'],
                    'total_promotions' => $promoAnalysis['raw_data']['total_promotions'],
                    'promotion_frequency' => $promoAnalysis['raw_data']['promotion_frequency'],
                    'avg_discount_rate' => $promoAnalysis['raw_data']['avg_discount_rate'],
                    'max_discount_rate' => $promoAnalysis['raw_data']['max_discount_rate'],
                    'min_discount_rate' => $promoAnalysis['raw_data']['min_discount_rate'],
                    'promotion_duration_avg' => $promoAnalysis['raw_data']['promotion_duration_avg'],
                    'analysis_summary' => $promoAnalysis['summary'],
                    'price_deviation_rate' => $deviationRate,
                ]
            );
        });

        $this->info("\nCompetitor metrics calculation completed successfully for " . $date->toDateString());
        return 0;
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>用户注册 - 电商市场动态监测系统</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    创建新账户
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    加入电商市场动态监测系统
                </p>
            </div>

            <!-- Registration Form -->
            <div x-data="registerForm()" class="mt-8 space-y-6">
                <div class="bg-white py-8 px-6 shadow rounded-lg">
                    <form @submit.prevent="submitForm" class="space-y-6">
                        <!-- Name Field -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">
                                姓名 <span class="text-red-500">*</span>
                            </label>
                            <input 
                                x-model="form.name"
                                type="text" 
                                id="name" 
                                name="name" 
                                required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="请输入您的姓名"
                            >
                            <p x-show="errors.name" x-text="errors.name" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <!-- Username Field -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">
                                用户名 <span class="text-red-500">*</span>
                            </label>
                            <input 
                                x-model="form.username"
                                type="text" 
                                id="username" 
                                name="username" 
                                required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="3-50个字符，字母数字下划线"
                            >
                            <p x-show="errors.username" x-text="errors.username" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">
                                邮箱地址 <span class="text-red-500">*</span>
                            </label>
                            <input 
                                x-model="form.email"
                                type="email" 
                                id="email" 
                                name="email" 
                                required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="请输入有效的邮箱地址"
                            >
                            <p x-show="errors.email" x-text="errors.email" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <!-- Phone Field -->
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700">
                                手机号码（可选）
                            </label>
                            <input 
                                x-model="form.phone_number"
                                type="tel" 
                                id="phone_number" 
                                name="phone_number"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="13800138000"
                            >
                            <p x-show="errors.phone_number" x-text="errors.phone_number" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <!-- Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                密码 <span class="text-red-500">*</span>
                            </label>
                            <input 
                                x-model="form.password"
                                type="password" 
                                id="password" 
                                name="password" 
                                required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="8位以上，包含大小写字母、数字和特殊字符"
                            >
                            <p x-show="errors.password" x-text="errors.password" class="mt-1 text-sm text-red-600"></p>
                        </div>

                        <!-- Confirm Password Field -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                                确认密码 <span class="text-red-500">*</span>
                            </label>
                            <input 
                                x-model="form.password_confirmation"
                                type="password" 
                                id="password_confirmation" 
                                name="password_confirmation" 
                                required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="请再次输入密码"
                            >
                        </div>

                        <!-- Terms Agreement -->
                        <div class="flex items-center">
                            <input 
                                x-model="form.terms"
                                id="terms" 
                                name="terms" 
                                type="checkbox" 
                                required
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <label for="terms" class="ml-2 block text-sm text-gray-900">
                                我同意 
                                <a href="#" class="text-blue-600 hover:text-blue-500">服务条款</a> 
                                和 
                                <a href="#" class="text-blue-600 hover:text-blue-500">隐私政策</a>
                                <span class="text-red-500">*</span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button 
                                type="submit" 
                                :disabled="loading"
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <span x-show="!loading">创建账户</span>
                                <span x-show="loading">注册中...</span>
                            </button>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-sm text-gray-600">
                                已有账户？ 
                                <a href="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
                                    立即登录
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <div x-show="message.show" x-transition class="rounded-md p-4"
                 :class="{'bg-green-50 border border-green-200': message.type === 'success', 'bg-red-50 border border-red-200': message.type === 'error'}">
                <div class="flex">
                    <div class="ml-3">
                        <p class="text-sm" :class="{'text-green-800': message.type === 'success', 'text-red-800': message.type === 'error'}" x-text="message.text"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function registerForm() {
            return {
                form: {
                    name: '',
                    username: '',
                    email: '',
                    phone_number: '',
                    password: '',
                    password_confirmation: '',
                    terms: false
                },
                errors: {},
                loading: false,
                message: {
                    show: false,
                    type: '',
                    text: ''
                },

                async submitForm() {
                    this.loading = true;
                    this.errors = {};

                    try {
                        const response = await fetch('/auth/register', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(this.form)
                        });

                        const data = await response.json();

                        if (response.ok) {
                            this.showMessage('success', data.message || '注册成功！');
                            // 清空表单
                            this.form = {
                                name: '',
                                username: '',
                                email: '',
                                phone_number: '',
                                password: '',
                                password_confirmation: '',
                                terms: false
                            };
                        } else {
                            if (data.errors) {
                                this.errors = data.errors;
                            }
                            this.showMessage('error', data.message || '注册失败，请重试');
                        }
                    } catch (error) {
                        this.showMessage('error', '网络错误，请检查连接后重试');
                    } finally {
                        this.loading = false;
                    }
                },

                showMessage(type, text) {
                    this.message = { show: true, type, text };
                    setTimeout(() => {
                        this.message.show = false;
                    }, 5000);
                }
            }
        }
    </script>
</body>
</html>

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CompetitorMetric;
use App\Http\Resources\CompetitorMetricResource;
use Carbon\Carbon;

class CompetitorMetricsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CompetitorMetric::with(['sku.product', 'ownSku']);

        if ($request->has('sku_id')) {
            $query->where('sku_id', $request->input('sku_id'));
        }

        if ($request->has('start_date')) {
            $query->where('date', '>=', Carbon::parse($request->input('start_date')));
        }

        if ($request->has('end_date')) {
            $query->where('date', '<=', Carbon::parse($request->input('end_date')));
        }

        $metrics = $query->latest('date')->paginate(20);

        return CompetitorMetricResource::collection($metrics);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}

@extends('layouts.app')

@section('title', '编辑警报规则')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-edit text-primary me-2"></i>
        编辑警报规则
    </h1>
    <div>
        <a href="{{ route('alert-rules.index') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i>
            返回列表
        </a>
        <button type="button" class="btn btn-outline-danger" onclick="deleteRule()">
            <i class="fas fa-trash me-1"></i>
            删除规则
        </button>
    </div>
</div>

<div class="row">
    {{-- 主表单 --}}
    <div class="col-lg-8">
        <form id="alertRuleForm" method="POST" action="{{ route('alert-rules.update', $alertRule->id) }}">
            @csrf
            @method('PUT')
            
            {{-- 基本信息 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="rule_name" class="form-label">
                                    规则名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control @error('rule_name') is-invalid @enderror" 
                                       id="rule_name" 
                                       name="rule_name" 
                                       value="{{ old('rule_name', $alertRule->rule_name) }}" 
                                       placeholder="为您的警报规则起一个描述性的名称"
                                       required>
                                @error('rule_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="scope" class="form-label">适用范围</label>
                                <select class="form-select @error('scope') is-invalid @enderror" 
                                        id="scope" 
                                        name="scope">
                                    @foreach(\App\Models\AlertRule::getScopeOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ old('scope', $alertRule->scope) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('scope')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rule_title" class="form-label">规则标题</label>
                        <input type="text" 
                               class="form-control @error('rule_title') is-invalid @enderror" 
                               id="rule_title" 
                               name="rule_title" 
                               value="{{ old('rule_title', $alertRule->rule_title) }}" 
                               placeholder="简短的规则标题">
                        @error('rule_title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  placeholder="详细描述此警报规则的用途和触发条件">{{ old('description', $alertRule->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1" 
                               {{ old('is_active', $alertRule->is_active) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            启用此规则
                        </label>
                    </div>
                </div>
            </div>

            {{-- 警报类型配置 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        警报类型与条件
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <label for="type" class="form-label">
                            警报类型 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('type') is-invalid @enderror" 
                                id="type" 
                                name="type" 
                                onchange="updateConfigFields()" 
                                required>
                            <option value="">请选择警报类型</option>
                            @foreach(\App\Models\AlertRule::getAlertTypes() as $value => $label)
                                <option value="{{ $value }}" {{ old('type', $alertRule->type) == $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- 动态配置字段 --}}
                    <div id="configFields">
                        {{-- 这里会通过JavaScript动态填充 --}}
                    </div>
                </div>
            </div>

            {{-- 关联设置 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        关联设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product_id" class="form-label">关联产品</label>
                                <select class="form-select @error('product_id') is-invalid @enderror" 
                                        id="product_id" 
                                        name="product_id"
                                        onchange="loadProductSkus()">
                                    <option value="">请选择产品</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}" {{ old('product_id', $alertRule->product_id) == $product->id ? 'selected' : '' }}>
                                            {{ $product->name }} {{ $product->brand ? "({$product->brand})" : '' }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('product_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="product_sku_id" class="form-label">关联SKU</label>
                                <select class="form-select @error('product_sku_id') is-invalid @enderror" 
                                        id="product_sku_id" 
                                        name="product_sku_id">
                                    <option value="">请选择SKU</option>
                                    {{-- SKU选项会通过JavaScript动态加载 --}}
                                </select>
                                @error('product_sku_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task_id" class="form-label">关联监控任务</label>
                                <select class="form-select @error('task_id') is-invalid @enderror" 
                                        id="task_id" 
                                        name="task_id">
                                    <option value="">请选择监控任务</option>
                                    @foreach($monitorTasks as $task)
                                        <option value="{{ $task->id }}" {{ old('task_id', $alertRule->task_id) == $task->id ? 'selected' : '' }}>
                                            {{ $task->task_name }} {{ $task->platform ? "({$task->platform})" : '' }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('task_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_id" class="form-label">渠道ID</label>
                                <input type="text" 
                                       class="form-control @error('channel_id') is-invalid @enderror" 
                                       id="channel_id" 
                                       name="channel_id" 
                                       value="{{ old('channel_id', $alertRule->channel_id) }}" 
                                       placeholder="例如：taobao, tmall, jd">
                                @error('channel_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- 通知设置 --}}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        通知设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_method" class="form-label">通知方式</label>
                                <select class="form-select @error('notification_method') is-invalid @enderror" 
                                        id="notification_method" 
                                        name="notification_method">
                                    @foreach(\App\Models\AlertRule::getNotificationMethods() as $value => $label)
                                        <option value="{{ $value }}" {{ old('notification_method', $alertRule->notification_method) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('notification_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cooldown_minutes" class="form-label">冷却时间（分钟）</label>
                                <input type="number" 
                                       class="form-control @error('cooldown_minutes') is-invalid @enderror" 
                                       id="cooldown_minutes" 
                                       name="cooldown_minutes" 
                                       value="{{ old('cooldown_minutes', $alertRule->cooldown_minutes) }}" 
                                       min="1" 
                                       max="10080"
                                       placeholder="60">
                                @error('cooldown_minutes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">防止频繁触发，建议设置为30-1440分钟</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- 提交按钮 --}}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ route('alert-rules.index') }}" class="btn btn-outline-secondary me-md-2">
                    <i class="fas fa-times me-1"></i>取消
                </a>
                <button type="submit" class="btn btn-primary" id="submitBtn">
                    <i class="fas fa-save me-1"></i>
                    <span class="btn-text">更新规则</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </div>
        </form>
    </div>

    {{-- 统计信息 --}}
    <div class="col-lg-4">
        <div class="card sticky-top mb-4" style="top: 100px;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    规则统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="mb-1 text-primary">{{ $alertRule->trigger_count }}</h4>
                            <small class="text-muted">触发次数</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="mb-1 {{ $alertRule->is_active ? 'text-success' : 'text-muted' }}">
                                {{ $alertRule->is_active ? '启用' : '禁用' }}
                            </h4>
                            <small class="text-muted">状态</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="mb-1 text-info">
                            {{ $alertRule->last_triggered_at ? $alertRule->last_triggered_at->format('m-d') : '未触发' }}
                        </h4>
                        <small class="text-muted">最后触发</small>
                    </div>
                </div>
                
                @if($alertRule->last_triggered_at)
                <hr>
                <div class="text-center">
                    <small class="text-muted">
                        上次触发时间：{{ $alertRule->last_triggered_at->format('Y-m-d H:i:s') }}
                    </small>
                </div>
                @endif
                
                @if($alertRule->isInCooldown())
                <div class="alert alert-warning mt-3 small">
                    <i class="fas fa-clock me-1"></i>
                    规则在冷却期中，剩余 {{ $alertRule->getCooldownRemainingMinutes() }} 分钟
                </div>
                @endif
            </div>
        </div>

        {{-- 帮助信息 --}}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    编辑提示
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0 small">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        修改规则类型后需要重新配置触发条件
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        调整冷却时间可以控制警报频率
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-link text-info me-2"></i>
                        关联产品和SKU可以更精确地监控
                    </li>
                    <li>
                        <i class="fas fa-bell text-primary me-2"></i>
                        建议测试通知设置确保能正常接收
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

{{-- 删除确认模态框 --}}
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除规则"<strong>{{ $alertRule->rule_name }}</strong>"吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    此操作不可恢复，所有相关的警报日志也将被删除。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>
</div>

{{-- 配置模板 --}}
@include('alert-rules.partials.config-templates')
@endsection

@push('scripts')
<script>
// 当前规则数据
const currentRule = @json($alertRule);

// 页面加载时根据当前类型初始化配置字段
document.addEventListener('DOMContentLoaded', function() {
    updateConfigFields();
    loadProductSkus(); // 如果已选择产品，加载其SKU
});

// 更新配置字段
function updateConfigFields() {
    const type = document.getElementById('type').value;
    const configFields = document.getElementById('configFields');
    
    if (!type) {
        configFields.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>请先选择警报类型以配置具体的触发条件</div>';
        return;
    }
    
    // 根据类型显示对应的配置模板
    let templateId = '';
    switch (type) {
        case 'promotion_price_deviation':
            templateId = 'promotion-price-deviation-template';
            break;
        case 'channel_price_deviation':
            templateId = 'channel-price-deviation-template';
            break;
        case 'product_status_change':
            templateId = 'product-status-change-template';
            break;
        case 'inventory_anomaly':
            templateId = 'inventory-anomaly-template';
            break;
        case 'data_update_anomaly':
            templateId = 'data-update-anomaly-template';
            break;
        case 'price_drop':
        case 'price_rise':
            templateId = 'price-change-template';
            break;
        default:
            templateId = 'custom-template';
            break;
    }
    
    const template = document.getElementById(templateId);
    if (template) {
        configFields.innerHTML = template.innerHTML;
        // 填充现有值
        fillCurrentValues();
    }
}

// 填充当前值到配置字段
function fillCurrentValues() {
    const fields = [
        'threshold', 'percentage_threshold', 'promotion_deviation_threshold',
        'channel_deviation_threshold', 'inventory_threshold', 'data_update_hours_threshold',
        'status_change_type', 'comparison', 'official_price'
    ];
    
    fields.forEach(field => {
        const element = document.getElementById(field);
        if (element && currentRule[field] !== null && currentRule[field] !== undefined) {
            element.value = currentRule[field];
        }
    });
}

// 加载产品SKU
function loadProductSkus() {
    const productId = document.getElementById('product_id').value;
    const skuSelect = document.getElementById('product_sku_id');
    
    if (!productId) {
        skuSelect.innerHTML = '<option value="">请先选择产品</option>';
        return;
    }
    
    // 显示加载状态
    skuSelect.innerHTML = '<option value="">加载中...</option>';
    
    fetch(`/api/products/${productId}/skus`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let options = '<option value="">请选择SKU</option>';
                data.data.forEach(sku => {
                    const selected = currentRule.product_sku_id == sku.id ? 'selected' : '';
                    options += `<option value="${sku.id}" ${selected}>${sku.sku_code} - ${sku.title || 'N/A'}</option>`;
                });
                skuSelect.innerHTML = options;
            } else {
                skuSelect.innerHTML = '<option value="">加载失败</option>';
            }
        })
        .catch(error => {
            console.error('Error loading SKUs:', error);
            skuSelect.innerHTML = '<option value="">加载失败</option>';
        });
}

// 删除规则
function deleteRule() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 确认删除
function confirmDelete() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/alert-rules/${currentRule.id}`;
    form.innerHTML = `
        @csrf
        @method('DELETE')
    `;
    
    document.body.appendChild(form);
    form.submit();
}

// 表单提交处理
document.getElementById('alertRuleForm').addEventListener('submit', function() {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.spinner-border');
    
    submitBtn.disabled = true;
    btnText.textContent = '更新中...';
    spinner.classList.remove('d-none');
});
</script>
@endpush 
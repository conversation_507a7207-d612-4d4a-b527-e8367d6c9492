<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserActionLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'action',
        'resource_type',
        'resource_id',
        'description',
        'ip_address',
        'user_agent',
        'extra_data',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'extra_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 操作类型常量
     */
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_REGISTER = 'register';
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_VIEW = 'view';
    const ACTION_EXPORT = 'export';
    const ACTION_IMPORT = 'import';

    /**
     * 获取日志对应的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取操作的显示名称
     */
    public function getActionDisplayAttribute(): string
    {
        return match($this->action) {
            self::ACTION_LOGIN => '登录',
            self::ACTION_LOGOUT => '退出',
            self::ACTION_REGISTER => '注册',
            self::ACTION_CREATE => '创建',
            self::ACTION_UPDATE => '更新',
            self::ACTION_DELETE => '删除',
            self::ACTION_VIEW => '查看',
            self::ACTION_EXPORT => '导出',
            self::ACTION_IMPORT => '导入',
            default => '未知操作',
        };
    }

    /**
     * 记录用户操作
     */
    public static function log(
        int $userId,
        string $action,
        string $description,
        ?string $resourceType = null,
        ?int $resourceId = null,
        ?array $extraData = null
    ): void {
        static::create([
            'user_id' => $userId,
            'action' => $action,
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'description' => $description,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'extra_data' => $extraData,
        ]);
    }

    /**
     * 记录登录操作
     */
    public static function logLogin(int $userId, bool $success = true): void
    {
        static::log(
            $userId,
            self::ACTION_LOGIN,
            $success ? '用户登录成功' : '用户登录失败'
        );
    }

    /**
     * 记录注册操作
     */
    public static function logRegister(int $userId): void
    {
        static::log(
            $userId,
            self::ACTION_REGISTER,
            '用户注册成功'
        );
    }

    /**
     * 根据用户ID获取日志
     */
    public static function byUser(int $userId)
    {
        return static::where('user_id', $userId)->orderBy('created_at', 'desc');
    }

    /**
     * 根据操作类型获取日志
     */
    public static function byAction(string $action)
    {
        return static::where('action', $action)->orderBy('created_at', 'desc');
    }

    /**
     * 获取最近的日志
     */
    public static function recent(int $limit = 50)
    {
        return static::with('user')
                    ->orderBy('created_at', 'desc')
                    ->limit($limit);
    }
} 
<?php

namespace Database\Factories;

use App\Models\PriceHistory;
use Illuminate\Database\Eloquent\Factories\Factory;

class PriceHistoryFactory extends Factory
{
    protected $model = PriceHistory::class;

    public function definition(): array
    {
        return [
            'sku_id' => \App\Models\ProductSku::factory(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'sub_price' => $this->faker->optional()->randomFloat(2, 5, 900),
            'quantity' => $this->faker->numberBetween(0, 100),
            'sales' => $this->faker->numberBetween(0, 5000),
            'timestamp' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 
<?php

namespace App\Http\Controllers;

use App\Services\GroupReportingService;
use Illuminate\Http\JsonResponse;

class GroupReportingController extends Controller
{
    protected $reportingService;

    public function __construct(GroupReportingService $reportingService)
    {
        $this->reportingService = $reportingService;
    }

    /**
     * 显示指定任务组的聚合报告
     *
     * @param int $groupId
     * @return JsonResponse
     */
    public function show(int $groupId): JsonResponse
    {
        try {
            $report = $this->reportingService->generateReport($groupId);
            return response()->json($report);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Task group not found.'], 404);
        } catch (\Exception $e) {
            // 在实际应用中，这里应该记录错误日志
            return response()->json(['error' => 'An unexpected error occurred while generating the report.', 'details' => $e->getMessage()], 500);
        }
    }
} 
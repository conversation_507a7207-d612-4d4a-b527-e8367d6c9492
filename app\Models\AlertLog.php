<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AlertLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'rule_id',
        'group_alert_rule_id',
        'task_id',
        'sku_id',
        'alert_title',
        'message',
        'severity',
        'status',
        'trigger_value',
        'threshold_value',
        'context_data',
        'notification_method',
        'notification_sent',
        'notification_sent_at',
        'notification_error',
        'read_at',
        'resolved_at',
    ];

    protected $casts = [
        'trigger_value' => 'decimal:2',
        'threshold_value' => 'decimal:2',
        'context_data' => 'array',
        'notification_sent' => 'boolean',
        'notification_sent_at' => 'datetime',
        'read_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * 告警接收用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 触发的告警规则
     */
    public function rule(): BelongsTo
    {
        return $this->belongsTo(AlertRule::class, 'rule_id');
    }

    /**
     * 关联的监控任务
     */
    public function task(): BelongsTo
    {
        return $this->belongsTo(MonitorTask::class, 'task_id');
    }

    /**
     * 相关的SKU
     */
    public function sku(): BelongsTo
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    /**
     * 分组告警规则
     */
    public function groupRule(): BelongsTo
    {
        return $this->belongsTo(AlertRule::class, 'group_alert_rule_id');
    }

    /**
     * 标记为已读
     */
    public function markAsRead(): void
    {
        if (!$this->read_at) {
            $this->read_at = now();
            $this->status = 'read';
            $this->save();
        }
    }

    /**
     * 标记为已解决
     */
    public function markAsResolved(): void
    {
        $this->resolved_at = now();
        $this->status = 'resolved';
        $this->save();
    }

    /**
     * 标记为已忽略
     */
    public function markAsIgnored(): void
    {
        $this->status = 'ignored';
        $this->save();
    }

    /**
     * 记录通知发送成功
     */
    public function markNotificationSent(): void
    {
        $this->notification_sent = true;
        $this->notification_sent_at = now();
        $this->notification_error = null;
        $this->save();
    }

    /**
     * 记录通知发送失败
     */
    public function recordNotificationError(string $error): void
    {
        $this->notification_sent = false;
        $this->notification_error = $error;
        $this->save();
    }

    /**
     * 检查是否未读
     */
    public function isUnread(): bool
    {
        return $this->status === 'unread';
    }

    /**
     * 检查是否已解决
     */
    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    /**
     * 获取严重程度中文
     */
    public function getSeverityTextAttribute(): string
    {
        $severityMap = [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'critical' => '紧急'
        ];

        return $severityMap[$this->severity] ?? $this->severity;
    }

    /**
     * 获取状态中文
     */
    public function getStatusTextAttribute(): string
    {
        $statusMap = [
            'unread' => '未读',
            'read' => '已读',
            'resolved' => '已解决',
            'ignored' => '已忽略'
        ];

        return $statusMap[$this->status] ?? $this->status;
    }

    /**
     * 获取通知方式中文
     */
    public function getNotificationMethodTextAttribute(): string
    {
        $methodMap = [
            'email' => '邮件',
            'sms' => '短信',
            'push' => '推送',
            'system' => '系统内'
        ];

        return $methodMap[$this->notification_method] ?? $this->notification_method;
    }

    /**
     * 获取严重程度样式类
     */
    public function getSeverityClassAttribute(): string
    {
        $classMap = [
            'low' => 'text-blue-600',
            'medium' => 'text-yellow-600',
            'high' => 'text-orange-600',
            'critical' => 'text-red-600'
        ];

        return $classMap[$this->severity] ?? 'text-gray-600';
    }

    /**
     * 按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按状态筛选
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 未读的告警
     */
    public function scopeUnread($query)
    {
        return $query->where('status', 'unread');
    }

    /**
     * 按严重程度筛选
     */
    public function scopeWithSeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * 最近的告警
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 通知发送失败的告警
     */
    public function scopeNotificationFailed($query)
    {
        return $query->where('notification_sent', false)
                    ->whereNotNull('notification_error');
    }

    /**
     * 按严重程度排序（紧急优先）
     */
    public function scopeOrderBySeverity($query)
    {
        return $query->orderByRaw("FIELD(severity, 'critical', 'high', 'medium', 'low')");
    }

    /**
     * 获取用户未读告警数量
     */
    public static function getUnreadCountForUser($userId): int
    {
        return static::forUser($userId)->unread()->count();
    }

    /**
     * 获取用户最近告警统计
     */
    public static function getStatsForUser($userId, $days = 7): array
    {
        $query = static::forUser($userId)->recent($days);
        
        return [
            'total' => $query->count(),
            'unread' => $query->unread()->count(),
            'critical' => $query->withSeverity('critical')->count(),
            'high' => $query->withSeverity('high')->count(),
            'medium' => $query->withSeverity('medium')->count(),
            'low' => $query->withSeverity('low')->count(),
        ];
    }
}

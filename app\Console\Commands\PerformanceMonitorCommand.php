<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PerformanceMonitoringService;
use Illuminate\Support\Facades\Cache;

class PerformanceMonitorCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'performance:monitor
                            {action? : 监控操作 (metrics|alerts|optimize|watch)}
                            {--format=table : 输出格式 (table|json|summary)}
                            {--interval=30 : 监控间隔（秒）}
                            {--threshold=80 : 告警阈值}
                            {--platform= : 特定平台的指标}
                            {--export= : 导出指标到文件}';

    /**
     * The console command description.
     */
    protected $description = '性能监控和分析工具 - 监控队列、API、系统资源和并发性能';

    protected PerformanceMonitoringService $performanceService;

    public function __construct(PerformanceMonitoringService $performanceService)
    {
        parent::__construct();
        $this->performanceService = $performanceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action') ?? 'metrics';

        switch ($action) {
            case 'metrics':
                $this->showMetrics();
                break;
            case 'alerts':
                $this->showAlerts();
                break;
            case 'optimize':
                $this->showOptimizations();
                break;
            case 'watch':
                $this->watchMetrics();
                break;
            default:
                $this->error("未知操作：{$action}");
                $this->showHelp();
                return 1;
        }

        return 0;
    }

    /**
     * 显示性能指标
     */
    protected function showMetrics(): void
    {
        $this->info('📊 正在收集性能指标...');
        
        try {
            $metrics = $this->performanceService->collectMetrics();
            $format = $this->option('format');
            $platform = $this->option('platform');

            if ($format === 'json') {
                $this->line(json_encode($metrics, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                return;
            }

            $this->displayMetricsSummary($metrics, $platform);

            // 导出功能
            if ($exportFile = $this->option('export')) {
                $this->exportMetrics($metrics, $exportFile);
            }

        } catch (\Exception $e) {
            $this->error("获取性能指标失败: {$e->getMessage()}");
        }
    }

    /**
     * 显示性能告警
     */
    protected function showAlerts(): void
    {
        $this->info('⚠️  检查性能告警...');
        
        try {
            $alerts = $this->performanceService->getPerformanceAlerts();
            
            if (empty($alerts)) {
                $this->info('✅ 没有性能告警');
                return;
            }

            $this->warn(sprintf('发现 %d 个性能问题:', count($alerts)));
            $this->newLine();

            $alertData = [];
            foreach ($alerts as $alert) {
                $levelEmoji = $this->getAlertEmoji($alert['level']);
                $alertData[] = [
                    $levelEmoji . ' ' . $alert['level'],
                    $alert['type'],
                    $alert['message'],
                    $alert['value'] ?? 'N/A',
                    $alert['threshold'] ?? 'N/A',
                ];
            }

            $this->table(
                ['级别', '类型', '消息', '当前值', '阈值'],
                $alertData
            );

        } catch (\Exception $e) {
            $this->error("获取性能告警失败: {$e->getMessage()}");
        }
    }

    /**
     * 显示优化建议
     */
    protected function showOptimizations(): void
    {
        $this->info('🔧 生成优化建议...');
        
        try {
            $recommendations = $this->performanceService->optimizeConcurrency();
            
            if (empty($recommendations)) {
                $this->info('✅ 当前配置已优化，无需调整');
                return;
            }

            $this->warn('发现以下优化建议:');
            $this->newLine();

            foreach ($recommendations as $rec) {
                $this->line("📈 {$rec['action']}: ");
                $this->line("   当前值: {$rec['current']}");
                $this->line("   建议值: {$rec['recommended']}");
                $this->line("   原因: {$rec['reason']}");
                $this->newLine();
            }

        } catch (\Exception $e) {
            $this->error("生成优化建议失败: {$e->getMessage()}");
        }
    }

    /**
     * 实时监控指标
     */
    protected function watchMetrics(): void
    {
        $interval = (int) $this->option('interval');
        $this->info("🔄 开始实时监控 (间隔: {$interval}秒, 按Ctrl+C停止)...");
        
        while (true) {
            try {
                $this->line("\033[2J\033[H"); // 清屏
                $this->line('📊 实时性能监控 - ' . date('Y-m-d H:i:s'));
                $this->line(str_repeat('=', 60));
                
                $metrics = $this->performanceService->collectMetrics();
                $this->displayRealTimeMetrics($metrics);
                
                $alerts = $this->performanceService->getPerformanceAlerts();
                if (!empty($alerts)) {
                    $this->newLine();
                    $this->warn('⚠️  活跃告警: ' . count($alerts));
                    foreach ($alerts as $alert) {
                        $emoji = $this->getAlertEmoji($alert['level']);
                        $this->line("   {$emoji} {$alert['message']}");
                    }
                }
                
                sleep($interval);
                
            } catch (\Exception $e) {
                $this->error("监控过程中出错: {$e->getMessage()}");
                sleep($interval);
            }
        }
    }

    /**
     * 显示指标摘要
     */
    protected function displayMetricsSummary(array $metrics, ?string $platform = null): void
    {
        $this->line('📊 系统性能指标摘要');
        $this->line(str_repeat('=', 50));
        
        // 队列指标
        $this->displayQueueMetrics($metrics['queue_metrics']);
        
        // API指标
        $this->displayApiMetrics($metrics['api_metrics'], $platform);
        
        // 系统指标
        $this->displaySystemMetrics($metrics['system_metrics']);
        
        // 并发指标
        $this->displayConcurrencyMetrics($metrics['concurrency_metrics']);
        
        // 速率限制指标
        $this->displayRateLimitMetrics($metrics['rate_limit_metrics']);
    }

    /**
     * 显示队列指标
     */
    protected function displayQueueMetrics(array $queueMetrics): void
    {
        $this->newLine();
        $this->info('📦 队列指标');
        
        $data = [
            ['连接类型', $queueMetrics['connection']],
            ['待处理任务', $queueMetrics['total_pending']],
            ['失败任务', $queueMetrics['total_failed']],
            ['今日处理', $queueMetrics['total_processed_today']],
        ];
        
        $this->table(['指标', '数值'], $data);
        
        // 显示各队列详情
        if (!empty($queueMetrics['queues'])) {
            $this->line('队列详情:');
            $queueData = [];
            foreach ($queueMetrics['queues'] as $priority => $queue) {
                $queueData[] = [
                    $priority,
                    $queue['name'],
                    $queue['pending_jobs'],
                    $queue['delayed_jobs'],
                    $queue['reserved_jobs'],
                ];
            }
            $this->table(
                ['优先级', '队列名', '待处理', '延迟', '保留'],
                $queueData
            );
        }
    }

    /**
     * 显示API指标
     */
    protected function displayApiMetrics(array $apiMetrics, ?string $platform = null): void
    {
        $this->newLine();
        $this->info('🌐 API指标');
        
        $data = [
            ['总请求数', $apiMetrics['total_requests']],
            ['成功请求', $apiMetrics['successful_requests']],
            ['失败请求', $apiMetrics['failed_requests']],
            ['成功率', sprintf('%.2f%%', $apiMetrics['success_rate'] ?? 0)],
            ['平均响应时间', sprintf('%.2fms', $apiMetrics['avg_response_time'] ?? 0)],
        ];
        
        $this->table(['指标', '数值'], $data);
        
        // 显示平台特定指标
        if (!empty($apiMetrics['platforms'])) {
            if ($platform && isset($apiMetrics['platforms'][$platform])) {
                $this->displayPlatformMetrics($platform, $apiMetrics['platforms'][$platform]);
            } else {
                $this->line('平台指标:');
                $platformData = [];
                foreach ($apiMetrics['platforms'] as $platformName => $platformMetrics) {
                    $platformData[] = [
                        $platformName,
                        $platformMetrics['requests'],
                        sprintf('%.2fms', $platformMetrics['avg_response_time']),
                        sprintf('%.2f%%', $platformMetrics['success_rate'] ?? 100),
                    ];
                }
                $this->table(
                    ['平台', '请求数', '平均响应时间', '成功率'],
                    $platformData
                );
            }
        }
    }

    /**
     * 显示系统指标
     */
    protected function displaySystemMetrics(array $systemMetrics): void
    {
        $this->newLine();
        $this->info('💻 系统指标');
        
        $memoryUsage = $systemMetrics['memory_usage'];
        $diskUsage = $systemMetrics['disk_usage'];
        $loadAverage = $systemMetrics['load_average'];
        
        $data = [
            ['内存使用率', sprintf('%.2f%%', $memoryUsage['usage_percentage'])],
            ['当前内存', $this->formatBytes($memoryUsage['current'])],
            ['峰值内存', $this->formatBytes($memoryUsage['peak'])],
            ['磁盘使用率', sprintf('%.2f%%', $diskUsage['usage_percentage'])],
            ['可用磁盘', $this->formatBytes($diskUsage['free'])],
            ['1分钟负载', sprintf('%.2f', $loadAverage['1min'])],
            ['5分钟负载', sprintf('%.2f', $loadAverage['5min'])],
            ['15分钟负载', sprintf('%.2f', $loadAverage['15min'])],
        ];
        
        $this->table(['指标', '数值'], $data);
    }

    /**
     * 显示并发指标
     */
    protected function displayConcurrencyMetrics(array $concurrencyMetrics): void
    {
        $this->newLine();
        $this->info('⚡ 并发控制指标');
        
        $data = [
            ['配置并发数', $concurrencyMetrics['configured_concurrency']],
            ['当前活跃任务', $concurrencyMetrics['current_active_jobs']],
            ['最优并发数', $concurrencyMetrics['optimal_concurrency']],
            ['并发利用率', sprintf('%.2f%%', $concurrencyMetrics['concurrency_utilization'])],
        ];
        
        $this->table(['指标', '数值'], $data);
        
        if (!empty($concurrencyMetrics['recommendations'])) {
            $this->line('优化建议:');
            foreach ($concurrencyMetrics['recommendations'] as $recommendation) {
                $this->line("  • {$recommendation}");
            }
        }
    }

    /**
     * 显示速率限制指标
     */
    protected function displayRateLimitMetrics(array $rateLimitMetrics): void
    {
        $this->newLine();
        $this->info('🚦 速率限制指标');
        
        $enabled = $rateLimitMetrics['enabled'] ? '✅ 启用' : '❌ 禁用';
        $currentUsage = $rateLimitMetrics['current_usage'];
        
        $data = [
            ['状态', $enabled],
            ['每分钟限制', $rateLimitMetrics['limits']['per_minute']],
            ['每小时限制', $rateLimitMetrics['limits']['per_hour']],
            ['当前分钟使用', $currentUsage['per_minute']],
            ['当前小时使用', $currentUsage['per_hour']],
            ['违规次数', count($rateLimitMetrics['violations'])],
        ];
        
        $this->table(['指标', '数值'], $data);
    }

    /**
     * 显示实时指标
     */
    protected function displayRealTimeMetrics(array $metrics): void
    {
        // 简化的实时显示
        $queue = $metrics['queue_metrics'];
        $api = $metrics['api_metrics'];
        $system = $metrics['system_metrics'];
        $concurrency = $metrics['concurrency_metrics'];
        
        $this->line("📦 队列: {$queue['total_pending']} 待处理, {$queue['total_failed']} 失败");
        $this->line("🌐 API: {$api['total_requests']} 请求, " . sprintf('%.2f%%', $api['success_rate'] ?? 0) . " 成功率");
        $this->line("💻 系统: " . sprintf('%.1f%%', $system['memory_usage']['usage_percentage']) . " 内存, " . sprintf('%.2f', $system['load_average']['1min']) . " 负载");
        $this->line("⚡ 并发: {$concurrency['current_active_jobs']}/{$concurrency['configured_concurrency']} (" . sprintf('%.1f%%', $concurrency['concurrency_utilization']) . " 利用率)");
    }

    /**
     * 显示平台详细指标
     */
    protected function displayPlatformMetrics(string $platform, array $metrics): void
    {
        $this->newLine();
        $this->info("📋 {$platform} 平台详细指标");
        
        if (!empty($metrics['endpoints'])) {
            $endpointData = [];
            foreach ($metrics['endpoints'] as $endpoint => $endpointMetrics) {
                $endpointData[] = [
                    $endpoint,
                    $endpointMetrics['requests'],
                    sprintf('%.2fms', $endpointMetrics['avg_response_time']),
                    sprintf('%.2f%%', $endpointMetrics['success_rate']),
                ];
            }
            $this->table(
                ['接口', '请求数', '平均响应时间', '成功率'],
                $endpointData
            );
        }
    }

    /**
     * 导出指标
     */
    protected function exportMetrics(array $metrics, string $filename): void
    {
        try {
            $content = json_encode($metrics, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            file_put_contents($filename, $content);
            $this->info("✅ 指标已导出到: {$filename}");
        } catch (\Exception $e) {
            $this->error("导出失败: {$e->getMessage()}");
        }
    }

    /**
     * 获取告警图标
     */
    protected function getAlertEmoji(string $level): string
    {
        return match($level) {
            'error' => '🔴',
            'warning' => '🟡',
            'info' => '🔵',
            default => '⚪',
        };
    }

    /**
     * 格式化字节数
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 显示帮助信息
     */
    protected function showHelp(): void
    {
        $this->newLine();
        $this->info('📖 性能监控命令帮助');
        $this->line('');
        $this->line('可用操作:');
        $this->line('  metrics   - 显示性能指标');
        $this->line('  alerts    - 显示性能告警');
        $this->line('  optimize  - 显示优化建议');
        $this->line('  watch     - 实时监控');
        $this->line('');
        $this->line('示例:');
        $this->line('  php artisan performance:monitor metrics');
        $this->line('  php artisan performance:monitor alerts');
        $this->line('  php artisan performance:monitor watch --interval=10');
        $this->line('  php artisan performance:monitor metrics --format=json --export=metrics.json');
    }
}
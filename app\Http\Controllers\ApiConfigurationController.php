<?php

namespace App\Http\Controllers;

use App\Models\ApiConfiguration;
use App\Services\ApiRateLimitService;
use App\Services\ApiHealthMonitorService;
use App\Services\ApiVersionManagerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ApiConfigurationController extends Controller
{
    protected ApiRateLimitService $rateLimitService;
    protected ApiHealthMonitorService $healthMonitorService;
    protected ApiVersionManagerService $versionManagerService;

    public function __construct(
        ApiRateLimitService $rateLimitService,
        ApiHealthMonitorService $healthMonitorService,
        ApiVersionManagerService $versionManagerService
    ) {
        $this->rateLimitService = $rateLimitService;
        $this->healthMonitorService = $healthMonitorService;
        $this->versionManagerService = $versionManagerService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ApiConfiguration::query();

        // Apply filters
        if ($request->has('platform_type') && $request->platform_type !== '') {
            $query->byPlatform($request->platform_type);
        }

        if ($request->has('is_active') && $request->is_active !== '') {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('is_deprecated') && $request->is_deprecated !== '') {
            $query->where('is_deprecated', $request->boolean('is_deprecated'));
        }

        if ($request->has('health_status') && $request->health_status !== '') {
            $query->where('health_status', $request->health_status);
        }

        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('platform_type', 'like', "%{$search}%");
            });
        }

        $configurations = $query->orderBy('name')->paginate(15);

        if ($request->expectsJson()) {
            return response()->json([
                'data' => $configurations->items(),
                'pagination' => [
                    'current_page' => $configurations->currentPage(),
                    'last_page' => $configurations->lastPage(),
                    'per_page' => $configurations->perPage(),
                    'total' => $configurations->total()
                ]
            ]);
        }

        return view('api-configurations.index', compact('configurations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $platforms = [
            'shopify' => 'Shopify',
            'magento' => 'Magento',
            'woocommerce' => 'WooCommerce',
            'opencart' => 'OpenCart',
            'prestashop' => 'PrestaShop',
            'custom' => 'Custom Platform'
        ];

        $authTypes = [
            'api_key' => 'API Key',
            'bearer_token' => 'Bearer Token',
            'oauth' => 'OAuth',
            'basic_auth' => 'Basic Authentication',
            'custom' => 'Custom Authentication'
        ];

        return view('api-configurations.create', compact('platforms', 'authTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = $this->validateApiConfiguration($request);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $validatedData = $validator->validated();
            
            // Process auth credentials and merge
            $validatedData['auth_credentials'] = $this->processAuthCredentials($request);
            
            // Process JSON fields
            $validatedData['headers'] = $request->input('headers', []);
            $validatedData['timeout_settings'] = $request->input('timeout_settings', []);
            $validatedData['retry_settings'] = $request->input('retry_settings', []);
            $validatedData['custom_config'] = $request->input('custom_config', []);

            $configuration = ApiConfiguration::create($validatedData);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'API configuration created successfully',
                    'data' => $configuration
                ], 201);
            }

            return redirect()->route('api-configurations.index')
                           ->with('success', 'API configuration created successfully');

        } catch (\Exception $e) {
            Log::error('Failed to create API configuration', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Failed to create API configuration',
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->with('error', 'Failed to create API configuration: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $configuration = ApiConfiguration::findOrFail($id);

        if (request()->expectsJson()) {
            return response()->json([
                'data' => $configuration,
                'health_stats' => $configuration->getHealthCheckStats(),
                'usage_stats' => $configuration->getUsageStats()
            ]);
        }

        return view('api-configurations.show', compact('configuration'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $configuration = ApiConfiguration::findOrFail($id);
        
        $platforms = [
            'shopify' => 'Shopify',
            'magento' => 'Magento',
            'woocommerce' => 'WooCommerce',
            'opencart' => 'OpenCart',
            'prestashop' => 'PrestaShop',
            'custom' => 'Custom Platform'
        ];

        $authTypes = [
            'api_key' => 'API Key',
            'bearer_token' => 'Bearer Token',
            'oauth' => 'OAuth',
            'basic_auth' => 'Basic Authentication',
            'custom' => 'Custom Authentication'
        ];

        return view('api-configurations.edit', compact('configuration', 'platforms', 'authTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $configuration = ApiConfiguration::findOrFail($id);
        
        $validator = $this->validateApiConfiguration($request, $configuration->id);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $data = $request->validated();
            
            // Only update auth credentials if provided
            if ($request->has('auth_credentials') && !empty($request->input('auth_credentials'))) {
                $data['auth_credentials'] = $this->processAuthCredentials($request);
            } else {
                unset($data['auth_credentials']);
            }
            
            // Process JSON fields
            $data['headers'] = $request->has('headers') ? $request->input('headers', []) : $configuration->headers;
            $data['timeout_settings'] = $request->has('timeout_settings') ? $request->input('timeout_settings', []) : $configuration->timeout_settings;
            $data['retry_settings'] = $request->has('retry_settings') ? $request->input('retry_settings', []) : $configuration->retry_settings;
            $data['custom_config'] = $request->has('custom_config') ? $request->input('custom_config', []) : $configuration->custom_config;

            $configuration->update($data);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'API configuration updated successfully',
                    'data' => $configuration->fresh()
                ]);
            }

            return redirect()->route('api-configurations.index')
                           ->with('success', 'API configuration updated successfully');

        } catch (\Exception $e) {
            Log::error('Failed to update API configuration', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Failed to update API configuration',
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->with('error', 'Failed to update API configuration: ' . $e->getMessage())
                           ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            $name = $configuration->name;
            $configuration->delete();

            if (request()->expectsJson()) {
                return response()->json([
                    'message' => 'API configuration deleted successfully'
                ]);
            }

            return redirect()->route('api-configurations.index')
                           ->with('success', "API configuration '{$name}' deleted successfully");

        } catch (\Exception $e) {
            Log::error('Failed to delete API configuration', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            if (request()->expectsJson()) {
                return response()->json([
                    'message' => 'Failed to delete API configuration',
                    'error' => $e->getMessage()
                ], 500);
            }

            return redirect()->route('api-configurations.index')
                           ->with('error', 'Failed to delete API configuration: ' . $e->getMessage());
        }
    }

    /**
     * Toggle the active status of an API configuration
     */
    public function toggleActive($id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            $configuration->update(['is_active' => !$configuration->is_active]);

            return response()->json([
                'message' => 'API configuration status updated successfully',
                'is_active' => $configuration->is_active
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to toggle API configuration status', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Failed to update status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Perform health check on an API configuration
     */
    public function healthCheck($id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            
            // Check rate limits
            if (!$this->rateLimitService->canMakeRequest($configuration->id)) {
                $limits = $this->rateLimitService->getRemainingLimits($configuration->id);
                return response()->json([
                    'status' => 'rate_limited',
                    'message' => 'Rate limit exceeded',
                    'limits' => $limits,
                    'data' => $configuration->only(['id', 'name', 'health_status'])
                ], 429);
            }

            // Perform health check
            $result = $this->healthMonitorService->checkHealth($configuration);
            
            // Record the request
            $this->rateLimitService->recordRequest($configuration->id);

            return response()->json([
                'status' => $result['status'],
                'response_time' => $result['response_time'],
                'http_status' => $result['http_status'],
                'message' => $result['message'],
                'data' => $configuration->fresh(),
                'health_stats' => $configuration->fresh()->getHealthCheckStats()
            ]);

        } catch (\Exception $e) {
            Log::error('Health check failed', [
                'api_configuration_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Health check failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk health check for all active configurations
     */
    public function bulkHealthCheck(): JsonResponse
    {
        try {
            $configurations = ApiConfiguration::active()
                                            ->whereNotNull('health_check_endpoint')
                                            ->get();

            $results = $this->healthMonitorService->bulkHealthCheck($configurations);

            return response()->json([
                'message' => 'Bulk health check completed',
                'results' => $results,
                'total_checked' => count($results),
                'healthy_count' => count(array_filter($results, fn($r) => $r['status'] === 'healthy')),
                'unhealthy_count' => count(array_filter($results, fn($r) => $r['status'] === 'unhealthy')),
                'rate_limited_count' => count(array_filter($results, fn($r) => $r['status'] === 'rate_limited'))
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk health check failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'message' => 'Bulk health check failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get configuration for API client usage
     */
    public function getClientConfig($slug): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::where('slug', $slug)
                                           ->active()
                                           ->notDeprecated()
                                           ->firstOrFail();

            $configuration->markAsUsed();

            return response()->json([
                'data' => $configuration->toClientConfig()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Configuration not found or not available',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Get rate limit status for a configuration
     */
    public function getRateLimitStatus($id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            $limits = $this->rateLimitService->getRemainingLimits($configuration->id);
            $usage = $this->rateLimitService->getUsageStats($configuration->id);

            return response()->json([
                'configuration' => $configuration->only(['id', 'name', 'platform_type']),
                'limits' => $limits,
                'usage' => $usage,
                'can_make_request' => $this->rateLimitService->canMakeRequest($configuration->id)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get rate limit status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset rate limits for a configuration
     */
    public function resetRateLimits($id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            $this->rateLimitService->resetLimits($configuration->id);

            return response()->json([
                'message' => 'Rate limits reset successfully',
                'configuration' => $configuration->only(['id', 'name']),
                'limits' => $this->rateLimitService->getRemainingLimits($configuration->id)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to reset rate limits',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate API configuration request
     */
    private function validateApiConfiguration(Request $request, $excludeId = null)
    {
        $rules = [
            'name' => ['required', 'string', 'max:255', Rule::unique('api_configurations')->ignore($excludeId)],
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('api_configurations')->ignore($excludeId)],
            'description' => 'nullable|string',
            'platform_type' => 'required|string|max:100',
            'base_url' => 'required|url|max:500',
            'auth_type' => 'required|in:api_key,bearer_token,oauth,basic_auth,custom',
            'version' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'is_deprecated' => 'boolean',
            'rate_limit_per_minute' => 'integer|min:1|max:10000',
            'rate_limit_per_hour' => 'integer|min:1|max:1000000',
            'rate_limit_per_day' => 'integer|min:1|max:10000000',
            'health_check_endpoint' => 'nullable|string|max:255',
            'health_check_interval' => 'integer|min:60|max:86400'
        ];

        return Validator::make($request->all(), $rules);
    }

    /**
     * Process authentication credentials based on auth type
     */
    private function processAuthCredentials(Request $request): array
    {
        $authType = $request->input('auth_type');
        $credentials = [];

        switch ($authType) {
            case 'api_key':
                $credentials = [
                    'api_key' => $request->input('api_key'),
                    'key_name' => $request->input('key_name', 'X-API-Key')
                ];
                break;
            case 'bearer_token':
                $credentials = [
                    'token' => $request->input('bearer_token')
                ];
                break;
            case 'oauth':
                $credentials = [
                    'client_id' => $request->input('oauth_client_id'),
                    'client_secret' => $request->input('oauth_client_secret'),
                    'access_token' => $request->input('oauth_access_token'),
                    'refresh_token' => $request->input('oauth_refresh_token'),
                    'token_url' => $request->input('oauth_token_url')
                ];
                break;
            case 'basic_auth':
                $credentials = [
                    'username' => $request->input('basic_username'),
                    'password' => $request->input('basic_password')
                ];
                break;
            case 'custom':
                $credentials = $request->input('custom_auth_data', []);
                break;
        }

        return $credentials;
    }

    /**
     * Add authentication headers to request
     */
    private function addAuthHeaders(array &$headers, ApiConfiguration $config): void
    {
        $authType = $config->auth_type;
        $credentials = $config->auth_credentials;

        if (!$credentials) {
            return;
        }

        switch ($authType) {
            case 'api_key':
                $keyName = $credentials['key_name'] ?? 'X-API-Key';
                $headers[$keyName] = $credentials['api_key'] ?? '';
                break;
            case 'bearer_token':
                $headers['Authorization'] = 'Bearer ' . ($credentials['token'] ?? '');
                break;
            case 'basic_auth':
                $headers['Authorization'] = 'Basic ' . base64_encode(
                    ($credentials['username'] ?? '') . ':' . ($credentials['password'] ?? '')
                );
                break;
            // OAuth and custom auth would need more complex handling
        }
    }

    /**
     * Get version information for a configuration
     */
    public function getVersionInfo($id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);
            $versionInfo = $configuration->getVersionInfo();
            $compatibilityInfo = $this->versionManagerService->getCompatibilityInfo($configuration);

            return response()->json([
                'configuration' => $configuration->only(['id', 'name', 'platform_type', 'version']),
                'version_info' => $versionInfo,
                'compatibility' => $compatibilityInfo,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get version information',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new version of an existing configuration
     */
    public function createVersion(Request $request, $id): JsonResponse
    {
        try {
            $baseConfiguration = ApiConfiguration::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'version' => 'required|string|max:20',
                'changes' => 'array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $newVersion = $this->versionManagerService->createNewVersion(
                $baseConfiguration,
                $request->input('version'),
                $request->input('changes', [])
            );

            return response()->json([
                'message' => 'New version created successfully',
                'configuration' => $newVersion,
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create new version',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upgrade to a specific version
     */
    public function upgradeVersion(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'platform_type' => 'required|string',
                'name' => 'required|string',
                'target_version' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $targetConfig = $this->versionManagerService->upgradeToVersion(
                $request->input('platform_type'),
                $request->input('name'),
                $request->input('target_version')
            );

            if (!$targetConfig) {
                return response()->json([
                    'message' => 'Target version not found'
                ], 404);
            }

            return response()->json([
                'message' => 'Version upgraded successfully',
                'configuration' => $targetConfig,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to upgrade version',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deprecate a version
     */
    public function deprecateVersion(Request $request, $id): JsonResponse
    {
        try {
            $configuration = ApiConfiguration::findOrFail($id);

            $reason = $request->input('reason', '');
            $this->versionManagerService->deprecateVersion($configuration, $reason);

            return response()->json([
                'message' => 'Version deprecated successfully',
                'configuration' => $configuration->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to deprecate version',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get migration path between versions
     */
    public function getMigrationPath(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'platform_type' => 'required|string',
                'name' => 'required|string',
                'from_version' => 'required|string',
                'to_version' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $migrationPath = $this->versionManagerService->getMigrationPath(
                $request->input('platform_type'),
                $request->input('name'),
                $request->input('from_version'),
                $request->input('to_version')
            );

            return response()->json($migrationPath);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get migration path',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get version statistics
     */
    public function getVersionStatistics(Request $request): JsonResponse
    {
        try {
            $platformType = $request->input('platform_type');
            $statistics = $this->versionManagerService->getVersionStatistics($platformType);

            return response()->json($statistics);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get version statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

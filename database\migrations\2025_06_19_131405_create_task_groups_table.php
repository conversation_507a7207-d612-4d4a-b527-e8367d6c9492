<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade')->comment('创建组的用户');
            $table->string('name', 100)->comment('分组名称');
            $table->text('description')->nullable()->comment('分组描述');
            $table->string('color', 7)->default('#007bff')->comment('分组颜色标识');
            $table->json('settings')->nullable()->comment('分组设置配置');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'is_active']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_groups');
    }
};

<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductSku;
use App\Models\PriceHistory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

/**
 * 数据标准化服务
 * 负责数据映射、清洗、格式转换和去重处理
 */
class DataStandardizationService
{
    /**
     * 价格变化阈值百分比
     */
    const PRICE_CHANGE_THRESHOLD = 0.05; // 5%

    /**
     * 价格历史记录间隔（小时）
     */
    const PRICE_HISTORY_INTERVAL = 24; // 24小时

    /**
     * 核心数据字段映射配置
     */
    private array $fieldMapping = [
        // 产品基本信息
        'product_id' => ['id', 'item_id', 'product_id'],
        'title' => ['title', 'name', 'product_name'],
        'price' => ['price', 'current_price', 'sale_price'],
        'original_price' => ['original_price', 'market_price', 'list_price'],
        'category_id' => ['category_id', 'cat_id', 'category'],
        'category_path' => ['category_path', 'cat_path', 'breadcrumb'],
        'shop_id' => ['shop_id', 'shopId', 'seller_id'],
        'shop_name' => ['shop_name', 'shopName', 'seller_name'],
        'state' => ['state', 'status', 'product_status'],
        'is_sku' => ['is_sku', 'has_sku', 'sku_enabled'],
        'item_type' => ['item_type', 'itemType', 'product_type'],
        'sale_count' => ['sale', 'sale_count', 'sales_count', 'sold_count'],
        'comment_count' => ['comment_count', 'commentCount', 'review_count'],
        'pic_urls' => ['pic_urls', 'images', 'picture_urls', 'image_urls'],
        'promotion' => ['promotion', 'promotions', 'promotion_info'],
        'delivery' => ['delivery', 'shipping', 'logistics'],
        'props' => ['props', 'properties', 'attributes'],
        'timestamp' => ['timestamp', 'updated_at', 'crawl_time'],
        
        // SKU相关字段
        'sku_id' => ['sku_id', 'id', 'code'],
        'sku_code' => ['sku_code', 'code', 'sku'],
        'sub_price' => ['sub_price', 'subPrice', 'discount_price'],
        'sub_price_title' => ['sub_price_title', 'subPriceTitle', 'discount_title'],
        'quantity' => ['quantity', 'stock', 'inventory'],
        'sku_props' => ['props', 'properties', 'attributes', 'specs'],
    ];

    /**
     * 标准化产品数据
     *
     * @param array $rawData 原始API数据
     * @param string $platform 平台名称
     * @return array 标准化后的数据
     */
    public function standardizeProductData(array $rawData, string $platform = 'unknown'): array
    {
        try {
            Log::info("开始标准化产品数据", [
                'platform' => $platform,
                'raw_data_keys' => array_keys($rawData)
            ]);

            // 1. 映射核心字段
            $mappedData = $this->mapCoreFields($rawData);
            
            // 2. 清洗数据
            $cleanedData = $this->cleanData($mappedData);
            
            // 3. 格式转换
            $formattedData = $this->formatData($cleanedData);
            
            // 4. 添加平台信息和时间戳
            $formattedData['source_platform'] = $platform;
            $formattedData['standardized_at'] = Carbon::now()->toDateTimeString();
            
            Log::info("产品数据标准化完成", [
                'platform' => $platform,
                'product_id' => $formattedData['product_id'] ?? 'unknown'
            ]);
            
            return $formattedData;
            
        } catch (Exception $e) {
            Log::error("产品数据标准化失败", [
                'platform' => $platform,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 标准化SKU数据
     *
     * @param array $rawSkus 原始SKU数据数组
     * @param string $platform 平台名称
     * @return array 标准化后的SKU数据
     */
    public function standardizeSkuData(array $rawSkus, string $platform = 'unknown'): array
    {
        $standardizedSkus = [];
        
        foreach ($rawSkus as $rawSku) {
            try {
                // 1. 映射SKU字段
                $mappedSku = $this->mapSkuFields($rawSku);
                
                // 2. 清洗SKU数据
                $cleanedSku = $this->cleanSkuData($mappedSku);
                
                // 3. 格式转换
                $formattedSku = $this->formatSkuData($cleanedSku);
                
                // 4. 添加平台信息
                $formattedSku['source_platform'] = $platform;
                $formattedSku['standardized_at'] = Carbon::now()->toDateTimeString();
                
                $standardizedSkus[] = $formattedSku;
                
            } catch (Exception $e) {
                Log::warning("SKU数据标准化失败，跳过该SKU", [
                    'platform' => $platform,
                    'sku_data' => $rawSku,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
        
        return $standardizedSkus;
    }

    /**
     * 去重并保存产品数据
     *
     * @param array $standardizedData 标准化后的数据
     * @return Product 产品模型实例
     */
    public function deduplicateAndSaveProduct(array $standardizedData): Product
    {
        $productId = $standardizedData['product_id'] ?? null;
        $platform = $standardizedData['source_platform'] ?? 'unknown';
        
        if (!$productId) {
            throw new Exception("产品ID不能为空");
        }

        // 查找现有产品
        $product = Product::where('source_id', $productId)
                         ->where('source_platform', $platform)
                         ->first();

        $productData = [
            'title' => $standardizedData['title'] ?? '',
            'category_id' => $standardizedData['category_id'],
            'category_path' => $standardizedData['category_path'] ?? '',
            'shop_id' => $standardizedData['shop_id'] ?? '',
            'shop_name' => $standardizedData['shop_name'] ?? '',
            'item_type' => $standardizedData['item_type'] ?? '',
            'state' => $standardizedData['state'] ?? 'active',
            'source_platform' => $platform,
            'source_id' => $productId,
            'description' => $standardizedData['description'] ?? '',
            'image_url' => $this->getMainImageUrl($standardizedData['pic_urls'] ?? []),
            'images' => $standardizedData['pic_urls'] ?? [],
            'total_sales' => $standardizedData['sale_count'] ?? 0,
            'review_count' => $standardizedData['comment_count'] ?? 0,
        ];

        if ($product) {
            // 更新现有产品
            $product->update($productData);
            Log::info("更新现有产品", [
                'product_id' => $productId,
                'platform' => $platform,
                'db_id' => $product->id
            ]);
        } else {
            // 创建新产品
            $product = Product::create($productData);
            Log::info("创建新产品", [
                'product_id' => $productId,
                'platform' => $platform,
                'db_id' => $product->id
            ]);
        }

        return $product;
    }

    /**
     * 去重并保存SKU数据
     *
     * @param Product $product 产品实例
     * @param array $standardizedSkus 标准化后的SKU数据
     * @return array 保存的SKU实例数组
     */
    public function deduplicateAndSaveSkus(Product $product, array $standardizedSkus): array
    {
        $savedSkus = [];
        
        foreach ($standardizedSkus as $skuData) {
            $skuId = $skuData['sku_id'] ?? null;
            
            if (!$skuId) {
                Log::warning("SKU ID为空，跳过该SKU", ['sku_data' => $skuData]);
                continue;
            }

            // 查找现有SKU
            $sku = ProductSku::where('product_id', $product->id)
                            ->where('sku_code', $skuId)
                            ->first();

            $skuSaveData = [
                'product_id' => $product->id,
                'sku_code' => $skuId,
                'price' => $skuData['price'] ?? 0,
                'sub_price' => $skuData['sub_price'],
                'sub_price_title' => $skuData['sub_price_title'] ?? '',
                'quantity' => $skuData['quantity'] ?? 0,
                'is_sku' => $skuData['is_sku'] ?? false,
                'props' => $skuData['sku_props'] ?? [],
                'delivery' => $skuData['delivery'] ?? [],
                'sku_image' => $skuData['sku_image'] ?? '',
                'original_price' => $skuData['original_price'],
                'promotion_type' => $skuData['promotion_type'] ?? '',
                'promotion_info' => $skuData['promotion_info'] ?? '',
                'sales_count' => $skuData['sales_count'] ?? 0,
                'status' => $skuData['status'] ?? 'active',
            ];

            if ($sku) {
                // 更新现有SKU
                $sku->update($skuSaveData);
                Log::info("更新现有SKU", [
                    'sku_id' => $skuId,
                    'product_id' => $product->id,
                    'db_id' => $sku->id
                ]);
            } else {
                // 创建新SKU
                $sku = ProductSku::create($skuSaveData);
                Log::info("创建新SKU", [
                    'sku_id' => $skuId,
                    'product_id' => $product->id,
                    'db_id' => $sku->id
                ]);
            }

            $savedSkus[] = $sku;
        }

        return $savedSkus;
    }

    /**
     * 智能保存价格历史记录
     *
     * @param array $skus SKU实例数组
     * @return int 保存的价格历史记录数
     */
    public function savePriceHistoryIntelligently(array $skus): int
    {
        $savedCount = 0;
        
        foreach ($skus as $sku) {
            try {
                // 获取最新的价格历史记录
                $latestHistory = PriceHistory::where('sku_id', $sku->id)
                                           ->orderBy('timestamp', 'desc')
                                           ->first();

                $shouldSave = false;
                $reason = '';

                if (!$latestHistory) {
                    // 没有历史记录，直接保存
                    $shouldSave = true;
                    $reason = 'first_record';
                } else {
                    // 检查价格变化
                    $priceDifference = abs($sku->price - $latestHistory->price);
                    $priceChangePercent = $latestHistory->price > 0 ? 
                        ($priceDifference / $latestHistory->price) : 0;
                    
                    // 检查数量变化
                    $quantityChanged = $sku->quantity != $latestHistory->quantity;
                    
                    // 检查时间间隔
                    $hoursSinceLastRecord = Carbon::parse($latestHistory->timestamp)
                                                 ->diffInHours(Carbon::now());
                    
                    if ($priceChangePercent >= self::PRICE_CHANGE_THRESHOLD) {
                        $shouldSave = true;
                        $reason = 'price_change';
                    } elseif ($quantityChanged) {
                        $shouldSave = true;
                        $reason = 'quantity_change';
                    } elseif ($hoursSinceLastRecord >= self::PRICE_HISTORY_INTERVAL) {
                        $shouldSave = true;
                        $reason = 'time_interval';
                    }
                }

                if ($shouldSave) {
                    PriceHistory::create([
                        'sku_id' => $sku->id,
                        'price' => $sku->price,
                        'quantity' => $sku->quantity,
                        'timestamp' => Carbon::now(),
                        'change_reason' => $reason,
                    ]);
                    
                    $savedCount++;
                    
                    Log::info("保存价格历史记录", [
                        'sku_id' => $sku->id,
                        'price' => $sku->price,
                        'quantity' => $sku->quantity,
                        'reason' => $reason
                    ]);
                }
                
            } catch (Exception $e) {
                Log::error("保存价格历史记录失败", [
                    'sku_id' => $sku->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $savedCount;
    }

    /**
     * 映射核心字段
     *
     * @param array $rawData 原始数据
     * @return array 映射后的数据
     */
    private function mapCoreFields(array $rawData): array
    {
        $mappedData = [];
        
        foreach ($this->fieldMapping as $coreField => $possibleFields) {
            $mappedData[$coreField] = $this->findFieldValue($rawData, $possibleFields);
        }
        
        return $mappedData;
    }

    /**
     * 映射SKU字段
     *
     * @param array $rawSku 原始SKU数据
     * @return array 映射后的SKU数据
     */
    private function mapSkuFields(array $rawSku): array
    {
        $skuMapping = [
            'sku_id' => ['id', 'sku_id', 'code'],
            'sku_code' => ['code', 'sku_code', 'sku'],
            'price' => ['price', 'current_price', 'sale_price'],
            'original_price' => ['original_price', 'market_price', 'list_price'],
            'sub_price' => ['sub_price', 'subPrice', 'discount_price'],
            'sub_price_title' => ['sub_price_title', 'subPriceTitle', 'discount_title'],
            'quantity' => ['quantity', 'stock', 'inventory'],
            'sku_props' => ['props', 'properties', 'attributes', 'specs'],
            'sku_image' => ['pic_url', 'image', 'picture', 'image_url'],
            'sales_count' => ['sales_count', 'sold_count', 'sale_count'],
            'status' => ['status', 'state', 'enabled'],
            'promotion_type' => ['promotion_type', 'promo_type'],
            'promotion_info' => ['promotion_info', 'promo_info', 'promotion'],
            'delivery' => ['delivery', 'shipping', 'logistics'],
            'is_sku' => ['is_sku', 'has_sku'],
        ];
        
        $mappedSku = [];
        foreach ($skuMapping as $coreField => $possibleFields) {
            $mappedSku[$coreField] = $this->findFieldValue($rawSku, $possibleFields);
        }
        
        return $mappedSku;
    }

    /**
     * 查找字段值
     *
     * @param array $data 数据数组
     * @param array $possibleFields 可能的字段名数组
     * @return mixed 找到的值或null
     */
    private function findFieldValue(array $data, array $possibleFields)
    {
        foreach ($possibleFields as $field) {
            if (isset($data[$field])) {
                return $data[$field];
            }
        }
        return null;
    }

    /**
     * 清洗数据
     *
     * @param array $data 映射后的数据
     * @return array 清洗后的数据
     */
    private function cleanData(array $data): array
    {
        $cleanedData = [];
        
        foreach ($data as $key => $value) {
            $cleanedData[$key] = $this->cleanFieldValue($key, $value);
        }
        
        return $cleanedData;
    }

    /**
     * 清洗SKU数据
     *
     * @param array $skuData 映射后的SKU数据
     * @return array 清洗后的SKU数据
     */
    private function cleanSkuData(array $skuData): array
    {
        $cleanedData = [];
        
        foreach ($skuData as $key => $value) {
            $cleanedData[$key] = $this->cleanSkuFieldValue($key, $value);
        }
        
        return $cleanedData;
    }

    /**
     * 清洗字段值
     *
     * @param string $fieldName 字段名
     * @param mixed $value 原始值
     * @return mixed 清洗后的值
     */
    private function cleanFieldValue(string $fieldName, $value)
    {
        // 处理空值
        if (is_null($value) || $value === '') {
            return $this->getDefaultValue($fieldName);
        }
        
        // 处理字符串类型
        if (is_string($value)) {
            $value = trim($value);
            if ($value === '') {
                return $this->getDefaultValue($fieldName);
            }
        }
        
        // 根据字段类型进行特定清洗
        switch ($fieldName) {
            case 'price':
            case 'original_price':
                return $this->cleanPriceValue($value);
                
            case 'sale_count':
            case 'comment_count':
                return $this->cleanIntegerValue($value);
                
            case 'is_sku':
                return $this->cleanBooleanValue($value);
                
            case 'pic_urls':
                return $this->cleanArrayValue($value);
                
            case 'timestamp':
                return $this->cleanTimestampValue($value);
                
            default:
                return $value;
        }
    }

    /**
     * 清洗SKU字段值
     *
     * @param string $fieldName 字段名
     * @param mixed $value 原始值
     * @return mixed 清洗后的值
     */
    private function cleanSkuFieldValue(string $fieldName, $value)
    {
        // 处理空值
        if (is_null($value) || $value === '') {
            return $this->getSkuDefaultValue($fieldName);
        }
        
        // 处理字符串类型
        if (is_string($value)) {
            $value = trim($value);
            if ($value === '') {
                return $this->getSkuDefaultValue($fieldName);
            }
        }
        
        // 根据字段类型进行特定清洗
        switch ($fieldName) {
            case 'price':
            case 'original_price':
            case 'sub_price':
                return $this->cleanPriceValue($value);
                
            case 'quantity':
            case 'sales_count':
                return $this->cleanIntegerValue($value);
                
            case 'is_sku':
                return $this->cleanBooleanValue($value);
                
            case 'sku_props':
            case 'delivery':
                return $this->cleanArrayValue($value);
                
            default:
                return $value;
        }
    }

    /**
     * 清洗价格值
     *
     * @param mixed $value 原始价格值
     * @return float|null 清洗后的价格
     */
    private function cleanPriceValue($value): ?float
    {
        if (is_null($value) || $value === '') {
            return null;
        }
        
        // 如果是数字，直接转换
        if (is_numeric($value)) {
            return (float)$value;
        }
        
        // 如果是字符串，提取数字
        if (is_string($value)) {
            // 移除货币符号和其他非数字字符
            $cleanValue = preg_replace('/[^0-9.]/', '', $value);
            
            if ($cleanValue !== '' && is_numeric($cleanValue)) {
                return (float)$cleanValue;
            }
        }
        
        return null;
    }

    /**
     * 清洗整数值
     *
     * @param mixed $value 原始值
     * @return int 清洗后的整数
     */
    private function cleanIntegerValue($value): int
    {
        if (is_null($value) || $value === '') {
            return 0;
        }
        
        if (is_numeric($value)) {
            return (int)$value;
        }
        
        if (is_string($value)) {
            $cleanValue = preg_replace('/[^0-9]/', '', $value);
            return $cleanValue !== '' ? (int)$cleanValue : 0;
        }
        
        return 0;
    }

    /**
     * 清洗布尔值
     *
     * @param mixed $value 原始值
     * @return bool 清洗后的布尔值
     */
    private function cleanBooleanValue($value): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            $value = strtolower(trim($value));
            return in_array($value, ['true', '1', 'yes', 'on', 'enabled']);
        }
        
        if (is_numeric($value)) {
            return (bool)$value;
        }
        
        return false;
    }

    /**
     * 清洗数组值
     *
     * @param mixed $value 原始值
     * @return array 清洗后的数组
     */
    private function cleanArrayValue($value): array
    {
        if (is_array($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            // 尝试JSON解码
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded;
            }
            
            // 如果不是JSON，按逗号分割
            return array_filter(array_map('trim', explode(',', $value)));
        }
        
        return [];
    }

    /**
     * 清洗时间戳值
     *
     * @param mixed $value 原始时间戳
     * @return string 清洗后的时间戳
     */
    private function cleanTimestampValue($value): string
    {
        if (is_null($value) || $value === '') {
            return Carbon::now()->toDateTimeString();
        }
        
        try {
            if (is_numeric($value)) {
                return Carbon::createFromTimestamp($value)->toDateTimeString();
            }
            
            if (is_string($value)) {
                return Carbon::parse($value)->toDateTimeString();
            }
        } catch (Exception $e) {
            Log::warning("时间戳解析失败，使用当前时间", [
                'value' => $value,
                'error' => $e->getMessage()
            ]);
        }
        
        return Carbon::now()->toDateTimeString();
    }

    /**
     * 格式化数据
     *
     * @param array $data 清洗后的数据
     * @return array 格式化后的数据
     */
    private function formatData(array $data): array
    {
        // 格式化URL
        if (isset($data['pic_urls']) && is_array($data['pic_urls'])) {
            $data['pic_urls'] = array_map([$this, 'formatImageUrl'], $data['pic_urls']);
        }
        
        // 格式化分类路径
        if (isset($data['category_path'])) {
            $data['category_path'] = $this->formatCategoryPath($data['category_path']);
        }
        
        return $data;
    }

    /**
     * 格式化SKU数据
     *
     * @param array $skuData 清洗后的SKU数据
     * @return array 格式化后的SKU数据
     */
    private function formatSkuData(array $skuData): array
    {
        // 格式化SKU图片URL
        if (isset($skuData['sku_image'])) {
            $skuData['sku_image'] = $this->formatImageUrl($skuData['sku_image']);
        }
        
        return $skuData;
    }

    /**
     * 格式化图片URL
     *
     * @param string $url 原始URL
     * @return string 格式化后的URL
     */
    private function formatImageUrl(string $url): string
    {
        if (empty($url)) {
            return '';
        }
        
        // 确保URL以http://或https://开头
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'https:' . $url;
        }
        
        return $url;
    }

    /**
     * 格式化分类路径
     *
     * @param mixed $categoryPath 原始分类路径
     * @return string 格式化后的分类路径
     */
    private function formatCategoryPath($categoryPath): string
    {
        if (is_array($categoryPath)) {
            return implode(' > ', $categoryPath);
        }
        
        if (is_string($categoryPath)) {
            return $categoryPath;
        }
        
        return '';
    }

    /**
     * 获取主图片URL
     *
     * @param array $picUrls 图片URL数组
     * @return string 主图片URL
     */
    private function getMainImageUrl(array $picUrls): string
    {
        return !empty($picUrls) ? $picUrls[0] : '';
    }

    /**
     * 获取字段默认值
     *
     * @param string $fieldName 字段名
     * @return mixed 默认值
     */
    private function getDefaultValue(string $fieldName)
    {
        $defaults = [
            'title' => '',
            'price' => null,
            'original_price' => null,
            'category_id' => null,
            'category_path' => '',
            'shop_id' => '',
            'shop_name' => '',
            'state' => 'active',
            'is_sku' => false,
            'item_type' => '',
            'sale_count' => 0,
            'comment_count' => 0,
            'pic_urls' => [],
            'promotion' => [],
            'delivery' => [],
            'props' => [],
            'timestamp' => Carbon::now()->toDateTimeString(),
        ];
        
        return $defaults[$fieldName] ?? null;
    }

    /**
     * 获取SKU字段默认值
     *
     * @param string $fieldName 字段名
     * @return mixed 默认值
     */
    private function getSkuDefaultValue(string $fieldName)
    {
        $defaults = [
            'sku_id' => '',
            'sku_code' => '',
            'price' => null,
            'original_price' => null,
            'sub_price' => null,
            'sub_price_title' => '',
            'quantity' => 0,
            'sku_props' => [],
            'sku_image' => '',
            'sales_count' => 0,
            'status' => 'active',
            'promotion_type' => '',
            'promotion_info' => '',
            'delivery' => [],
            'is_sku' => false,
        ];
        
        return $defaults[$fieldName] ?? null;
    }
} 
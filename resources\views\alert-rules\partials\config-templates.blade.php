{{-- 配置模板，用于不同警报类型的动态字段 --}}
<div id="configTemplates" style="display: none;">
    
    {{-- 促销价偏差模板 --}}
    <div id="promotion-price-deviation-template">
        <div class="mb-3">
            <label for="promotion_deviation_threshold" class="form-label">
                促销价偏差率阈值 (%) <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="promotion_deviation_threshold" 
                   name="promotion_deviation_threshold" 
                   min="0" 
                   max="100" 
                   step="0.1" 
                   placeholder="例如：15"
                   required>
            <div class="form-text">促销价格偏差超过此百分比时触发警报</div>
        </div>
        
        <div class="mb-3">
            <label for="official_price" class="form-label">官方价格参考</label>
            <input type="number" 
                   class="form-control" 
                   id="official_price" 
                   name="official_price" 
                   min="0" 
                   step="0.01" 
                   placeholder="例如：99.99">
            <div class="form-text">用于计算偏差的官方参考价格</div>
        </div>
    </div>

    {{-- 渠道价偏差模板 --}}
    <div id="channel-price-deviation-template">
        <div class="mb-3">
            <label for="channel_deviation_threshold" class="form-label">
                渠道价偏差率阈值 (%) <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="channel_deviation_threshold" 
                   name="channel_deviation_threshold" 
                   min="0" 
                   max="100" 
                   step="0.1" 
                   placeholder="例如：10"
                   required>
            <div class="form-text">不同渠道价格偏差超过此百分比时触发警报</div>
        </div>
    </div>

    {{-- 产品状态变化模板 --}}
    <div id="product-status-change-template">
        <div class="mb-3">
            <label for="status_change_type" class="form-label">
                状态变化类型 <span class="text-danger">*</span>
            </label>
            <select class="form-select" id="status_change_type" name="status_change_type" required>
                <option value="">请选择状态变化类型</option>
                <option value="on_shelf">商品上架</option>
                <option value="off_shelf">商品下架</option>
                <option value="any">任意状态变化</option>
            </select>
            <div class="form-text">监控产品状态的具体变化类型</div>
        </div>
    </div>

    {{-- 库存异常模板 --}}
    <div id="inventory-anomaly-template">
        <div class="mb-3">
            <label for="inventory_threshold" class="form-label">
                库存阈值 <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="inventory_threshold" 
                   name="inventory_threshold" 
                   min="0" 
                   placeholder="例如：100"
                   required>
            <div class="form-text">库存低于此数量时触发警报</div>
        </div>
    </div>

    {{-- 数据更新异常模板 --}}
    <div id="data-update-anomaly-template">
        <div class="mb-3">
            <label for="data_update_hours_threshold" class="form-label">
                数据更新阈值 (小时) <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="data_update_hours_threshold" 
                   name="data_update_hours_threshold" 
                   min="1" 
                   max="168" 
                   placeholder="例如：24"
                   required>
            <div class="form-text">数据超过此小时数未更新时触发警报</div>
        </div>
    </div>

    {{-- 价格变化模板 --}}
    <div id="price-change-template">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="percentage_threshold" class="form-label">
                        百分比阈值 (%)
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="percentage_threshold" 
                           name="percentage_threshold" 
                           min="0" 
                           max="100" 
                           step="0.1" 
                           placeholder="例如：10">
                    <div class="form-text">价格变化超过此百分比时触发</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="threshold" class="form-label">
                        金额阈值 (¥)
                    </label>
                    <input type="number" 
                           class="form-control" 
                           id="threshold" 
                           name="threshold" 
                           min="0" 
                           step="0.01" 
                           placeholder="例如：50.00">
                    <div class="form-text">价格变化超过此金额时触发</div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="comparison" class="form-label">比较操作符</label>
            <select class="form-select" id="comparison" name="comparison">
                <option value="greater_than">大于</option>
                <option value="less_than">小于</option>
                <option value="equal">等于</option>
                <option value="greater_equal">大于等于</option>
                <option value="less_equal">小于等于</option>
            </select>
        </div>
    </div>

    {{-- 自定义模板 --}}
    <div id="custom-template">
        <div class="mb-3">
            <label for="threshold" class="form-label">
                阈值 <span class="text-danger">*</span>
            </label>
            <input type="number" 
                   class="form-control" 
                   id="threshold" 
                   name="threshold" 
                   step="0.01" 
                   placeholder="请输入数值阈值"
                   required>
            <div class="form-text">触发警报的数值阈值</div>
        </div>
        
        <div class="mb-3">
            <label for="comparison" class="form-label">比较操作符</label>
            <select class="form-select" id="comparison" name="comparison">
                <option value="greater_than">大于</option>
                <option value="less_than">小于</option>
                <option value="equal">等于</option>
                <option value="greater_equal">大于等于</option>
                <option value="less_equal">小于等于</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label for="conditions" class="form-label">自定义条件 (JSON)</label>
            <textarea class="form-control" 
                      id="conditions" 
                      name="conditions" 
                      rows="4" 
                      placeholder='{"key": "value", "operator": "equals"}'></textarea>
            <div class="form-text">高级用户可以配置JSON格式的自定义条件</div>
        </div>
    </div>
</div> 
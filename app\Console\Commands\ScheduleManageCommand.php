<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SchedulingService;
use App\Models\MonitorTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 调度管理命令
 * 
 * 提供调度系统的管理功能：
 * - 查看调度状态
 * - 手动触发任务
 * - 暂停/恢复任务
 * - 调度统计信息
 */
class ScheduleManageCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'schedule:manage 
                          {action : 管理动作 (status|trigger|pause|resume|stats|test)}
                          {--task-id= : 任务ID（适用于trigger/pause/resume动作）}
                          {--all : 应用到所有任务（适用于pause/resume动作）}
                          {--format=table : 输出格式 (table|json)}
                          {--watch : 持续监控模式}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '管理数据收集任务调度系统';

    /**
     * 调度服务实例
     *
     * @var SchedulingService
     */
    protected $schedulingService;

    /**
     * 创建新命令实例
     */
    public function __construct(SchedulingService $schedulingService)
    {
        parent::__construct();
        $this->schedulingService = $schedulingService;
    }

    /**
     * 执行命令
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                return $this->showStatus();
                
            case 'trigger':
                return $this->triggerTask();
                
            case 'pause':
                return $this->pauseTask();
                
            case 'resume':
                return $this->resumeTask();
                
            case 'stats':
                return $this->showStats();
                
            case 'test':
                return $this->testScheduling();
                
            default:
                $this->error("不支持的动作: {$action}");
                $this->showHelp();
                return 1;
        }
    }

    /**
     * 显示调度状态
     */
    protected function showStatus()
    {
        $this->info('📅 调度系统状态');
        $this->line('');

        // 获取所有监控任务
        $tasks = MonitorTask::with(['priceHistories' => function($query) {
            $query->latest('timestamp')->limit(1);
        }])->get();

        if ($tasks->isEmpty()) {
            $this->warn('暂无监控任务');
            return 0;
        }

        // 准备表格数据
        $headers = ['ID', '任务名称', '平台', '状态', '启用', '频率', '上次执行', '成功/失败', '队列'];
        $rows = [];

        foreach ($tasks as $task) {
            $lastExecution = $task->last_executed_at 
                ? $task->last_executed_at->diffForHumans() 
                : '从未执行';

            $frequency = $this->formatFrequency($task->collection_frequency ?? 'hourly');
            
            $successFailure = sprintf('%d/%d', 
                $task->success_count ?? 0, 
                $task->failure_count ?? 0
            );

            $status = $this->formatStatus($task->status);
            $enabled = $task->is_enabled ? '✅' : '❌';

            $rows[] = [
                $task->id,
                $this->truncate($task->task_name, 20),
                $task->platform ?? 'N/A',
                $status,
                $enabled,
                $frequency,
                $lastExecution,
                $successFailure,
                $task->queue_name ?? 'default'
            ];
        }

        if ($this->option('format') === 'json') {
            $this->line(json_encode($rows, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        } else {
            $this->table($headers, $rows);
        }

        // 显示调度统计
        $this->showScheduleStats();

        // 监控模式
        if ($this->option('watch')) {
            $this->watchStatus();
        }

        return 0;
    }

    /**
     * 显示调度统计信息
     */
    protected function showScheduleStats()
    {
        $stats = $this->schedulingService->getSchedulingStats();
        
        $this->line('');
        $this->info('📊 调度统计');
        
        $statsTable = [
            ['活跃任务数', $stats['active_scheduled_tasks']],
            ['总任务数', $stats['total_tasks']],
            ['调度器状态', $stats['scheduler_status']],
            ['最后检查', $stats['last_health_check']]
        ];

        $this->table(['指标', '值'], $statsTable);

        // 显示缓存的健康检查数据
        $healthStats = Cache::get('scheduling_stats');
        if ($healthStats) {
            $this->line('');
            $this->comment('💾 缓存的健康状态:');
            $this->line("  最后更新: " . $healthStats['last_health_check']);
        }
    }

    /**
     * 手动触发任务
     */
    protected function triggerTask()
    {
        $taskId = $this->option('task-id');
        
        if (!$taskId) {
            $this->error('请指定任务ID: --task-id=<id>');
            return 1;
        }

        $this->info("🚀 触发任务 #{$taskId}...");

        $task = MonitorTask::find($taskId);
        if (!$task) {
            $this->error("任务 #{$taskId} 不存在");
            return 1;
        }

        if (!$task->is_enabled) {
            $this->warn("任务 #{$taskId} 已禁用，是否继续？");
            if (!$this->confirm('继续执行？')) {
                return 0;
            }
        }

        $success = $this->schedulingService->triggerTask($taskId);
        
        if ($success) {
            $this->info("✅ 任务 #{$taskId} 已成功触发");
            $this->line("任务已加入队列: {$task->queue_name}");
        } else {
            $this->error("❌ 任务 #{$taskId} 触发失败，请查看日志");
            return 1;
        }

        return 0;
    }

    /**
     * 暂停任务
     */
    protected function pauseTask()
    {
        if ($this->option('all')) {
            return $this->pauseAllTasks();
        }

        $taskId = $this->option('task-id');
        
        if (!$taskId) {
            $this->error('请指定任务ID: --task-id=<id> 或使用 --all 暂停所有任务');
            return 1;
        }

        $this->info("⏸️ 暂停任务 #{$taskId}...");

        $success = $this->schedulingService->pauseTask($taskId);
        
        if ($success) {
            $this->info("✅ 任务 #{$taskId} 已暂停");
        } else {
            $this->error("❌ 任务 #{$taskId} 暂停失败");
            return 1;
        }

        return 0;
    }

    /**
     * 暂停所有任务
     */
    protected function pauseAllTasks()
    {
        $this->warn('⚠️ 即将暂停所有活跃任务');
        
        if (!$this->confirm('确定要暂停所有任务？')) {
            return 0;
        }

        $activeTasks = MonitorTask::where('is_enabled', true)->get();
        $pausedCount = 0;

        $this->withProgressBar($activeTasks, function ($task) use (&$pausedCount) {
            if ($this->schedulingService->pauseTask($task->id)) {
                $pausedCount++;
            }
        });

        $this->line('');
        $this->info("✅ 已暂停 {$pausedCount}/{$activeTasks->count()} 个任务");

        return 0;
    }

    /**
     * 恢复任务
     */
    protected function resumeTask()
    {
        if ($this->option('all')) {
            return $this->resumeAllTasks();
        }

        $taskId = $this->option('task-id');
        
        if (!$taskId) {
            $this->error('请指定任务ID: --task-id=<id> 或使用 --all 恢复所有任务');
            return 1;
        }

        $this->info("▶️ 恢复任务 #{$taskId}...");

        $success = $this->schedulingService->resumeTask($taskId);
        
        if ($success) {
            $this->info("✅ 任务 #{$taskId} 已恢复");
        } else {
            $this->error("❌ 任务 #{$taskId} 恢复失败");
            return 1;
        }

        return 0;
    }

    /**
     * 恢复所有任务
     */
    protected function resumeAllTasks()
    {
        $this->info('▶️ 恢复所有暂停的任务');
        
        $pausedTasks = MonitorTask::where('is_enabled', false)->get();
        
        if ($pausedTasks->isEmpty()) {
            $this->info('没有暂停的任务需要恢复');
            return 0;
        }

        $resumedCount = 0;

        $this->withProgressBar($pausedTasks, function ($task) use (&$resumedCount) {
            if ($this->schedulingService->resumeTask($task->id)) {
                $resumedCount++;
            }
        });

        $this->line('');
        $this->info("✅ 已恢复 {$resumedCount}/{$pausedTasks->count()} 个任务");

        return 0;
    }

    /**
     * 显示详细统计信息
     */
    protected function showStats()
    {
        $this->info('📈 调度系统详细统计');
        $this->line('');

        // 基本统计
        $totalTasks = MonitorTask::count();
        $activeTasks = MonitorTask::where('is_enabled', true)->where('status', 'active')->count();
        $pausedTasks = MonitorTask::where('is_enabled', false)->count();
        $disabledTasks = MonitorTask::where('status', 'disabled')->count();

        $basicStats = [
            ['总任务数', $totalTasks],
            ['活跃任务', $activeTasks],
            ['暂停任务', $pausedTasks],
            ['禁用任务', $disabledTasks],
        ];

        $this->table(['指标', '数量'], $basicStats);

        // 平台分布
        $this->line('');
        $this->info('🏪 平台分布');
        
        $platformStats = MonitorTask::selectRaw('platform, count(*) as count, sum(is_enabled) as enabled_count')
            ->groupBy('platform')
            ->get();

        if ($platformStats->isNotEmpty()) {
            $platformRows = [];
            foreach ($platformStats as $stat) {
                $platformRows[] = [
                    $stat->platform ?? '未设置',
                    $stat->count,
                    $stat->enabled_count,
                    $stat->count - $stat->enabled_count
                ];
            }
            $this->table(['平台', '总数', '启用', '禁用'], $platformRows);
        }

        // 频率分布
        $this->line('');
        $this->info('⏰ 频率分布');
        
        $frequencyStats = MonitorTask::selectRaw('collection_frequency, count(*) as count')
            ->groupBy('collection_frequency')
            ->get();

        if ($frequencyStats->isNotEmpty()) {
            $frequencyRows = [];
            foreach ($frequencyStats as $stat) {
                $frequency = $stat->collection_frequency ?? 'hourly';
                $frequencyRows[] = [
                    $this->formatFrequency($frequency),
                    $stat->count
                ];
            }
            $this->table(['频率', '任务数'], $frequencyRows);
        }

        // 执行统计
        $this->line('');
        $this->info('📊 执行统计（最近24小时）');
        
        $recentTasks = MonitorTask::where('last_executed_at', '>=', now()->subDay())
            ->get();

        $totalExecutions = $recentTasks->sum('execution_count');
        $totalSuccesses = $recentTasks->sum('success_count');
        $totalFailures = $recentTasks->sum('failure_count');
        $successRate = $totalExecutions > 0 ? round(($totalSuccesses / $totalExecutions) * 100, 2) : 0;

        $executionStats = [
            ['总执行次数', $totalExecutions],
            ['成功次数', $totalSuccesses],
            ['失败次数', $totalFailures],
            ['成功率', $successRate . '%'],
        ];

        $this->table(['指标', '值'], $executionStats);

        return 0;
    }

    /**
     * 测试调度功能
     */
    protected function testScheduling()
    {
        $this->info('🧪 测试调度系统...');
        $this->line('');

        // 测试1: 检查调度服务
        $this->line('1. 检查调度服务...');
        try {
            $stats = $this->schedulingService->getSchedulingStats();
            $this->info('   ✅ 调度服务正常');
        } catch (\Exception $e) {
            $this->error("   ❌ 调度服务异常: {$e->getMessage()}");
            return 1;
        }

        // 测试2: 检查可用频率
        $this->line('2. 检查可用调度频率...');
        $frequencies = SchedulingService::getAvailableFrequencies();
        $this->info("   ✅ 可用频率: " . count($frequencies) . " 种");
        
        if ($this->option('format') === 'json') {
            $this->line('   频率列表: ' . json_encode($frequencies, JSON_UNESCAPED_UNICODE));
        }

        // 测试3: 检查任务状态
        $this->line('3. 检查监控任务...');
        $validTasks = MonitorTask::where('is_enabled', true)
            ->where('status', 'active')
            ->count();
        $this->info("   ✅ 有效任务数: {$validTasks}");

        // 测试4: 检查缓存
        $this->line('4. 检查缓存状态...');
        $cacheStats = Cache::get('scheduling_stats');
        if ($cacheStats) {
            $this->info('   ✅ 调度缓存正常');
        } else {
            $this->warn('   ⚠️ 调度缓存为空');
        }

        $this->line('');
        $this->info('🎉 调度系统测试完成');

        return 0;
    }

    /**
     * 监控模式
     */
    protected function watchStatus()
    {
        $this->info('');
        $this->info('👁️ 进入监控模式（按 Ctrl+C 退出）');
        
        while (true) {
            sleep(10); // 每10秒刷新一次
            
            // 清屏并重新显示状态
            system('clear');
            $this->showStatus();
        }
    }

    /**
     * 格式化频率显示
     */
    protected function formatFrequency($frequency)
    {
        $frequencies = SchedulingService::getAvailableFrequencies();
        return $frequencies[$frequency] ?? $frequency;
    }

    /**
     * 格式化状态显示
     */
    protected function formatStatus($status)
    {
        $statusMap = [
            'active' => '🟢 活跃',
            'paused' => '🟡 暂停',
            'disabled' => '🔴 禁用',
            'maintenance' => '🟠 维护',
        ];

        return $statusMap[$status] ?? $status;
    }

    /**
     * 截断长文本
     */
    protected function truncate($text, $length = 50)
    {
        return mb_strlen($text) > $length 
            ? mb_substr($text, 0, $length - 3) . '...' 
            : $text;
    }

    /**
     * 显示帮助信息
     */
    protected function showHelp()
    {
        $this->line('');
        $this->info('📖 使用示例：');
        $this->line('  php artisan schedule:manage status                    # 查看调度状态');
        $this->line('  php artisan schedule:manage status --watch            # 监控模式');
        $this->line('  php artisan schedule:manage trigger --task-id=1       # 手动触发任务');
        $this->line('  php artisan schedule:manage pause --task-id=1         # 暂停指定任务');
        $this->line('  php artisan schedule:manage pause --all               # 暂停所有任务');
        $this->line('  php artisan schedule:manage resume --task-id=1        # 恢复指定任务');
        $this->line('  php artisan schedule:manage resume --all              # 恢复所有任务');
        $this->line('  php artisan schedule:manage stats                     # 显示详细统计');
        $this->line('  php artisan schedule:manage test                      # 测试调度系统');
        $this->line('');
    }
} 
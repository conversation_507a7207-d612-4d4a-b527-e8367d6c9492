<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alert_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('接收告警的用户ID');
            $table->foreignId('rule_id')->constrained('alert_rules')->onDelete('cascade')->comment('触发的规则ID');
            $table->foreignId('task_id')->constrained('monitor_tasks')->onDelete('cascade')->comment('关联的监控任务ID');
            $table->foreignId('sku_id')->nullable()->constrained('product_skus')->onDelete('set null')->comment('相关SKU ID');
            $table->string('alert_title')->comment('告警标题');
            $table->text('message')->comment('告警消息内容');
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium')->comment('严重程度');
            $table->enum('status', ['unread', 'read', 'resolved', 'ignored'])->default('unread')->comment('处理状态');
            $table->decimal('trigger_value', 10, 2)->nullable()->comment('触发值');
            $table->decimal('threshold_value', 10, 2)->nullable()->comment('阈值');
            $table->json('context_data')->nullable()->comment('上下文数据（价格变化详情等）');
            $table->enum('notification_method', ['email', 'sms', 'push', 'system'])->comment('通知方式');
            $table->boolean('notification_sent')->default(false)->comment('是否已发送通知');
            $table->timestamp('notification_sent_at')->nullable()->comment('通知发送时间');
            $table->text('notification_error')->nullable()->comment('通知发送错误信息');
            $table->timestamp('read_at')->nullable()->comment('阅读时间');
            $table->timestamp('resolved_at')->nullable()->comment('解决时间');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['rule_id', 'created_at']);
            $table->index(['task_id', 'created_at']);
            $table->index(['severity', 'status']);
            $table->index(['created_at', 'status']);
            $table->index('notification_sent');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alert_logs');
    }
};

/**
 * UX增强和响应式设计优化
 */

/* ==================== 全局响应式优化 ==================== */

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 改进的焦点样式 */
*:focus {
    outline: 2px solid #4e73df;
    outline-offset: 2px;
    border-radius: 4px;
}

/* 按钮焦点样式 */
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* ==================== 加载状态和动画 ==================== */

/* 页面加载动画 */
.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 骨架屏加载 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
}

.skeleton-title {
    height: 1.5rem;
    width: 60%;
    margin-bottom: 1rem;
    border-radius: 4px;
}

/* ==================== 微交互动画 ==================== */

/* 悬停效果 */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 点击反馈 */
.click-feedback {
    transition: transform 0.1s ease;
}

.click-feedback:active {
    transform: scale(0.98);
}

/* 淡入动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 滑入动画 */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* ==================== 表单增强 ==================== */

/* 浮动标签效果 */
.floating-label {
    position: relative;
    margin-bottom: 1.5rem;
}

.floating-label input,
.floating-label textarea,
.floating-label select {
    width: 100%;
    padding: 1rem 0.75rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.floating-label label {
    position: absolute;
    top: 1rem;
    left: 0.75rem;
    font-size: 1rem;
    color: #666;
    pointer-events: none;
    transition: all 0.3s ease;
}

.floating-label input:focus,
.floating-label textarea:focus,
.floating-label select:focus {
    border-color: #4e73df;
    outline: none;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label,
.floating-label textarea:focus + label,
.floating-label textarea:not(:placeholder-shown) + label,
.floating-label select:focus + label,
.floating-label select:not([value=""]) + label {
    top: 0.25rem;
    font-size: 0.75rem;
    color: #4e73df;
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* ==================== 通知和反馈 ==================== */

/* 改进的Toast通知 */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9999;
}

.toast {
    min-width: 300px;
    margin-bottom: 0.5rem;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.toast.hide {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}

/* 进度指示器 */
.progress-enhanced {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #e9ecef;
}

.progress-enhanced .progress-bar {
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* ==================== 数据表格增强 ==================== */

/* 响应式表格 */
.table-responsive-enhanced {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-enhanced {
    margin-bottom: 0;
}

.table-enhanced th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem 0.75rem;
}

.table-enhanced td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e3e6f0;
}

.table-enhanced tbody tr:hover {
    background-color: #f8f9fc;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* ==================== 移动端优化 ==================== */

/* 移动端导航 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    /* 移动端表格 */
    .table-mobile {
        font-size: 0.875rem;
    }

    .table-mobile th,
    .table-mobile td {
        padding: 0.5rem 0.25rem;
    }

    /* 移动端卡片 */
    .card-mobile {
        margin-bottom: 1rem;
        border-radius: 8px;
    }

    /* 移动端按钮组 */
    .btn-group-mobile .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }

    /* 移动端表单优化 */
    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 0.75rem;
    }

    /* 移动端模态框优化 */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    /* 移动端图表容器 */
    .chart-container {
        height: 250px;
        margin-bottom: 1rem;
    }

    /* 移动端工具栏 */
    .toolbar-mobile {
        flex-direction: column;
        gap: 0.5rem;
    }

    .toolbar-mobile .btn {
        width: 100%;
        justify-content: center;
    }

    /* 移动端搜索框 */
    .search-mobile {
        margin-bottom: 1rem;
    }

    /* 移动端分页 */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination .page-item {
        margin: 0.125rem;
    }

    /* 移动端统计卡片 */
    .stats-card-mobile {
        text-align: center;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    /* 移动端拖拽优化 */
    .draggable-mobile {
        touch-action: none;
        user-select: none;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .col-tablet-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* ==================== 可访问性增强 ==================== */

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000;
        border-color: #000;
        color: #fff;
    }
    
    .card {
        border: 2px solid #000;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 屏幕阅读器专用内容 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ==================== 打印样式 ==================== */

@media print {
    .no-print {
        display: none !important;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .card {
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .btn {
        display: none;
    }
}

{"name": "ecommerce/market-dynamics-monitoring", "description": "电商市场动态监测系统 - E-commerce Market Dynamics Monitoring System", "type": "project", "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.0", "laravel/sanctum": "^3.3", "laravel/scout": "^10.0", "laravel/tinker": "^2.8", "league/csv": "^9.8", "meilisearch/meilisearch-php": "^1.0", "phpoffice/phpspreadsheet": "^4.3", "predis/predis": "^2.0", "spatie/laravel-permission": "^6.20"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
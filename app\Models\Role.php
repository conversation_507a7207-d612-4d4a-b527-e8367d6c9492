<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'level',
        'is_active',
        'permissions_config',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'permissions_config' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 角色级别常量
     */
    const LEVEL_ADMIN = 'admin';
    const LEVEL_ADVANCED = 'advanced';
    const LEVEL_NORMAL = 'normal';
    const LEVEL_READONLY = 'readonly';

    /**
     * 获取角色的用户
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'role_user')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * 获取角色的权限
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'permission_role')
                    ->withTimestamps();
    }

    /**
     * 检查角色是否有特定权限
     */
    public function hasPermission(string $permission): bool
    {
        return $this->permissions()
                    ->where('name', $permission)
                    ->where('is_active', true)
                    ->exists();
    }

    /**
     * 给角色分配权限
     */
    public function givePermission(string|Permission $permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->syncWithoutDetaching([$permission->id]);
    }

    /**
     * 给角色分配多个权限
     */
    public function givePermissions(array $permissions): void
    {
        $permissionIds = collect($permissions)->map(function ($permission) {
            if (is_string($permission)) {
                return Permission::where('name', $permission)->firstOrFail()->id;
            }
            return $permission instanceof Permission ? $permission->id : $permission;
        })->toArray();

        $this->permissions()->syncWithoutDetaching($permissionIds);
    }

    /**
     * 撤销角色的权限
     */
    public function revokePermission(string|Permission $permission): void
    {
        if (is_string($permission)) {
            $permission = Permission::where('name', $permission)->firstOrFail();
        }

        $this->permissions()->detach($permission->id);
    }

    /**
     * 同步角色权限（替换所有权限）
     */
    public function syncPermissions(array $permissions): void
    {
        $permissionIds = collect($permissions)->map(function ($permission) {
            if (is_string($permission)) {
                return Permission::where('name', $permission)->firstOrFail()->id;
            }
            return $permission instanceof Permission ? $permission->id : $permission;
        })->toArray();

        $this->permissions()->sync($permissionIds);
    }

    /**
     * 检查角色是否激活
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * 获取角色级别的显示名称
     */
    public function getLevelDisplayAttribute(): string
    {
        return match($this->level) {
            self::LEVEL_ADMIN => '管理员',
            self::LEVEL_ADVANCED => '高级用户',
            self::LEVEL_NORMAL => '普通用户',
            self::LEVEL_READONLY => '只读用户',
            default => '未知',
        };
    }

    /**
     * 获取角色的用户数量
     */
    public function getUserCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * 获取角色的权限数量
     */
    public function getPermissionCountAttribute(): int
    {
        return $this->permissions()->where('is_active', true)->count();
    }
} 
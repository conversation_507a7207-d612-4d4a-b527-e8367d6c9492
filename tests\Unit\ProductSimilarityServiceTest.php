<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Product;
use App\Services\ProductSimilarityService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductSimilarityServiceTest extends TestCase
{
    use RefreshDatabase;

    private ProductSimilarityService $similarityService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->similarityService = new ProductSimilarityService();
    }

    /** @test */
    public function it_calculates_title_similarity_correctly()
    {
        // 完全相同的标题
        $score1 = $this->similarityService->calculateTitleSimilarity(
            'iPhone 14 Pro Max 128GB 紫色',
            'iPhone 14 Pro Max 128GB 紫色'
        );
        $this->assertEquals(1.0, $score1);

        // 部分相同的标题
        $score2 = $this->similarityService->calculateTitleSimilarity(
            'iPhone 14 Pro Max 128GB 紫色',
            'iPhone 14 Pro Max 256GB 深空黑'
        );
        $this->assertGreaterThan(0.5, $score2);
        $this->assertLessThan(1.0, $score2);

        // 完全不同的标题
        $score3 = $this->similarityService->calculateTitleSimilarity(
            'iPhone 14 Pro Max',
            '小米13 Ultra'
        );
        $this->assertLessThan(0.3, $score3);

        // 空标题
        $score4 = $this->similarityService->calculateTitleSimilarity('', 'iPhone 14');
        $this->assertEquals(0.0, $score4);
    }

    /** @test */
    public function it_calculates_category_similarity_correctly()
    {
        // 完全相同的分类
        $score1 = $this->similarityService->calculateCategorySimilarity(
            '手机/智能手机/苹果',
            '手机/智能手机/苹果'
        );
        $this->assertEquals(1.0, $score1);

        // 部分相同的分类路径
        $score2 = $this->similarityService->calculateCategorySimilarity(
            '手机/智能手机/苹果',
            '手机/智能手机/华为'
        );
        $this->assertGreaterThan(0.5, $score2);
        $this->assertLessThan(1.0, $score2);

        // 完全不同的分类
        $score3 = $this->similarityService->calculateCategorySimilarity(
            '手机/智能手机',
            '服装/上衣/T恤'
        );
        $this->assertEquals(0.0, $score3);

        // 层级包含关系
        $score4 = $this->similarityService->calculateCategorySimilarity(
            '手机/智能手机/苹果/iPhone',
            '手机/智能手机/苹果'
        );
        $this->assertGreaterThan(0.8, $score4);
    }

    /** @test */
    public function it_calculates_price_similarity_correctly()
    {
        // 相同价格
        $score1 = $this->similarityService->calculatePriceSimilarity(100.0, 100.0);
        $this->assertEquals(1.0, $score1);

        // 价格在阈值内
        $score2 = $this->similarityService->calculatePriceSimilarity(100.0, 110.0);
        $this->assertGreaterThan(0.5, $score2);

        // 价格超出阈值
        $score3 = $this->similarityService->calculatePriceSimilarity(100.0, 150.0);
        $this->assertEquals(0.0, $score3);

        // 零价格或负价格
        $score4 = $this->similarityService->calculatePriceSimilarity(0.0, 100.0);
        $this->assertEquals(0.0, $score4);

        $score5 = $this->similarityService->calculatePriceSimilarity(-10.0, 100.0);
        $this->assertEquals(0.0, $score5);
    }

    /** @test */
    public function it_calculates_shop_type_similarity_correctly()
    {
        // 都是官方店
        $score1 = $this->similarityService->calculateShopTypeSimilarity('官方旗舰店', '苹果官方店');
        $this->assertEquals(1.0, $score1);

        // 都是第三方
        $score2 = $this->similarityService->calculateShopTypeSimilarity('普通店铺', '个人卖家');
        $this->assertEquals(0.75, $score2);

        // 一个官方一个第三方
        $score3 = $this->similarityService->calculateShopTypeSimilarity('官方旗舰店', '普通店铺');
        $this->assertEquals(0.5, $score3);

        // 空值处理
        $score4 = $this->similarityService->calculateShopTypeSimilarity(null, '官方店');
        $this->assertEquals(0.3, $score4);
    }

    /** @test */
    public function it_calculates_overall_similarity_correctly()
    {
        $product1 = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max 128GB 紫色',
            'category_path' => '手机/智能手机/苹果',
            'min_price' => 8999.00,
            'shop_type' => '官方旗舰店'
        ]);

        $product2 = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max 256GB 深空黑',
            'category_path' => '手机/智能手机/苹果',
            'min_price' => 9899.00,
            'shop_type' => '苹果授权店'
        ]);

        $similarity = $this->similarityService->calculateSimilarity($product1, $product2);

        $this->assertIsArray($similarity);
        $this->assertArrayHasKey('total_score', $similarity);
        $this->assertArrayHasKey('title_score', $similarity);
        $this->assertArrayHasKey('category_score', $similarity);
        $this->assertArrayHasKey('price_score', $similarity);
        $this->assertArrayHasKey('shop_type_score', $similarity);
        $this->assertArrayHasKey('weights', $similarity);

        // 检查分数范围
        $this->assertGreaterThanOrEqual(0.0, $similarity['total_score']);
        $this->assertLessThanOrEqual(1.0, $similarity['total_score']);

        // 验证权重加总为1
        $weights = $similarity['weights'];
        $totalWeight = $weights['title'] + $weights['category'] + $weights['price'] + $weights['shop_type'];
        $this->assertEqualsWithDelta(1.0, $totalWeight, 0.001, 'Total weights should equal 1.0');
    }

    /** @test */
    public function it_handles_batch_similarity_calculation()
    {
        $targetProduct = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max',
            'category_path' => '手机/智能手机/苹果',
            'min_price' => 8999.00,
            'shop_type' => '官方旗舰店'
        ]);

        $products = Product::factory(3)->create([
            'category_path' => '手机/智能手机',
            'min_price' => 5000.00,
        ]);

        $results = $this->similarityService->calculateBatchSimilarity($targetProduct, $products->all());

        $this->assertIsArray($results);
        $this->assertCount(3, $results);

        foreach ($results as $result) {
            $this->assertArrayHasKey('product_id', $result);
            $this->assertArrayHasKey('product', $result);
            $this->assertArrayHasKey('similarity', $result);
            $this->assertNotEquals($targetProduct->id, $result['product_id']);
        }

        // 验证结果按相似度排序（降序）
        $scores = array_column($results, 'similarity');
        $totalScores = array_column($scores, 'total_score');
        $sortedScores = $totalScores;
        rsort($sortedScores); // 降序排序
        $this->assertEquals($sortedScores, $totalScores);
    }

    /** @test */
    public function it_returns_algorithm_configuration()
    {
        $config = $this->similarityService->getAlgorithmConfig();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('weights', $config);
        $this->assertArrayHasKey('thresholds', $config);
        $this->assertArrayHasKey('methods', $config);

        // 验证权重配置
        $weights = $config['weights'];
        $this->assertArrayHasKey('title', $weights);
        $this->assertArrayHasKey('category', $weights);
        $this->assertArrayHasKey('price', $weights);
        $this->assertArrayHasKey('shop_type', $weights);

        // 验证权重总和为1
        $totalWeight = array_sum($weights);
        $this->assertEqualsWithDelta(1.0, $totalWeight, 0.001, 'Total weight should equal 1.0');
    }

    /** @test */
    public function it_handles_edge_cases_gracefully()
    {
        $product1 = Product::factory()->create([
            'title' => '',
            'category_path' => '',
            'min_price' => 0,
            'shop_type' => null
        ]);

        $product2 = Product::factory()->create([
            'title' => 'Normal Product',
            'category_path' => '分类/子分类',
            'min_price' => 100.0,
            'shop_type' => '店铺'
        ]);

        $similarity = $this->similarityService->calculateSimilarity($product1, $product2);

        $this->assertIsArray($similarity);
        $this->assertGreaterThanOrEqual(0.0, $similarity['total_score']);
        $this->assertLessThanOrEqual(1.0, $similarity['total_score']);

        // 大部分分项应该是0，因为product1的字段大多为空
        $this->assertEquals(0.0, $similarity['title_score']);
        $this->assertEquals(0.0, $similarity['category_score']);
        $this->assertEquals(0.0, $similarity['price_score']);
    }

    /** @test */
    public function it_handles_unicode_characters_in_titles()
    {
        $score1 = $this->similarityService->calculateTitleSimilarity(
            '苹果iPhone14 Pro Max手机📱',
            '苹果iPhone14 Pro Max手机🔥'
        );
        $this->assertGreaterThan(0.8, $score1);

        $score2 = $this->similarityService->calculateTitleSimilarity(
            '小米Redmi Note 12 Pro+ 5G手机',
            '小米Redmi Note 12 Pro Plus 5G手机'
        );
        $this->assertGreaterThan(0.8, $score2);
    }

    /** @test */
    public function it_calculates_cosine_similarity_correctly()
    {
        // 测试不同的分词组合
        $title1 = "苹果 iPhone 14 Pro Max 手机";
        $title2 = "苹果 iPhone 14 Pro 手机";
        
        $score = $this->similarityService->calculateTitleSimilarity($title1, $title2);
        $this->assertGreaterThan(0.7, $score);
        $this->assertLessThan(1.0, $score);
    }
} 
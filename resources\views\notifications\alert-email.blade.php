<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $subject ?? '电商监测系统警报' }}</title>
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* 头部样式 */
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .email-header .system-name {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        /* 严重程度标识 */
        .severity-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 15px;
            letter-spacing: 0.5px;
        }
        
        .severity-emergency {
            background-color: #dc3545;
            color: white;
        }
        
        .severity-important {
            background-color: #fd7e14;
            color: white;
        }
        
        .severity-general {
            background-color: #17a2b8;
            color: white;
        }
        
        /* 内容区域 */
        .email-content {
            padding: 30px 20px;
        }
        
        .alert-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.3;
        }
        
        .alert-message {
            font-size: 16px;
            color: #555;
            margin-bottom: 25px;
            line-height: 1.6;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            border-radius: 4px;
        }
        
        /* 详细信息表格 */
        .alert-details {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .alert-details-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .alert-details-body {
            padding: 0;
        }
        
        .detail-item {
            display: flex;
            padding: 12px 20px;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: #6c757d;
            flex: 1;
        }
        
        /* 突出显示的值 */
        .detail-value.highlight {
            color: #dc3545;
            font-weight: 600;
        }
        
        .detail-value.success {
            color: #28a745;
            font-weight: 600;
        }
        
        /* 操作按钮 */
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102,126,234,0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        
        /* 页脚 */
        .email-footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
        
        .footer-note {
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .footer-links {
            margin-top: 15px;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-header, .email-content, .email-footer {
                padding-left: 15px;
                padding-right: 15px;
            }
            
            .detail-item {
                flex-direction: column;
                padding: 15px 20px;
            }
            
            .detail-label {
                margin-bottom: 5px;
                min-width: auto;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
        
        /* 打印样式 */
        @media print {
            body {
                background-color: white;
            }
            
            .email-container {
                box-shadow: none;
                border: 1px solid #ccc;
            }
            
            .btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- 邮件头部 -->
        <div class="email-header">
            <h1>{{ $alertData['title'] ?? '系统警报通知' }}</h1>
            <div class="system-name">电商市场动态监测系统</div>
            <div class="severity-badge severity-{{ $severity }}">
                @switch($severity)
                    @case('emergency')
                        🚨 紧急警报
                        @break
                    @case('important')
                        ⚠️ 重要通知
                        @break
                    @case('general')
                        ℹ️ 一般通知
                        @break
                    @default
                        📢 系统通知
                @endswitch
            </div>
        </div>

        <!-- 邮件内容 -->
        <div class="email-content">
            <div class="alert-title">
                {{ $alertData['title'] ?? '系统警报' }}
            </div>

            <div class="alert-message">
                {{ $alertData['message'] ?? '系统检测到异常情况，请及时查看并处理。' }}
            </div>

            <!-- 详细信息 -->
            @if(isset($alertData) && is_array($alertData))
            <div class="alert-details">
                <div class="alert-details-header">
                    📊 详细信息
                </div>
                <div class="alert-details-body">
                    <!-- 时间信息 -->
                    <div class="detail-item">
                        <div class="detail-label">发生时间:</div>
                        <div class="detail-value">{{ $alertData['created_at'] ?? now()->format('Y-m-d H:i:s') }}</div>
                    </div>
                    
                    <!-- 严重程度 -->
                    <div class="detail-item">
                        <div class="detail-label">严重程度:</div>
                        <div class="detail-value {{ $severity === 'emergency' ? 'highlight' : '' }}">
                            @switch($severity)
                                @case('emergency')
                                    紧急 (需要立即处理)
                                    @break
                                @case('important')
                                    重要 (需要及时关注)
                                    @break
                                @case('general')
                                    一般 (常规信息)
                                    @break
                                @default
                                    未知
                            @endswitch
                        </div>
                    </div>

                    <!-- 价格相关信息 -->
                    @if(isset($alertData['current_price']))
                    <div class="detail-item">
                        <div class="detail-label">当前价格:</div>
                        <div class="detail-value">¥{{ number_format($alertData['current_price'], 2) }}</div>
                    </div>
                    @endif

                    @if(isset($alertData['previous_price']))
                    <div class="detail-item">
                        <div class="detail-label">之前价格:</div>
                        <div class="detail-value">¥{{ number_format($alertData['previous_price'], 2) }}</div>
                    </div>
                    @endif

                    @if(isset($alertData['price_change_percentage']))
                    <div class="detail-item">
                        <div class="detail-label">价格变化:</div>
                        <div class="detail-value {{ $alertData['price_change_percentage'] < 0 ? 'highlight' : 'success' }}">
                            {{ $alertData['price_change_percentage'] > 0 ? '+' : '' }}{{ number_format($alertData['price_change_percentage'], 2) }}%
                        </div>
                    </div>
                    @endif

                    <!-- 库存相关信息 -->
                    @if(isset($alertData['current_stock']))
                    <div class="detail-item">
                        <div class="detail-label">当前库存:</div>
                        <div class="detail-value {{ $alertData['current_stock'] < 10 ? 'highlight' : '' }}">
                            {{ $alertData['current_stock'] }} 件
                        </div>
                    </div>
                    @endif

                    @if(isset($alertData['stock_change']))
                    <div class="detail-item">
                        <div class="detail-label">库存变化:</div>
                        <div class="detail-value {{ $alertData['stock_change'] < 0 ? 'highlight' : 'success' }}">
                            {{ $alertData['stock_change'] > 0 ? '+' : '' }}{{ $alertData['stock_change'] }} 件
                        </div>
                    </div>
                    @endif

                    <!-- 产品信息 -->
                    @if(isset($alertData['sku_id']))
                    <div class="detail-item">
                        <div class="detail-label">产品SKU:</div>
                        <div class="detail-value">{{ $alertData['sku_id'] }}</div>
                    </div>
                    @endif

                    @if(isset($alertData['product_name']))
                    <div class="detail-item">
                        <div class="detail-label">产品名称:</div>
                        <div class="detail-value">{{ $alertData['product_name'] }}</div>
                    </div>
                    @endif

                    <!-- 规则信息 -->
                    @if(isset($alertRule) && $alertRule)
                    <div class="detail-item">
                        <div class="detail-label">触发规则:</div>
                        <div class="detail-value">{{ $alertRule->name }}</div>
                    </div>
                    @endif

                    <!-- 其他自定义字段 -->
                    @foreach($alertData as $key => $value)
                        @if(!in_array($key, ['title', 'message', 'created_at', 'current_price', 'previous_price', 'price_change_percentage', 'current_stock', 'stock_change', 'sku_id', 'product_name']) && !is_array($value))
                        <div class="detail-item">
                            <div class="detail-label">{{ ucfirst(str_replace('_', ' ', $key)) }}:</div>
                            <div class="detail-value">{{ $value }}</div>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- 操作建议 -->
            @if($severity === 'emergency')
            <div class="alert-message" style="border-left-color: #dc3545; background-color: #f8d7da;">
                <strong>🚨 紧急处理建议:</strong><br>
                此警报标记为紧急级别，建议立即登录系统查看详细信息并采取相应措施。
                @if(isset($alertData['price_change_percentage']) && $alertData['price_change_percentage'] < -20)
                价格下降幅度较大，可能影响利润率，请检查定价策略。
                @endif
                @if(isset($alertData['current_stock']) && $alertData['current_stock'] < 5)
                库存已降至危险水平，请及时补货以避免缺货。
                @endif
            </div>
            @elseif($severity === 'important')
            <div class="alert-message" style="border-left-color: #fd7e14; background-color: #fff3cd;">
                <strong>⚠️ 处理建议:</strong><br>
                建议在适当时间内查看此警报，评估是否需要调整相关设置或策略。
            </div>
            @endif

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="{{ url('/alert-rules') }}" class="btn">
                    🔍 查看详细信息
                </a>
                @if(isset($alertRule) && $alertRule)
                <a href="{{ url('/alert-rules/' . $alertRule->id) }}" class="btn btn-secondary">
                    ⚙️ 管理规则
                </a>
                @endif
            </div>
        </div>

        <!-- 邮件页脚 -->
        <div class="email-footer">
            <div class="footer-note">
                此邮件由系统自动发送，请勿直接回复。
            </div>
            <div>
                如有疑问，请联系系统管理员或访问帮助中心。
            </div>
            <div class="footer-links">
                <a href="{{ url('/profile/preferences') }}">通知设置</a>
                <a href="{{ url('/dashboard') }}">系统首页</a>
                <a href="{{ url('/help') }}">帮助中心</a>
            </div>
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                © {{ date('Y') }} 电商市场动态监测系统. 保留所有权利。
            </div>
        </div>
    </div>
</body>
</html> 
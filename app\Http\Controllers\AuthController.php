<?php

namespace App\Http\Controllers;

use App\Http\Requests\RegisterRequest;
use App\Http\Requests\LoginRequest;
use App\Models\User;
use App\Models\Role;
use App\Models\UserActionLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class AuthController extends Controller
{
    /**
     * 显示注册页面
     */
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    /**
     * 处理用户注册
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            // 验证验证码（如果启用）
            if (config('auth.captcha_enabled', false)) {
                $this->validateCaptcha($request->input('captcha'));
            }

            // 创建用户
            $user = $this->createUser($request->validated());

            // 分配默认角色
            $this->assignDefaultRole($user);

            // 发送邮箱验证邮件
            if (config('auth.email_verification', true)) {
                event(new Registered($user));
            }

            // 记录注册日志
            UserActionLog::logRegister($user->id);

            DB::commit();

            // 记录成功日志
            Log::info('User registered successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'username' => $user->username,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '注册成功！请检查您的邮箱以验证账户。',
                'data' => [
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'requires_verification' => !$user->hasVerifiedEmail(),
                ]
            ], 201);

        } catch (ValidationException $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => '注册数据验证失败。',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            DB::rollback();

            // 记录错误日志
            Log::error('User registration failed', [
                'error' => $e->getMessage(),
                'email' => $request->input('email'),
                'username' => $request->input('username'),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '注册过程中发生错误，请稍后重试。',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 创建用户
     */
    private function createUser(array $data): User
    {
        return User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'username' => $data['username'],
            'phone_number' => $data['phone_number'] ?? null,
            'password' => Hash::make($data['password']),
            'status' => User::STATUS_ACTIVE,
        ]);
    }

    /**
     * 分配默认角色
     */
    private function assignDefaultRole(User $user): void
    {
        $defaultRole = Role::where('name', 'user')
                          ->where('is_active', true)
                          ->first();

        if ($defaultRole) {
            $user->roles()->attach($defaultRole->id, [
                'assigned_at' => now(),
                'assigned_by' => $user->id, // 自注册
            ]);
        }
    }

    /**
     * 验证验证码
     */
    private function validateCaptcha(?string $captcha): void
    {
        if (!$captcha) {
            throw ValidationException::withMessages([
                'captcha' => ['验证码是必填项。']
            ]);
        }

        // 这里可以集成具体的验证码验证逻辑
        // 例如：Google reCAPTCHA, 图形验证码等
        
        // 示例验证逻辑（实际应用中需要替换）
        if (!$this->verifyCaptcha($captcha)) {
            throw ValidationException::withMessages([
                'captcha' => ['验证码验证失败，请重试。']
            ]);
        }
    }

    /**
     * 验证码验证逻辑（示例）
     */
    private function verifyCaptcha(string $captcha): bool
    {
        // 这里应该实现具体的验证码验证逻辑
        // 例如：验证session中的验证码、调用第三方API等
        
        // 目前返回true，实际应用中需要实现
        return true;
    }

    /**
     * 检查用户名可用性
     */
    public function checkUsername(Request $request): JsonResponse
    {
        $request->validate([
            'username' => 'required|string|min:3|max:50'
        ]);

        $username = strtolower(trim($request->input('username')));
        $exists = User::where('username', $username)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? '用户名已被使用' : '用户名可用'
        ]);
    }

    /**
     * 检查邮箱可用性
     */
    public function checkEmail(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $email = strtolower(trim($request->input('email')));
        $exists = User::where('email', $email)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? '邮箱已被注册' : '邮箱可用'
        ]);
    }

    /**
     * 发送验证码到手机
     */
    public function sendPhoneVerification(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|regex:/^1[3-9]\d{9}$/'
        ]);

        $phoneNumber = $request->input('phone_number');

        try {
            // 检查手机号是否已被注册
            if (User::where('phone_number', $phoneNumber)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => '该手机号已被注册'
                ], 422);
            }

            // 生成验证码
            $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

            // 存储验证码到缓存（5分钟有效期）
            cache()->put("phone_verification_{$phoneNumber}", $code, 300);

            // 这里应该调用短信服务发送验证码
            // 示例：$this->sendSMS($phoneNumber, $code);

            Log::info('Phone verification code sent', [
                'phone' => $phoneNumber,
                'code' => $code, // 生产环境中不应记录验证码
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => '验证码已发送，请注意查收'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send phone verification', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '验证码发送失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 验证手机验证码
     */
    public function verifyPhoneCode(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|size:6'
        ]);

        $phoneNumber = $request->input('phone_number');
        $code = $request->input('code');

        $cachedCode = cache()->get("phone_verification_{$phoneNumber}");

        if (!$cachedCode || $cachedCode !== $code) {
            return response()->json([
                'success' => false,
                'message' => '验证码无效或已过期'
            ], 422);
        }

        // 清除验证码
        cache()->forget("phone_verification_{$phoneNumber}");

        return response()->json([
            'success' => true,
            'message' => '验证码验证成功'
        ]);
    }

    /**
     * 显示登录页面
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * 处理用户登录
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();
        $loginField = filter_var($credentials['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        
        $authCredentials = [
            $loginField => $credentials['login'],
            'password' => $credentials['password']
        ];

        if (Auth::attempt($authCredentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            
            $user = Auth::user();

            UserActionLog::logLogin($user->id, $request->ip(), $request->userAgent());
            
            Log::info('User logged in successfully', [
                'user_id' => $user->id,
                'username' => $user->username,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '登录成功！',
                'data' => [
                    'redirect_url' => session()->pull('url.intended', route('dashboard'))
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => '登录失败。',
            'errors' => [
                'login' => ['用户名/邮箱或密码错误。']
            ]
        ], 401);
    }

    /**
     * OTP登录 - 发送验证码
     */
    public function sendOtpLogin(Request $request): JsonResponse
    {
        $request->validate([
            'contact' => 'required|string'
        ]);

        $contact = $request->input('contact');
        $contactType = $this->getContactType($contact);

        try {
            // 查找用户
            $user = $this->findUserByContact($contact, $contactType);
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => '用户不存在'
                ], 404);
            }

            if ($user->status !== User::STATUS_ACTIVE) {
                return response()->json([
                    'success' => false,
                    'message' => '账户已被禁用'
                ], 423);
            }

            // 检查OTP发送频率限制
            $rateLimitKey = "otp_rate_limit_{$contact}";
            if (Cache::has($rateLimitKey)) {
                return response()->json([
                    'success' => false,
                    'message' => '发送太频繁，请等待后重试'
                ], 429);
            }

            // 生成OTP
            $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            
            // 存储OTP（5分钟有效）
            Cache::put("otp_login_{$contact}", $otp, 300);
            
            // 设置发送频率限制（60秒）
            Cache::put($rateLimitKey, true, 60);

            // 发送OTP
            if ($contactType === 'email') {
                $this->sendOtpEmail($user->email, $otp);
            } else {
                $this->sendOtpSms($user->phone_number, $otp);
            }

            Log::info('OTP login code sent', [
                'user_id' => $user->id,
                'contact' => $contact,
                'contact_type' => $contactType,
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => '验证码已发送',
                'contact_type' => $contactType
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send OTP login code', [
                'contact' => $contact,
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '验证码发送失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * OTP登录 - 验证并登录
     */
    public function verifyOtpLogin(Request $request): JsonResponse
    {
        $request->validate([
            'contact' => 'required|string',
            'otp' => 'required|string|size:6',
            'remember' => 'boolean'
        ]);

        $contact = $request->input('contact');
        $otp = $request->input('otp');
        $remember = $request->boolean('remember');

        try {
            // 验证OTP
            $cachedOtp = Cache::get("otp_login_{$contact}");
            
            if (!$cachedOtp || $cachedOtp !== $otp) {
                return response()->json([
                    'success' => false,
                    'message' => '验证码无效或已过期'
                ], 422);
            }

            // 查找用户
            $contactType = $this->getContactType($contact);
            $user = $this->findUserByContact($contact, $contactType);

            if (!$user || $user->status !== User::STATUS_ACTIVE) {
                return response()->json([
                    'success' => false,
                    'message' => '用户不存在或已被禁用'
                ], 404);
            }

            // 清除OTP
            Cache::forget("otp_login_{$contact}");

            // 登录用户
            Auth::login($user, $remember);

            // 记录登录日志
            UserActionLog::logLogin($user->id, $request->ip(), 'otp');

            // 更新最后登录时间
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip()
            ]);

            // 重新生成会话ID
            $request->session()->regenerate();

            Log::info('User logged in via OTP', [
                'user_id' => $user->id,
                'contact' => $contact,
                'contact_type' => $contactType,
                'ip' => $request->ip(),
                'remember' => $remember
            ]);

            return response()->json([
                'success' => true,
                'message' => '登录成功！',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'username' => $user->username,
                        'email' => $user->email,
                        'phone_number' => $user->phone_number,
                        'avatar' => $user->avatar,
                        'roles' => $user->roles->pluck('name')
                    ],
                    'token' => $user->createToken('auth-token')->plainTextToken ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('OTP login verification failed', [
                'contact' => $contact,
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '登录失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 用户注销
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            if ($user) {
                // 记录注销日志
                UserActionLog::logLogout($user->id, $request->ip());
                
                Log::info('User logged out', [
                    'user_id' => $user->id,
                    'ip' => $request->ip()
                ]);
            }

            // 注销用户
            Auth::logout();

            // 清除会话
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return response()->json([
                'success' => true,
                'message' => '注销成功'
            ]);

        } catch (\Exception $e) {
            Log::error('Logout failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '注销失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 忘记密码 - 发送重置链接
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ]);

        try {
            $status = Password::sendResetLink(
                $request->only('email')
            );

            if ($status === Password::RESET_LINK_SENT) {
                Log::info('Password reset link sent', [
                    'email' => $request->input('email'),
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '密码重置链接已发送到您的邮箱'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => '发送密码重置链接失败'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Failed to send password reset link', [
                'email' => $request->input('email'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '发送密码重置链接失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 重置密码
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        try {
            $status = Password::reset(
                $request->only('email', 'password', 'password_confirmation', 'token'),
                function ($user, $password) {
                    $user->forceFill([
                        'password' => Hash::make($password)
                    ])->save();
                }
            );

            if ($status === Password::PASSWORD_RESET) {
                Log::info('Password reset successfully', [
                    'email' => $request->input('email'),
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '密码重置成功'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => '密码重置失败，令牌无效或已过期'
            ], 422);

        } catch (\Exception $e) {
            Log::error('Password reset failed', [
                'email' => $request->input('email'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'message' => '密码重置失败，请稍后重试'
            ], 500);
        }
    }

    /**
     * 获取登录字段类型
     */
    private function getLoginField(string $login): string
    {
        if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        } elseif (preg_match('/^1[3-9]\d{9}$/', $login)) {
            return 'phone_number';
        } else {
            return 'username';
        }
    }

    /**
     * 获取联系方式类型
     */
    private function getContactType(string $contact): string
    {
        if (filter_var($contact, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        } elseif (preg_match('/^1[3-9]\d{9}$/', $contact)) {
            return 'phone';
        } else {
            return 'username';
        }
    }

    /**
     * 根据联系方式查找用户
     */
    private function findUserByContact(string $contact, string $type): ?User
    {
        switch ($type) {
            case 'email':
                return User::where('email', $contact)->first();
            case 'phone':
                return User::where('phone_number', $contact)->first();
            case 'username':
                return User::where('username', $contact)->first();
            default:
                return null;
        }
    }

    /**
     * 检查账户锁定状态
     */
    private function checkAccountLockout(string $login): void
    {
        $key = $this->throttleKey($login);
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= 5) {
            $lockoutTime = Cache::get($key . '_lockout');
            if ($lockoutTime && Carbon::now()->lt($lockoutTime)) {
                $remaining = Carbon::now()->diffInMinutes($lockoutTime);
                
                event(new Lockout($login));
                
                throw ValidationException::withMessages([
                    'login' => ["账户已被锁定，请 {$remaining} 分钟后重试。"]
                ]);
            }
        }
    }

    /**
     * 是否需要验证码
     */
    private function shouldVerifyCaptcha(string $login): bool
    {
        // 强制禁用验证码以进行本地开发调试
        return false;

        // 原始逻辑：
        // return Cache::has($this->throttleKey($login).':lockout') ||
        //        (config('auth.captcha_after_failed_logins') > 0 &&
        //         Cache::get($this->throttleKey($login), 0) >= config('auth.captcha_after_failed_logins'));
    }

    /**
     * 增加登录失败次数
     */
    private function incrementLoginAttempts(string $login): void
    {
        $key = $this->throttleKey($login);
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, 900); // 15分钟过期
        
        if ($attempts >= 5) {
            // 锁定15分钟
            Cache::put($key . '_lockout', Carbon::now()->addMinutes(15), 900);
        }
    }

    /**
     * 清除登录失败记录
     */
    private function clearLoginAttempts(string $login): void
    {
        $key = $this->throttleKey($login);
        Cache::forget($key);
        Cache::forget($key . '_lockout');
    }

    /**
     * 获取限流键名
     */
    private function throttleKey(string $login): string
    {
        return 'login_attempts_' . Str::lower($login);
    }

    /**
     * 发送OTP邮件
     */
    private function sendOtpEmail(string $email, string $otp): void
    {
        // 这里应该实现邮件发送逻辑
        // 示例：Mail::to($email)->send(new OtpMail($otp));
        
        Log::info('OTP email sent', [
            'email' => $email,
            'otp' => $otp // 生产环境中不应记录OTP
        ]);
    }

    /**
     * 发送OTP短信
     */
    private function sendOtpSms(string $phone, string $otp): void
    {
        // 这里应该实现短信发送逻辑
        // 示例：SMS::send($phone, "您的验证码是：{$otp}，5分钟内有效。");
        
        Log::info('OTP SMS sent', [
            'phone' => $phone,
            'otp' => $otp // 生产环境中不应记录OTP
        ]);
    }
} 
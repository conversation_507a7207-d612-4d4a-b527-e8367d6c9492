<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>偏好设置 - 电商市场动态监测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            background: #f8f9fa;
            min-height: calc(100vh - 100px);
            border-radius: 10px;
        }
        .nav-link {
            color: #6c757d;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: #667eea;
            color: white;
        }
        .form-floating label {
            color: #6c757d;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 15px;
        }
        .preference-section {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .preference-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #667eea;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .theme-preview {
            width: 60px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .theme-preview.active {
            border-color: #667eea;
        }
        .theme-light {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }
        .theme-dark {
            background: linear-gradient(135deg, #343a40 0%, #212529 100%);
        }
        .theme-auto {
            background: linear-gradient(135deg, #ffffff 0%, #ffffff 50%, #343a40 50%, #343a40 100%);
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="bi bi-graph-up"></i> 电商监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">
                    <i class="bi bi-speedometer2"></i> 管理后台
                </a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <img src="{{ auth()->user()->avatar_url }}" alt="头像" class="rounded-circle" width="24" height="24">
                        {{ auth()->user()->display_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('profile.index') }}">个人中心</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="{{ route('profile.index') }}">
                            <i class="bi bi-house me-2"></i>个人中心
                        </a>
                        <a class="nav-link" href="{{ route('profile.edit') }}">
                            <i class="bi bi-person-gear me-2"></i>个人资料
                        </a>
                        <a class="nav-link" href="{{ route('profile.change-password') }}">
                            <i class="bi bi-shield-lock me-2"></i>修改密码
                        </a>
                        <a class="nav-link active" href="{{ route('profile.preferences') }}">
                            <i class="bi bi-gear me-2"></i>偏好设置
                        </a>
                        <a class="nav-link" href="{{ route('profile.activity-log') }}">
                            <i class="bi bi-clock-history me-2"></i>活动日志
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-white border-0 pb-0">
                        <h4 class="mb-0">
                            <i class="bi bi-gear me-2"></i>偏好设置
                        </h4>
                        <p class="text-muted mb-0">个性化您的使用体验</p>
                    </div>
                    <div class="card-body pt-4">
                        <form action="{{ route('profile.preferences.update') }}" method="POST">
                            @csrf
                            @method('PUT')

                            <!-- 界面设置 -->
                            <div class="preference-section">
                                <h5 class="mb-3">
                                    <i class="bi bi-palette me-2"></i>界面设置
                                </h5>

                                <!-- 主题选择 -->
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label class="form-label">主题</label>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="d-flex gap-3">
                                            <div class="text-center">
                                                <div class="theme-preview theme-light {{ $preferences['theme'] == 'light' ? 'active' : '' }}" 
                                                     onclick="selectTheme('light')"></div>
                                                <small class="d-block mt-1">明亮</small>
                                                <input type="radio" name="theme" value="light" 
                                                       {{ $preferences['theme'] == 'light' ? 'checked' : '' }} style="display: none;">
                                            </div>
                                            <div class="text-center">
                                                <div class="theme-preview theme-dark {{ $preferences['theme'] == 'dark' ? 'active' : '' }}" 
                                                     onclick="selectTheme('dark')"></div>
                                                <small class="d-block mt-1">深色</small>
                                                <input type="radio" name="theme" value="dark" 
                                                       {{ $preferences['theme'] == 'dark' ? 'checked' : '' }} style="display: none;">
                                            </div>
                                            <div class="text-center">
                                                <div class="theme-preview theme-auto {{ $preferences['theme'] == 'auto' ? 'active' : '' }}" 
                                                     onclick="selectTheme('auto')"></div>
                                                <small class="d-block mt-1">自动</small>
                                                <input type="radio" name="theme" value="auto" 
                                                       {{ $preferences['theme'] == 'auto' ? 'checked' : '' }} style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 语言选择 -->
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="language" class="form-label">语言</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="language" name="language">
                                            <option value="zh-CN" {{ $preferences['language'] == 'zh-CN' ? 'selected' : '' }}>简体中文</option>
                                            <option value="zh-TW" {{ $preferences['language'] == 'zh-TW' ? 'selected' : '' }}>繁體中文</option>
                                            <option value="en-US" {{ $preferences['language'] == 'en-US' ? 'selected' : '' }}>English</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 时区选择 -->
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="timezone" class="form-label">时区</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Asia/Shanghai" {{ $preferences['timezone'] == 'Asia/Shanghai' ? 'selected' : '' }}>中国标准时间 (UTC+8)</option>
                                            <option value="Asia/Hong_Kong" {{ $preferences['timezone'] == 'Asia/Hong_Kong' ? 'selected' : '' }}>香港时间 (UTC+8)</option>
                                            <option value="Asia/Taipei" {{ $preferences['timezone'] == 'Asia/Taipei' ? 'selected' : '' }}>台北时间 (UTC+8)</option>
                                            <option value="UTC" {{ $preferences['timezone'] == 'UTC' ? 'selected' : '' }}>协调世界时 (UTC)</option>
                                            <option value="America/New_York" {{ $preferences['timezone'] == 'America/New_York' ? 'selected' : '' }}>美国东部时间 (UTC-5)</option>
                                            <option value="Europe/London" {{ $preferences['timezone'] == 'Europe/London' ? 'selected' : '' }}>伦敦时间 (UTC+0)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 通知设置 -->
                            <div class="preference-section">
                                <h5 class="mb-3">
                                    <i class="bi bi-bell me-2"></i>通知设置
                                </h5>

                                <!-- 基本通知设置 (保持兼容性) -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>邮件通知</strong>
                                                <div class="text-muted small">接收重要更新和警报邮件</div>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" name="notifications[email]" value="1" 
                                                       {{ $preferences['notifications']['email'] ? 'checked' : '' }}>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>短信通知</strong>
                                                <div class="text-muted small">接收紧急警报短信</div>
                                            </div>
                                            <label class="switch">
                                                <input type="checkbox" name="notifications[sms]" value="1" 
                                                       {{ $preferences['notifications']['sms'] ? 'checked' : '' }}>
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 详细通知偏好设置 -->
                                <div class="card border-0 bg-light mb-4">
                                    <div class="card-header bg-transparent border-0">
                                        <h6 class="mb-0">
                                            <i class="bi bi-gear-fill me-2"></i>详细通知设置
                                        </h6>
                                        <small class="text-muted">根据警报严重程度和通知类型进行个性化设置</small>
                                    </div>
                                    <div class="card-body">
                                        <!-- 通知渠道总开关 -->
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                                    <div>
                                                        <strong class="text-primary">
                                                            <i class="bi bi-envelope me-2"></i>邮件通知
                                                        </strong>
                                                        <div class="text-muted small">启用邮件通知功能</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" name="notification_preferences[email_enabled]" value="1" 
                                                               {{ $notificationPreferences['email_enabled'] ? 'checked' : '' }}
                                                               onchange="toggleEmailNotifications(this.checked)">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                                    <div>
                                                        <strong class="text-success">
                                                            <i class="bi bi-bell me-2"></i>站内通知
                                                        </strong>
                                                        <div class="text-muted small">启用浏览器内通知</div>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" name="notification_preferences[in_site_enabled]" value="1" 
                                                               {{ $notificationPreferences['in_site_enabled'] ? 'checked' : '' }}
                                                               onchange="toggleInSiteNotifications(this.checked)">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 按严重程度的通知设置 -->
                                        <div class="notification-levels">
                                            <!-- 紧急通知设置 -->
                                            <div class="level-section border-bottom pb-3 mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-danger me-2">紧急</span>
                                                    <strong>紧急警报通知</strong>
                                                    <small class="text-muted ms-2">系统故障、安全问题等需要立即处理的警报</small>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input email-notification" type="checkbox" 
                                                                   name="notification_preferences[emergency_email]" value="1"
                                                                   {{ $notificationPreferences['emergency_email'] ? 'checked' : '' }}
                                                                   id="emergency_email">
                                                            <label class="form-check-label" for="emergency_email">
                                                                邮件通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input insite-notification" type="checkbox" 
                                                                   name="notification_preferences[emergency_in_site]" value="1"
                                                                   {{ $notificationPreferences['emergency_in_site'] ? 'checked' : '' }}
                                                                   id="emergency_in_site">
                                                            <label class="form-check-label" for="emergency_in_site">
                                                                站内通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <select class="form-select form-select-sm" 
                                                                name="notification_preferences[frequency_control][emergency]">
                                                            <option value="immediate" {{ $notificationPreferences['frequency_control']['emergency'] == 'immediate' ? 'selected' : '' }}>立即通知</option>
                                                            <option value="hourly_digest" {{ $notificationPreferences['frequency_control']['emergency'] == 'hourly_digest' ? 'selected' : '' }}>每小时摘要</option>
                                                            <option value="daily_digest" {{ $notificationPreferences['frequency_control']['emergency'] == 'daily_digest' ? 'selected' : '' }}>每日摘要</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 重要通知设置 -->
                                            <div class="level-section border-bottom pb-3 mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-warning me-2">重要</span>
                                                    <strong>重要警报通知</strong>
                                                    <small class="text-muted ms-2">价格异常变化、监控任务失败等重要问题</small>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input email-notification" type="checkbox" 
                                                                   name="notification_preferences[important_email]" value="1"
                                                                   {{ $notificationPreferences['important_email'] ? 'checked' : '' }}
                                                                   id="important_email">
                                                            <label class="form-check-label" for="important_email">
                                                                邮件通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input insite-notification" type="checkbox" 
                                                                   name="notification_preferences[important_in_site]" value="1"
                                                                   {{ $notificationPreferences['important_in_site'] ? 'checked' : '' }}
                                                                   id="important_in_site">
                                                            <label class="form-check-label" for="important_in_site">
                                                                站内通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <select class="form-select form-select-sm" 
                                                                name="notification_preferences[frequency_control][important]">
                                                            <option value="immediate" {{ $notificationPreferences['frequency_control']['important'] == 'immediate' ? 'selected' : '' }}>立即通知</option>
                                                            <option value="hourly_digest" {{ $notificationPreferences['frequency_control']['important'] == 'hourly_digest' ? 'selected' : '' }}>每小时摘要</option>
                                                            <option value="daily_digest" {{ $notificationPreferences['frequency_control']['important'] == 'daily_digest' ? 'selected' : '' }}>每日摘要</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 一般通知设置 -->
                                            <div class="level-section">
                                                <div class="d-flex align-items-center mb-2">
                                                    <span class="badge bg-info me-2">一般</span>
                                                    <strong>一般通知</strong>
                                                    <small class="text-muted ms-2">系统状态更新、常规信息通知等</small>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input email-notification" type="checkbox" 
                                                                   name="notification_preferences[general_email]" value="1"
                                                                   {{ $notificationPreferences['general_email'] ? 'checked' : '' }}
                                                                   id="general_email">
                                                            <label class="form-check-label" for="general_email">
                                                                邮件通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input insite-notification" type="checkbox" 
                                                                   name="notification_preferences[general_in_site]" value="1"
                                                                   {{ $notificationPreferences['general_in_site'] ? 'checked' : '' }}
                                                                   id="general_in_site">
                                                            <label class="form-check-label" for="general_in_site">
                                                                站内通知
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <select class="form-select form-select-sm" 
                                                                name="notification_preferences[frequency_control][general]">
                                                            <option value="immediate" {{ $notificationPreferences['frequency_control']['general'] == 'immediate' ? 'selected' : '' }}>立即通知</option>
                                                            <option value="hourly_digest" {{ $notificationPreferences['frequency_control']['general'] == 'hourly_digest' ? 'selected' : '' }}>每小时摘要</option>
                                                            <option value="daily_digest" {{ $notificationPreferences['frequency_control']['general'] == 'daily_digest' ? 'selected' : '' }}>每日摘要</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 快速设置按钮 -->
                                        <div class="d-flex gap-2 mt-4 pt-3 border-top">
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="enableAllNotifications()">
                                                <i class="bi bi-check-all me-1"></i>全部启用
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="disableAllNotifications()">
                                                <i class="bi bi-x-circle me-1"></i>全部禁用
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="setRecommendedSettings()">
                                                <i class="bi bi-star me-1"></i>推荐设置
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据显示设置 -->
                            <div class="preference-section">
                                <h5 class="mb-3">
                                    <i class="bi bi-table me-2"></i>数据显示设置
                                </h5>

                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="items_per_page" class="form-label">每页显示条数</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="items_per_page" name="data_display[items_per_page]">
                                            <option value="10" {{ $preferences['data_display']['items_per_page'] == 10 ? 'selected' : '' }}>10条</option>
                                            <option value="25" {{ $preferences['data_display']['items_per_page'] == 25 ? 'selected' : '' }}>25条</option>
                                            <option value="50" {{ $preferences['data_display']['items_per_page'] == 50 ? 'selected' : '' }}>50条</option>
                                            <option value="100" {{ $preferences['data_display']['items_per_page'] == 100 ? 'selected' : '' }}>100条</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="date_format" class="form-label">日期格式</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="date_format" name="data_display[date_format]">
                                            <option value="Y-m-d" {{ $preferences['data_display']['date_format'] == 'Y-m-d' ? 'selected' : '' }}>2024-01-28</option>
                                            <option value="m/d/Y" {{ $preferences['data_display']['date_format'] == 'm/d/Y' ? 'selected' : '' }}>01/28/2024</option>
                                            <option value="d/m/Y" {{ $preferences['data_display']['date_format'] == 'd/m/Y' ? 'selected' : '' }}>28/01/2024</option>
                                            <option value="Y年m月d日" {{ $preferences['data_display']['date_format'] == 'Y年m月d日' ? 'selected' : '' }}>2024年01月28日</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="time_format" class="form-label">时间格式</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="time_format" name="data_display[time_format]">
                                            <option value="H:i:s" {{ $preferences['data_display']['time_format'] == 'H:i:s' ? 'selected' : '' }}>24小时制 (14:30:25)</option>
                                            <option value="g:i:s A" {{ $preferences['data_display']['time_format'] == 'g:i:s A' ? 'selected' : '' }}>12小时制 (2:30:25 PM)</option>
                                            <option value="H:i" {{ $preferences['data_display']['time_format'] == 'H:i' ? 'selected' : '' }}>24小时制简化 (14:30)</option>
                                            <option value="g:i A" {{ $preferences['data_display']['time_format'] == 'g:i A' ? 'selected' : '' }}>12小时制简化 (2:30 PM)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ route('profile.index') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-left me-2"></i>返回
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg me-2"></i>保存设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    @if(session('success'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle me-2"></i>
                <strong class="me-auto">成功</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('success') }}
            </div>
        </div>
    </div>
    @endif

    @if(session('error'))
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong class="me-auto">错误</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                {{ session('error') }}
            </div>
        </div>
    </div>
    @endif

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 主题选择功能
        function selectTheme(theme) {
            // 移除所有激活状态
            document.querySelectorAll('.theme-preview').forEach(preview => {
                preview.classList.remove('active');
            });
            
            // 激活选中的主题
            document.querySelector(`.theme-${theme}`).classList.add('active');
            
            // 更新隐藏的radio按钮
            document.querySelector(`input[name="theme"][value="${theme}"]`).checked = true;
        }

        // 通知设置管理功能
        function toggleEmailNotifications(enabled) {
            const emailCheckboxes = document.querySelectorAll('.email-notification');
            emailCheckboxes.forEach(checkbox => {
                checkbox.disabled = !enabled;
                if (!enabled) {
                    checkbox.checked = false;
                }
            });
        }

        function toggleInSiteNotifications(enabled) {
            const inSiteCheckboxes = document.querySelectorAll('.insite-notification');
            inSiteCheckboxes.forEach(checkbox => {
                checkbox.disabled = !enabled;
                if (!enabled) {
                    checkbox.checked = false;
                }
            });
        }

        function enableAllNotifications() {
            // 启用所有总开关
            document.querySelector('input[name="notification_preferences[email_enabled]"]').checked = true;
            document.querySelector('input[name="notification_preferences[in_site_enabled]"]').checked = true;
            
            // 启用所有通知类型
            const allCheckboxes = document.querySelectorAll('.email-notification, .insite-notification');
            allCheckboxes.forEach(checkbox => {
                checkbox.disabled = false;
                checkbox.checked = true;
            });
            
            // 设置所有频率为立即通知
            const frequencySelects = document.querySelectorAll('select[name*="frequency_control"]');
            frequencySelects.forEach(select => {
                select.value = 'immediate';
            });
        }

        function disableAllNotifications() {
            // 禁用所有总开关
            document.querySelector('input[name="notification_preferences[email_enabled]"]').checked = false;
            document.querySelector('input[name="notification_preferences[in_site_enabled]"]').checked = false;
            
            // 禁用所有通知类型
            const allCheckboxes = document.querySelectorAll('.email-notification, .insite-notification');
            allCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.disabled = true;
            });
        }

        function setRecommendedSettings() {
            // 启用邮件和站内通知
            document.querySelector('input[name="notification_preferences[email_enabled]"]').checked = true;
            document.querySelector('input[name="notification_preferences[in_site_enabled]"]').checked = true;
            
            // 启用所有邮件和站内通知选项
            const allCheckboxes = document.querySelectorAll('.email-notification, .insite-notification');
            allCheckboxes.forEach(checkbox => {
                checkbox.disabled = false;
                checkbox.checked = true;
            });
            
            // 设置推荐的频率控制
            document.querySelector('select[name="notification_preferences[frequency_control][emergency]"]').value = 'immediate';
            document.querySelector('select[name="notification_preferences[frequency_control][important]"]').value = 'immediate';
            document.querySelector('select[name="notification_preferences[frequency_control][general]"]').value = 'daily_digest';
            
            // 一般通知的邮件通知默认关闭（推荐设置）
            document.querySelector('#general_email').checked = false;
        }

        // 页面加载时初始化状态
        document.addEventListener('DOMContentLoaded', function() {
            // 根据总开关状态更新子选项状态
            const emailEnabled = document.querySelector('input[name="notification_preferences[email_enabled]"]').checked;
            const inSiteEnabled = document.querySelector('input[name="notification_preferences[in_site_enabled]"]').checked;
            
            toggleEmailNotifications(emailEnabled);
            toggleInSiteNotifications(inSiteEnabled);
        });

        // 自动隐藏Toast消息
        setTimeout(function() {
            var toasts = document.querySelectorAll('.toast');
            toasts.forEach(function(toast) {
                var bsToast = new bootstrap.Toast(toast);
                bsToast.hide();
            });
        }, 5000);
    </script>
</body>
</html> 
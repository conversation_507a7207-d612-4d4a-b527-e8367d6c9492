<?php

namespace App\Http\Controllers;

use App\Models\TaskGroup;
use App\Models\MonitorTask;
use App\Services\TaskGroupService;
use App\Services\TaskGroupReportService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class TaskGroupController extends Controller
{
    protected $taskGroupService;
    protected $reportService;

    public function __construct(TaskGroupService $taskGroupService, TaskGroupReportService $reportService)
    {
        $this->taskGroupService = $taskGroupService;
        $this->reportService = $reportService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = TaskGroup::forUser(Auth::id())
                          ->with(['monitorTasks:id,task_group_id,task_name,status,is_enabled'])
                          ->ordered();

        if ($request->filled('active_only')) {
            $query->active();
        }

        $groups = $query->get()->map(function ($group) {
            return [
                'id' => $group->id,
                'name' => $group->name,
                'description' => $group->description,
                'color' => $group->color,
                'is_active' => $group->is_active,
                'sort_order' => $group->sort_order,
                'stats' => $group->stats,
                'created_at' => $group->created_at,
                'tasks_count' => $group->monitorTasks->count(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $groups,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $validated['user_id'] = Auth::id();
        $validated['color'] = $validated['color'] ?? '#007bff';
        $validated['sort_order'] = $validated['sort_order'] ?? 0;

        $group = TaskGroup::create($validated);

        return response()->json([
            'success' => true,
            'data' => $group,
            'message' => '任务分组创建成功',
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())
                         ->with(['monitorTasks' => function ($query) {
                             $query->select('id', 'task_group_id', 'task_name', 'status', 'is_enabled', 'last_collected_at');
                         }])
                         ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'group' => $group,
                'stats' => $group->stats,
                'tasks' => $group->monitorTasks,
            ],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:100',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $group->update($validated);

        return response()->json([
            'success' => true,
            'data' => $group,
            'message' => '任务分组更新成功',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        DB::transaction(function () use ($group) {
            // 将分组下的任务移出分组
            $group->monitorTasks()->update(['task_group_id' => null]);
            
            // 删除分组
            $group->delete();
        });

        return response()->json([
            'success' => true,
            'message' => '任务分组删除成功',
        ]);
    }

    /**
     * Assign tasks to a group
     */
    public function assignTasks(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'task_ids' => 'required|array',
            'task_ids.*' => 'exists:monitor_tasks,id',
        ]);

        try {
            $result = $this->taskGroupService->batchAssignTasks(
                $group, 
                $validated['task_ids'], 
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => '任务分配成功',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Remove tasks from a group
     */
    public function removeTasks(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'task_ids' => 'required|array',
            'task_ids.*' => 'exists:monitor_tasks,id',
        ]);

        DB::transaction(function () use ($validated, $group) {
            // 从分组中移除任务
            MonitorTask::whereIn('id', $validated['task_ids'])
                      ->where('task_group_id', $group->id)
                      ->where('user_id', Auth::id())
                      ->update(['task_group_id' => null]);
        });

        return response()->json([
            'success' => true,
            'message' => '任务移除成功',
        ]);
    }

    /**
     * Get unassigned tasks for user
     */
    public function unassignedTasks(): JsonResponse
    {
        $tasks = MonitorTask::where('user_id', Auth::id())
                           ->whereNull('task_group_id')
                           ->select('id', 'task_name', 'platform', 'status', 'is_enabled')
                           ->get();

        return response()->json([
            'success' => true,
            'data' => $tasks,
        ]);
    }

    /**
     * Get group statistics
     */
    public function statistics(int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $stats = [
            'basic' => $group->stats,
            'daily_collections' => $this->getDailyCollectionStats($group),
            'task_status_distribution' => $this->getTaskStatusDistribution($group),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get daily collection statistics
     */
    private function getDailyCollectionStats(TaskGroup $group): array
    {
        // 这里可以根据需要实现详细的统计逻辑
        return [];
    }

    /**
     * Get task status distribution
     */
    private function getTaskStatusDistribution(TaskGroup $group): array
    {
        return $group->monitorTasks()
                   ->selectRaw('status, count(*) as count')
                   ->groupBy('status')
                   ->pluck('count', 'status')
                   ->toArray();
    }

    /**
     * Auto group tasks based on criteria
     */
    public function autoGroup(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'criteria' => 'required|array',
            'criteria.*.group_name' => 'required|string|max:100',
            'criteria.*.description' => 'nullable|string|max:500',
            'criteria.*.color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'criteria.*.sort_order' => 'nullable|integer|min:0',
            'criteria.*.conditions' => 'required|array',
            'criteria.*.conditions.*.field' => 'required|in:platform,status,monitor_type,frequency',
            'criteria.*.conditions.*.operator' => 'required|in:=,!=,like,in',
            'criteria.*.conditions.*.value' => 'required',
        ]);

        try {
            $results = $this->taskGroupService->autoGroupTasks(Auth::id(), $validated['criteria']);

            return response()->json([
                'success' => true,
                'message' => '自动分组完成',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get group distribution analysis
     */
    public function distributionAnalysis(): JsonResponse
    {
        $analysis = $this->taskGroupService->getGroupDistributionAnalysis(Auth::id());

        return response()->json([
            'success' => true,
            'data' => $analysis,
        ]);
    }

    /**
     * Duplicate a group
     */
    public function duplicate(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'new_name' => 'required|string|max:100',
            'copy_tasks' => 'nullable|boolean',
        ]);

        try {
            $newGroup = $this->taskGroupService->duplicateGroup(
                $group,
                $validated['new_name'],
                Auth::id(),
                $validated['copy_tasks'] ?? false
            );

            return response()->json([
                'success' => true,
                'message' => '分组复制成功',
                'data' => $newGroup,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Merge multiple groups
     */
    public function merge(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'group_ids' => 'required|array|min:2',
            'group_ids.*' => 'exists:task_groups,id',
            'target_name' => 'required|string|max:100',
        ]);

        try {
            $targetGroup = $this->taskGroupService->mergeGroups(
                $validated['group_ids'],
                $validated['target_name'],
                Auth::id()
            );

            return response()->json([
                'success' => true,
                'message' => '分组合并成功',
                'data' => $targetGroup,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * 获取任务组详细报告
     */
    public function getReport(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'period' => 'nullable|in:7d,30d,90d,1y',
        ]);

        try {
            // 根据period参数设置默认日期范围
            $options = [];
            if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
                $options['start_date'] = Carbon::parse($validated['start_date']);
                $options['end_date'] = Carbon::parse($validated['end_date']);
            } elseif (!empty($validated['period'])) {
                $days = [
                    '7d' => 7,
                    '30d' => 30,
                    '90d' => 90,
                    '1y' => 365
                ][$validated['period']];
                
                $options['start_date'] = Carbon::now()->subDays($days);
                $options['end_date'] = Carbon::now();
            }

            $report = $this->reportService->getGroupReport($id, $options);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成报告时发生错误: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取多个任务组的对比报告
     */
    public function getComparisonReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'group_ids' => 'required|array|min:2|max:5',
            'group_ids.*' => 'exists:task_groups,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'period' => 'nullable|in:7d,30d,90d,1y',
        ]);

        // 验证用户对所有分组的访问权限
        $userGroups = TaskGroup::forUser(Auth::id())
                              ->whereIn('id', $validated['group_ids'])
                              ->pluck('id')
                              ->toArray();

        if (count($userGroups) !== count($validated['group_ids'])) {
            return response()->json([
                'success' => false,
                'message' => '您没有权限访问某些分组',
            ], 403);
        }

        try {
            // 设置日期范围选项
            $options = [];
            if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
                $options['start_date'] = Carbon::parse($validated['start_date']);
                $options['end_date'] = Carbon::parse($validated['end_date']);
            } elseif (!empty($validated['period'])) {
                $days = [
                    '7d' => 7,
                    '30d' => 30,
                    '90d' => 90,
                    '1y' => 365
                ][$validated['period']];
                
                $options['start_date'] = Carbon::now()->subDays($days);
                $options['end_date'] = Carbon::now();
            }

            $report = $this->reportService->getComparisonReport($validated['group_ids'], $options);

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成对比报告时发生错误: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取任务组报告摘要
     */
    public function getReportSummary(int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        try {
            // 获取最近7天的快速摘要
            $options = [
                'start_date' => Carbon::now()->subDays(7),
                'end_date' => Carbon::now()
            ];

            $fullReport = $this->reportService->getGroupReport($id, $options);

            // 提取摘要信息
            $summary = [
                'group_info' => $fullReport['group_info'],
                'health_score' => $fullReport['task_overview']['health_score'],
                'total_tasks' => $fullReport['task_overview']['total_tasks'],
                'active_tasks' => $fullReport['task_overview']['active_tasks'],
                'total_alerts' => $fullReport['alert_summary']['alert_statistics']['total_alerts'],
                'unread_alerts' => $fullReport['alert_summary']['alert_statistics']['unread_alerts'],
                'success_rate' => $fullReport['performance_metrics']['collection_stats']['success_rate'],
                'avg_price' => $fullReport['price_analytics']['price_statistics']['average_price'],
                'period' => $fullReport['period'],
                'generated_at' => $fullReport['generated_at']
            ];

            return response()->json([
                'success' => true,
                'data' => $summary,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成报告摘要时发生错误: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 导出报告数据
     */
    public function exportReport(Request $request, int $id): JsonResponse
    {
        $group = TaskGroup::forUser(Auth::id())->findOrFail($id);

        $validated = $request->validate([
            'format' => 'required|in:json,csv,excel',
            'sections' => 'nullable|array',
            'sections.*' => 'in:group_info,task_overview,performance_metrics,price_analytics,alert_summary,trend_data',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            $options = [];
            if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
                $options['start_date'] = Carbon::parse($validated['start_date']);
                $options['end_date'] = Carbon::parse($validated['end_date']);
            }

            $report = $this->reportService->getGroupReport($id, $options);

            // 如果指定了特定部分，只返回这些部分
            if (!empty($validated['sections'])) {
                $filteredReport = [];
                foreach ($validated['sections'] as $section) {
                    if (isset($report[$section])) {
                        $filteredReport[$section] = $report[$section];
                    }
                }
                $filteredReport['generated_at'] = $report['generated_at'];
                $filteredReport['period'] = $report['period'];
                $report = $filteredReport;
            }

            // 根据格式返回数据
            switch ($validated['format']) {
                case 'json':
                    return response()->json([
                        'success' => true,
                        'data' => $report,
                        'meta' => [
                            'export_format' => 'json',
                            'exported_at' => Carbon::now(),
                        ]
                    ]);

                case 'csv':
                case 'excel':
                    // 这里可以实现CSV/Excel导出逻辑
                    // 目前返回JSON格式的数据，前端可以处理转换
                    return response()->json([
                        'success' => true,
                        'data' => $report,
                        'meta' => [
                            'export_format' => $validated['format'],
                            'exported_at' => Carbon::now(),
                            'note' => '请在前端处理' . strtoupper($validated['format']) . '格式转换'
                        ]
                    ]);

                default:
                    return response()->json([
                        'success' => false,
                        'message' => '该导出格式暂不支持',
                    ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出报告时发生错误: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * 显示任务分组管理页面
     */
    public function indexPage()
    {
        return view('task-groups.index');
    }

    /**
     * 显示任务分组报告页面
     */
    public function reportPage(TaskGroup $taskGroup)
    {
        // 验证用户权限
        if ($taskGroup->user_id !== Auth::id()) {
            abort(403, '您没有权限访问此分组');
        }

        return view('task-groups.report', compact('taskGroup'));
    }
}

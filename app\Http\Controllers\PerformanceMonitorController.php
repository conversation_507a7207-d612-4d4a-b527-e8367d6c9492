<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\PerformanceMonitoringService;
use Illuminate\Support\Facades\Log;

class PerformanceMonitorController extends Controller
{
    protected $performanceService;

    public function __construct(PerformanceMonitoringService $performanceService)
    {
        $this->performanceService = $performanceService;
    }

    /**
     * 获取任务性能统计总览
     */
    public function getTaskPerformanceStats(): JsonResponse
    {
        try {
            $stats = $this->performanceService->getTaskPerformanceStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => '任务性能统计获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取任务性能统计失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取任务性能统计失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取任务执行时间分析
     */
    public function getTaskExecutionTimes(): JsonResponse
    {
        try {
            $executionTimes = $this->performanceService->getTaskExecutionTimes();
            
            return response()->json([
                'success' => true,
                'data' => $executionTimes,
                'message' => '任务执行时间分析获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取任务执行时间分析失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取任务执行时间分析失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取队列深度分析
     */
    public function getQueueDepthAnalysis(): JsonResponse
    {
        try {
            $analysis = $this->performanceService->getQueueDepthAnalysis();
            
            return response()->json([
                'success' => true,
                'data' => $analysis,
                'message' => '队列深度分析获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取队列深度分析失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取队列深度分析失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取任务重试分析
     */
    public function getTaskRetryAnalysis(): JsonResponse
    {
        try {
            $retryAnalysis = $this->performanceService->getTaskRetryAnalysis();
            
            return response()->json([
                'success' => true,
                'data' => $retryAnalysis,
                'message' => '任务重试分析获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取任务重试分析失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取任务重试分析失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取平台性能比较
     */
    public function getPlatformPerformanceComparison(): JsonResponse
    {
        try {
            $comparison = $this->performanceService->getPlatformPerformanceComparison();
            
            return response()->json([
                'success' => true,
                'data' => $comparison,
                'message' => '平台性能比较获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取平台性能比较失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取平台性能比较失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取任务瓶颈分析
     */
    public function getTaskBottleneckAnalysis(): JsonResponse
    {
        try {
            $bottleneckAnalysis = $this->performanceService->getTaskBottleneckAnalysis();
            
            return response()->json([
                'success' => true,
                'data' => $bottleneckAnalysis,
                'message' => '任务瓶颈分析获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取任务瓶颈分析失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取任务瓶颈分析失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取实时任务性能快照
     */
    public function getTaskPerformanceSnapshot(): JsonResponse
    {
        try {
            $snapshot = $this->performanceService->getTaskPerformanceSnapshot();
            
            return response()->json([
                'success' => true,
                'data' => $snapshot,
                'message' => '实时任务性能快照获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取实时任务性能快照失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取实时任务性能快照失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 记录任务执行
     */
    public function recordTaskExecution(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'job_type' => 'required|string|in:data_collection_jobs,retry_jobs,batch_jobs',
                'execution_time' => 'required|numeric|min:0',
                'success' => 'boolean'
            ]);

            $this->performanceService->recordTaskExecution(
                $request->input('job_type'),
                $request->input('execution_time'),
                $request->input('success', true)
            );

            return response()->json([
                'success' => true,
                'message' => '任务执行记录成功'
            ]);
        } catch (\Exception $e) {
            Log::error('记录任务执行失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '记录任务执行失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 记录任务重试
     */
    public function recordTaskRetry(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'platform' => 'required|string',
                'attempt_number' => 'required|integer|min:1',
                'error_type' => 'nullable|string',
                'final_success' => 'boolean'
            ]);

            $this->performanceService->recordTaskRetry(
                $request->input('platform'),
                $request->input('attempt_number'),
                $request->input('error_type'),
                $request->input('final_success', false)
            );

            return response()->json([
                'success' => true,
                'message' => '任务重试记录成功'
            ]);
        } catch (\Exception $e) {
            Log::error('记录任务重试失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '记录任务重试失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 记录队列深度
     */
    public function recordQueueDepth(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'queue_name' => 'required|string',
                'depth' => 'required|integer|min:0'
            ]);

            $this->performanceService->recordQueueDepth(
                $request->input('queue_name'),
                $request->input('depth')
            );

            return response()->json([
                'success' => true,
                'message' => '队列深度记录成功'
            ]);
        } catch (\Exception $e) {
            Log::error('记录队列深度失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '记录队列深度失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 记录小时级指标
     */
    public function recordHourlyMetrics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'response_time' => 'required|numeric|min:0',
                'success' => 'boolean'
            ]);

            $this->performanceService->recordHourlyMetrics(
                $request->input('response_time'),
                $request->input('success', true)
            );

            return response()->json([
                'success' => true,
                'message' => '小时级指标记录成功'
            ]);
        } catch (\Exception $e) {
            Log::error('记录小时级指标失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '记录小时级指标失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取性能监控仪表板数据
     */
    public function getDashboardData(): JsonResponse
    {
        try {
            $dashboardData = [
                'snapshot' => $this->performanceService->getTaskPerformanceSnapshot(),
                'execution_times' => $this->performanceService->getTaskExecutionTimes(),
                'queue_analysis' => $this->performanceService->getQueueDepthAnalysis(),
                'retry_analysis' => $this->performanceService->getTaskRetryAnalysis(),
                'platform_comparison' => $this->performanceService->getPlatformPerformanceComparison(),
                'bottleneck_analysis' => $this->performanceService->getTaskBottleneckAnalysis(),
            ];

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
                'message' => '性能监控仪表板数据获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取性能监控仪表板数据失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取性能监控仪表板数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取性能趋势报告
     */
    public function getPerformanceTrends(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'hours' => 'nullable|integer|min:1|max:168', // 最多7天
            ]);

            $hours = $request->input('hours', 24);
            
            // 这里可以扩展为更详细的趋势分析
            $trends = [
                'period_hours' => $hours,
                'queue_depth_trend' => [],
                'response_time_trend' => [],
                'success_rate_trend' => [],
                'throughput_trend' => [],
            ];

            // 获取指定时间段的趋势数据
            for ($i = $hours - 1; $i >= 0; $i--) {
                $timestamp = time() - ($i * 3600);
                $hour = date('Y-m-d-H', $timestamp);
                
                $trends['queue_depth_trend'][] = [
                    'timestamp' => $timestamp,
                    'hour' => $hour,
                    'avg_depth' => cache("queue_depth_hourly:{$hour}", 0),
                ];
                
                $trends['response_time_trend'][] = [
                    'timestamp' => $timestamp,
                    'hour' => $hour,
                    'avg_response_time' => cache("response_time_hourly:{$hour}", 0),
                ];
                
                $trends['success_rate_trend'][] = [
                    'timestamp' => $timestamp,
                    'hour' => $hour,
                    'success_rate' => cache("success_rate_hourly:{$hour}", 100),
                ];
                
                $trends['throughput_trend'][] = [
                    'timestamp' => $timestamp,
                    'hour' => $hour,
                    'throughput' => cache("throughput_hourly:{$hour}", 0),
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $trends,
                'message' => '性能趋势报告获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取性能趋势报告失败', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => '获取性能趋势报告失败：' . $e->getMessage()
            ], 500);
        }
    }
} 
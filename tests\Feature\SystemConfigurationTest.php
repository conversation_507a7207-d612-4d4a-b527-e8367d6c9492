<?php

namespace Tests\Feature;

use App\Models\SystemConfiguration;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SystemConfigurationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建管理员角色和用户
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->roles()->attach($adminRole->id);
        
        // 运行系统配置Seeder
        $this->seed(\Database\Seeders\SystemConfigurationSeeder::class);
    }

    public function test_can_create_system_configuration()
    {
        $config = SystemConfiguration::create([
            'key' => 'test_config',
            'category' => 'test',
            'value' => 'test_value',
            'type' => 'string',
            'label' => 'Test Configuration',
            'description' => 'This is a test configuration',
        ]);

        $this->assertInstanceOf(SystemConfiguration::class, $config);
        $this->assertEquals('test_config', $config->key);
        $this->assertEquals('test_value', $config->value);
    }

    public function test_can_get_configuration_value()
    {
        $value = SystemConfiguration::getValue('system_name');
        $this->assertEquals('电商市场动态监控系统', $value);

        // 测试不存在的配置
        $defaultValue = SystemConfiguration::getValue('non_existent_key', 'default');
        $this->assertEquals('default', $defaultValue);
    }

    public function test_can_set_configuration_value()
    {
        $result = SystemConfiguration::setValue('test_key', 'test_value');
        $this->assertTrue($result);

        $value = SystemConfiguration::getValue('test_key');
        $this->assertEquals('test_value', $value);
    }

    public function test_can_get_configurations_by_category()
    {
        $generalConfigs = SystemConfiguration::getByCategory('general');
        
        $this->assertIsArray($generalConfigs);
        $this->assertArrayHasKey('system_name', $generalConfigs);
        $this->assertEquals('电商市场动态监控系统', $generalConfigs['system_name']);
    }

    public function test_can_get_public_configurations()
    {
        $publicConfigs = SystemConfiguration::getAllPublic();
        
        $this->assertIsArray($publicConfigs);
        $this->assertArrayHasKey('system_name', $publicConfigs);
        $this->assertArrayHasKey('enable_health_monitoring', $publicConfigs);
    }

    public function test_encrypted_configuration()
    {
        $config = new SystemConfiguration([
            'key' => 'secret_key',
            'category' => 'security',
            'type' => 'password',
            'label' => 'Secret Key',
            'is_encrypted' => true,
        ]);
        $config->value = 'secret_password'; // 设置值以触发加密
        $config->save();

        // 刷新模型以获取数据库中的实际值
        $config->refresh();

        // 验证数据库中的值被加密
        $rawValue = $config->getAttributes()['value'];
        $this->assertNotEquals('secret_password', $rawValue);

        // 验证通过模型访问时自动解密
        $this->assertEquals('secret_password', $config->value);
    }

    public function test_configuration_type_casting()
    {
        // 测试整数类型
        $intConfig = SystemConfiguration::create([
            'key' => 'test_int',
            'value' => '123',
            'type' => 'integer',
            'label' => 'Test Integer',
            'category' => 'test',
        ]);
        $this->assertIsInt($intConfig->value);
        $this->assertEquals(123, $intConfig->value);

        // 测试布尔类型
        $boolConfig = SystemConfiguration::create([
            'key' => 'test_bool',
            'value' => 'true',
            'type' => 'boolean',
            'label' => 'Test Boolean',
            'category' => 'test',
        ]);
        $this->assertIsBool($boolConfig->value);
        $this->assertTrue($boolConfig->value);

        // 测试JSON类型
        $jsonConfig = SystemConfiguration::create([
            'key' => 'test_json',
            'value' => '{"key": "value"}',
            'type' => 'json',
            'label' => 'Test JSON',
            'category' => 'test',
        ]);
        $this->assertIsArray($jsonConfig->value);
        $this->assertEquals(['key' => 'value'], $jsonConfig->value);
    }

    public function test_configuration_caching()
    {
        // 清除缓存
        Cache::flush();

        // 第一次访问，应该从数据库获取
        $value1 = SystemConfiguration::getValue('system_name');
        $this->assertEquals('电商市场动态监控系统', $value1);

        // 验证缓存中有值
        $cacheKey = SystemConfiguration::CACHE_PREFIX . 'system_name';
        $this->assertTrue(Cache::has($cacheKey));

        // 第二次访问，应该从缓存获取
        $value2 = SystemConfiguration::getValue('system_name');
        $this->assertEquals($value1, $value2);
    }

    public function test_cache_clearing()
    {
        // 设置一个值并通过getValue触发缓存
        SystemConfiguration::setValue('test_cache', 'test_value');
        $value = SystemConfiguration::getValue('test_cache'); // 这会触发缓存
        $cacheKey = SystemConfiguration::CACHE_PREFIX . 'test_cache';
        $this->assertTrue(Cache::has($cacheKey));

        // 清除特定键的缓存
        SystemConfiguration::clearCache('test_cache');
        $this->assertFalse(Cache::has($cacheKey));

        // 清除所有缓存
        $value = SystemConfiguration::getValue('test_cache'); // 重新触发缓存
        $this->assertTrue(Cache::has($cacheKey));
        SystemConfiguration::clearCache();
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_controller_get_by_category()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/system-configurations/category/general');

        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'key',
                    'category',
                    'value',
                    'type',
                    'label',
                    'description',
                ]
            ]);
    }

    public function test_controller_get_public_configurations()
    {
        $response = $this->getJson('/system-configurations/public');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'system_name',
                'system_description',
                'enable_health_monitoring',
            ]);
    }

    public function test_controller_update_configuration()
    {
        $response = $this->actingAs($this->adminUser)
            ->putJson('/system-configurations/system_name', [
                'value' => '新的系统名称'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'configuration',
            ]);

        // 验证配置已更新
        $this->assertEquals('新的系统名称', SystemConfiguration::getValue('system_name'));
    }

    public function test_controller_batch_update()
    {
        $response = $this->actingAs($this->adminUser)
            ->postJson('/system-configurations/batch-update', [
                'configurations' => [
                    'system_name' => '批量更新的系统名称',
                    'default_monitoring_frequency' => 120,
                ]
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'updated',
                'errors',
            ]);

        // 验证配置已更新
        $this->assertEquals('批量更新的系统名称', SystemConfiguration::getValue('system_name'));
        $this->assertEquals(120, SystemConfiguration::getValue('default_monitoring_frequency'));
    }

    public function test_controller_reset_to_default()
    {
        // 先修改配置
        SystemConfiguration::setValue('system_name', '修改后的名称');
        
        $response = $this->actingAs($this->adminUser)
            ->postJson('/system-configurations/system_name/reset');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'configuration',
            ]);
    }

    public function test_controller_export_configurations()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/system-configurations/export');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'configurations',
                'exported_at',
            ]);
    }

    public function test_validation_rules()
    {
        $config = SystemConfiguration::where('key', 'default_monitoring_frequency')->first();
        $this->assertNotNull($config);
        $this->assertNotEmpty($config->validation_rules);

        // 测试无效值的更新
        $response = $this->actingAs($this->adminUser)
            ->putJson('/system-configurations/default_monitoring_frequency', [
                'value' => 0 // 应该失败，因为最小值是1
            ]);

        $response->assertStatus(422);
    }

    public function test_requires_admin_role()
    {
        $regularUser = User::factory()->create();

        $response = $this->actingAs($regularUser)
            ->getJson('/system-configurations/');

        $response->assertStatus(403);
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}

<?php

namespace App\Providers;

use App\Services\ConfigurationService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 注册配置管理服务
        $this->app->singleton('configuration', function ($app) {
            return new ConfigurationService();
        });
        
        $this->app->singleton(ConfigurationService::class, function ($app) {
            return $app['configuration'];
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
} 
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('competitor_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('sku_id')->constrained('product_skus')->onDelete('cascade');
            $table->foreignId('own_sku_id')->nullable()->constrained('product_skus')->onDelete('set null');
            $table->date('date');
            
            // MarketAnalysisService 指标 (占位)
            $table->decimal('sales_volume', 15, 2)->nullable();
            $table->decimal('sales_revenue', 15, 2)->nullable();
            $table->decimal('market_share_volume', 8, 4)->nullable();
            $table->decimal('market_share_revenue', 8, 4)->nullable();

            // CompetitorPromotionAnalysisService 指标
            $table->json('promotion_types')->nullable();
            $table->integer('total_promotions')->nullable();
            $table->decimal('promotion_frequency', 8, 2)->nullable();
            $table->decimal('avg_discount_rate', 8, 2)->nullable();
            $table->decimal('max_discount_rate', 8, 2)->nullable();
            $table->decimal('min_discount_rate', 8, 2)->nullable();
            $table->integer('promotion_duration_avg')->nullable();
            $table->text('analysis_summary')->nullable();
            
            // PriceComparisonService 指标
            $table->decimal('price_deviation_rate', 8, 2)->nullable();

            $table->timestamps();

            $table->unique(['sku_id', 'own_sku_id', 'date'], 'sku_own_sku_date_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('competitor_metrics');
    }
};

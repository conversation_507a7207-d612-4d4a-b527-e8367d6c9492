<?php

namespace App\Services\DataCollection;

use App\Models\DataSource;
use App\Models\Product;
use App\Models\PriceHistory;
use App\Services\DataCollection\Contracts\PlatformAdapterInterface;
use App\Services\DataStandardizationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * 数据收集服务类
 * 提供高级的数据收集方法和业务逻辑
 */
class DataCollectionService
{
    private PlatformAdapterFactory $adapterFactory;
    private DataStandardizationService $standardizationService;

    public function __construct(
        PlatformAdapterFactory $adapterFactory,
        DataStandardizationService $standardizationService
    ) {
        $this->adapterFactory = $adapterFactory;
        $this->standardizationService = $standardizationService;
    }

    /**
     * 根据数据源收集商品信息
     *
     * @param DataSource $dataSource 数据源
     * @return array 收集结果
     */
    public function collectFromDataSource(DataSource $dataSource): array
    {
        try {
            Log::info("开始收集数据源数据", [
                'data_source_id' => $dataSource->id,
                'platform' => $dataSource->platform,
                'url' => $dataSource->url,
            ]);

            $adapter = $this->adapterFactory->create($dataSource->platform);
            $itemId = $this->extractItemIdFromUrl($dataSource->url);
            
            if (!$itemId) {
                throw new Exception("无法从URL中提取商品ID: " . $dataSource->url);
            }

            // 获取商品详情
            $rawItemData = $adapter->getItemDetail($itemId);
            
            // 数据标准化处理
            $standardizedData = $this->standardizationService->standardizeProductData(
                $rawItemData, 
                $dataSource->platform
            );
            
            // 保存或更新商品信息
            $product = $this->saveOrUpdateProduct($standardizedData, $dataSource);
            
            // 保存价格历史
            $this->savePriceHistory($product, $standardizedData);

            Log::info("数据源数据收集成功", [
                'data_source_id' => $dataSource->id,
                'product_id' => $product->id,
                'title' => $product->title,
                'standardized' => true,
            ]);

            return [
                'success' => true,
                'product' => $product,
                'standardized_data' => $standardizedData,
                'message' => '数据收集成功',
            ];

        } catch (Exception $e) {
            Log::error("数据源数据收集失败", [
                'data_source_id' => $dataSource->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '数据收集失败',
            ];
        }
    }

    /**
     * 批量收集数据源
     *
     * @param array $dataSourceIds 数据源ID数组
     * @return array 批量收集结果
     */
    public function collectBatch(array $dataSourceIds): array
    {
        $results = [
            'success_count' => 0,
            'failed_count' => 0,
            'results' => [],
        ];

        $dataSources = DataSource::whereIn('id', $dataSourceIds)
            ->where('auto_monitor', true)
            ->get();

        foreach ($dataSources as $dataSource) {
            $result = $this->collectFromDataSource($dataSource);
            
            if ($result['success']) {
                $results['success_count']++;
            } else {
                $results['failed_count']++;
            }
            
            $results['results'][] = array_merge($result, [
                'data_source_id' => $dataSource->id,
                'platform' => $dataSource->platform,
            ]);
        }

        Log::info("批量数据收集完成", [
            'total' => count($dataSources),
            'success' => $results['success_count'],
            'failed' => $results['failed_count'],
        ]);

        return $results;
    }

    /**
     * 收集同款商品
     *
     * @param string $platform 平台名称
     * @param string $itemId 商品ID
     * @return array 同款商品数据
     */
    public function collectSimilarProducts(string $platform, string $itemId): array
    {
        try {
            $adapter = $this->adapterFactory->create($platform);
            $rawSimilarProducts = $adapter->getSimilarProducts($itemId);

            // 批量标准化商品数据
            $standardizedProducts = $this->standardizationService->standardizeBatchData(
                $rawSimilarProducts,
                $platform
            );

            Log::info("同款商品收集成功", [
                'platform' => $platform,
                'item_id' => $itemId,
                'count' => count($standardizedProducts),
                'standardized' => true,
            ]);

            return [
                'success' => true,
                'data' => $standardizedProducts,
                'raw_data' => $rawSimilarProducts,
                'count' => count($standardizedProducts),
            ];

        } catch (Exception $e) {
            Log::error("同款商品收集失败", [
                'platform' => $platform,
                'item_id' => $itemId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [],
                'count' => 0,
            ];
        }
    }

    /**
     * 收集店铺商品
     *
     * @param string $platform 平台名称
     * @param string $shopId 店铺ID
     * @param int $page 页码
     * @return array 店铺商品数据
     */
    public function collectShopItems(string $platform, string $shopId, int $page = 1): array
    {
        try {
            $adapter = $this->adapterFactory->create($platform);
            $rawShopData = $adapter->getShopItems($shopId, $page);

            // 标准化店铺数据
            $standardizedShopData = $rawShopData;
            if (isset($rawShopData['items']) && is_array($rawShopData['items'])) {
                $standardizedShopData['items'] = $this->standardizationService->standardizeBatchData(
                    $rawShopData['items'],
                    $platform
                );
            }

            Log::info("店铺商品收集成功", [
                'platform' => $platform,
                'shop_id' => $shopId,
                'page' => $page,
                'count' => count($standardizedShopData['items'] ?? []),
                'standardized' => true,
            ]);

            return [
                'success' => true,
                'data' => $standardizedShopData,
                'raw_data' => $rawShopData,
            ];

        } catch (Exception $e) {
            Log::error("店铺商品收集失败", [
                'platform' => $platform,
                'shop_id' => $shopId,
                'page' => $page,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => ['items' => [], 'pagination' => []],
            ];
        }
    }

    /**
     * 搜索商品
     *
     * @param string $platform 平台名称
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @return array 搜索结果
     */
    public function searchProducts(string $platform, string $keyword, int $page = 1): array
    {
        try {
            $adapter = $this->adapterFactory->create($platform);
            $rawSearchData = $adapter->searchItems($keyword, $page);

            // 标准化搜索结果数据
            $standardizedSearchData = $rawSearchData;
            if (isset($rawSearchData['items']) && is_array($rawSearchData['items'])) {
                $standardizedSearchData['items'] = $this->standardizationService->standardizeBatchData(
                    $rawSearchData['items'],
                    $platform
                );
            }

            Log::info("商品搜索成功", [
                'platform' => $platform,
                'keyword' => $keyword,
                'page' => $page,
                'count' => count($standardizedSearchData['items'] ?? []),
                'standardized' => true,
            ]);

            return [
                'success' => true,
                'data' => $standardizedSearchData,
                'raw_data' => $rawSearchData,
            ];

        } catch (Exception $e) {
            Log::error("商品搜索失败", [
                'platform' => $platform,
                'keyword' => $keyword,
                'page' => $page,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => ['items' => [], 'pagination' => [], 'keyword' => $keyword],
            ];
        }
    }

    /**
     * 从URL中提取商品ID
     *
     * @param string $url 商品URL
     * @return string|null 商品ID
     */
    private function extractItemIdFromUrl(string $url): ?string
    {
        // 淘宝URL模式匹配
        if (preg_match('/(?:id=|item\/|\/item\.htm\?.*id=)(\d+)/', $url, $matches)) {
            return $matches[1];
        }

        // 如果URL本身就是商品ID
        if (preg_match('/^\d+$/', trim($url))) {
            return trim($url);
        }

        return null;
    }

    /**
     * 保存或更新商品信息
     *
     * @param array $itemData 商品数据
     * @param DataSource $dataSource 数据源
     * @return Product 商品模型
     */
    private function saveOrUpdateProduct(array $itemData, DataSource $dataSource): Product
    {
        return DB::transaction(function () use ($itemData, $dataSource) {
            $product = Product::updateOrCreate(
                [
                    'platform_item_id' => $itemData['item_id'],
                    'platform' => $itemData['platform'],
                ],
                [
                    'title' => $itemData['title'],
                    'current_price' => $this->parsePrice($itemData['price']),
                    'promotion_price' => $this->parsePrice($itemData['promotion_price'] ?? null),
                    'sale_count' => $this->parseInt($itemData['sale_count'] ?? 0),
                    'comment_count' => $this->parseInt($itemData['comment_count'] ?? 0),
                    'category_id' => $itemData['category_id'] ?? null,
                    'category_path' => $itemData['category_path'] ?? null,
                    'shop_name' => $itemData['shop_name'] ?? null,
                    'shop_id' => $itemData['shop_id'] ?? null,
                    'seller_id' => $itemData['seller_id'] ?? null,
                    'location' => $itemData['location'] ?? null,
                    'pic_urls' => json_encode($itemData['pic_urls'] ?? []),
                    'desc_urls' => json_encode($itemData['desc_urls'] ?? []),
                    'properties' => json_encode($itemData['properties'] ?? []),
                    'status' => 'active',
                    'last_collected_at' => now(),
                    'data_source_id' => $dataSource->id,
                ]
            );

            // 保存或更新SKU信息
            $this->saveProductSkus($product, $itemData['skus'] ?? []);

            return $product;
        });
    }

    /**
     * 保存商品SKU信息
     */
    private function saveProductSkus(Product $product, array $skus): void
    {
        // 这里可以扩展为保存SKU到独立的表
        // 目前简单存储在product的skus字段中
        $product->update(['skus' => json_encode($skus)]);
    }

    /**
     * 保存价格历史
     */
    private function savePriceHistory(Product $product, array $itemData): void
    {
        $currentPrice = $this->parsePrice($itemData['price']);
        $promotionPrice = $this->parsePrice($itemData['promotion_price'] ?? null);

        PriceHistory::create([
            'product_id' => $product->id,
            'price' => $currentPrice,
            'promotion_price' => $promotionPrice,
            'sale_count' => $this->parseInt($itemData['sale_count'] ?? 0),
            'comment_count' => $this->parseInt($itemData['comment_count'] ?? 0),
            'collected_at' => now(),
        ]);
    }

    /**
     * 解析价格字符串为数值
     */
    private function parsePrice(?string $priceStr): ?float
    {
        if (empty($priceStr)) {
            return null;
        }

        // 移除非数字字符（除了小数点）
        $price = preg_replace('/[^\d.]/', '', $priceStr);
        
        return is_numeric($price) ? (float) $price : null;
    }

    /**
     * 解析整数字符串
     */
    private function parseInt($value): int
    {
        if (is_numeric($value)) {
            return (int) $value;
        }

        // 处理带单位的数字，如 "1.2万"
        if (is_string($value)) {
            if (strpos($value, '万') !== false) {
                $num = floatval(str_replace('万', '', $value));
                return (int) ($num * 10000);
            }
            
            return (int) preg_replace('/[^\d]/', '', $value);
        }

        return 0;
    }

    /**
     * 通用数据收集方法
     * 用于Job系统调用
     *
     * @param array $parameters 参数数组
     * @return array 收集结果
     */
    public function collectData(array $parameters): array
    {
        try {
            Log::info("开始Job数据收集", ['parameters' => $parameters]);

            // 检查是否为测试模式
            if (!empty($parameters['test'])) {
                Log::info("测试模式数据收集");
                return [
                    'success' => true,
                    'message' => '测试模式数据收集成功',
                    'data' => $parameters,
                ];
            }

            // 根据task_id获取数据源
            if (!empty($parameters['task_id'])) {
                $taskId = $parameters['task_id'];
                
                // 首先获取监控任务
                $monitorTask = \App\Models\MonitorTask::find($taskId);
                if (!$monitorTask) {
                    throw new Exception("找不到任务ID {$taskId}");
                }
                
                // 根据监控任务的商品ID和平台查找数据源
                $dataSource = DataSource::where('platform', $monitorTask->platform)
                    ->where('source_id', $monitorTask->product_id)
                    ->where('auto_monitor', true)
                    ->first();
                
                if (!$dataSource) {
                    throw new Exception("找不到任务ID {$taskId} 对应的数据源");
                }

                return $this->collectFromDataSource($dataSource);
            }

            // 根据平台和商品ID收集
            if (!empty($parameters['platform']) && !empty($parameters['item_id'])) {
                $adapter = $this->adapterFactory->create($parameters['platform']);
                $itemData = $adapter->getItemDetail($parameters['item_id']);
                
                return [
                    'success' => true,
                    'message' => '数据收集成功',
                    'data' => $itemData,
                ];
            }

            throw new Exception("参数不足，无法进行数据收集");

        } catch (Exception $e) {
            Log::error("Job数据收集失败", [
                'parameters' => $parameters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '数据收集失败',
            ];
        }
    }

    /**
     * 测试所有平台连接
     *
     * @return array 连接测试结果
     */
    public function testAllPlatformConnections(): array
    {
        return $this->adapterFactory->testAllConnections();
    }

    /**
     * 验证数据标准化
     *
     * @param array $rawData 原始数据
     * @param string $platform 平台名称
     * @return array 验证结果
     */
    public function validateDataStandardization(array $rawData, string $platform): array
    {
        try {
            // 测试单个数据标准化
            $standardizedSingle = $this->standardizationService->standardizeProductData($rawData, $platform);
            
            // 测试批量数据标准化
            $batchRawData = [$rawData, $rawData]; // 创建两个相同的数据用于测试
            $standardizedBatch = $this->standardizationService->standardizeBatchData($batchRawData, $platform);
            
            // 验证标准化效果
            $validation = $this->standardizationService->validateStandardizedData($standardizedSingle);
            
            Log::info("数据标准化验证成功", [
                'platform' => $platform,
                'single_validation' => $validation,
                'batch_count' => count($standardizedBatch),
            ]);

            return [
                'success' => true,
                'original_data' => $rawData,
                'standardized_single' => $standardizedSingle,
                'standardized_batch' => $standardizedBatch,
                'validation' => $validation,
                'message' => '数据标准化验证成功',
            ];

        } catch (Exception $e) {
            Log::error("数据标准化验证失败", [
                'platform' => $platform,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '数据标准化验证失败',
            ];
        }
    }

    /**
     * 优化内存使用的批量数据处理
     */
    public function processBatchDataOptimized(array $tasks, int $batchSize = 100): array
    {
        $results = [];
        $batches = array_chunk($tasks, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            $batchResults = [];
            
            try {
                // 记录批次开始时的内存使用
                $memoryStart = memory_get_usage(true);
                
                foreach ($batch as $task) {
                    $batchResults[] = $this->collectSingleTaskData($task);
                }
                
                $results = array_merge($results, $batchResults);
                
                // 主动清理内存
                unset($batchResults);
                
                // 强制垃圾回收
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                
                $memoryEnd = memory_get_usage(true);
                $memoryUsed = $memoryEnd - $memoryStart;
                
                Log::info("批次处理完成", [
                    'batch_index' => $batchIndex,
                    'batch_size' => count($batch),
                    'memory_used' => round($memoryUsed / 1024 / 1024, 2) . 'MB',
                    'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB'
                ]);
                
                // 如果内存使用超过阈值，暂停一下
                if ($memoryEnd > (256 * 1024 * 1024)) { // 256MB
                    sleep(1);
                }
                
            } catch (Exception $e) {
                Log::error("批次处理失败", [
                    'batch_index' => $batchIndex,
                    'error' => $e->getMessage()
                ]);
                
                // 继续处理下一批次
                continue;
            }
        }
        
        return $results;
    }
    
    /**
     * 内存监控和清理
     */
    protected function monitorAndCleanMemory(): void
    {
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $memoryLimit = $this->getMemoryLimit();
        
        // 如果内存使用超过75%，进行清理
        if ($currentMemory > ($memoryLimit * 0.75)) {
            Log::warning('内存使用率过高，开始清理', [
                'current_memory' => round($currentMemory / 1024 / 1024, 2) . 'MB',
                'memory_limit' => round($memoryLimit / 1024 / 1024, 2) . 'MB',
                'usage_percentage' => round(($currentMemory / $memoryLimit) * 100, 2) . '%'
            ]);
            
            // 清理缓存
            if (method_exists($this, 'clearInternalCache')) {
                $this->clearInternalCache();
            }
            
            // 强制垃圾回收
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                Log::info('垃圾回收完成', ['cycles_collected' => $collected]);
            }
        }
    }
    
    /**
     * 获取内存限制（字节）
     */
    protected function getMemoryLimit(): int
    {
        $limit = ini_get('memory_limit');
        
        if ($limit == -1) {
            return PHP_INT_MAX;
        }
        
        $unit = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;
        
        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * 清理内部缓存
     */
    protected function clearInternalCache(): void
    {
        // 清理类内部的缓存变量
        if (property_exists($this, 'cache')) {
            $this->cache = [];
        }
        
        if (property_exists($this, 'tempData')) {
            $this->tempData = [];
        }
        
        // 清理适配器缓存
        if (isset($this->adapters)) {
            foreach ($this->adapters as $adapter) {
                if (method_exists($adapter, 'clearCache')) {
                    $adapter->clearCache();
                }
            }
        }
    }
} 
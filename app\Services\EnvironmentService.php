<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Exception;

/**
 * 环境变量管理服务
 * 
 * 提供环境变量的读取、写入和管理功能
 */
class EnvironmentService
{
    /**
     * 环境变量文件路径
     *
     * @var string
     */
    private $envPath;

    /**
     * 构造函数
     *
     * @param string|null $envPath 环境变量文件路径
     */
    public function __construct(?string $envPath = null)
    {
        $this->envPath = $envPath ?: base_path('.env');
    }

    /**
     * 获取环境变量值
     *
     * @param string $key 环境变量键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        return env($key, $default);
    }

    /**
     * 设置环境变量值
     *
     * @param string $key 环境变量键
     * @param string $value 环境变量值
     * @param bool $backup 是否备份原文件
     * @return bool
     */
    public function set(string $key, string $value, bool $backup = true): bool
    {
        try {
            if ($backup) {
                $this->backup();
            }

            $envContent = $this->getEnvContent();
            $updatedContent = $this->updateEnvContent($envContent, $key, $value);
            
            return File::put($this->envPath, $updatedContent) !== false;
        } catch (Exception $e) {
            \Log::error('环境变量设置失败', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量设置环境变量
     *
     * @param array $variables 环境变量数组 ['KEY' => 'value', ...]
     * @param bool $backup 是否备份原文件
     * @return bool
     */
    public function setMultiple(array $variables, bool $backup = true): bool
    {
        try {
            if ($backup) {
                $this->backup();
            }

            $envContent = $this->getEnvContent();
            
            foreach ($variables as $key => $value) {
                $envContent = $this->updateEnvContent($envContent, $key, $value);
            }
            
            return File::put($this->envPath, $envContent) !== false;
        } catch (Exception $e) {
            \Log::error('批量环境变量设置失败', [
                'variables_count' => count($variables),
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 删除环境变量
     *
     * @param string $key 环境变量键
     * @param bool $backup 是否备份原文件
     * @return bool
     */
    public function delete(string $key, bool $backup = true): bool
    {
        try {
            if ($backup) {
                $this->backup();
            }

            $envContent = $this->getEnvContent();
            $lines = explode("\n", $envContent);
            $updatedLines = [];

            foreach ($lines as $line) {
                if (!Str::startsWith(trim($line), $key . '=')) {
                    $updatedLines[] = $line;
                }
            }

            return File::put($this->envPath, implode("\n", $updatedLines)) !== false;
        } catch (Exception $e) {
            \Log::error('环境变量删除失败', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 检查环境变量是否存在
     *
     * @param string $key 环境变量键
     * @return bool
     */
    public function has(string $key): bool
    {
        $envContent = $this->getEnvContent();
        $lines = explode("\n", $envContent);

        foreach ($lines as $line) {
            if (Str::startsWith(trim($line), $key . '=')) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取所有环境变量
     *
     * @return array
     */
    public function getAll(): array
    {
        $envContent = $this->getEnvContent();
        $lines = explode("\n", $envContent);
        $variables = [];

        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line) || Str::startsWith($line, '#')) {
                continue;
            }

            if (Str::contains($line, '=')) {
                [$key, $value] = explode('=', $line, 2);
                $variables[trim($key)] = trim($value, '"\'');
            }
        }

        return $variables;
    }

    /**
     * 验证环境变量配置
     *
     * @param array $requiredKeys 必需的环境变量键
     * @return array 验证结果
     */
    public function validate(array $requiredKeys = []): array
    {
        $results = [
            'valid' => true,
            'missing' => [],
            'empty' => [],
            'warnings' => []
        ];

        $allVars = $this->getAll();

        // 检查必需的键
        foreach ($requiredKeys as $key) {
            if (!isset($allVars[$key])) {
                $results['missing'][] = $key;
                $results['valid'] = false;
            } elseif (empty($allVars[$key])) {
                $results['empty'][] = $key;
            }
        }

        // 检查常见配置问题
        if (isset($allVars['APP_DEBUG']) && $allVars['APP_DEBUG'] === 'true' && 
            isset($allVars['APP_ENV']) && $allVars['APP_ENV'] === 'production') {
            $results['warnings'][] = '生产环境不应启用调试模式';
        }

        if (isset($allVars['DB_PASSWORD']) && empty($allVars['DB_PASSWORD'])) {
            $results['warnings'][] = '数据库密码为空可能存在安全风险';
        }

        return $results;
    }

    /**
     * 备份环境变量文件
     *
     * @return string|null 备份文件路径
     */
    public function backup(): ?string
    {
        try {
            $backupPath = $this->envPath . '.backup.' . date('Y-m-d_H-i-s');
            
            if (File::exists($this->envPath)) {
                File::copy($this->envPath, $backupPath);
                return $backupPath;
            }
            
            return null;
        } catch (Exception $e) {
            \Log::error('环境变量文件备份失败', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 恢复环境变量文件
     *
     * @param string $backupPath 备份文件路径
     * @return bool
     */
    public function restore(string $backupPath): bool
    {
        try {
            if (File::exists($backupPath)) {
                return File::copy($backupPath, $this->envPath);
            }
            
            return false;
        } catch (Exception $e) {
            \Log::error('环境变量文件恢复失败', [
                'backup_path' => $backupPath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 清理旧备份文件
     *
     * @param int $keepDays 保留天数
     * @return int 删除的文件数量
     */
    public function cleanupBackups(int $keepDays = 7): int
    {
        $deleted = 0;
        $backupDir = dirname($this->envPath);
        $backupPattern = basename($this->envPath) . '.backup.*';
        
        try {
            $files = File::glob($backupDir . '/' . $backupPattern);
            $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
            
            foreach ($files as $file) {
                if (File::lastModified($file) < $cutoffTime) {
                    File::delete($file);
                    $deleted++;
                }
            }
        } catch (Exception $e) {
            \Log::error('备份文件清理失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $deleted;
    }

    /**
     * 生成环境变量模板
     *
     * @param array $variables 变量定义 ['KEY' => ['default' => 'value', 'comment' => 'description'], ...]
     * @return string
     */
    public function generateTemplate(array $variables): string
    {
        $template = "# 环境变量配置文件\n";
        $template .= "# 生成时间: " . date('Y-m-d H:i:s') . "\n\n";

        $currentGroup = '';
        
        foreach ($variables as $key => $config) {
            // 检查是否需要添加分组注释
            $group = $this->getVariableGroup($key);
            if ($group !== $currentGroup) {
                $currentGroup = $group;
                $template .= "\n# " . str_repeat('=', 50) . "\n";
                $template .= "# {$group}\n";
                $template .= "# " . str_repeat('=', 50) . "\n\n";
            }

            // 添加变量注释
            if (isset($config['comment'])) {
                $template .= "# {$config['comment']}\n";
            }

            // 添加变量定义
            $value = $config['default'] ?? '';
            $template .= "{$key}={$value}\n\n";
        }

        return $template;
    }

    /**
     * 获取环境变量文件内容
     *
     * @return string
     */
    private function getEnvContent(): string
    {
        if (!File::exists($this->envPath)) {
            return '';
        }

        return File::get($this->envPath);
    }

    /**
     * 更新环境变量内容
     *
     * @param string $content 原内容
     * @param string $key 键
     * @param string $value 值
     * @return string
     */
    private function updateEnvContent(string $content, string $key, string $value): string
    {
        $lines = explode("\n", $content);
        $updated = false;
        $quotedValue = $this->quoteValue($value);

        foreach ($lines as &$line) {
            if (Str::startsWith(trim($line), $key . '=')) {
                $line = "{$key}={$quotedValue}";
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            $lines[] = "{$key}={$quotedValue}";
        }

        return implode("\n", $lines);
    }

    /**
     * 给值添加引号（如果需要）
     *
     * @param string $value 值
     * @return string
     */
    private function quoteValue(string $value): string
    {
        // 如果值包含空格、特殊字符或为空，则添加引号
        if (empty($value) || 
            Str::contains($value, [' ', '#', '"', "'", '\\', '$']) ||
            Str::startsWith($value, ['(', '[', '{']) ||
            Str::endsWith($value, [')', ']', '}'])) {
            return '"' . str_replace('"', '\\"', $value) . '"';
        }

        return $value;
    }

    /**
     * 获取变量分组
     *
     * @param string $key 变量键
     * @return string
     */
    private function getVariableGroup(string $key): string
    {
        $groups = [
            'APP_' => '应用配置',
            'DB_' => '数据库配置',
            'REDIS_' => 'Redis配置',
            'CACHE_' => '缓存配置',
            'QUEUE_' => '队列配置',
            'MAIL_' => '邮件配置',
            'AWS_' => 'AWS配置',
            'TAOBAO_' => '淘宝API配置',
            'JD_' => '京东API配置',
            'PDD_' => '拼多多API配置',
            'API_' => 'API配置',
            'COLLECTION_' => '数据收集配置',
            'MONITORING_' => '监控配置',
            'RATE_' => '限流配置',
            'DEBUG_' => '调试配置'
        ];

        foreach ($groups as $prefix => $groupName) {
            if (Str::startsWith($key, $prefix)) {
                return $groupName;
            }
        }

        return '其他配置';
    }
} 
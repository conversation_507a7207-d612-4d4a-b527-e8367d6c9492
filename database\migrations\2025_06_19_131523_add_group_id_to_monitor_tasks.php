<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->foreignId('task_group_id')->nullable()
                  ->constrained()->nullOnDelete()
                  ->comment('所属任务分组');
            
            $table->index('task_group_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->dropForeign(['task_group_id']);
            $table->dropColumn('task_group_id');
        });
    }
};

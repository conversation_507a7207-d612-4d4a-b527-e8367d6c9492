<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建权限
        $permissions = [
            ['name' => 'view_dashboard', 'display_name' => '查看仪表板', 'description' => '查看系统仪表板', 'module' => 'dashboard', 'action' => 'view', 'is_active' => true],
            ['name' => 'manage_users', 'display_name' => '用户管理', 'description' => '管理系统用户', 'module' => 'user', 'action' => 'manage', 'is_active' => true],
            ['name' => 'manage_roles', 'display_name' => '角色管理', 'description' => '管理系统角色', 'module' => 'role', 'action' => 'manage', 'is_active' => true],
            ['name' => 'manage_monitors', 'display_name' => '监控管理', 'description' => '管理监控任务', 'module' => 'monitor', 'action' => 'manage', 'is_active' => true],
            ['name' => 'view_reports', 'display_name' => '查看报告', 'description' => '查看监控报告', 'module' => 'report', 'action' => 'view', 'is_active' => true],
            ['name' => 'export_data', 'display_name' => '导出数据', 'description' => '导出监控数据', 'module' => 'data', 'action' => 'export', 'is_active' => true],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // 创建角色
        $roles = [
            [
                'name' => 'admin',
                'display_name' => '系统管理员',
                'description' => '拥有所有权限的系统管理员',
                'level' => 'admin',
                'is_active' => true,
                'permissions' => ['view_dashboard', 'manage_users', 'manage_roles', 'manage_monitors', 'view_reports', 'export_data']
            ],
            [
                'name' => 'advanced',
                'display_name' => '高级用户',
                'description' => '拥有监控管理和报告权限的高级用户',
                'level' => 'advanced',
                'is_active' => true,
                'permissions' => ['view_dashboard', 'manage_monitors', 'view_reports', 'export_data']
            ],
            [
                'name' => 'normal',
                'display_name' => '普通用户',
                'description' => '拥有基本监控权限的普通用户',
                'level' => 'normal',
                'is_active' => true,
                'permissions' => ['view_dashboard', 'manage_monitors', 'view_reports']
            ],
            [
                'name' => 'readonly',
                'display_name' => '只读用户',
                'description' => '只能查看数据的用户',
                'level' => 'readonly',
                'is_active' => true,
                'permissions' => ['view_dashboard', 'view_reports']
            ],
        ];

        foreach ($roles as $roleData) {
            $permissions = $roleData['permissions'];
            unset($roleData['permissions']);
            
            $role = Role::create($roleData);
            
            // 分配权限给角色
            $permissionIds = Permission::whereIn('name', $permissions)->pluck('id');
            $role->permissions()->attach($permissionIds);
        }

        // 创建测试用户
        $users = [
            [
                'name' => '系统管理员',
                'email' => '<EMAIL>',
                'username' => 'admin',
                'password' => Hash::make('admin123'),
                'status' => 'active',
                'email_verified_at' => now(),
                'role' => 'admin'
            ],
            [
                'name' => '张三',
                'email' => '<EMAIL>',
                'username' => 'zhangsan',
                'password' => Hash::make('password123'),
                'status' => 'active',
                'email_verified_at' => now(),
                'role' => 'advanced'
            ],
            [
                'name' => '李四',
                'email' => '<EMAIL>',
                'username' => 'lisi',
                'password' => Hash::make('password123'),
                'status' => 'active',
                'email_verified_at' => now(),
                'role' => 'normal'
            ],
            [
                'name' => '王五',
                'email' => '<EMAIL>',
                'username' => 'wangwu',
                'password' => Hash::make('password123'),
                'status' => 'active',
                'email_verified_at' => now(),
                'role' => 'readonly'
            ],
        ];

        foreach ($users as $userData) {
            $roleName = $userData['role'];
            unset($userData['role']);
            
            $user = User::create($userData);
            
            // 分配角色给用户
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $user->roles()->attach($role->id, [
                    'assigned_at' => now(),
                    'assigned_by' => 1, // 假设由第一个用户（admin）分配
                ]);
            }
        }

        $this->command->info('初始数据已成功创建：');
        $this->command->info('- 6个权限');
        $this->command->info('- 4个角色（admin, advanced, normal, readonly）');
        $this->command->info('- 4个测试用户');
        $this->command->info('');
        $this->command->info('测试账户信息：');
        $this->command->info('管理员: <EMAIL> / admin123');
        $this->command->info('高级用户: <EMAIL> / password123');
        $this->command->info('普通用户: <EMAIL> / password123');
        $this->command->info('只读用户: <EMAIL> / password123');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->unique()->comment('权限名称');
            $table->string('display_name', 150)->comment('权限显示名称');
            $table->text('description')->nullable()->comment('权限描述');
            $table->string('module', 50)->comment('所属模块');
            $table->string('action', 50)->comment('操作类型');
            $table->string('resource', 100)->nullable()->comment('资源标识');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->timestamps();
            
            // 索引
            $table->index(['name', 'is_active']);
            $table->index(['module', 'action']);
            $table->index('resource');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
}; 
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monitor_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('创建任务的用户ID');
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('cascade')->comment('监控的产品ID');
            $table->foreignId('sku_id')->nullable()->constrained('product_skus')->onDelete('cascade')->comment('监控的SKU ID');
            $table->string('task_name')->comment('任务名称');
            $table->text('description')->nullable()->comment('任务描述');
            $table->enum('monitor_type', ['product', 'sku', 'both'])->default('sku')->comment('监控类型');
            $table->enum('frequency', ['5min', '15min', '30min', '1hour', '6hour', '12hour', '24hour'])->default('1hour')->comment('监控频率');
            $table->timestamp('last_collected_at')->nullable()->comment('最后采集时间');
            $table->timestamp('next_collection_at')->nullable()->comment('下次采集时间');
            $table->enum('status', ['active', 'paused', 'stopped', 'error'])->default('active')->comment('任务状态');
            $table->unsignedInteger('collection_count')->default(0)->comment('已采集次数');
            $table->json('monitor_settings')->nullable()->comment('监控设置（采集字段、频率等）');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('error_occurred_at')->nullable()->comment('错误发生时间');
            $table->unsignedInteger('consecutive_errors')->default(0)->comment('连续错误次数');
            $table->boolean('is_public')->default(false)->comment('是否公开（其他用户可见）');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['product_id', 'status']);
            $table->index(['sku_id', 'status']);
            $table->index(['status', 'next_collection_at']);
            $table->index(['frequency', 'status']);
            $table->index('last_collected_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monitor_tasks');
    }
};

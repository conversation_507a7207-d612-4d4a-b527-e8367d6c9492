<?php

namespace App\Services;

use App\Models\PriceHistory;
use App\Services\PriceDeviationCalculator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

/**
 * 价格历史服务
 * 
 * 负责价格历史数据的创建、更新和偏差率计算集成
 */
class PriceHistoryService
{
    protected PriceDeviationCalculator $calculator;

    public function __construct(PriceDeviationCalculator $calculator)
    {
        $this->calculator = $calculator;
    }

    /**
     * 创建价格历史记录（带偏差率计算）
     * 
     * @param array $data 价格数据
     * @return PriceHistory|null 创建的价格历史记录
     */
    public function createPriceHistory(array $data): ?PriceHistory
    {
        try {
            DB::beginTransaction();

            // 创建基础价格历史记录
            $priceHistory = new PriceHistory();
            $priceHistory->fill($data);
            
            // 如果没有设置时间戳，使用当前时间
            if (!isset($data['timestamp'])) {
                $priceHistory->timestamp = Carbon::now();
            }

            // 设置初始计算状态
            $priceHistory->calculation_status = 'pending';

            $priceHistory->save();

            // 立即计算偏差率
            $this->calculateDeviationRates($priceHistory, $data);

            DB::commit();

            Log::info('价格历史记录创建成功', [
                'price_history_id' => $priceHistory->id,
                'sku_id' => $priceHistory->sku_id,
                'promotion_deviation_rate' => $priceHistory->promotion_deviation_rate,
                'channel_deviation_rate' => $priceHistory->channel_deviation_rate
            ]);

            return $priceHistory->fresh();

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('价格历史记录创建失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 更新价格历史记录（带偏差率重新计算）
     * 
     * @param PriceHistory $priceHistory 价格历史记录
     * @param array $data 更新数据
     * @return bool 是否更新成功
     */
    public function updatePriceHistory(PriceHistory $priceHistory, array $data): bool
    {
        try {
            DB::beginTransaction();

            $priceHistory->fill($data);
            $priceHistory->save();

            // 重新计算偏差率
            $this->calculateDeviationRates($priceHistory, $data);

            DB::commit();

            Log::info('价格历史记录更新成功', [
                'price_history_id' => $priceHistory->id,
                'sku_id' => $priceHistory->sku_id
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('价格历史记录更新失败', [
                'price_history_id' => $priceHistory->id,
                'data' => $data,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 为价格历史记录计算偏差率
     * 
     * @param PriceHistory $priceHistory 价格历史记录
     * @param array|null $additionalData 额外数据（包含官方指导价等）
     * @return bool 是否计算成功
     */
    public function calculateDeviationRates(PriceHistory $priceHistory, ?array $additionalData = null): bool
    {
        try {
            $updated = false;

            // 计算促销价格偏差率
            if ($priceHistory->price !== null && $priceHistory->sub_price !== null) {
                $promotionRate = $this->calculator->calculatePromotionDeviation(
                    $priceHistory->price,
                    $priceHistory->sub_price
                );

                if ($promotionRate !== null) {
                    $priceHistory->promotion_deviation_rate = $promotionRate;
                    $updated = true;
                }
            }

            // 计算渠道价格偏差率
            $officialGuidePrice = $priceHistory->official_guide_price 
                               ?? $additionalData['official_guide_price'] 
                               ?? null;

            if ($officialGuidePrice !== null && $priceHistory->sub_price !== null) {
                $channelRate = $this->calculator->calculateChannelDeviation(
                    $officialGuidePrice,
                    $priceHistory->sub_price
                );

                if ($channelRate !== null) {
                    $priceHistory->channel_deviation_rate = $channelRate;
                    // 如果从额外数据中获取了官方指导价，保存到记录中
                    if ($priceHistory->official_guide_price === null) {
                        $priceHistory->official_guide_price = $officialGuidePrice;
                    }
                    $updated = true;
                }
            }

            // 更新计算状态和时间
            if ($updated || $priceHistory->promotion_deviation_rate !== null || $priceHistory->channel_deviation_rate !== null) {
                $priceHistory->calculation_status = 'calculated';
                $priceHistory->deviation_calculated_at = Carbon::now();
            } else {
                $priceHistory->calculation_status = 'failed';
            }

            $priceHistory->save();

            return $updated;

        } catch (Exception $e) {
            $priceHistory->calculation_status = 'failed';
            $priceHistory->save();

            Log::error('偏差率计算失败', [
                'price_history_id' => $priceHistory->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 批量计算偏差率
     * 
     * @param array $filters 过滤条件
     * @param int $limit 每批处理数量
     * @return array 处理结果
     */
    public function batchCalculateDeviationRates(array $filters = [], int $limit = 100): array
    {
        $query = PriceHistory::needsCalculation();

        // 应用过滤条件
        if (!empty($filters['sku_id'])) {
            $query->where('sku_id', $filters['sku_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $records = $query->limit($limit)->get();
        
        $successCount = 0;
        $failureCount = 0;
        $processedIds = [];

        foreach ($records as $record) {
            if ($this->calculateDeviationRates($record)) {
                $successCount++;
            } else {
                $failureCount++;
            }
            $processedIds[] = $record->id;
        }

        $result = [
            'processed' => count($records),
            'success' => $successCount,
            'failed' => $failureCount,
            'processed_ids' => $processedIds
        ];

        Log::info('批量偏差率计算完成', $result);

        return $result;
    }

    /**
     * 获取需要计算偏差率的记录数量
     * 
     * @param array $filters 过滤条件
     * @return int 记录数量
     */
    public function getPendingCalculationCount(array $filters = []): int
    {
        $query = PriceHistory::needsCalculation();

        if (!empty($filters['sku_id'])) {
            $query->where('sku_id', $filters['sku_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query->count();
    }

    /**
     * 创建带价格变化计算的价格历史记录
     * 
     * @param array $data 价格数据
     * @param int|null $skuId SKU ID
     * @return PriceHistory|null 创建的价格历史记录
     */
    public function createWithPriceChangeCalculation(array $data, ?int $skuId = null): ?PriceHistory
    {
        if ($skuId) {
            $data['sku_id'] = $skuId;
        }

        // 获取上一条价格记录来计算价格变化
        if (isset($data['sku_id'])) {
            $lastRecord = PriceHistory::forSku($data['sku_id'])
                                    ->orderBy('timestamp', 'desc')
                                    ->first();

            if ($lastRecord && isset($data['price'])) {
                $currentPrice = (float) $data['price'];
                $lastPrice = (float) $lastRecord->price;
                
                $data['price_change'] = $currentPrice - $lastPrice;
                
                if ($lastPrice > 0) {
                    $data['price_change_rate'] = (($currentPrice - $lastPrice) / $lastPrice) * 100;
                } else {
                    $data['price_change_rate'] = 0;
                }
            }
        }

        return $this->createPriceHistory($data);
    }

    /**
     * 获取SKU的偏差率趋势
     * 
     * @param int $skuId SKU ID
     * @param int $days 天数
     * @return array 趋势数据
     */
    public function getDeviationTrend(int $skuId, int $days = 30): array
    {
        $records = PriceHistory::forSku($skuId)
                              ->calculated()
                              ->recent($days)
                              ->orderBy('timestamp')
                              ->select([
                                  'timestamp',
                                  'price',
                                  'sub_price',
                                  'official_guide_price',
                                  'promotion_deviation_rate',
                                  'channel_deviation_rate'
                              ])
                              ->get();

        return [
            'records' => $records,
            'stats' => PriceHistory::getDeviationStats($skuId, $days),
            'count' => $records->count()
        ];
    }
} 
<?php

namespace Tests\Feature;

use App\Models\ApiConfiguration;
use App\Services\ApiRateLimitService;
use App\Services\ApiHealthMonitorService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ApiRateLimitTest extends TestCase
{
    use RefreshDatabase;

    protected ApiRateLimitService $rateLimitService;
    protected ApiHealthMonitorService $healthMonitorService;
    protected ApiConfiguration $config;

    protected function setUp(): void
    {
        parent::setUp();

        $this->rateLimitService = new ApiRateLimitService();
        $this->healthMonitorService = new ApiHealthMonitorService();

        $this->config = ApiConfiguration::create([
            'name' => 'Test API',
            'slug' => 'test-api',
            'description' => 'Test API Configuration',
            'platform_type' => 'custom',
            'base_url' => 'https://api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'rate_limit_per_minute' => 60,
            'rate_limit_per_hour' => 1000,
            'rate_limit_per_day' => 10000,
            'health_check_endpoint' => '/health',
            'is_active' => true,
        ]);
    }

    public function test_can_check_initial_rate_limits()
    {
        $this->assertTrue($this->rateLimitService->canMakeRequest($this->config->id));
    }

    public function test_can_record_request_and_check_limits()
    {
        // Record a request
        $this->rateLimitService->recordRequest($this->config->id);

        // Should still be able to make requests
        $this->assertTrue($this->rateLimitService->canMakeRequest($this->config->id));

        // Get remaining limits
        $limits = $this->rateLimitService->getRemainingLimits($this->config->id);

        $this->assertEquals(59, $limits['per_minute']['remaining']);
        $this->assertEquals(999, $limits['per_hour']['remaining']);
        $this->assertEquals(9999, $limits['per_day']['remaining']);
    }

    public function test_rate_limit_exceeded()
    {
        // Simulate exceeding minute limit
        for ($i = 0; $i < 60; $i++) {
            $this->rateLimitService->recordRequest($this->config->id);
        }
        
        $this->assertFalse($this->rateLimitService->canMakeRequest($this->config->id));
        
        $limits = $this->rateLimitService->getRemainingLimits($this->config->id);
        $this->assertEquals(0, $limits['per_minute']['remaining']);
    }

    public function test_can_reset_rate_limits()
    {
        // Record some requests
        for ($i = 0; $i < 30; $i++) {
            $this->rateLimitService->recordRequest($this->config->id);
        }
        
        // Reset limits
        $this->rateLimitService->resetLimits($this->config->id);
        
        // Should be able to make requests again
        $this->assertTrue($this->rateLimitService->canMakeRequest($this->config->id));
        
        $limits = $this->rateLimitService->getRemainingLimits($this->config->id);
        $this->assertEquals(60, $limits['per_minute']['remaining']);
    }

    public function test_can_get_usage_stats()
    {
        // Record some requests
        for ($i = 0; $i < 5; $i++) {
            $this->rateLimitService->recordRequest($this->config->id);
        }
        
        $stats = $this->rateLimitService->getUsageStats($this->config->id);
        
        $this->assertEquals(5, $stats['total_requests']);
        $this->assertArrayHasKey('requests_per_minute', $stats);
        $this->assertArrayHasKey('requests_per_hour', $stats);
        $this->assertArrayHasKey('requests_per_day', $stats);
    }

    public function test_health_check_successful()
    {
        // Mock successful HTTP response
        Http::fake([
            'api.example.com/health' => Http::response(['status' => 'ok'], 200)
        ]);
        
        $result = $this->healthMonitorService->checkHealth($this->config);
        
        $this->assertEquals('healthy', $result['status']);
        $this->assertEquals(200, $result['http_status']);
        $this->assertArrayHasKey('response_time', $result);
        $this->assertArrayHasKey('message', $result);
    }

    public function test_health_check_failed()
    {
        // Mock failed HTTP response
        Http::fake([
            'api.example.com/health' => Http::response(['error' => 'Service unavailable'], 503)
        ]);
        
        $result = $this->healthMonitorService->checkHealth($this->config);
        
        $this->assertEquals('unhealthy', $result['status']);
        $this->assertEquals(503, $result['http_status']);
    }

    public function test_bulk_health_check()
    {
        $config2 = ApiConfiguration::create([
            'name' => 'Test API 2',
            'slug' => 'test-api-2',
            'description' => 'Test API Configuration 2',
            'platform_type' => 'custom',
            'base_url' => 'https://api2.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key-2'],
            'health_check_endpoint' => '/health',
            'is_active' => true,
        ]);
        
        // Mock HTTP responses
        Http::fake([
            'api.example.com/health' => Http::response(['status' => 'ok'], 200),
            'api2.example.com/health' => Http::response(['error' => 'Service unavailable'], 503)
        ]);
        
        $configurations = collect([$this->config, $config2]);
        $results = $this->healthMonitorService->bulkHealthCheck($configurations);
        
        $this->assertCount(2, $results);
        $this->assertEquals('healthy', $results[0]['status']);
        $this->assertEquals('unhealthy', $results[1]['status']);
    }

    public function test_controller_health_check_with_rate_limit()
    {
        // Create admin role first
        $adminRole = \App\Models\Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);

        // Create a user with admin role for authentication
        $user = \App\Models\User::factory()->create();
        $user->roles()->attach($adminRole->id);

        // Mock successful HTTP response
        Http::fake([
            'api.example.com/health' => Http::response(['status' => 'ok'], 200)
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api-configurations/{$this->config->id}/health-check");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'response_time',
                'http_status',
                'message',
                'data'
            ]);
    }

    public function test_controller_rate_limit_status()
    {
        // Create admin role first
        $adminRole = \App\Models\Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);

        // Create a user with admin role for authentication
        $user = \App\Models\User::factory()->create();
        $user->roles()->attach($adminRole->id);

        $response = $this->actingAs($user)
            ->getJson("/api-configurations/{$this->config->id}/rate-limits");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'configuration',
                'limits',
                'usage',
                'can_make_request'
            ]);
    }

    public function test_controller_reset_rate_limits()
    {
        // Create admin role first
        $adminRole = \App\Models\Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);

        // Create a user with admin role for authentication
        $user = \App\Models\User::factory()->create();
        $user->roles()->attach($adminRole->id);

        // Record some requests first
        for ($i = 0; $i < 10; $i++) {
            $this->rateLimitService->recordRequest($this->config->id);
        }

        $response = $this->actingAs($user)
            ->postJson("/api-configurations/{$this->config->id}/reset-limits");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'configuration',
                'limits'
            ]);

        // Verify limits were reset
        $this->assertTrue($this->rateLimitService->canMakeRequest($this->config->id));
    }

    protected function tearDown(): void
    {
        // Clean up cache
        Cache::flush();
        parent::tearDown();
    }
}

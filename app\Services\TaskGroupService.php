<?php

namespace App\Services;

use App\Models\TaskGroup;
use App\Models\MonitorTask;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TaskGroupService
{
    /**
     * 批量分配任务到分组
     */
    public function batchAssignTasks(TaskGroup $group, array $taskIds, int $userId): array
    {
        $assignedCount = MonitorTask::whereIn('id', $taskIds)
                                  ->where('user_id', $userId)
                                  ->update(['task_group_id' => $group->id]);

        return [
            'assigned_count' => $assignedCount,
            'group_id' => $group->id,
            'task_ids' => $taskIds
        ];
    }

    /**
     * 自动分组任务
     */
    public function autoGroupTasks(int $userId, array $criteria): array
    {
        $results = [];
        
        foreach ($criteria as $criterion) {
            $groupData = [
                'name' => $criterion['group_name'],
                'description' => $criterion['description'] ?? null,
                'color' => $criterion['color'] ?? '#007bff',
                'sort_order' => $criterion['sort_order'] ?? 0,
                'user_id' => $userId,
                'is_active' => true
            ];
            
            $group = TaskGroup::create($groupData);
            
            // 根据条件查找匹配的任务
            $query = MonitorTask::where('user_id', $userId)->whereNull('task_group_id');
            
            foreach ($criterion['conditions'] as $condition) {
                switch ($condition['operator']) {
                    case '=':
                        $query->where($condition['field'], $condition['value']);
                        break;
                    case '!=':
                        $query->where($condition['field'], '!=', $condition['value']);
                        break;
                    case 'like':
                        $query->where($condition['field'], 'like', '%' . $condition['value'] . '%');
                        break;
                    case 'in':
                        $query->whereIn($condition['field'], $condition['value']);
                        break;
                }
            }
            
            $matchedTasks = $query->get();
            $taskIds = $matchedTasks->pluck('id')->toArray();
            
            if (!empty($taskIds)) {
                MonitorTask::whereIn('id', $taskIds)->update(['task_group_id' => $group->id]);
            }
            
            $results[] = [
                'group' => $group,
                'matched_tasks_count' => count($taskIds),
                'task_ids' => $taskIds
            ];
        }
        
        return $results;
    }

    /**
     * 获取分组分布分析
     */
    public function getGroupDistributionAnalysis(int $userId): array
    {
        $totalTasks = MonitorTask::where('user_id', $userId)->count();
        $groupedTasks = MonitorTask::where('user_id', $userId)->whereNotNull('task_group_id')->count();
        $ungroupedTasks = $totalTasks - $groupedTasks;
        
        $groupStats = TaskGroup::where('user_id', $userId)
                              ->withCount('monitorTasks')
                              ->get()
                              ->map(function ($group) {
                                  return [
                                      'id' => $group->id,
                                      'name' => $group->name,
                                      'task_count' => $group->monitor_tasks_count,
                                      'color' => $group->color
                                  ];
                              });

        return [
            'total_tasks' => $totalTasks,
            'grouped_tasks' => $groupedTasks,
            'ungrouped_tasks' => $ungroupedTasks,
            'grouping_percentage' => $totalTasks > 0 ? round(($groupedTasks / $totalTasks) * 100, 2) : 0,
            'group_statistics' => $groupStats,
            'total_groups' => $groupStats->count()
        ];
    }

    /**
     * 复制分组
     */
    public function duplicateGroup(TaskGroup $originalGroup, string $newName, int $userId, bool $copyTasks = false): TaskGroup
    {
        $newGroupData = [
            'name' => $newName,
            'description' => $originalGroup->description,
            'color' => $originalGroup->color,
            'is_active' => $originalGroup->is_active,
            'sort_order' => $originalGroup->sort_order,
            'user_id' => $userId,
            'settings' => $originalGroup->settings
        ];
        
        $newGroup = TaskGroup::create($newGroupData);
        
        if ($copyTasks) {
            $originalTasks = $originalGroup->monitorTasks;
            foreach ($originalTasks as $task) {
                // 创建任务的副本
                $newTaskData = $task->toArray();
                unset($newTaskData['id']);
                $newTaskData['task_group_id'] = $newGroup->id;
                $newTaskData['task_name'] = $task->task_name . ' (副本)';
                
                MonitorTask::create($newTaskData);
            }
        }
        
        return $newGroup;
    }

    /**
     * 合并多个分组
     */
    public function mergeGroups(array $groupIds, string $targetName, int $userId): TaskGroup
    {
        $groups = TaskGroup::where('user_id', $userId)->whereIn('id', $groupIds)->get();
        
        if ($groups->count() !== count($groupIds)) {
            throw new \Exception('某些分组不存在或不属于当前用户');
        }
        
        // 创建目标分组
        $targetGroup = TaskGroup::create([
            'name' => $targetName,
            'description' => '由多个分组合并而成',
            'color' => $groups->first()->color,
            'is_active' => true,
            'sort_order' => 0,
            'user_id' => $userId
        ]);
        
        // 将所有任务移动到目标分组
        $allTaskIds = [];
        foreach ($groups as $group) {
            $taskIds = $group->monitorTasks()->pluck('id')->toArray();
            $allTaskIds = array_merge($allTaskIds, $taskIds);
        }
        
        if (!empty($allTaskIds)) {
            MonitorTask::whereIn('id', $allTaskIds)->update(['task_group_id' => $targetGroup->id]);
        }
        
        // 删除原分组
        TaskGroup::whereIn('id', $groupIds)->delete();
        
        return $targetGroup;
    }
} 
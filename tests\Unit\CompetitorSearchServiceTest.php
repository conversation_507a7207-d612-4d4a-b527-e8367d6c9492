<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CompetitorSearchService;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

class CompetitorSearchServiceTest extends TestCase
{
    use RefreshDatabase;

    private CompetitorSearchService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CompetitorSearchService();
    }

    public function test_search_competitors_with_keywords()
    {
        // 创建测试产品
        $product1 = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max 手机',
            'category_path' => '数码产品/手机/智能手机',
            'min_price' => 8999,
            'rating' => 4.8,
            'total_sales' => 1000,
        ]);

        $product2 = Product::factory()->create([
            'title' => '华为 Mate 50 智能手机',
            'category_path' => '数码产品/手机/智能手机',
            'min_price' => 4999,
            'rating' => 4.6,
            'total_sales' => 500,
        ]);

        // 搜索关键词
        $result = $this->service->searchCompetitors(
            keywords: ['手机', 'iPhone'],
            excludeWords: [],
            categories: ['数码产品/手机'],
            filters: [],
            operator: 'OR',
            limit: 10
        );

        $this->assertTrue($result['success']);
        $this->assertGreaterThan(0, $result['meta']['total']);
    }

    public function test_search_with_exclude_words()
    {
        // 创建测试产品
        Product::factory()->create([
            'title' => 'iPhone 14 全新正品',
            'category_path' => '数码产品/手机',
        ]);

        Product::factory()->create([
            'title' => 'iPhone 14 二手翻新',
            'category_path' => '数码产品/手机',
        ]);

        // 搜索并排除二手产品
        $result = $this->service->searchCompetitors(
            keywords: ['iPhone'],
            excludeWords: ['二手', '翻新'],
            categories: [],
            filters: [],
            operator: 'OR',
            limit: 10
        );

        $this->assertTrue($result['success']);
        // 验证结果不包含被排除的词
        foreach ($result['data'] as $product) {
            $this->assertStringNotContainsString('二手', $product->title);
            $this->assertStringNotContainsString('翻新', $product->title);
        }
    }

    public function test_category_path_matching()
    {
        // 创建不同分类的产品
        Product::factory()->create([
            'title' => '苹果手机',
            'category_path' => '数码产品/手机/智能手机/苹果',
        ]);

        Product::factory()->create([
            'title' => '苹果笔记本',
            'category_path' => '数码产品/电脑/笔记本/苹果',
        ]);

        // 搜索特定分类
        $result = $this->service->searchCompetitors(
            keywords: ['苹果'],
            excludeWords: [],
            categories: ['数码产品/手机'],
            filters: [],
            operator: 'OR',
            limit: 10
        );

        $this->assertTrue($result['success']);
        // 验证结果只包含手机分类
        foreach ($result['data'] as $product) {
            $this->assertStringContainsString('手机', $product->category_path);
        }
    }

    public function test_and_or_operator_logic()
    {
        // 创建测试产品
        Product::factory()->create([
            'title' => 'iPhone 14 Pro 手机',
            'category_path' => '数码产品/手机',
        ]);

        Product::factory()->create([
            'title' => 'iPhone 13 配件',
            'category_path' => '数码产品/配件',
        ]);

        Product::factory()->create([
            'title' => '华为 Pro 手机',
            'category_path' => '数码产品/手机',
        ]);

        // AND操作符 - 必须包含所有关键词
        $andResult = $this->service->searchCompetitors(
            keywords: ['iPhone', 'Pro'],
            excludeWords: [],
            categories: [],
            filters: [],
            operator: 'AND',
            limit: 10
        );

        // OR操作符 - 包含任一关键词即可
        $orResult = $this->service->searchCompetitors(
            keywords: ['iPhone', 'Pro'],
            excludeWords: [],
            categories: [],
            filters: [],
            operator: 'OR',
            limit: 10
        );

        $this->assertTrue($andResult['success']);
        $this->assertTrue($orResult['success']);
        
        // OR结果应该包含更多产品
        $this->assertGreaterThanOrEqual($andResult['meta']['total'], $orResult['meta']['total']);
    }

    public function test_find_similar_competitors()
    {
        // 创建目标产品
        $targetProduct = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max',
            'category_path' => '数码产品/手机/智能手机',
            'shop_id' => 1,
        ]);

        // 创建相似产品
        Product::factory()->create([
            'title' => 'iPhone 14 Pro',
            'category_path' => '数码产品/手机/智能手机',
            'shop_id' => 2,
        ]);

        Product::factory()->create([
            'title' => '华为 Mate 50 Pro',
            'category_path' => '数码产品/手机/智能手机',
            'shop_id' => 3,
        ]);

        // 查找相似竞争对手
        $result = $this->service->findSimilarCompetitors($targetProduct);

        $this->assertTrue($result['success']);
        $this->assertGreaterThan(0, $result['meta']['total']);
        
        // 验证结果不包含目标产品自己的店铺
        foreach ($result['data'] as $product) {
            $this->assertNotEquals($targetProduct->shop_id, $product->shop_id);
        }
    }

    public function test_relevance_scoring()
    {
        // 创建不同质量的产品
        $highQualityProduct = Product::factory()->create([
            'title' => 'iPhone 14 Pro Max 正品',
            'category_path' => '数码产品/手机/智能手机',
            'min_price' => 8999,
            'rating' => 4.9,
            'total_sales' => 10000,
        ]);

        $lowQualityProduct = Product::factory()->create([
            'title' => 'iPhone 14 山寨版',
            'category_path' => '数码产品/手机/智能手机',
            'min_price' => 999,
            'rating' => 2.1,
            'total_sales' => 10,
        ]);

        // 搜索并验证评分
        $result = $this->service->searchCompetitors(
            keywords: ['iPhone', '14'],
            excludeWords: [],
            categories: ['数码产品/手机'],
            filters: [],
            operator: 'AND',
            limit: 10
        );

        $this->assertTrue($result['success']);
        
        // 验证高质量产品得分更高
        $products = $result['data']->keyBy('id');
        if (isset($products[$highQualityProduct->id]) && isset($products[$lowQualityProduct->id])) {
            $this->assertGreaterThan(
                $products[$lowQualityProduct->id]->relevance_score,
                $products[$highQualityProduct->id]->relevance_score
            );
        }
    }

    public function test_search_suggestions()
    {
        // 创建测试产品
        Product::factory()->create([
            'title' => 'iPhone 14 Pro Max',
            'category_path' => '数码产品/手机',
            'shop_name' => '苹果官方旗舰店',
        ]);

        Product::factory()->create([
            'title' => 'iPhone 13 Pro',
            'category_path' => '数码产品/手机',
            'shop_name' => '苹果授权店',
        ]);

        // 获取搜索建议
        $result = $this->service->getSearchSuggestions('iPhone', 5);

        $this->assertTrue($result['success']);
        $this->assertIsArray($result['suggestions']);
        $this->assertLessThanOrEqual(5, count($result['suggestions']));
        
        // 验证建议格式
        if (!empty($result['suggestions'])) {
            $suggestion = $result['suggestions'][0];
            $this->assertArrayHasKey('title', $suggestion);
            $this->assertArrayHasKey('category', $suggestion);
            $this->assertArrayHasKey('shop', $suggestion);
        }
    }

    public function test_price_and_rating_filters()
    {
        // 创建不同价格和评分的产品
        Product::factory()->create([
            'title' => '高价手机',
            'min_price' => 5000,
            'rating' => 4.8,
        ]);

        Product::factory()->create([
            'title' => '低价手机',
            'min_price' => 1000,
            'rating' => 3.5,
        ]);

        // 使用价格和评分筛选
        $result = $this->service->searchCompetitors(
            keywords: ['手机'],
            excludeWords: [],
            categories: [],
            filters: [
                'min_price' => 2000,
                'max_price' => 8000,
                'min_rating' => 4.0,
            ],
            operator: 'OR',
            limit: 10
        );

        $this->assertTrue($result['success']);
        
        // 验证筛选结果
        foreach ($result['data'] as $product) {
            $this->assertGreaterThanOrEqual(2000, $product->min_price);
            $this->assertLessThanOrEqual(8000, $product->min_price);
            $this->assertGreaterThanOrEqual(4.0, $product->rating);
        }
    }
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 电商市场监测系统</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .content-wrapper {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table-responsive {
            border-radius: 0.5rem;
        }
        .status-active {
            color: #198754;
        }
        .status-inactive {
            color: #dc3545;
        }
        .status-locked {
            color: #fd7e14;
        }
        .role-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">管理后台</h5>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="{{ route('admin.users.index') }}">
                                <i class="fas fa-users me-2"></i>用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white-50" href="{{ route('admin.roles.index') }}">
                                <i class="fas fa-user-tag me-2"></i>角色管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white-50" href="#">
                                <i class="fas fa-chart-line me-2"></i>监控面板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white-50" href="#">
                                <i class="fas fa-cogs me-2"></i>系统设置
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3 text-white-50">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white-50" href="/">
                                <i class="fas fa-home me-2"></i>返回首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="nav-link text-white-50 btn btn-link border-0 p-0">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content-wrapper">
                <!-- 导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">用户管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选栏 -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <form method="GET" action="{{ route('admin.users.index') }}" class="row g-3">
                                    <div class="col-md-3">
                                        <label for="search" class="form-label">搜索用户</label>
                                        <input type="text" class="form-control" id="search" name="search" 
                                               value="{{ request('search') }}" placeholder="姓名/用户名/邮箱">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="status" class="form-label">状态</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="">全部状态</option>
                                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>正常</option>
                                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>禁用</option>
                                            <option value="locked" {{ request('status') == 'locked' ? 'selected' : '' }}>锁定</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="role" class="form-label">角色</label>
                                        <select class="form-select" id="role" name="role">
                                            <option value="">全部角色</option>
                                            @foreach($roles as $role)
                                                <option value="{{ $role->id }}" {{ request('role') == $role->id ? 'selected' : '' }}>
                                                    {{ $role->display_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="per_page" class="form-label">每页显示</label>
                                        <select class="form-select" id="per_page" name="per_page">
                                            <option value="10" {{ request('per_page') == '10' ? 'selected' : '' }}>10</option>
                                            <option value="25" {{ request('per_page') == '25' ? 'selected' : '' }}>25</option>
                                            <option value="50" {{ request('per_page') == '50' ? 'selected' : '' }}>50</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-undo"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">用户列表</h5>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#batchActionModal">
                                    <i class="fas fa-tasks"></i> 批量操作
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th>ID</th>
                                        <th>用户信息</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>最后登录</th>
                                        <th>注册时间</th>
                                        <th width="200">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($users as $user)
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input user-checkbox" value="{{ $user->id }}">
                                            </td>
                                            <td>{{ $user->id }}</td>
                                            <td>
                                                <div>
                                                    <strong>{{ $user->name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $user->username }}</small>
                                                    <br>
                                                    <small class="text-muted">{{ $user->email }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                @foreach($user->roles as $role)
                                                    <span class="badge bg-info role-badge">{{ $role->display_name }}</span>
                                                @endforeach
                                            </td>
                                            <td>
                                                @switch($user->status)
                                                    @case('active')
                                                        <span class="badge bg-success">正常</span>
                                                        @break
                                                    @case('inactive')
                                                        <span class="badge bg-danger">禁用</span>
                                                        @break
                                                    @case('locked')
                                                        <span class="badge bg-warning">锁定</span>
                                                        @break
                                                @endswitch
                                            </td>
                                            <td>
                                                @if($user->last_login_at)
                                                    <small>{{ $user->last_login_at->format('Y-m-d H:i:s') }}</small>
                                                @else
                                                    <small class="text-muted">从未登录</small>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $user->created_at->format('Y-m-d H:i:s') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-outline-info" title="查看详情">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.users.assign-roles', $user) }}" class="btn btn-outline-primary" title="分配角色">
                                                        <i class="fas fa-user-tag"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="toggleUserStatus({{ $user->id }}, '{{ $user->status }}')" title="切换状态">
                                                        <i class="fas fa-toggle-{{ $user->status === 'active' ? 'on' : 'off' }}"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="fas fa-users fa-3x mb-3"></i>
                                                    <p>暂无用户数据</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        @if($users->hasPages())
                            <div class="d-flex justify-content-center mt-4">
                                {{ $users->appends(request()->query())->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div class="modal fade" id="batchActionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchActionForm">
                        @csrf
                        <div class="mb-3">
                            <label for="batchAction" class="form-label">选择操作</label>
                            <select class="form-select" id="batchAction" name="action" required>
                                <option value="">请选择操作</option>
                                <option value="activate">启用用户</option>
                                <option value="deactivate">禁用用户</option>
                                <option value="assign_role">分配角色</option>
                            </select>
                        </div>
                        <div class="mb-3" id="roleSelectDiv" style="display: none;">
                            <label for="batchRole" class="form-label">选择角色</label>
                            <select class="form-select" id="batchRole" name="role_id">
                                <option value="">请选择角色</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->id }}">{{ $role->display_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeBatchAction()">执行操作</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF Token 设置
        window.axios = axios;
        window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        window.axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // 全选/取消全选
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 批量操作显示/隐藏角色选择
        document.getElementById('batchAction').addEventListener('change', function() {
            const roleDiv = document.getElementById('roleSelectDiv');
            if (this.value === 'assign_role') {
                roleDiv.style.display = 'block';
            } else {
                roleDiv.style.display = 'none';
            }
        });

        // 切换用户状态
        function toggleUserStatus(userId, currentStatus) {
            const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
            const action = newStatus === 'active' ? '启用' : '禁用';
            
            if (confirm(`确定要${action}此用户吗？`)) {
                fetch(`/admin/users/${userId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ status: newStatus })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('操作失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }

        // 执行批量操作
        function executeBatchAction() {
            const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
            
            if (selectedUsers.length === 0) {
                alert('请先选择要操作的用户');
                return;
            }

            const action = document.getElementById('batchAction').value;
            if (!action) {
                alert('请选择要执行的操作');
                return;
            }

            const formData = {
                action: action,
                user_ids: selectedUsers
            };

            if (action === 'assign_role') {
                const roleId = document.getElementById('batchRole').value;
                if (!roleId) {
                    alert('请选择要分配的角色');
                    return;
                }
                formData.role_id = roleId;
            }

            if (confirm(`确定要对选中的 ${selectedUsers.length} 个用户执行此操作吗？`)) {
                fetch('/admin/users/batch-action', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('操作失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }
    </script>
</body>
</html> 
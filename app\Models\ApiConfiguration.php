<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Services\SecureConfigurationService;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ApiConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'platform_type',
        'base_url',
        'auth_type',
        'auth_credentials',
        'version',
        'is_active',
        'is_deprecated',
        'rate_limit_per_minute',
        'rate_limit_per_hour',
        'rate_limit_per_day',
        'health_check_endpoint',
        'health_check_interval',
        'last_health_check_at',
        'health_status',
        'health_check_response',
        'headers',
        'timeout_settings',
        'retry_settings',
        'custom_config',
        'last_used_at',
        'request_count',
        'error_count'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_deprecated' => 'boolean',
        'rate_limit_per_minute' => 'integer',
        'rate_limit_per_hour' => 'integer',
        'rate_limit_per_day' => 'integer',
        'health_check_interval' => 'integer',
        'last_health_check_at' => 'datetime',
        'headers' => 'array',
        'timeout_settings' => 'array',
        'retry_settings' => 'array',
        'custom_config' => 'array',
        'last_used_at' => 'datetime',
        'request_count' => 'integer',
        'error_count' => 'integer'
    ];

    protected $hidden = [
        'auth_credentials'
    ];

    // Boot method to automatically generate slug
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name') && empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    // Encrypt auth_credentials when storing
    public function setAuthCredentialsAttribute($value)
    {
        if (is_array($value)) {
            $value = json_encode($value);
        }
        $this->attributes['auth_credentials'] = Crypt::encryptString($value);
    }

    // Decrypt auth_credentials when retrieving
    public function getAuthCredentialsAttribute($value)
    {
        if (empty($value)) {
            return null;
        }
        
        try {
            $decrypted = Crypt::decryptString($value);
            return json_decode($decrypted, true) ?: $decrypted;
        } catch (\Exception $e) {
            return null;
        }
    }

    // Scopes
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeNotDeprecated(Builder $query): Builder
    {
        return $query->where('is_deprecated', false);
    }

    public function scopeByPlatform(Builder $query, string $platform): Builder
    {
        return $query->where('platform_type', $platform);
    }

    public function scopeHealthy(Builder $query): Builder
    {
        return $query->where('health_status', 'healthy');
    }

    public function scopeUnhealthy(Builder $query): Builder
    {
        return $query->where('health_status', 'unhealthy');
    }

    public function scopeByVersion(Builder $query, string $version): Builder
    {
        return $query->where('version', $version);
    }

    public function scopeLatestVersion(Builder $query): Builder
    {
        return $query->orderBy('version', 'desc');
    }

    public function scopeDeprecated(Builder $query): Builder
    {
        return $query->where('is_deprecated', true);
    }

    // Helper methods
    public function isHealthy(): bool
    {
        return $this->health_status === 'healthy';
    }

    public function isExpired(): bool
    {
        if (!$this->last_health_check_at) {
            return true;
        }

        return $this->last_health_check_at->addSeconds($this->health_check_interval)
                   ->isPast();
    }

    public function needsHealthCheck(): bool
    {
        return $this->isExpired() || $this->health_status === 'unknown';
    }

    public function markAsUsed(): void
    {
        $this->update([
            'last_used_at' => now(),
            'request_count' => $this->request_count + 1
        ]);
    }

    public function recordError(): void
    {
        $this->increment('error_count');
    }

    public function updateHealthStatus(string $status, ?string $response = null): void
    {
        $this->update([
            'health_status' => $status,
            'last_health_check_at' => now(),
            'health_check_response' => $response
        ]);
    }

    public function getTimeoutSettings(): array
    {
        return $this->timeout_settings ?: [
            'connection_timeout' => 30,
            'read_timeout' => 60
        ];
    }

    public function getRetrySettings(): array
    {
        return $this->retry_settings ?: [
            'max_retries' => 3,
            'retry_delay' => 1000, // milliseconds
            'backoff_multiplier' => 2
        ];
    }

    public function getDefaultHeaders(): array
    {
        return $this->headers ?: [];
    }

    public function getRateLimitForPeriod(string $period): int
    {
        switch ($period) {
            case 'minute':
                return $this->rate_limit_per_minute;
            case 'hour':
                return $this->rate_limit_per_hour;
            case 'day':
                return $this->rate_limit_per_day;
            default:
                return $this->rate_limit_per_minute;
        }
    }

    // Generate API client configuration array
    public function toClientConfig(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'platform_type' => $this->platform_type,
            'base_url' => $this->base_url,
            'auth_type' => $this->auth_type,
            'auth_credentials' => $this->auth_credentials,
            'version' => $this->version,
            'headers' => $this->getDefaultHeaders(),
            'timeout_settings' => $this->getTimeoutSettings(),
            'retry_settings' => $this->getRetrySettings(),
            'rate_limits' => [
                'per_minute' => $this->rate_limit_per_minute,
                'per_hour' => $this->rate_limit_per_hour,
                'per_day' => $this->rate_limit_per_day
            ],
            'custom_config' => $this->custom_config ?: []
        ];
    }

    // Health check statistics
    public function getHealthCheckStats(): array
    {
        return [
            'status' => $this->health_status,
            'last_check' => $this->last_health_check_at?->format('Y-m-d H:i:s'),
            'next_check' => $this->last_health_check_at?->addSeconds($this->health_check_interval)->format('Y-m-d H:i:s'),
            'is_overdue' => $this->needsHealthCheck(),
            'check_interval' => $this->health_check_interval
        ];
    }

    // Usage statistics
    public function getUsageStats(): array
    {
        $errorRate = $this->request_count > 0 ?
            round(($this->error_count / $this->request_count) * 100, 2) : 0;

        return [
            'total_requests' => $this->request_count,
            'total_errors' => $this->error_count,
            'error_rate' => $errorRate,
            'last_used' => $this->last_used_at?->format('Y-m-d H:i:s'),
            'success_rate' => 100 - $errorRate
        ];
    }

    // Version management methods
    public function isLatestVersion(): bool
    {
        $latestVersion = static::where('platform_type', $this->platform_type)
            ->whereRaw('SUBSTRING_INDEX(name, " v", 1) = ?', [explode(' v', $this->name)[0]])
            ->max('version');

        return version_compare($this->version, $latestVersion) >= 0;
    }

    public function getOtherVersions()
    {
        $baseName = explode(' v', $this->name)[0];
        return static::where('platform_type', $this->platform_type)
            ->whereRaw('SUBSTRING_INDEX(name, " v", 1) = ?', [$baseName])
            ->where('id', '!=', $this->id)
            ->orderBy('version', 'desc')
            ->get();
    }

    public function markAsDeprecated(): void
    {
        $this->update(['is_deprecated' => true]);
    }

    public function markAsActive(): void
    {
        $this->update(['is_deprecated' => false]);
    }

    public function compareVersion(string $version): int
    {
        return version_compare($this->version, $version);
    }

    public function isVersionGreaterThan(string $version): bool
    {
        return $this->compareVersion($version) > 0;
    }

    public function isVersionLessThan(string $version): bool
    {
        return $this->compareVersion($version) < 0;
    }

    public function getVersionInfo(): array
    {
        return [
            'current_version' => $this->version,
            'is_latest' => $this->isLatestVersion(),
            'is_deprecated' => $this->is_deprecated,
            'other_versions' => $this->getOtherVersions()->pluck('version')->toArray(),
        ];
    }

    // Static methods for version management
    public static function getAvailableVersions(string $platformType, string $name): array
    {
        return static::where('platform_type', $platformType)
            ->where('name', $name)
            ->orderBy('version', 'desc')
            ->pluck('version', 'id')
            ->toArray();
    }

    public static function getLatestVersionConfig(string $platformType, string $name): ?self
    {
        return static::where('platform_type', $platformType)
            ->where('name', $name)
            ->where('is_active', true)
            ->where('is_deprecated', false)
            ->orderBy('version', 'desc')
            ->first();
    }

    public static function deprecateOlderVersions(string $platformType, string $name, string $currentVersion): void
    {
        static::where('platform_type', $platformType)
            ->where('name', $name)
            ->where('version', '<', $currentVersion)
            ->update(['is_deprecated' => true]);
    }

    // Security and environment variable methods
    public function getSecureCredentials(): array
    {
        $secureService = app(SecureConfigurationService::class);
        return $secureService->getEffectiveCredentials($this);
    }

    public function getEffectiveBaseUrl(): string
    {
        $secureService = app(SecureConfigurationService::class);
        return $secureService->getEffectiveValue($this, 'base_url') ?? $this->base_url;
    }

    public function validateSecurity(): array
    {
        $secureService = app(SecureConfigurationService::class);
        $configData = $secureService->getDecryptedConfiguration($this);
        return $secureService->validateSecurity($configData);
    }

    public function hasEnvironmentOverrides(): bool
    {
        $platformUpper = strtoupper($this->platform_type);
        $envKeys = [
            "API_CONFIG_{$platformUpper}_API_KEY",
            "API_CONFIG_{$platformUpper}_SECRET_KEY",
            "API_CONFIG_{$platformUpper}_ACCESS_TOKEN",
            "API_CONFIG_{$platformUpper}_BASE_URL",
        ];

        foreach ($envKeys as $envKey) {
            if (env($envKey) !== null) {
                return true;
            }
        }

        return false;
    }

    public function getEnvironmentOverrides(): array
    {
        $platformUpper = strtoupper($this->platform_type);
        $envKeys = [
            "API_CONFIG_{$platformUpper}_API_KEY",
            "API_CONFIG_{$platformUpper}_SECRET_KEY",
            "API_CONFIG_{$platformUpper}_ACCESS_TOKEN",
            "API_CONFIG_{$platformUpper}_BASE_URL",
        ];

        $overrides = [];
        foreach ($envKeys as $envKey) {
            if (env($envKey) !== null) {
                $overrides[$envKey] = '***masked***';
            }
        }

        return $overrides;
    }
}

<?php

namespace App\Services;

use App\Models\Product;
use App\Services\ProductSimilarityService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Laravel\Scout\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
use Exception;

/**
 * 竞争对手搜索服务
 * 使用MeiliSearch进行关键词和分类匹配
 */
class CompetitorSearchService
{
    private const CACHE_TTL = 3600; // 1小时缓存
    private const SEARCH_TIMEOUT = 30; // 30秒超时
    private const MAX_KEYWORDS = 20; // 最大关键词数量
    private const MAX_CATEGORIES = 10; // 最大分类数量
    private const DEFAULT_LIMIT = 100;
    private const MAX_LIMIT = 500;
    
    private ProductSimilarityService $similarityService;
    
    public function __construct(ProductSimilarityService $similarityService)
    {
        $this->similarityService = $similarityService;
    }

    /**
     * 搜索竞争对手产品
     *
     * @param array $keywords 关键词数组
     * @param array $excludeWords 排除词数组
     * @param array $categories 分类数组
     * @param array $filters 筛选条件
     * @param string $operator 操作符 (AND|OR)
     * @param int $limit 结果数量限制
     * @return array 搜索结果
     */
    public function searchCompetitors(
        array $keywords = [],
        array $excludeWords = [],
        array $categories = [],
        array $filters = [],
        string $operator = 'OR',
        int $limit = 100
    ): array {
        try {
            // 参数验证和清理
            $validatedParams = $this->validateAndCleanParams(
                $keywords, $excludeWords, $categories, $filters, $operator, $limit
            );
            
            if (!$validatedParams['success']) {
                return $validatedParams;
            }
            
            extract($validatedParams['data']);
            
            // 生成缓存键
            $cacheKey = $this->generateCacheKey($keywords, $excludeWords, $categories, $filters, $operator, $limit);
            
            // 尝试从缓存获取结果
            if ($cachedResult = Cache::get($cacheKey)) {
                Log::info('竞争对手搜索缓存命中', ['cache_key' => $cacheKey]);
                return $cachedResult;
            }
            
            // 执行搜索
            $searchResult = $this->performSearch($keywords, $excludeWords, $categories, $filters, $operator, $limit);
            
            // 缓存结果
            if ($searchResult['success']) {
                Cache::put($cacheKey, $searchResult, self::CACHE_TTL);
            }
            
            return $searchResult;
            
        } catch (Exception $e) {
            return $this->handleException($e, '竞争对手搜索失败');
        }
    }

    /**
     * 验证和清理搜索参数
     */
    private function validateAndCleanParams($keywords, $excludeWords, $categories, $filters, $operator, $limit): array
    {
        try {
            // 清理和验证关键词
            $keywords = array_filter(array_map('trim', $keywords));
            if (count($keywords) > self::MAX_KEYWORDS) {
                return [
                    'success' => false,
                    'error' => "关键词数量不能超过" . self::MAX_KEYWORDS . "个",
                    'error_code' => 'TOO_MANY_KEYWORDS'
                ];
            }
            
            // 清理排除词
            $excludeWords = array_filter(array_map('trim', $excludeWords));
            
            // 清理和验证分类
            $categories = array_filter(array_map('trim', $categories));
            if (count($categories) > self::MAX_CATEGORIES) {
                return [
                    'success' => false,
                    'error' => "分类数量不能超过" . self::MAX_CATEGORIES . "个",
                    'error_code' => 'TOO_MANY_CATEGORIES'
                ];
            }
            
            // 验证操作符
            if (!in_array($operator, ['AND', 'OR'])) {
                $operator = 'OR';
            }
            
            // 验证限制数量
            $limit = max(1, min($limit, self::MAX_LIMIT));
            
            // 验证筛选条件
            $filters = $this->validateFilters($filters);
            
            return [
                'success' => true,
                'data' => compact('keywords', 'excludeWords', 'categories', 'filters', 'operator', 'limit')
            ];
            
        } catch (Exception $e) {
            Log::error('参数验证失败', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'error' => '参数验证失败',
                'error_code' => 'VALIDATION_ERROR'
            ];
        }
    }

    /**
     * 验证筛选条件
     */
    private function validateFilters(array $filters): array
    {
        $validatedFilters = [];
        
        // 平台筛选
        if (isset($filters['platform']) && in_array($filters['platform'], ['taobao', 'tmall', 'jd', 'pdd'])) {
            $validatedFilters['platform'] = $filters['platform'];
        }
        
        // 价格筛选
        if (isset($filters['min_price']) && is_numeric($filters['min_price']) && $filters['min_price'] >= 0) {
            $validatedFilters['min_price'] = (float)$filters['min_price'];
        }
        
        if (isset($filters['max_price']) && is_numeric($filters['max_price']) && $filters['max_price'] > 0) {
            $validatedFilters['max_price'] = (float)$filters['max_price'];
        }
        
        // 确保最大价格大于最小价格
        if (isset($validatedFilters['min_price']) && isset($validatedFilters['max_price'])) {
            if ($validatedFilters['max_price'] <= $validatedFilters['min_price']) {
                unset($validatedFilters['max_price']);
            }
        }
        
        // 销量筛选
        if (isset($filters['min_sales']) && is_numeric($filters['min_sales']) && $filters['min_sales'] >= 0) {
            $validatedFilters['min_sales'] = (int)$filters['min_sales'];
        }
        
        // 评分筛选
        if (isset($filters['min_rating']) && is_numeric($filters['min_rating']) && $filters['min_rating'] >= 0 && $filters['min_rating'] <= 5) {
            $validatedFilters['min_rating'] = (float)$filters['min_rating'];
        }
        
        return $validatedFilters;
    }

    /**
     * 生成缓存键
     */
    private function generateCacheKey($keywords, $excludeWords, $categories, $filters, $operator, $limit): string
    {
        $keyData = [
            'keywords' => $keywords,
            'exclude_words' => $excludeWords,
            'categories' => $categories,
            'filters' => $filters,
            'operator' => $operator,
            'limit' => $limit
        ];
        
        return 'competitor_search:' . md5(json_encode($keyData));
    }

    /**
     * 执行搜索
     */
    private function performSearch($keywords, $excludeWords, $categories, $filters, $operator, $limit): array
    {
        try {
            // 设置搜索超时
            set_time_limit(self::SEARCH_TIMEOUT);
            
            // 如果没有关键词和分类，返回空结果
            if (empty($keywords) && empty($categories)) {
                return [
                    'success' => true,
                    'data' => [],
                    'meta' => [
                        'total' => 0,
                        'limit' => $limit,
                        'search_time' => 0,
                        'cache_hit' => false
                    ],
                    'message' => '请输入搜索关键词或选择分类'
                ];
            }
            
            $startTime = microtime(true);
            
            // 预处理关键词
            $processedKeywords = $this->preprocessKeywords($keywords);
            
            // 构建搜索查询
            $searchQuery = $this->buildSearchQuery($processedKeywords, $excludeWords, $operator);
            
            // 执行搜索
            $searchBuilder = Product::search($searchQuery);
            
            // 应用分类筛选
            if (!empty($categories)) {
                $searchBuilder = $this->applyCategoryFilters($searchBuilder, $categories);
            }
            
            // 应用额外筛选
            $searchBuilder = $this->applyAdditionalFilters($searchBuilder, $filters);
            
            // 获取结果
            $results = $searchBuilder->take($limit)->get();
            
            // 计算相关性评分
            if ($results->isNotEmpty()) {
                $results = $this->calculateRelevanceScores($results, $processedKeywords, $categories);
                $results = $results->take($limit); // 重新限制数量
            }
            
            $searchTime = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::info('竞争对手搜索完成', [
                'keywords' => $keywords,
                'results_count' => $results->count(),
                'search_time_ms' => $searchTime
            ]);
            
            return [
                'success' => true,
                'data' => $results->values()->toArray(),
                'meta' => [
                    'total' => $results->count(),
                    'limit' => $limit,
                    'search_time' => $searchTime,
                    'cache_hit' => false,
                    'keywords_processed' => $processedKeywords,
                    'search_query' => $searchQuery
                ],
                'message' => "找到 {$results->count()} 个相关产品"
            ];
            
        } catch (QueryException $e) {
            Log::error('数据库查询错误', [
                'error' => $e->getMessage(),
                'sql' => $e->getSql() ?? 'N/A'
            ]);
            
            return [
                'success' => false,
                'error' => '数据库查询失败，请稍后重试',
                'error_code' => 'DATABASE_ERROR'
            ];
            
        } catch (Exception $e) {
            return $this->handleException($e, '搜索执行失败');
        }
    }

    /**
     * 构建搜索查询字符串
     *
     * @param array $keywords 关键词数组
     * @param array $excludeWords 排除词数组
     * @param string $operator 操作符
     * @return string 查询字符串
     */
    private function buildSearchQuery(array $keywords, array $excludeWords, string $operator): string
    {
        $query = '';
        
        // 构建关键词查询
        if (!empty($keywords)) {
            // 预处理关键词，支持模糊匹配和同义词
            $processedKeywords = $this->preprocessKeywords($keywords);
            
            if ($operator === 'AND') {
                // AND逻辑：所有关键词都必须匹配
                $query .= implode(' ', $processedKeywords);
            } else {
                // OR逻辑：任一关键词匹配即可
                $query .= '(' . implode(' OR ', $processedKeywords) . ')';
            }
        }
        
        // 添加排除词
        if (!empty($excludeWords)) {
            foreach ($excludeWords as $excludeWord) {
                $query .= ' NOT "' . trim($excludeWord) . '"';
            }
        }
        
        return trim($query);
    }

    /**
     * 预处理关键词，支持模糊匹配和同义词扩展
     *
     * @param array $keywords 原始关键词数组
     * @return array 处理后的关键词数组
     */
    private function preprocessKeywords(array $keywords): array
    {
        $processedKeywords = [];
        $synonyms = $this->getSynonyms();
        
        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (empty($keyword)) continue;
            
            // 基础关键词
            $keywordVariants = ['"' . $keyword . '"'];
            
            // 添加同义词
            if (isset($synonyms[$keyword])) {
                foreach ($synonyms[$keyword] as $synonym) {
                    $keywordVariants[] = '"' . $synonym . '"';
                }
            }
            
            // 模糊匹配（去掉引号进行部分匹配）
            if (mb_strlen($keyword) > 2) {
                $keywordVariants[] = $keyword;
            }
            
            // 组合所有变体
            $processedKeywords[] = '(' . implode(' OR ', $keywordVariants) . ')';
        }
        
        return $processedKeywords;
    }

    /**
     * 获取同义词映射表
     *
     * @return array 同义词映射
     */
    private function getSynonyms(): array
    {
        return [
            '手机' => ['mobile', 'smartphone', '移动电话', '智能手机'],
            '电脑' => ['computer', 'pc', '计算机', '笔记本'],
            '笔记本' => ['laptop', 'notebook', '便携电脑'],
            '平板' => ['tablet', 'pad', '平板电脑'],
            '耳机' => ['headphone', 'earphone', '头戴式耳机', '入耳式耳机'],
            '音箱' => ['speaker', '扬声器', '音响'],
            '充电器' => ['charger', '充电头', '电源适配器'],
            '数据线' => ['cable', 'usb线', '充电线'],
            '保护壳' => ['case', '手机壳', '保护套'],
            '贴膜' => ['screen protector', '钢化膜', '保护膜'],
            // 可以根据业务需要扩展更多同义词
        ];
    }

    /**
     * 应用分类筛选
     *
     * @param Builder $searchBuilder Scout查询构建器
     * @param array $categories 分类数组
     * @return Builder
     */
    private function applyCategoryFilters(Builder $searchBuilder, array $categories): Builder
    {
        if (empty($categories)) {
            return $searchBuilder;
        }
        
        // 构建分类筛选条件，支持层级匹配和模糊匹配
        $categoryFilters = [];
        
        foreach ($categories as $category) {
            $category = trim($category);
            if (empty($category)) continue;
            
            // 精确匹配
            $categoryFilters[] = "category_path = '{$category}'";
            
            // 子分类匹配（以该分类开头）
            $categoryFilters[] = "category_path LIKE '{$category}%'";
            
            // 父分类匹配（包含该分类）
            $categoryFilters[] = "category_path LIKE '%{$category}%'";
            
            // 处理分类路径的层级结构
            $pathSegments = $this->parseCategoryPath($category);
            foreach ($pathSegments as $segment) {
                if (!empty($segment) && $segment !== $category) {
                    $categoryFilters[] = "category_path LIKE '%{$segment}%'";
                }
            }
        }
        
        if (!empty($categoryFilters)) {
            // 去重并应用筛选
            $uniqueFilters = array_unique($categoryFilters);
            $searchBuilder->whereRaw('(' . implode(' OR ', $uniqueFilters) . ')');
        }
        
        return $searchBuilder;
    }

    /**
     * 解析分类路径，提取各层级分类
     *
     * @param string $categoryPath 分类路径
     * @return array 分类层级数组
     */
    private function parseCategoryPath(string $categoryPath): array
    {
        // 支持多种分隔符：/ > - _
        $segments = preg_split('/[\/>\-_]+/', $categoryPath);
        
        $parsedSegments = [];
        $currentPath = '';
        
        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (empty($segment)) continue;
            
            // 单个分类段
            $parsedSegments[] = $segment;
            
            // 累积路径
            if (empty($currentPath)) {
                $currentPath = $segment;
            } else {
                $currentPath .= '/' . $segment;
                $parsedSegments[] = $currentPath;
            }
        }
        
        return array_unique($parsedSegments);
    }

    /**
     * 计算搜索结果的相关性评分
     *
     * @param Collection $results 搜索结果
     * @param array $keywords 关键词数组
     * @param array $categories 分类数组
     * @return Collection 带评分的结果
     */
    private function calculateRelevanceScores($results, array $keywords, array $categories)
    {
        return $results->map(function ($product) use ($keywords, $categories) {
            $score = 0;
            
            // 标题匹配评分 (权重: 40%)
            $titleScore = $this->calculateTitleMatchScore($product->title, $keywords);
            $score += $titleScore * 0.4;
            
            // 分类匹配评分 (权重: 30%)
            $categoryScore = $this->calculateCategoryMatchScore($product->category_path, $categories);
            $score += $categoryScore * 0.3;
            
            // 产品质量评分 (权重: 20%)
            $qualityScore = $this->calculateQualityScore($product);
            $score += $qualityScore * 0.2;
            
            // 商家信誉评分 (权重: 10%)
            $shopScore = $this->calculateShopScore($product);
            $score += $shopScore * 0.1;
            
            // 添加评分到产品对象
            $product->relevance_score = round($score, 2);
            
            return $product;
        })->sortByDesc('relevance_score');
    }

    /**
     * 计算标题匹配评分
     *
     * @param string $title 产品标题
     * @param array $keywords 关键词数组
     * @return float 评分 (0-100)
     */
    private function calculateTitleMatchScore(string $title, array $keywords): float
    {
        if (empty($keywords)) {
            return 0;
        }
        
        $title = mb_strtolower($title);
        $totalScore = 0;
        $maxScore = count($keywords) * 100;
        
        foreach ($keywords as $keyword) {
            $keyword = mb_strtolower(trim($keyword));
            if (empty($keyword)) continue;
            
            // 完全匹配
            if (mb_strpos($title, $keyword) !== false) {
                $totalScore += 100;
            } else {
                // 部分匹配（使用编辑距离）
                $partialScore = $this->calculatePartialMatchScore($title, $keyword);
                $totalScore += $partialScore;
            }
        }
        
        return $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;
    }

    /**
     * 计算分类匹配评分
     *
     * @param string $categoryPath 产品分类路径
     * @param array $categories 目标分类数组
     * @return float 评分 (0-100)
     */
    private function calculateCategoryMatchScore(string $categoryPath, array $categories): float
    {
        if (empty($categories)) {
            return 50; // 没有分类限制时给中等分
        }
        
        $categoryPath = mb_strtolower($categoryPath);
        $maxScore = 0;
        
        foreach ($categories as $category) {
            $category = mb_strtolower(trim($category));
            if (empty($category)) continue;
            
            // 完全匹配
            if ($categoryPath === $category) {
                $maxScore = max($maxScore, 100);
            } 
            // 包含匹配
            elseif (mb_strpos($categoryPath, $category) !== false) {
                $maxScore = max($maxScore, 80);
            } 
            // 层级匹配
            else {
                $segments = $this->parseCategoryPath($category);
                foreach ($segments as $segment) {
                    if (mb_strpos($categoryPath, mb_strtolower($segment)) !== false) {
                        $maxScore = max($maxScore, 60);
                        break;
                    }
                }
            }
        }
        
        return $maxScore;
    }

    /**
     * 计算产品质量评分
     *
     * @param Product $product 产品对象
     * @return float 评分 (0-100)
     */
    private function calculateQualityScore($product): float
    {
        $score = 0;
        
        // 评分因子 (30%)
        if (isset($product->rating) && $product->rating > 0) {
            $score += ($product->rating / 5) * 30;
        }
        
        // 销量因子 (40%)
        if (isset($product->total_sales) && $product->total_sales > 0) {
            // 使用对数缩放，避免销量过大的产品占主导
            $salesScore = min(log10($product->total_sales + 1) * 10, 40);
            $score += $salesScore;
        }
        
        // 价格合理性因子 (30%)
        if (isset($product->min_price) && $product->min_price > 0) {
            // 价格在合理范围内得分更高
            $priceScore = $this->calculatePriceReasonabilityScore($product->min_price);
            $score += $priceScore * 0.3;
        }
        
        return min($score, 100);
    }

    /**
     * 计算商家信誉评分
     *
     * @param Product $product 产品对象
     * @return float 评分 (0-100)
     */
    private function calculateShopScore($product): float
    {
        $score = 50; // 基础分
        
        // 店铺评分
        if (isset($product->shop) && isset($product->shop->rating)) {
            $score = ($product->shop->rating / 5) * 100;
        }
        
        return $score;
    }

    /**
     * 计算部分匹配评分
     *
     * @param string $text 文本
     * @param string $keyword 关键词
     * @return float 评分 (0-100)
     */
    private function calculatePartialMatchScore(string $text, string $keyword): float
    {
        // 简单的相似度计算
        $textWords = preg_split('/[\s\-_\/\\\|,，。！？；：]+/u', $text);
        $maxScore = 0;
        
        foreach ($textWords as $word) {
            $word = trim($word);
            if (empty($word)) continue;
            
            // 计算编辑距离相似度
            $similarity = $this->calculateStringSimilarity($word, $keyword);
            $maxScore = max($maxScore, $similarity * 100);
        }
        
        return $maxScore;
    }

    /**
     * 计算字符串相似度
     *
     * @param string $str1 字符串1
     * @param string $str2 字符串2
     * @return float 相似度 (0-1)
     */
    private function calculateStringSimilarity(string $str1, string $str2): float
    {
        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);
        
        if ($len1 === 0 || $len2 === 0) {
            return 0;
        }
        
        // 使用最长公共子序列计算相似度
        $maxLen = max($len1, $len2);
        $lcs = $this->longestCommonSubsequence($str1, $str2);
        
        return $lcs / $maxLen;
    }

    /**
     * 计算最长公共子序列长度
     *
     * @param string $str1 字符串1
     * @param string $str2 字符串2
     * @return int 最长公共子序列长度
     */
    private function longestCommonSubsequence(string $str1, string $str2): int
    {
        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);
        
        $dp = array_fill(0, $len1 + 1, array_fill(0, $len2 + 1, 0));
        
        for ($i = 1; $i <= $len1; $i++) {
            for ($j = 1; $j <= $len2; $j++) {
                if (mb_substr($str1, $i - 1, 1) === mb_substr($str2, $j - 1, 1)) {
                    $dp[$i][$j] = $dp[$i - 1][$j - 1] + 1;
                } else {
                    $dp[$i][$j] = max($dp[$i - 1][$j], $dp[$i][$j - 1]);
                }
            }
        }
        
        return $dp[$len1][$len2];
    }

    /**
     * 计算价格合理性评分
     *
     * @param float $price 价格
     * @return float 评分 (0-100)
     */
    private function calculatePriceReasonabilityScore(float $price): float
    {
        // 简单的价格合理性评估
        // 这里可以根据具体业务逻辑调整
        if ($price <= 0) return 0;
        if ($price <= 50) return 90;      // 低价区间
        if ($price <= 200) return 100;    // 中价区间
        if ($price <= 1000) return 80;    // 高价区间
        return 60;                        // 超高价区间
    }

    /**
     * 应用额外筛选条件
     *
     * @param Builder $searchBuilder Scout查询构建器
     * @param array $filters 筛选条件
     * @return Builder
     */
    private function applyAdditionalFilters(Builder $searchBuilder, array $filters): Builder
    {
        // 平台筛选
        if (isset($filters['platform'])) {
            $searchBuilder->where('source_platform', $filters['platform']);
        }
        
        // 价格范围筛选
        if (isset($filters['min_price'])) {
            $searchBuilder->where('min_price', '>=', $filters['min_price']);
        }
        
        if (isset($filters['max_price'])) {
            $searchBuilder->where('max_price', '<=', $filters['max_price']);
        }
        
        // 销量筛选
        if (isset($filters['min_sales'])) {
            $searchBuilder->where('total_sales', '>=', $filters['min_sales']);
        }
        
        // 评分筛选
        if (isset($filters['min_rating'])) {
            $searchBuilder->where('rating', '>=', $filters['min_rating']);
        }
        
        // 店铺筛选
        if (isset($filters['shop_id'])) {
            $searchBuilder->where('shop_id', $filters['shop_id']);
        }
        
        // 排除特定店铺
        if (isset($filters['exclude_shops']) && is_array($filters['exclude_shops'])) {
            foreach ($filters['exclude_shops'] as $shopId) {
                $searchBuilder->where('shop_id', '!=', $shopId);
            }
        }
        
        return $searchBuilder;
    }

    /**
     * 按相似度搜索竞争对手 (使用新的相似度算法)
     *
     * @param Product $targetProduct 目标产品
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    public function findSimilarCompetitors(Product $targetProduct, array $options = []): array
    {
        try {
            // 设置默认选项
            $limit = $options['limit'] ?? 50;
            $minSimilarityScore = $options['min_similarity'] ?? 0.3; // 最低相似度阈值
            $useAdvancedSimilarity = $options['use_advanced_similarity'] ?? true;
            
            if ($useAdvancedSimilarity) {
                return $this->findSimilarCompetitorsAdvanced($targetProduct, $options);
            }
            
            // 回退到原始搜索方法
            $keywords = $this->extractKeywordsFromProduct($targetProduct);
            $categories = [$targetProduct->category_path];
            
            // 排除自己
            $filters = array_merge($options['filters'] ?? [], [
                'exclude_shops' => [$targetProduct->shop_id],
            ]);
            
            return $this->searchCompetitors(
                keywords: $keywords,
                excludeWords: $options['exclude_words'] ?? [],
                categories: $categories,
                filters: $filters,
                operator: $options['operator'] ?? 'OR',
                limit: $limit
            );
            
        } catch (Exception $e) {
            return $this->handleException($e, '相似产品搜索失败');
        }
    }

    /**
     * 使用高级相似度算法搜索竞争对手
     *
     * @param Product $targetProduct 目标产品
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    public function findSimilarCompetitorsAdvanced(Product $targetProduct, array $options = []): array
    {
        try {
            $limit = $options['limit'] ?? 50;
            $minSimilarityScore = $options['min_similarity'] ?? 0.3;
            $cacheKey = "similarity_search:{$targetProduct->id}:" . md5(json_encode($options));
            
            // 尝试从缓存获取结果
            if ($cachedResult = Cache::get($cacheKey)) {
                Log::info('相似度搜索缓存命中', ['product_id' => $targetProduct->id]);
                return $cachedResult;
            }
            
            Log::info('开始高级相似度搜索', [
                'target_product_id' => $targetProduct->id,
                'limit' => $limit,
                'min_similarity' => $minSimilarityScore
            ]);
            
            // 第一步：获取候选产品（使用基础搜索扩大候选范围）
            $candidates = $this->getCandidateProducts($targetProduct, $options);
            
            if (empty($candidates)) {
                return [
                    'success' => true,
                    'data' => [],
                    'total' => 0,
                    'message' => '未找到候选产品'
                ];
            }
            
            // 第二步：使用相似度算法计算分数
            $similarityResults = $this->similarityService->calculateBatchSimilarity(
                $targetProduct, 
                $candidates
            );
            
            // 第三步：过滤和排序结果
            $filteredResults = array_filter($similarityResults, function($result) use ($minSimilarityScore) {
                return $result['similarity']['total_score'] >= $minSimilarityScore;
            });
            
            $limitedResults = array_slice($filteredResults, 0, $limit);
            
            // 格式化结果
            $formattedResults = [];
            foreach ($limitedResults as $result) {
                $formattedResults[] = [
                    'product' => $result['product'],
                    'similarity_score' => $result['similarity']['total_score'],
                    'similarity_breakdown' => $result['similarity'],
                    'relevance_score' => $this->calculateOverallRelevanceScore($result)
                ];
            }
            
            $response = [
                'success' => true,
                'data' => $formattedResults,
                'total' => count($formattedResults),
                'algorithm_info' => $this->similarityService->getAlgorithmConfig(),
                'search_params' => [
                    'target_product_id' => $targetProduct->id,
                    'min_similarity_score' => $minSimilarityScore,
                    'candidates_found' => count($candidates),
                    'results_after_filtering' => count($filteredResults)
                ],
                'message' => "找到 " . count($formattedResults) . " 个相似产品"
            ];
            
            // 缓存结果
            Cache::put($cacheKey, $response, self::CACHE_TTL);
            
            Log::info('高级相似度搜索完成', [
                'target_product_id' => $targetProduct->id,
                'results_count' => count($formattedResults),
                'candidates_count' => count($candidates)
            ]);
            
            return $response;
            
        } catch (Exception $e) {
            Log::error('高级相似度搜索失败', [
                'target_product_id' => $targetProduct->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->handleException($e, '高级相似度搜索失败');
        }
    }

    /**
     * 获取候选产品
     *
     * @param Product $targetProduct 目标产品
     * @param array $options 选项
     * @return array 候选产品列表
     */
    private function getCandidateProducts(Product $targetProduct, array $options = []): array
    {
        try {
            $candidateLimit = ($options['limit'] ?? 50) * 5; // 获取5倍数量的候选产品
            $maxCandidates = min($candidateLimit, 500); // 最多500个候选
            
            // 策略1：基于分类的候选
            $categoryQuery = Product::query()
                ->where('category_path', 'LIKE', '%' . $this->extractMainCategory($targetProduct->category_path) . '%')
                ->where('id', '!=', $targetProduct->id);
            
            // 策略2：基于价格范围的候选
            if ($targetProduct->min_price > 0) {
                $priceRange = $targetProduct->min_price * 0.3; // 30%价格浮动
                $categoryQuery = $categoryQuery->whereBetween('min_price', [
                    $targetProduct->min_price - $priceRange,
                    $targetProduct->min_price + $priceRange
                ]);
            }
            
            // 应用额外过滤条件
            if (isset($options['filters']['platform'])) {
                $categoryQuery = $categoryQuery->where('platform', $options['filters']['platform']);
            }
            
            $categoryBasedCandidates = $categoryQuery
                ->take($maxCandidates / 2)
                ->get()
                ->toArray();
            
            // 策略3：基于关键词的候选（使用搜索引擎）
            $keywords = $this->extractKeywordsFromProduct($targetProduct);
            if (!empty($keywords)) {
                try {
                    $keywordSearchResult = $this->searchCompetitors(
                        keywords: array_slice($keywords, 0, 5), // 取前5个关键词
                        categories: [],
                        filters: $options['filters'] ?? [],
                        operator: 'OR',
                        limit: $maxCandidates / 2
                    );
                    
                    $keywordBasedCandidates = $keywordSearchResult['success'] ? 
                        $keywordSearchResult['data'] : [];
                } catch (Exception $e) {
                    Log::warning('关键词候选搜索失败', ['error' => $e->getMessage()]);
                    $keywordBasedCandidates = [];
                }
            } else {
                $keywordBasedCandidates = [];
            }
            
            // 合并和去重候选产品
            $allCandidates = array_merge($categoryBasedCandidates, $keywordBasedCandidates);
            $uniqueCandidates = [];
            $seenIds = [];
            
            foreach ($allCandidates as $candidate) {
                $candidateId = is_array($candidate) ? $candidate['id'] : $candidate->id;
                if (!in_array($candidateId, $seenIds)) {
                    $seenIds[] = $candidateId;
                    $uniqueCandidates[] = is_array($candidate) ? 
                        Product::find($candidate['id']) : $candidate;
                }
                
                if (count($uniqueCandidates) >= $maxCandidates) {
                    break;
                }
            }
            
            // 过滤掉null值
            $uniqueCandidates = array_filter($uniqueCandidates, function($candidate) {
                return $candidate !== null;
            });
            
            Log::info('候选产品获取完成', [
                'target_product_id' => $targetProduct->id,
                'category_candidates' => count($categoryBasedCandidates),
                'keyword_candidates' => count($keywordBasedCandidates),
                'unique_candidates' => count($uniqueCandidates)
            ]);
            
            return array_values($uniqueCandidates);
            
        } catch (Exception $e) {
            Log::error('获取候选产品失败', [
                'target_product_id' => $targetProduct->id,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 提取主要分类
     *
     * @param string $categoryPath 分类路径
     * @return string 主要分类
     */
    private function extractMainCategory(string $categoryPath): string
    {
        $parts = $this->parseCategoryPath($categoryPath);
        return $parts[0] ?? $categoryPath;
    }

    /**
     * 计算综合相关性评分
     *
     * @param array $similarityResult 相似度结果
     * @return float 综合评分
     */
    private function calculateOverallRelevanceScore(array $similarityResult): float
    {
        $product = $similarityResult['product'];
        $similarity = $similarityResult['similarity'];
        
        // 基础相似度分数 (权重70%)
        $baseScore = $similarity['total_score'] * 0.7;
        
        // 产品质量分数 (权重20%)
        $qualityScore = $this->calculateQualityScore($product) * 0.2;
        
        // 商店信誉分数 (权重10%)
        $shopScore = $this->calculateShopScore($product) * 0.1;
        
        return min(1.0, $baseScore + $qualityScore + $shopScore);
    }

    /**
     * 从产品中提取关键词
     *
     * @param Product $product 产品模型
     * @return array 关键词数组
     */
    private function extractKeywordsFromProduct(Product $product): array
    {
        $title = $product->title;
        
        // 简单的关键词提取逻辑
        // 移除常见的停用词和符号
        $stopWords = ['的', '和', '与', '或', '及', '等', '为', '是', '有', '在', '了', '到', '从'];
        $keywords = [];
        
        // 分词（简单按空格和常见分隔符分割）
        $words = preg_split("/[\s\-_\/\\\|,，。！？；：]+/u", $title);
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 1 && !in_array($word, $stopWords)) {
                $keywords[] = $word;
            }
        }
        
        return array_unique($keywords);
    }

    /**
     * 获取搜索建议
     *
     * @param string $query 查询字符串
     * @param int $limit 建议数量
     * @return array 搜索建议
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        try {
            // 参数验证
            $query = trim($query);
            if (strlen($query) < 2) {
                return [
                    'success' => true,
                    'suggestions' => [],
                    'message' => '查询字符串太短'
                ];
            }
            
            $limit = max(1, min($limit, 20));
            
            // 生成缓存键
            $cacheKey = "search_suggestions:" . md5($query . $limit);
            
            // 尝试从缓存获取
            if ($cachedSuggestions = Cache::get($cacheKey)) {
                return $cachedSuggestions;
            }
            
            // 设置较短的超时时间
            set_time_limit(10);
            
            $results = Product::search($query)
                ->take($limit)
                ->get(['title', 'category_path', 'shop_name']);
            
            $suggestions = [];
            foreach ($results as $product) {
                $suggestions[] = [
                    'title' => $product->title,
                    'category' => $product->category_path,
                    'shop' => $product->shop_name,
                ];
            }
            
            $response = [
                'success' => true,
                'suggestions' => $suggestions,
                'message' => "找到 {$results->count()} 个建议"
            ];
            
            // 缓存建议结果（较短时间）
            Cache::put($cacheKey, $response, 600); // 10分钟
            
            return $response;
            
        } catch (Exception $e) {
            Log::error('搜索建议获取失败', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'suggestions' => [],
                'error' => '搜索建议获取失败',
                'error_code' => 'SUGGESTIONS_ERROR'
            ];
        }
    }

    /**
     * 统一异常处理
     */
    private function handleException(Exception $e, string $defaultMessage): array
    {
        $errorCode = 'UNKNOWN_ERROR';
        $errorMessage = $defaultMessage;
        
        // 根据异常类型设置不同的错误码和消息
        if ($e instanceof QueryException) {
            $errorCode = 'DATABASE_ERROR';
            $errorMessage = '数据库操作失败';
        } elseif ($e instanceof \Laravel\Scout\Engines\EngineException) {
            $errorCode = 'SEARCH_ENGINE_ERROR';
            $errorMessage = '搜索引擎错误';
        } elseif ($e instanceof \Illuminate\Http\Client\RequestException) {
            $errorCode = 'NETWORK_ERROR';
            $errorMessage = '网络请求失败';
        } elseif ($e instanceof \InvalidArgumentException) {
            $errorCode = 'INVALID_ARGUMENT';
            $errorMessage = '参数错误';
        }
        
        Log::error($defaultMessage, [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        
        return [
            'success' => false,
            'error' => $errorMessage,
            'error_code' => $errorCode,
            'debug' => app()->environment('local') ? [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ] : null
        ];
    }

    /**
     * 清理搜索缓存
     */
    public function clearSearchCache(?string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // 清理特定模式的缓存
                $keys = Cache::getRedis()->keys("*{$pattern}*");
                if (!empty($keys)) {
                    Cache::getRedis()->del($keys);
                }
            } else {
                // 清理所有搜索相关缓存
                $keys = Cache::getRedis()->keys('competitor_search:*');
                $suggestionKeys = Cache::getRedis()->keys('search_suggestions:*');
                
                $allKeys = array_merge($keys, $suggestionKeys);
                if (!empty($allKeys)) {
                    Cache::getRedis()->del($allKeys);
                }
            }
            
            Log::info('搜索缓存清理完成', ['pattern' => $pattern]);
            return true;
            
        } catch (Exception $e) {
            Log::error('搜索缓存清理失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取搜索统计信息
     */
    public function getSearchStats(): array
    {
        try {
            $stats = [
                'total_searches' => 0,
                'cache_hit_rate' => 0,
                'average_search_time' => 0,
                'popular_keywords' => [],
                'error_rate' => 0
            ];
            
            // 这里可以实现更复杂的统计逻辑
            // 例如从日志或专门的统计表中获取数据
            
            return [
                'success' => true,
                'data' => $stats
            ];
            
        } catch (Exception $e) {
            return $this->handleException($e, '获取搜索统计失败');
        }
    }
} 
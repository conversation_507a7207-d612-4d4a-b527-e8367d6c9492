<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CompetitorPromotionAnalysisService;
use App\Models\ProductSku;
use App\Models\PriceHistory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class CompetitorPromotionAnalysisServiceTest extends TestCase
{
    use RefreshDatabase;

    private $service;
    private $sku;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CompetitorPromotionAnalysisService();
        $this->sku = ProductSku::factory()->create();
    }

    /**
     * 测试有促销数据时的分析
     */
    public function test_analyze_with_promotion_data()
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // 创建模拟数据
        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(5),
            'promotion_info' => [['type' => 'discount', 'discount_rate' => 10], ['type' => 'gifts']],
        ]);
        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(10),
            'promotion_info' => [['type' => 'discount', 'discount_rate' => 15]],
        ]);
        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(10), // 同一天
            'promotion_info' => [['type' => 'full_reduction']],
        ]);
        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(20),
            'promotion_info' => [], // 无促销
        ]);

        $result = $this->service->analyze($this->sku->id, $startDate, $endDate);

        $this->assertEquals(['discount' => 2, 'gifts' => 1, 'full_reduction' => 1], $result['promotion_types']);
        $this->assertEquals(4, $result['total_promotions']);
        
        // 31天内有2天有促销 (day 5, day 10)
        $expectedFrequency = (2 / 31) * 100;
        $this->assertEquals(round($expectedFrequency, 2), $result['promotion_frequency']);
        
        $this->assertEquals(12.50, $result['avg_discount_rate']);
        $this->assertEquals(15, $result['max_discount_rate']);
        $this->assertEquals(10, $result['min_discount_rate']);
        $this->assertEquals(3, $result['data_points_count']);
        
        // 持续时间估算测试: 5号, 10号. 两个不连续的周期，每个1天
        $this->assertEquals(1, $result['promotion_duration_avg']);
    }

    /**
     * 测试没有促销数据时的分析
     */
    public function test_analyze_without_promotion_data()
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(5),
            'promotion_info' => null,
        ]);
        PriceHistory::factory()->create([
            'sku_id' => $this->sku->id,
            'timestamp' => $startDate->copy()->addDays(10),
            'promotion_info' => [],
        ]);

        $result = $this->service->analyze($this->sku->id, $startDate, $endDate);

        $this->assertEquals([], $result['promotion_types']);
        $this->assertEquals(0, $result['total_promotions']);
        $this->assertEquals(0.0, $result['promotion_frequency']);
        $this->assertEquals(0.0, $result['avg_discount_rate']);
        $this->assertEquals(0.0, $result['max_discount_rate']);
        $this->assertEquals(0.0, $result['min_discount_rate']);
        $this->assertEquals(0, $result['promotion_duration_avg']);
        $this->assertEquals(0, $result['data_points_count']);
    }
    
    /**
     * 测试连续促销周期的持续时间计算
     */
    public function test_average_promotion_duration_with_continuous_periods()
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // 周期1: 连续3天
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(1), 'promotion_info' => [['type' => 'd']]]);
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(2), 'promotion_info' => [['type' => 'd']]]);
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(3), 'promotion_info' => [['type' => 'd']]]);
        
        // 间断
        
        // 周期2: 连续2天
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(10), 'promotion_info' => [['type' => 'd']]]);
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(11), 'promotion_info' => [['type' => 'd']]]);

        // 间断

        // 周期3: 1天
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(20), 'promotion_info' => [['type' => 'd']]]);

        $result = $this->service->analyze($this->sku->id, $startDate, $endDate);
        
        // 平均持续时间 = (3 + 2 + 1) / 3 = 2
        $this->assertEquals(2, $result['promotion_duration_avg']);
    }

    /**
     * 测试促销策略的定性总结
     */
    public function test_it_can_summarize_promotion_strategy()
    {
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // 制造一个"频繁" (超过20%天数有促销), "短期" (平均<3天), "高折扣" (>25%) 的场景
        // 总天数 = 31. 促销天数 > 0.2 * 31 = 6.2天. 我们设置7天有促销。
        
        // 周期1: 2天, 折扣30%
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(1), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(2), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);
        
        // 周期2: 2天, 折扣30%
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(5), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(6), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);

        // 周期3: 1天, 折扣30%
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(10), 'promotion_info' => [['type' => 'clearance', 'discount_rate' => 30]]]);
        
        // 周期4: 1天, 折扣30%
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(15), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);

        // 周期5: 1天, 折扣30%
        PriceHistory::factory()->create(['sku_id' => $this->sku->id, 'timestamp' => $startDate->copy()->addDays(20), 'promotion_info' => [['type' => 'flash_sale', 'discount_rate' => 30]]]);


        $summaryResult = $this->service->summarizeStrategy($this->sku->id, $startDate, $endDate);

        // 验证描述符
        $expectedDescriptors = [
            'frequency' => '频繁', // 7 / 31 = ~22.5% (> 20%)
            'duration' => '短期',   // (2+2+1+1+1) / 5 = 1.4天 (< 3)
            'depth' => '高折扣',     // 30% (> 25%)
            'dominant_type' => 'flash_sale'
        ];
        $this->assertEquals($expectedDescriptors, $summaryResult['descriptors']);

        // 验证总结陈述
        $expectedSummary = '该竞争对手倾向于采用频繁、短期的促销活动，提供高折扣。最主要的促销类型是"flash_sale"。';
        $this->assertEquals($expectedSummary, $summaryResult['summary']);
    }
} 
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompetitorMetricResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'date' => $this->date->format('Y-m-d'),
            'competitor_sku' => [
                'id' => $this->sku->id,
                'name' => $this->sku->sku_name,
                'product' => $this->sku->product->title,
            ],
            'own_sku_comparison' => [
                'id' => $this->ownSku->id,
                'name' => $this->ownSku->sku_name,
            ],
            'analysis' => [
                'promotion_summary' => $this->analysis_summary,
                'price_deviation_rate' => $this->price_deviation_rate,
            ],
            'metrics' => [
                'promotion_frequency' => $this->promotion_frequency,
                'avg_discount_rate' => $this->avg_discount_rate,
                'total_promotions' => $this->total_promotions,
                'promotion_types' => $this->promotion_types,
            ],
            'calculated_at' => $this->created_at->toDateTimeString(),
        ];
    }
}

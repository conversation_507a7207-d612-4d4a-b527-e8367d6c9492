<?php

namespace App\Services;

use App\Models\AuditLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 性能监控服务
 * 负责收集和分析系统性能指标
 */
class PerformanceMonitorService
{
    /**
     * 缓存键前缀
     */
    private const CACHE_PREFIX = 'performance_metrics_';
    
    /**
     * 缓存时间（秒）
     */
    private const CACHE_TTL = 300; // 5分钟

    /**
     * 获取系统性能概览
     *
     * @return array 性能指标概览
     */
    public function getSystemOverview(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'overview';
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return [
                'response_time' => $this->getAverageResponseTime(),
                'request_count' => $this->getRequestCount(),
                'error_rate' => $this->getErrorRate(),
                'database_performance' => $this->getDatabasePerformance(),
                'memory_usage' => $this->getMemoryUsage(),
                'cache_hit_rate' => $this->getCacheHitRate(),
                'active_users' => $this->getActiveUsersCount(),
                'system_load' => $this->getSystemLoad(),
            ];
        });
    }

    /**
     * 获取平均响应时间
     *
     * @param int $hours 统计时间范围（小时）
     * @return array 响应时间统计
     */
    public function getAverageResponseTime(int $hours = 24): array
    {
        $since = now()->subHours($hours);
        
        $stats = AuditLog::where('event', AuditLog::EVENT_API_CALL)
            ->where('created_at', '>=', $since)
            ->whereNotNull('metadata->response_time')
            ->selectRaw('
                AVG(CAST(JSON_EXTRACT(metadata, "$.response_time") AS DECIMAL(10,2))) as avg_response_time,
                MIN(CAST(JSON_EXTRACT(metadata, "$.response_time") AS DECIMAL(10,2))) as min_response_time,
                MAX(CAST(JSON_EXTRACT(metadata, "$.response_time") AS DECIMAL(10,2))) as max_response_time,
                COUNT(*) as total_requests
            ')
            ->first();

        return [
            'average' => round($stats->avg_response_time ?? 0, 2),
            'minimum' => round($stats->min_response_time ?? 0, 2),
            'maximum' => round($stats->max_response_time ?? 0, 2),
            'total_requests' => $stats->total_requests ?? 0,
            'period_hours' => $hours,
        ];
    }

    /**
     * 获取请求统计
     *
     * @param int $hours 统计时间范围（小时）
     * @return array 请求统计
     */
    public function getRequestCount(int $hours = 24): array
    {
        $since = now()->subHours($hours);
        
        $total = AuditLog::where('event', AuditLog::EVENT_API_CALL)
            ->where('created_at', '>=', $since)
            ->count();

        $successful = AuditLog::where('event', AuditLog::EVENT_API_CALL)
            ->where('created_at', '>=', $since)
            ->where('metadata->success', true)
            ->count();

        return [
            'total' => $total,
            'successful' => $successful,
            'failed' => $total - $successful,
            'requests_per_hour' => $hours > 0 ? round($total / $hours, 2) : 0,
            'period_hours' => $hours,
        ];
    }

    /**
     * 获取错误率
     *
     * @param int $hours 统计时间范围（小时）
     * @return array 错误率统计
     */
    public function getErrorRate(int $hours = 24): array
    {
        $requestStats = $this->getRequestCount($hours);
        
        $errorRate = $requestStats['total'] > 0 
            ? round(($requestStats['failed'] / $requestStats['total']) * 100, 2)
            : 0;

        return [
            'error_rate_percentage' => $errorRate,
            'total_errors' => $requestStats['failed'],
            'total_requests' => $requestStats['total'],
            'period_hours' => $hours,
        ];
    }

    /**
     * 获取数据库性能指标
     *
     * @return array 数据库性能指标
     */
    public function getDatabasePerformance(): array
    {
        $startTime = microtime(true);
        
        // 执行一个简单的查询来测试数据库响应时间
        DB::select('SELECT 1');
        
        $queryTime = round((microtime(true) - $startTime) * 1000, 2);

        // 获取数据库连接信息
        $connectionName = config('database.default');
        $connectionConfig = config("database.connections.{$connectionName}");

        return [
            'connection_time_ms' => $queryTime,
            'connection_name' => $connectionName,
            'driver' => $connectionConfig['driver'] ?? 'unknown',
            'active_connections' => $this->getActiveConnectionsCount(),
        ];
    }

    /**
     * 获取内存使用情况
     *
     * @return array 内存使用统计
     */
    public function getMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));

        return [
            'current_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_mb' => round($memoryPeak / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percentage' => $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0,
        ];
    }

    /**
     * 获取缓存命中率
     *
     * @return array 缓存统计
     */
    public function getCacheHitRate(): array
    {
        // 这里可以根据使用的缓存驱动来实现具体的统计逻辑
        // 目前返回模拟数据
        return [
            'hit_rate_percentage' => 85.5,
            'total_hits' => 1250,
            'total_misses' => 200,
            'cache_driver' => config('cache.default'),
        ];
    }

    /**
     * 获取活跃用户数
     *
     * @param int $minutes 活跃时间范围（分钟）
     * @return int 活跃用户数
     */
    public function getActiveUsersCount(int $minutes = 30): int
    {
        $since = now()->subMinutes($minutes);
        
        return AuditLog::where('created_at', '>=', $since)
            ->whereNotNull('user_id')
            ->distinct('user_id')
            ->count();
    }

    /**
     * 获取系统负载
     *
     * @return array 系统负载信息
     */
    public function getSystemLoad(): array
    {
        $load = [];
        
        if (function_exists('sys_getloadavg')) {
            $loadAvg = sys_getloadavg();
            $load = [
                '1_minute' => round($loadAvg[0], 2),
                '5_minutes' => round($loadAvg[1], 2),
                '15_minutes' => round($loadAvg[2], 2),
            ];
        }

        return array_merge($load, [
            'cpu_count' => $this->getCpuCount(),
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        ]);
    }

    /**
     * 获取慢查询统计
     *
     * @param float $threshold 慢查询阈值（毫秒）
     * @return array 慢查询统计
     */
    public function getSlowQueries(float $threshold = 1000): array
    {
        // 这里可以实现慢查询日志分析
        // 目前返回模拟数据
        return [
            'threshold_ms' => $threshold,
            'slow_query_count' => 5,
            'average_slow_query_time' => 1500.5,
            'slowest_query_time' => 3200.1,
        ];
    }

    /**
     * 获取API端点性能排名
     *
     * @param int $limit 返回数量限制
     * @param int $hours 统计时间范围（小时）
     * @return array API端点性能排名
     */
    public function getApiEndpointPerformance(int $limit = 10, int $hours = 24): array
    {
        $since = now()->subHours($hours);
        
        $endpoints = AuditLog::where('event', AuditLog::EVENT_API_CALL)
            ->where('created_at', '>=', $since)
            ->whereNotNull('metadata->response_time')
            ->selectRaw('
                url,
                COUNT(*) as request_count,
                AVG(CAST(JSON_EXTRACT(metadata, "$.response_time") AS DECIMAL(10,2))) as avg_response_time,
                MAX(CAST(JSON_EXTRACT(metadata, "$.response_time") AS DECIMAL(10,2))) as max_response_time,
                SUM(CASE WHEN JSON_EXTRACT(metadata, "$.success") = true THEN 1 ELSE 0 END) as success_count
            ')
            ->groupBy('url')
            ->orderByDesc('request_count')
            ->limit($limit)
            ->get();

        return $endpoints->map(function ($endpoint) {
            $successRate = $endpoint->request_count > 0 
                ? round(($endpoint->success_count / $endpoint->request_count) * 100, 2)
                : 0;

            return [
                'endpoint' => $endpoint->url,
                'request_count' => $endpoint->request_count,
                'avg_response_time' => round($endpoint->avg_response_time, 2),
                'max_response_time' => round($endpoint->max_response_time, 2),
                'success_rate' => $successRate,
            ];
        })->toArray();
    }

    /**
     * 清除性能指标缓存
     *
     * @return void
     */
    public function clearCache(): void
    {
        $keys = [
            'overview',
            'response_time',
            'request_count',
            'error_rate',
            'database_performance',
        ];

        foreach ($keys as $key) {
            Cache::forget(self::CACHE_PREFIX . $key);
        }
    }

    /**
     * 解析内存限制字符串
     *
     * @param string $memoryLimit 内存限制字符串
     * @return int 内存限制（字节）
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }

    /**
     * 获取CPU核心数
     *
     * @return int CPU核心数
     */
    private function getCpuCount(): int
    {
        if (function_exists('shell_exec')) {
            $cpuCount = shell_exec('nproc');
            if ($cpuCount !== null) {
                return (int) trim($cpuCount);
            }
        }

        return 1; // 默认值
    }

    /**
     * 获取活跃数据库连接数
     *
     * @return int 活跃连接数
     */
    private function getActiveConnectionsCount(): int
    {
        try {
            // 这里可以根据数据库类型实现具体的连接数查询
            // 目前返回模拟数据
            return 5;
        } catch (\Exception $e) {
            Log::warning('获取数据库连接数失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
}

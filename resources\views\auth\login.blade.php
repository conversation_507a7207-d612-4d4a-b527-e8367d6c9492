<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>用户登录 - 电商市场监测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .login-form {
            padding: 30px;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e1e8ed;
        }

        .tab-button {
            flex: 1;
            padding: 12px;
            background: none;
            border: none;
            color: #657786;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-row {
            display: flex;
            gap: 10px;
        }

        .form-row .form-control {
            flex: 1;
        }

        .send-otp-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
            transition: background-color 0.3s;
        }

        .send-otp-btn:hover {
            background: #5a67d8;
        }

        .send-otp-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkbox-group label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            transform: none;
            cursor: not-allowed;
        }

        .links {
            text-align: center;
            margin-top: 20px;
        }

        .links a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }

        .links a:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                max-width: none;
            }

            .login-header {
                padding: 20px;
            }

            .login-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>欢迎登录</h1>
            <p>电商市场动态监测系统</p>
        </div>

        <div class="login-form">
            <div id="alertContainer"></div>

            <div class="login-tabs">
                <button type="button" class="tab-button active" data-tab="password">密码登录</button>
                <button type="button" class="tab-button" data-tab="otp">验证码登录</button>
            </div>

            <!-- 密码登录 -->
            <div id="password-tab" class="tab-content active">
                <form id="passwordLoginForm">
                    <div class="form-group">
                        <label for="login">用户名/邮箱/手机号</label>
                        <input type="text" id="login" name="login" class="form-control" required
                               placeholder="请输入用户名、邮箱或手机号">
                    </div>

                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" class="form-control" required
                               placeholder="请输入密码">
                    </div>

                    <div id="captcha-group" class="form-group" style="display: none;">
                        <label for="captcha">验证码</label>
                        <input type="text" id="captcha" name="captcha" class="form-control"
                               placeholder="请输入验证码">
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="remember" name="remember" value="1">
                        <label for="remember">记住我</label>
                    </div>

                    <button type="submit" class="submit-btn">
                        <span class="btn-text">登录</span>
                        <div class="loading">
                            登录中...
                        </div>
                    </button>
                </form>
            </div>

            <!-- 验证码登录 -->
            <div id="otp-tab" class="tab-content">
                <form id="otpLoginForm">
                    <div class="form-group">
                        <label for="contact">邮箱/手机号</label>
                        <input type="text" id="contact" name="contact" class="form-control" required
                               placeholder="请输入邮箱或手机号">
                    </div>

                    <div class="form-group">
                        <label for="otp">验证码</label>
                        <div class="form-row">
                            <input type="text" id="otp" name="otp" class="form-control"
                                   placeholder="请输入验证码" maxlength="6">
                            <button type="button" id="sendOtpBtn" class="send-otp-btn">发送验证码</button>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="rememberOtp" name="remember" value="1">
                        <label for="rememberOtp">记住我</label>
                    </div>

                    <button type="submit" class="submit-btn">
                        <span class="btn-text">登录</span>
                        <div class="loading">
                            登录中...
                        </div>
                    </button>
                </form>
            </div>

            <div class="links">
                <a href="#" id="forgotPasswordLink">忘记密码？</a>
                <a href="{{ route('auth.register') }}">注册账户</a>
            </div>
        </div>
    </div>

    <!-- 忘记密码模态框 -->
    <div id="forgotPasswordModal" style="display: none;">
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 400px;">
                <h3 style="margin-bottom: 20px;">重置密码</h3>
                <form id="forgotPasswordForm">
                    <div class="form-group">
                        <label for="resetEmail">邮箱地址</label>
                        <input type="email" id="resetEmail" name="email" class="form-control" required
                               placeholder="请输入注册邮箱">
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="button" id="cancelReset" style="flex: 1; padding: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">取消</button>
                        <button type="submit" style="flex: 1; padding: 10px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">发送重置链接</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // CSRF token setup
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                
                // Update button states
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Update tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabName + '-tab').classList.add('active');
                
                // Clear alerts
                clearAlerts();
            });
        });

        // Password login form
        document.getElementById('passwordLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.submit-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const loading = submitBtn.querySelector('.loading');
            
            try {
                setLoading(submitBtn, btnText, loading, true);
                clearAlerts();
                
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('登录成功！正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                } else {
                    showAlert(data.message || '登录失败', 'error');
                    
                    // Show captcha if too many attempts
                    if (data.errors && data.errors.login && data.errors.login[0].includes('验证码')) {
                        document.getElementById('captcha-group').style.display = 'block';
                    }
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                setLoading(submitBtn, btnText, loading, false);
            }
        });

        // OTP send button
        document.getElementById('sendOtpBtn').addEventListener('click', async function() {
            const contact = document.getElementById('contact').value.trim();
            
            if (!contact) {
                showAlert('请输入邮箱或手机号', 'error');
                return;
            }
            
            const button = this;
            const originalText = button.textContent;
            
            try {
                button.disabled = true;
                button.textContent = '发送中...';
                clearAlerts();
                
                const response = await fetch('/send-otp-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ contact: contact })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('验证码已发送，请注意查收', 'success');
                    startCountdown(button, 60);
                } else {
                    showAlert(data.message || '发送失败', 'error');
                    button.disabled = false;
                    button.textContent = originalText;
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        });

        // OTP login form
        document.getElementById('otpLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('.submit-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const loading = submitBtn.querySelector('.loading');
            
            try {
                setLoading(submitBtn, btnText, loading, true);
                clearAlerts();
                
                const response = await fetch('/verify-otp-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        contact: formData.get('contact'),
                        otp: formData.get('otp'),
                        remember: formData.get('remember') === '1'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('登录成功！正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                } else {
                    showAlert(data.message || '登录失败', 'error');
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                setLoading(submitBtn, btnText, loading, false);
            }
        });

        // Forgot password
        document.getElementById('forgotPasswordLink').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('forgotPasswordModal').style.display = 'block';
        });

        document.getElementById('cancelReset').addEventListener('click', function() {
            document.getElementById('forgotPasswordModal').style.display = 'none';
        });

        document.getElementById('forgotPasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            try {
                submitBtn.disabled = true;
                submitBtn.textContent = '发送中...';
                
                const response = await fetch('/forgot-password', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('重置链接已发送到您的邮箱', 'success');
                    document.getElementById('forgotPasswordModal').style.display = 'none';
                    this.reset();
                } else {
                    showAlert(data.message || '发送失败', 'error');
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        });

        // Utility functions
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
        }

        function clearAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }

        function setLoading(submitBtn, btnText, loading, isLoading) {
            if (isLoading) {
                submitBtn.disabled = true;
                btnText.style.display = 'none';
                loading.classList.add('show');
            } else {
                submitBtn.disabled = false;
                btnText.style.display = 'inline';
                loading.classList.remove('show');
            }
        }

        function startCountdown(button, seconds) {
            let remaining = seconds;
            const interval = setInterval(() => {
                button.textContent = `${remaining}s 后重新发送`;
                remaining--;
                
                if (remaining < 0) {
                    clearInterval(interval);
                    button.disabled = false;
                    button.textContent = '发送验证码';
                }
            }, 1000);
        }
    </script>
</body>
</html> 
<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\DatabaseMessage;
use App\Models\AlertRule;

class AlertNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $alertData;
    public $severity;
    public $alertRule;

    /**
     * Create a new notification instance.
     */
    public function __construct($alertData, $severity = 'general', AlertRule $alertRule = null)
    {
        $this->alertData = $alertData;
        $this->severity = strtolower($severity);
        $this->alertRule = $alertRule;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        $channels = ['database']; // 默认使用数据库通道进行站内通知
        
        // 根据用户偏好和严重程度决定发送渠道
        $userPreferences = $this->getUserNotificationPreferences($notifiable);
        
        if ($this->shouldSendEmail($userPreferences)) {
            $channels[] = 'mail';
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        $subject = $this->getSubjectBySeverity();
        
        try {
            return (new MailMessage)
                        ->subject($subject)
                        ->view('notifications.alert-email', [
                            'alertData' => $this->alertData,
                            'severity' => $this->severity,
                            'alertRule' => $this->alertRule,
                            'subject' => $subject,
                            'user' => $notifiable
                        ]);
        } catch (\Exception $e) {
            // 如果自定义模板失败，回退到简单模板
            Log::error('邮件模板渲染失败，使用默认模板', [
                'error' => $e->getMessage(),
                'severity' => $this->severity,
                'user_id' => $notifiable->id ?? null
            ]);
            
            return (new MailMessage)
                        ->subject($subject)
                        ->greeting('您好！')
                        ->line($this->getNotificationMessage())
                        ->line('严重程度：' . $this->getSeverityLabel())
                        ->when($this->alertRule, function($mail) {
                            return $mail->line('规则名称：' . $this->alertRule->name);
                        })
                        ->line('请及时查看系统以获取更多详细信息。')
                        ->action('查看详情', url('/alert-rules'))
                        ->line('谢谢您的关注！');
        }
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        return [
            'message' => $this->getNotificationMessage(),
            'severity' => $this->severity,
            'alert_data' => $this->alertData,
            'alert_rule_id' => $this->alertRule ? $this->alertRule->id : null,
            'alert_rule_name' => $this->alertRule ? $this->alertRule->name : null,
            'created_at' => now(),
        ];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable)
    {
        return [
            'message' => $this->getNotificationMessage(),
            'severity' => $this->severity,
            'alert_data' => $this->alertData,
        ];
    }

    /**
     * 获取通知消息内容
     */
    private function getNotificationMessage()
    {
        if (is_array($this->alertData) && isset($this->alertData['message'])) {
            return $this->alertData['message'];
        }
        
        if (is_string($this->alertData)) {
            return $this->alertData;
        }

        return '系统检测到异常情况，请查看详细信息。';
    }

    /**
     * 根据严重程度获取邮件主题
     */
    private function getSubjectBySeverity()
    {
        switch ($this->severity) {
            case 'emergency':
                return '【紧急警报】电商监测系统';
            case 'important':
                return '【重要通知】电商监测系统';
            case 'general':
            default:
                return '【系统通知】电商监测系统';
        }
    }

    /**
     * 获取严重程度标签
     */
    private function getSeverityLabel()
    {
        switch ($this->severity) {
            case 'emergency':
                return '紧急';
            case 'important':
                return '重要';
            case 'general':
            default:
                return '一般';
        }
    }

    /**
     * 获取用户通知偏好设置
     */
    private function getUserNotificationPreferences($notifiable)
    {
        // 如果用户模型有通知偏好字段，从那里获取
        if (method_exists($notifiable, 'getNotificationPreferences')) {
            return $notifiable->getNotificationPreferences();
        }

        // 默认偏好设置
        return [
            'email_enabled' => true,
            'emergency_email' => true,
            'important_email' => true,
            'general_email' => false, // 一般通知默认不发送邮件
        ];
    }

    /**
     * 判断是否应该发送邮件
     */
    private function shouldSendEmail($preferences)
    {
        if (!$preferences['email_enabled']) {
            return false;
        }

        switch ($this->severity) {
            case 'emergency':
                return $preferences['emergency_email'] ?? true;
            case 'important':
                return $preferences['important_email'] ?? true;
            case 'general':
                return $preferences['general_email'] ?? false;
            default:
                return false;
        }
    }
} 
/**
 * UX增强功能JavaScript
 */

class UXEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupSmoothScrolling();
        this.setupFormEnhancements();
        this.setupTooltips();
        this.setupKeyboardNavigation();
        this.setupProgressiveEnhancement();
        this.setupPerformanceOptimizations();
        this.setupAccessibilityFeatures();
    }

    // ==================== 懒加载优化 ====================
    setupLazyLoading() {
        // 图片懒加载
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('fade-in');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // 内容懒加载
        const contentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.lazy-content').forEach(el => {
            contentObserver.observe(el);
        });
    }

    // ==================== 平滑滚动 ====================
    setupSmoothScrolling() {
        // 锚点平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 回到顶部按钮
        this.createBackToTopButton();
    }

    createBackToTopButton() {
        const backToTop = document.createElement('button');
        backToTop.innerHTML = '<i class="fas fa-chevron-up"></i>';
        backToTop.className = 'btn btn-primary btn-floating back-to-top';
        backToTop.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        backToTop.setAttribute('aria-label', '回到顶部');

        document.body.appendChild(backToTop);

        // 显示/隐藏逻辑
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
                backToTop.classList.add('fade-in');
            } else {
                backToTop.style.display = 'none';
            }
        });

        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // ==================== 表单增强 ====================
    setupFormEnhancements() {
        // 实时表单验证
        document.querySelectorAll('input, textarea, select').forEach(field => {
            field.addEventListener('blur', () => this.validateField(field));
            field.addEventListener('input', () => this.clearFieldError(field));
        });

        // 表单提交增强
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        });

        // 浮动标签
        this.setupFloatingLabels();
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');

        // 清除之前的错误状态
        this.clearFieldError(field);

        // 必填验证
        if (required && !value) {
            this.showFieldError(field, '此字段为必填项');
            return false;
        }

        // 邮箱验证
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, '请输入有效的邮箱地址');
                return false;
            }
        }

        // 电话验证
        if (type === 'tel' && value) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, '请输入有效的手机号码');
                return false;
            }
        }

        // 密码强度验证
        if (type === 'password' && value) {
            if (value.length < 6) {
                this.showFieldError(field, '密码长度至少6位');
                return false;
            }
        }

        this.showFieldSuccess(field);
        return true;
    }

    showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        
        let errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    showFieldSuccess(field) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid', 'is-valid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    setupFloatingLabels() {
        document.querySelectorAll('.floating-label input, .floating-label textarea, .floating-label select').forEach(field => {
            // 设置placeholder为空，让CSS效果生效
            if (!field.placeholder) {
                field.placeholder = ' ';
            }
        });
    }

    handleFormSubmit(e) {
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // 显示加载状态
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
            submitBtn.disabled = true;
            
            // 模拟提交延迟后恢复按钮状态
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
    }

    // ==================== 工具提示 ====================
    setupTooltips() {
        // 初始化Bootstrap工具提示
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // 自定义工具提示
        document.querySelectorAll('[data-tooltip]').forEach(el => {
            el.addEventListener('mouseenter', (e) => this.showTooltip(e));
            el.addEventListener('mouseleave', () => this.hideTooltip());
        });
    }

    showTooltip(e) {
        const text = e.target.getAttribute('data-tooltip');
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 9999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = e.target.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        setTimeout(() => tooltip.style.opacity = '1', 10);
        
        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }

    // ==================== 键盘导航 ====================
    setupKeyboardNavigation() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) modal.hide();
                }
            }
        });

        // Tab键焦点管理
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.manageFocusOrder(e);
            }
        });
    }

    manageFocusOrder(e) {
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const focusableArray = Array.from(focusableElements);
        const currentIndex = focusableArray.indexOf(document.activeElement);
        
        if (e.shiftKey) {
            // Shift+Tab - 向前
            if (currentIndex === 0) {
                e.preventDefault();
                focusableArray[focusableArray.length - 1].focus();
            }
        } else {
            // Tab - 向后
            if (currentIndex === focusableArray.length - 1) {
                e.preventDefault();
                focusableArray[0].focus();
            }
        }
    }

    // ==================== 渐进式增强 ====================
    setupProgressiveEnhancement() {
        // 检测JavaScript支持
        document.documentElement.classList.add('js-enabled');
        
        // 检测触摸支持
        if ('ontouchstart' in window) {
            document.documentElement.classList.add('touch-enabled');
        }
        
        // 检测网络状态
        if ('navigator' in window && 'onLine' in navigator) {
            this.updateOnlineStatus();
            window.addEventListener('online', () => this.updateOnlineStatus());
            window.addEventListener('offline', () => this.updateOnlineStatus());
        }
    }

    updateOnlineStatus() {
        const isOnline = navigator.onLine;
        document.documentElement.classList.toggle('offline', !isOnline);
        
        if (!isOnline) {
            this.showOfflineNotification();
        }
    }

    showOfflineNotification() {
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning offline-notification';
        notification.innerHTML = `
            <i class="fas fa-wifi-slash me-2"></i>
            网络连接已断开，某些功能可能无法正常使用
        `;
        notification.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 9999;
            margin: 0;
            border-radius: 0;
        `;
        
        document.body.insertBefore(notification, document.body.firstChild);
        
        // 网络恢复时移除通知
        const removeNotification = () => {
            if (navigator.onLine && notification.parentNode) {
                notification.remove();
                window.removeEventListener('online', removeNotification);
            }
        };
        window.addEventListener('online', removeNotification);
    }

    // ==================== 性能优化 ====================
    setupPerformanceOptimizations() {
        // 防抖函数
        this.debounce = (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        };

        // 节流函数
        this.throttle = (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        };

        // 优化滚动事件
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16)); // 60fps
    }

    handleScroll() {
        // 滚动相关的优化处理
        const scrollTop = window.pageYOffset;
        
        // 视差效果（如果需要）
        document.querySelectorAll('.parallax').forEach(el => {
            const speed = el.dataset.speed || 0.5;
            el.style.transform = `translateY(${scrollTop * speed}px)`;
        });
    }

    // ==================== 可访问性增强 ====================
    setupAccessibilityFeatures() {
        // 跳转到主内容链接
        this.createSkipLink();
        
        // ARIA标签增强
        this.enhanceAriaLabels();
        
        // 焦点指示器增强
        this.enhanceFocusIndicators();
    }

    createSkipLink() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = '跳转到主内容';
        skipLink.className = 'skip-link sr-only';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: white;
            padding: 8px;
            text-decoration: none;
            z-index: 10000;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
            skipLink.classList.remove('sr-only');
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
            skipLink.classList.add('sr-only');
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    enhanceAriaLabels() {
        // 为没有标签的表单控件添加aria-label
        document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])').forEach(input => {
            const label = input.closest('label') || document.querySelector(`label[for="${input.id}"]`);
            if (!label && input.placeholder) {
                input.setAttribute('aria-label', input.placeholder);
            }
        });
        
        // 为按钮添加描述性标签
        document.querySelectorAll('button:not([aria-label])').forEach(button => {
            if (button.innerHTML.includes('fa-') && !button.textContent.trim()) {
                // 图标按钮需要标签
                const icon = button.querySelector('i[class*="fa-"]');
                if (icon) {
                    const iconClass = Array.from(icon.classList).find(cls => cls.startsWith('fa-'));
                    button.setAttribute('aria-label', this.getIconDescription(iconClass));
                }
            }
        });
    }

    getIconDescription(iconClass) {
        const descriptions = {
            'fa-edit': '编辑',
            'fa-trash': '删除',
            'fa-eye': '查看',
            'fa-download': '下载',
            'fa-upload': '上传',
            'fa-search': '搜索',
            'fa-plus': '添加',
            'fa-minus': '减少',
            'fa-save': '保存',
            'fa-close': '关闭',
            'fa-times': '关闭'
        };
        return descriptions[iconClass] || '操作';
    }

    enhanceFocusIndicators() {
        // 为所有可聚焦元素添加焦点指示器
        const style = document.createElement('style');
        style.textContent = `
            .focus-visible {
                outline: 2px solid #4e73df;
                outline-offset: 2px;
            }
        `;
        document.head.appendChild(style);
    }
}

// 初始化UX增强功能
document.addEventListener('DOMContentLoaded', () => {
    window.uxEnhancements = new UXEnhancements();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UXEnhancements;
}

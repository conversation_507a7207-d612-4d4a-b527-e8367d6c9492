<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_action_logs', function (Blueprint $table) {
            $table->string('resource_type', 50)->nullable()->after('action')->comment('资源类型');
            $table->unsignedBigInteger('resource_id')->nullable()->after('resource_type')->comment('资源ID');
            $table->json('extra_data')->nullable()->after('error_message')->comment('额外数据');
            $table->timestamp('updated_at')->nullable()->after('created_at')->comment('更新时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_action_logs', function (Blueprint $table) {
            $table->dropColumn(['resource_type', 'resource_id', 'extra_data', 'updated_at']);
        });
    }
};

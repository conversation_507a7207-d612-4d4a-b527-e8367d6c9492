<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('name')->nullable()->after('title')->comment('产品名称（简化版标题）');
            $table->string('brand')->nullable()->after('name')->comment('品牌名称');
            
            // 添加索引
            $table->index('name');
            $table->index('brand');
        });
        
        // 更新现有数据，将title复制到name字段
        DB::statement('UPDATE products SET name = title WHERE name IS NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['brand']);
            $table->dropColumn(['name', 'brand']);
        });
    }
};

<?php

namespace App\Services\DataCollection\Adapters;

use App\Services\DataCollection\AbstractPlatformAdapter;
use Exception;

/**
 * 淘宝平台适配器
 * 基于接口说明.txt中的API格式实现
 */
class TaobaoAdapter extends AbstractPlatformAdapter
{
    /**
     * 获取商品详情
     * API: /tb/new/item_detail_base
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 商品详情数据
     * @throws Exception
     */
    public function getItemDetail(string $itemId, array $options = []): array
    {
        $endpoint = '/tb/new/item_detail_base';
        $params = array_merge(['item_id' => $itemId], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        return $this->normalizeItemDetail($response['data'] ?? []);
    }

    /**
     * 获取同款商品
     * API: /taobao/SimilarProduct
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 同款商品列表
     * @throws Exception
     */
    public function getSimilarProducts(string $itemId, array $options = []): array
    {
        $endpoint = '/taobao/SimilarProduct';
        $params = array_merge(['itemId' => $itemId], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $products = $response['data'] ?? [];
        return array_map([$this, 'normalizeSimilarProduct'], $products);
    }

    /**
     * 获取店铺商品列表
     * API: /tb/new/shop_item
     *
     * @param string $shopId 店铺ID
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 店铺商品列表
     * @throws Exception
     */
    public function getShopItems(string $shopId, int $page = 1, array $options = []): array
    {
        $endpoint = '/tb/new/shop_item';
        $params = array_merge([
            'shop_id' => $shopId,
            'page' => $page
        ], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $items = $response['data'] ?? [];
        return [
            'items' => array_map([$this, 'normalizeShopItem'], $items),
            'pagination' => [
                'current_page' => $page,
                'has_more' => count($items) > 0,
            ]
        ];
    }

    /**
     * 搜索商品
     * API: /tb/new/search (假设存在此接口)
     *
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 搜索结果
     * @throws Exception
     */
    public function searchItems(string $keyword, int $page = 1, array $options = []): array
    {
        $endpoint = '/tb/new/search';
        $params = array_merge([
            'keyword' => $keyword,
            'page' => $page
        ], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $items = $response['data'] ?? [];
        return [
            'items' => array_map([$this, 'normalizeSearchItem'], $items),
            'pagination' => [
                'current_page' => $page,
                'has_more' => count($items) > 0,
            ],
            'keyword' => $keyword,
        ];
    }

    /**
     * 获取平台名称
     *
     * @return string 平台名称
     */
    public function getPlatformName(): string
    {
        return 'taobao';
    }

    /**
     * 标准化商品详情数据
     *
     * @param array $data 原始API数据
     * @return array 标准化数据
     */
    private function normalizeItemDetail(array $data): array
    {
        return [
            'item_id' => $data['id'] ?? '',
            'title' => $data['title'] ?? '',
            'price' => $data['price'] ?? '',
            'promotion_price' => null,
            'sale_count' => $data['sale'] ?? '',
            'comment_count' => $data['commentCount'] ?? '',
            'category_id' => $data['category_id'] ?? '',
            'category_path' => $data['category_path'] ?? '',
            'state' => $data['state'] ?? '',
            'is_sku' => $data['is_sku'] ?? false,
            'item_type' => $data['itemType'] ?? '',
            'shop_name' => $data['shopName'] ?? '',
            'shop_id' => $data['shopId'] ?? '',
            'seller_id' => $data['sellerId'] ?? '',
            'nick' => $data['nick'] ?? '',
            'postage' => $data['postage'] ?? 0,
            'location' => $data['from'] ?? '',
            'pic_urls' => $data['pic_urls'] ?? [],
            'desc_urls' => $data['desc_urls'] ?? [],
            'skus' => $this->normalizeSkus($data['skus'] ?? []),
            'promotions' => $data['promotion'] ?? [],
            'properties' => $data['props'] ?? [],
            'timestamp' => $data['timestamp'] ?? date('Y-m-d H:i:s'),
            'platform' => $this->getPlatformName(),
        ];
    }

    /**
     * 标准化SKU数据
     */
    private function normalizeSkus(array $skus): array
    {
        return array_map(function ($sku) {
            return [
                'sku_id' => $sku['id'] ?? '',
                'name' => $sku['name'] ?? '',
                'price' => $sku['price'] ?? '',
                'sub_price' => $sku['subPrice'] ?? '',
                'sub_price_title' => $sku['subPriceTitle'] ?? '',
                'quantity' => $sku['quantity'] ?? '',
                'pic_url' => $sku['picUrl'] ?? '',
            ];
        }, $skus);
    }

    /**
     * 标准化同款商品数据
     */
    private function normalizeSimilarProduct(array $data): array
    {
        return [
            'item_id' => $data['item_id'] ?? '',
            'title' => $data['title'] ?? '',
            'price' => $data['price'] ?? 0,
            'promotion_price' => $data['promotion_price'] ?? 0,
            'shop_name' => $data['shop_name'] ?? '',
            'shop_id' => $data['shop_id'] ?? '',
            'quantity' => $data['quantity'] ?? 0,
            'category_id' => $data['category_id'] ?? '',
            'category_name' => $data['category_name'] ?? '',
            'category_path' => $data['category_path'] ?? '',
            'pic_urls' => $data['pic_urls'] ?? [],
            'description' => $data['description'] ?? [],
            'video_url' => $data['video_url'] ?? '',
            'item_type' => $data['item_type'] ?? '',
            'status' => $data['status'] ?? '',
            'sku_list' => $data['sku_list'] ?? [],
            'platform' => $this->getPlatformName(),
        ];
    }

    /**
     * 标准化店铺商品数据
     */
    private function normalizeShopItem(array $data): array
    {
        return [
            'item_id' => $data['itemId'] ?? '',
            'title' => $data['title'] ?? '',
            'price' => $data['price'] ?? '',
            'coupon_price' => $data['couponPrice'] ?? '',
            'shop_name' => $data['shopName'] ?? '',
            'inventory' => $data['inventory'] ?? 0,
            'main_image_url' => $data['mainImageUrl'] ?? '',
            'tags' => $data['tags'] ?? [],
            'platform' => $this->getPlatformName(),
        ];
    }

    /**
     * 标准化搜索商品数据
     */
    private function normalizeSearchItem(array $data): array
    {
        return $this->normalizeShopItem($data);
    }

    /**
     * 验证API响应
     */
    protected function validateResponse(array $response): bool
    {
        return isset($response['code']) && 
               (
                   $response['code'] === 200 || 
                   $response['code'] === '200' ||
                   $response['code'] === 0 ||
                   $response['code'] === '0'
               );
    }

    /**
     * 测试API连接
     */
    public function testConnection(): bool
    {
        try {
            $testItemId = '718675380206';
            $this->getItemDetail($testItemId);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
} 
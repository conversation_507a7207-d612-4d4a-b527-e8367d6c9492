<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动日志 - 电商市场动态监测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            background: #f8f9fa;
            min-height: calc(100vh - 100px);
            border-radius: 10px;
        }
        .nav-link {
            color: #6c757d;
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: #667eea;
            color: white;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 15px;
        }
        .activity-timeline {
            position: relative;
            padding-left: 2rem;
        }
        .activity-timeline::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        .activity-item {
            position: relative;
            padding-bottom: 2rem;
            margin-bottom: 1rem;
        }
        .activity-item::before {
            content: '';
            position: absolute;
            left: -0.875rem;
            top: 0.25rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #fff;
            border: 3px solid #e9ecef;
        }
        .activity-item.login::before {
            border-color: #198754;
            background: #198754;
        }
        .activity-item.profile::before {
            border-color: #0d6efd;
            background: #0d6efd;
        }
        .activity-item.password::before {
            border-color: #fd7e14;
            background: #fd7e14;
        }
        .activity-item.preferences::before {
            border-color: #6f42c1;
            background: #6f42c1;
        }
        .activity-meta {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }
        .filter-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .pagination-custom .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        .pagination-custom .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="bi bi-graph-up"></i> 电商监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">
                    <i class="bi bi-speedometer2"></i> 管理后台
                </a>
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <img src="{{ auth()->user()->avatar_url }}" alt="头像" class="rounded-circle" width="24" height="24">
                        {{ auth()->user()->display_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('profile.index') }}">个人中心</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">退出登录</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="{{ route('profile.index') }}">
                            <i class="bi bi-house me-2"></i>个人中心
                        </a>
                        <a class="nav-link" href="{{ route('profile.edit') }}">
                            <i class="bi bi-person-gear me-2"></i>个人资料
                        </a>
                        <a class="nav-link" href="{{ route('profile.change-password') }}">
                            <i class="bi bi-shield-lock me-2"></i>修改密码
                        </a>
                        <a class="nav-link" href="{{ route('profile.preferences') }}">
                            <i class="bi bi-gear me-2"></i>偏好设置
                        </a>
                        <a class="nav-link active" href="{{ route('profile.activity-log') }}">
                            <i class="bi bi-clock-history me-2"></i>活动日志
                        </a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 筛选器 -->
                <div class="filter-card">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label mb-1">活动类型</label>
                            <select class="form-select form-select-sm" id="activityFilter">
                                <option value="">全部活动</option>
                                <option value="login">登录活动</option>
                                <option value="profile">资料更新</option>
                                <option value="password">密码修改</option>
                                <option value="preferences">偏好设置</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label mb-1">时间范围</label>
                            <select class="form-select form-select-sm" id="timeFilter">
                                <option value="">全部时间</option>
                                <option value="today">今天</option>
                                <option value="week">最近一周</option>
                                <option value="month">最近一月</option>
                                <option value="quarter">最近三月</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label mb-1">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary btn-sm me-2" onclick="applyFilters()">
                                    <i class="bi bi-funnel me-1"></i>筛选
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                    <i class="bi bi-x-circle me-1"></i>清除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 活动日志卡片 -->
                <div class="card">
                    <div class="card-header bg-white border-0 pb-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>活动日志
                                </h4>
                                <p class="text-muted mb-0">查看您的账户活动记录</p>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshActivities()">
                                <i class="bi bi-arrow-clockwise me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body pt-4">
                        <div class="activity-timeline" id="activityTimeline">
                            @if(isset($activities['data']) && count($activities['data']) > 0)
                                @foreach($activities['data'] as $activity)
                                    <div class="activity-item {{ $activity['action'] }}">
                                        <div class="d-flex">
                                            <div class="activity-icon bg-{{ $activity['action'] == 'login' ? 'success' : ($activity['action'] == 'profile' ? 'primary' : ($activity['action'] == 'password' ? 'warning' : 'info')) }}">
                                                <i class="bi bi-{{ $activity['action'] == 'login' ? 'box-arrow-in-right' : ($activity['action'] == 'profile' ? 'person-gear' : ($activity['action'] == 'password' ? 'shield-lock' : 'gear')) }}"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">{{ $activity['description'] }}</h6>
                                                        <div class="activity-meta">
                                                            <i class="bi bi-geo-alt me-1"></i>IP: {{ $activity['ip'] }}
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">
                                                        {{ $activity['created_at']->format('m/d H:i') }}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- 分页 -->
                                @if($activities['total'] > $activities['per_page'])
                                    <div class="d-flex justify-content-center mt-4">
                                        <nav>
                                            <ul class="pagination pagination-custom mb-0">
                                                <li class="page-item {{ $activities['current_page'] <= 1 ? 'disabled' : '' }}">
                                                    <a class="page-link" href="#" onclick="loadPage({{ $activities['current_page'] - 1 }})">
                                                        <i class="bi bi-chevron-left"></i>
                                                    </a>
                                                </li>
                                                
                                                @for($i = 1; $i <= ceil($activities['total'] / $activities['per_page']); $i++)
                                                    <li class="page-item {{ $i == $activities['current_page'] ? 'active' : '' }}">
                                                        <a class="page-link" href="#" onclick="loadPage({{ $i }})">{{ $i }}</a>
                                                    </li>
                                                @endfor
                                                
                                                <li class="page-item {{ $activities['current_page'] >= ceil($activities['total'] / $activities['per_page']) ? 'disabled' : '' }}">
                                                    <a class="page-link" href="#" onclick="loadPage({{ $activities['current_page'] + 1 }})">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-5">
                                    <div class="activity-icon bg-secondary mx-auto mb-3">
                                        <i class="bi bi-inbox"></i>
                                    </div>
                                    <h5 class="text-muted">暂无活动记录</h5>
                                    <p class="text-muted mb-0">您的账户活动将在此处显示</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 应用筛选器
        function applyFilters() {
            const activityType = document.getElementById('activityFilter').value;
            const timeRange = document.getElementById('timeFilter').value;
            
            // 显示加载状态
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();
            
            // 构建查询参数
            const params = new URLSearchParams();
            if (activityType) params.append('type', activityType);
            if (timeRange) params.append('time', timeRange);
            
            // 发送AJAX请求
            fetch(`{{ route('profile.activity-log') }}?${params.toString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateActivityTimeline(data.activities);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('加载失败，请重试');
            })
            .finally(() => {
                loadingModal.hide();
            });
        }

        // 清除筛选器
        function clearFilters() {
            document.getElementById('activityFilter').value = '';
            document.getElementById('timeFilter').value = '';
            refreshActivities();
        }

        // 刷新活动列表
        function refreshActivities() {
            applyFilters();
        }

        // 加载指定页面
        function loadPage(page) {
            if (page < 1) return;
            
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();
            
            fetch(`{{ route('profile.activity-log') }}?page=${page}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateActivityTimeline(data.activities);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('加载失败，请重试');
            })
            .finally(() => {
                loadingModal.hide();
            });
        }

        // 更新活动时间线
        function updateActivityTimeline(activities) {
            const timeline = document.getElementById('activityTimeline');
            
            if (activities.data && activities.data.length > 0) {
                let html = '';
                activities.data.forEach(activity => {
                    const iconClass = getActivityIcon(activity.action);
                    const colorClass = getActivityColor(activity.action);
                    
                    html += `
                        <div class="activity-item ${activity.action}">
                            <div class="d-flex">
                                <div class="activity-icon bg-${colorClass}">
                                    <i class="bi bi-${iconClass}"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">${activity.description}</h6>
                                            <div class="activity-meta">
                                                <i class="bi bi-geo-alt me-1"></i>IP: ${activity.ip}
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            ${formatDateTime(activity.created_at)}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                timeline.innerHTML = html;
            } else {
                timeline.innerHTML = `
                    <div class="text-center py-5">
                        <div class="activity-icon bg-secondary mx-auto mb-3">
                            <i class="bi bi-inbox"></i>
                        </div>
                        <h5 class="text-muted">暂无活动记录</h5>
                        <p class="text-muted mb-0">您的账户活动将在此处显示</p>
                    </div>
                `;
            }
        }

        // 获取活动图标
        function getActivityIcon(action) {
            const icons = {
                login: 'box-arrow-in-right',
                profile: 'person-gear',
                password: 'shield-lock',
                preferences: 'gear'
            };
            return icons[action] || 'circle';
        }

        // 获取活动颜色
        function getActivityColor(action) {
            const colors = {
                login: 'success',
                profile: 'primary',
                password: 'warning',
                preferences: 'info'
            };
            return colors[action] || 'secondary';
        }

        // 格式化日期时间
        function formatDateTime(datetime) {
            const date = new Date(datetime);
            return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }
    </script>
</body>
</html> 
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LoginRequest extends FormRequest
{
    /**
     * 确定用户是否有权发出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'login' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // 验证是否为有效的邮箱、手机号或用户名
                    $isEmail = filter_var($value, FILTER_VALIDATE_EMAIL);
                    $isPhone = preg_match('/^1[3-9]\d{9}$/', $value);
                    $isUsername = preg_match('/^[a-zA-Z0-9_]{3,50}$/', $value);
                    
                    if (!$isEmail && !$isPhone && !$isUsername) {
                        $fail('请输入有效的用户名、邮箱或手机号。');
                    }
                },
            ],
            'password' => [
                'required',
                'string',
                'min:6',
                'max:255',
            ],
            'remember' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'login' => '登录账号',
            'password' => '密码',
            'remember' => '记住我',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'login.required' => '请输入用户名、邮箱或手机号。',
            'login.string' => '登录账号格式不正确。',
            'login.max' => '登录账号不能超过255个字符。',
            
            'password.required' => '请输入密码。',
            'password.string' => '密码格式不正确。',
            'password.min' => '密码至少需要6个字符。',
            'password.max' => '密码不能超过255个字符。',
            
            'remember.boolean' => '记住我选项值无效。',
        ];
    }

    /**
     * 处理验证通过后的数据
     */
    public function validated($key = null, $default = null): array
    {
        $validated = parent::validated($key, $default);
        
        // 标准化登录字段
        if (isset($validated['login'])) {
            $validated['login'] = strtolower(trim($validated['login']));
        }
        
        // 确保remember是布尔值
        $validated['remember'] = $this->boolean('remember');
        
        return $validated;
    }

    /**
     * 获取登录字段类型
     */
    public function getLoginType(): string
    {
        $login = $this->input('login');
        
        if (filter_var($login, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        } elseif (preg_match('/^1[3-9]\d{9}$/', $login)) {
            return 'phone_number';
        } else {
            return 'username';
        }
    }

    /**
     * 获取用于数据库查询的登录凭据
     */
    public function getCredentials(): array
    {
        $loginType = $this->getLoginType();
        
        return [
            $loginType => $this->input('login'),
            'password' => $this->input('password'),
        ];
    }
} 
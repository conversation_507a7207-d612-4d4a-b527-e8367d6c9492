<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\OfficialGuidePriceService;

class OfficialGuidePriceServiceTest extends TestCase
{
    private OfficialGuidePriceService $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 我们先测试类是否能够正确定义和实例化
        $this->service = new OfficialGuidePriceService();
    }

    /** @test */
    public function service_can_be_instantiated()
    {
        $this->assertInstanceOf(OfficialGuidePriceService::class, $this->service);
    }

    /** @test */
    public function service_has_required_constants()
    {
        $this->assertEquals('manual', OfficialGuidePriceService::SOURCE_MANUAL);
        $this->assertEquals('import', OfficialGuidePriceService::SOURCE_IMPORT);
        $this->assertEquals('auto', OfficialGuidePriceService::SOURCE_AUTO);
        $this->assertEquals('api', OfficialGuidePriceService::SOURCE_API);
    }

    /** @test */
    public function validates_price_input()
    {
        // 测试正数价格验证
        $this->assertTrue($this->service->isValidPrice(100.00));
        $this->assertTrue($this->service->isValidPrice(0.01));
        
        // 测试负数和零价格验证
        $this->assertFalse($this->service->isValidPrice(-1.00));
        $this->assertFalse($this->service->isValidPrice(0.00));
    }

    /** @test */
    public function validates_source_input()
    {
        $validSources = ['manual', 'import', 'auto', 'api'];
        foreach ($validSources as $source) {
            $this->assertTrue($this->service->isValidSource($source));
        }
        
        $this->assertFalse($this->service->isValidSource('invalid'));
        $this->assertFalse($this->service->isValidSource(''));
        $this->assertFalse($this->service->isValidSource(null));
    }

    /** @test */
    public function percentile_calculation_works_correctly()
    {
        $prices = [100, 110, 120, 130, 140];
        
        // 测试50%分位数（中位数）
        $result = $this->service->calculatePercentile($prices, 50);
        $this->assertEquals(120, $result);
        
        // 测试80%分位数
        $result = $this->service->calculatePercentile($prices, 80);
        $this->assertEquals(132, $result); // 在第4和第5个元素之间插值
        
        // 测试100%分位数（最大值）
        $result = $this->service->calculatePercentile($prices, 100);
        $this->assertEquals(140, $result);
    }

    /** @test */
    public function average_calculation_works_correctly()
    {
        $prices = [100, 110, 120, 130, 140];
        
        $result = $this->service->calculateAverage($prices);
        $this->assertEquals(120, $result);
        
        // 测试小数结果
        $prices = [100, 110, 125];
        $result = $this->service->calculateAverage($prices);
        $this->assertEquals(111.67, round($result, 2));
    }

    /** @test */
    public function max_calculation_works_correctly()
    {
        $prices = [100, 110, 120, 130, 140];
        
        $result = $this->service->calculateMax($prices);
        $this->assertEquals(140, $result);
        
        // 测试单个价格
        $prices = [50];
        $result = $this->service->calculateMax($prices);
        $this->assertEquals(50, $result);
    }
} 
<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use App\Services\ConfigurationService;
use Carbon\Carbon;

class PerformanceMonitoringService
{
    protected $configService;
    protected $metricsCache = [];
    protected $samplingRate = 100; // 采样率百分比
    
    public function __construct(ConfigurationService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * 收集所有性能指标（优化版本）
     */
    public function collectMetrics(): array
    {
        // 检查缓存，避免频繁计算
        $cacheKey = 'performance_metrics:' . floor(time() / 60); // 1分钟缓存
        $cached = Cache::get($cacheKey);
        
        if ($cached && $this->shouldUseCachedMetrics()) {
            return $cached;
        }
        
        $startTime = microtime(true);
        
        try {
            // 并行收集指标以提高性能
            $metrics = [
                'timestamp' => Carbon::now()->toISOString(),
                'collection_time' => 0, // 将在最后计算
            ];
            
            // 优先收集轻量级指标
            $metrics['system_metrics'] = $this->getSystemMetricsOptimized();
            $metrics['api_metrics'] = $this->getApiMetricsOptimized();
            
            // 根据采样率决定是否收集重量级指标
            if ($this->shouldCollectDetailedMetrics()) {
                $metrics['queue_metrics'] = $this->getQueueMetrics();
                $metrics['worker_metrics'] = $this->getWorkerMetrics();
                $metrics['concurrency_metrics'] = $this->getConcurrencyMetrics();
                $metrics['rate_limit_metrics'] = $this->getRateLimitMetrics();
            } else {
                // 使用缓存的详细指标或默认值
                $metrics['queue_metrics'] = $this->getCachedQueueMetrics();
                $metrics['worker_metrics'] = $this->getCachedWorkerMetrics();
                $metrics['concurrency_metrics'] = $this->getCachedConcurrencyMetrics();
                $metrics['rate_limit_metrics'] = $this->getCachedRateLimitMetrics();
            }
            
            $metrics['collection_time'] = round((microtime(true) - $startTime) * 1000, 2); // 毫秒
            
            // 缓存指标数据
            $this->cacheMetrics($metrics, $cacheKey);
            
            return $metrics;
            
        } catch (\Exception $e) {
            Log::error('收集性能指标失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2)
            ]);
            
            // 返回基础指标
            return $this->getBasicMetrics();
        }
    }
    
    /**
     * 检查是否应该使用缓存的指标
     */
    protected function shouldUseCachedMetrics(): bool
    {
        // 在高负载时更倾向于使用缓存
        $currentLoad = sys_getloadavg()[0] ?? 0;
        return $currentLoad > 2.0;
    }
    
    /**
     * 检查是否应该收集详细指标
     */
    protected function shouldCollectDetailedMetrics(): bool
    {
        // 使用采样率来减少负载
        return rand(1, 100) <= $this->samplingRate;
    }
    
    /**
     * 获取优化的系统指标
     */
    protected function getSystemMetricsOptimized(): array
    {
        $cacheKey = 'system_metrics:' . floor(time() / 30); // 30秒缓存
        
        return Cache::remember($cacheKey, 30, function () {
            return $this->getSystemMetrics();
        });
    }
    
    /**
     * 获取优化的API指标
     */
    protected function getApiMetricsOptimized(): array
    {
        $cacheKey = 'api_metrics:' . floor(time() / 60); // 1分钟缓存
        
        return Cache::remember($cacheKey, 60, function () {
            return $this->getApiMetrics();
        });
    }
    
    /**
     * 获取缓存的队列指标
     */
    protected function getCachedQueueMetrics(): array
    {
        return Cache::get('queue_metrics_cache', [
            'connection' => 'redis',
            'queues' => [],
            'total_pending' => 0,
            'total_failed' => 0,
            'total_processed_today' => 0,
            'cached' => true
        ]);
    }
    
    /**
     * 获取缓存的工作进程指标
     */
    protected function getCachedWorkerMetrics(): array
    {
        return Cache::get('worker_metrics_cache', [
            'active_workers' => 0,
            'total_memory_usage' => 0,
            'avg_cpu_usage' => 0,
            'workers' => [],
            'cached' => true
        ]);
    }
    
    /**
     * 获取缓存的并发指标
     */
    protected function getCachedConcurrencyMetrics(): array
    {
        return Cache::get('concurrency_metrics_cache', [
            'current_active_jobs' => 0,
            'optimal_concurrency' => 5,
            'recommendations' => [],
            'cached' => true
        ]);
    }
    
    /**
     * 获取缓存的限流指标
     */
    protected function getCachedRateLimitMetrics(): array
    {
        return Cache::get('rate_limit_metrics_cache', [
            'current_usage' => [],
            'violations' => [],
            'total_violations_today' => 0,
            'cached' => true
        ]);
    }
    
    /**
     * 获取基础指标（出错时的备用方案）
     */
    protected function getBasicMetrics(): array
    {
        return [
            'timestamp' => Carbon::now()->toISOString(),
            'collection_time' => 0,
            'system_metrics' => [
                'memory_usage' => ['usage_percentage' => 0],
                'disk_usage' => ['usage_percentage' => 0],
                'load_average' => ['1min' => 0]
            ],
            'api_metrics' => [
                'total_requests' => 0,
                'success_rate' => 100,
                'error_rate' => 0
            ],
            'error' => 'Failed to collect detailed metrics'
        ];
    }

    /**
     * 获取队列指标
     */
    public function getQueueMetrics(): array
    {
        $queueConfig = $this->configService->getQueueConfig();
        $connection = $queueConfig['connection'] ?? 'redis';
        
        $metrics = [
            'connection' => $connection,
            'queues' => [],
            'total_pending' => 0,
            'total_failed' => 0,
            'total_processed_today' => 0,
        ];

        try {
            // 获取各个队列的指标
            $queues = $queueConfig['queues'] ?? [];
            foreach ($queues as $priority => $queueName) {
                $queueMetrics = $this->getQueueSpecificMetrics($queueName, $connection);
                $metrics['queues'][$priority] = $queueMetrics;
                $metrics['total_pending'] += $queueMetrics['pending_jobs'];
            }

            // 获取失败任务数量
            $metrics['total_failed'] = $this->getFailedJobsCount();
            
            // 获取今日处理任务数量
            $metrics['total_processed_today'] = $this->getProcessedJobsToday();

        } catch (\Exception $e) {
            Log::error('获取队列指标失败', ['error' => $e->getMessage()]);
            $metrics['error'] = $e->getMessage();
        }

        return $metrics;
    }

    /**
     * 获取特定队列的指标
     */
    protected function getQueueSpecificMetrics(string $queueName, string $connection): array
    {
        $metrics = [
            'name' => $queueName,
            'pending_jobs' => 0,
            'delayed_jobs' => 0,
            'reserved_jobs' => 0,
            'avg_wait_time' => 0,
        ];

        try {
            if ($connection === 'redis') {
                $redis = Redis::connection();
                
                // 获取待处理任务数量
                $metrics['pending_jobs'] = $redis->llen("queues:{$queueName}");
                
                // 获取延迟任务数量
                $metrics['delayed_jobs'] = $redis->zcard("queues:{$queueName}:delayed");
                
                // 获取保留任务数量
                $metrics['reserved_jobs'] = $redis->zcard("queues:{$queueName}:reserved");
                
            } elseif ($connection === 'database') {
                $metrics['pending_jobs'] = DB::table('jobs')
                    ->where('queue', $queueName)
                    ->whereNull('reserved_at')
                    ->count();
                    
                $metrics['delayed_jobs'] = DB::table('jobs')
                    ->where('queue', $queueName)
                    ->where('available_at', '>', time())
                    ->count();
                    
                $metrics['reserved_jobs'] = DB::table('jobs')
                    ->where('queue', $queueName)
                    ->whereNotNull('reserved_at')
                    ->count();
            }

        } catch (\Exception $e) {
            Log::warning("获取队列 {$queueName} 指标失败", ['error' => $e->getMessage()]);
        }

        return $metrics;
    }

    /**
     * 获取工作进程指标
     */
    public function getWorkerMetrics(): array
    {
        $metrics = [
            'active_workers' => 0,
            'total_memory_usage' => 0,
            'avg_cpu_usage' => 0,
            'workers' => [],
        ];

        try {
            // 获取活跃的工作进程
            $workers = $this->getActiveWorkers();
            $metrics['active_workers'] = count($workers);
            
            foreach ($workers as $worker) {
                $workerMetrics = $this->getWorkerProcessMetrics($worker);
                $metrics['workers'][] = $workerMetrics;
                $metrics['total_memory_usage'] += $workerMetrics['memory_usage'];
                $metrics['avg_cpu_usage'] += $workerMetrics['cpu_usage'];
            }
            
            if ($metrics['active_workers'] > 0) {
                $metrics['avg_cpu_usage'] /= $metrics['active_workers'];
            }

        } catch (\Exception $e) {
            Log::error('获取工作进程指标失败', ['error' => $e->getMessage()]);
            $metrics['error'] = $e->getMessage();
        }

        return $metrics;
    }

    /**
     * 获取API指标
     */
    public function getApiMetrics(): array
    {
        $cacheKey = 'api_metrics:' . date('Y-m-d-H');
        
        $metrics = Cache::get($cacheKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'avg_response_time' => 0,
            'platforms' => [],
            'error_rates' => [],
        ]);

        // 计算成功率
        if ($metrics['total_requests'] > 0) {
            $metrics['success_rate'] = ($metrics['successful_requests'] / $metrics['total_requests']) * 100;
            $metrics['error_rate'] = ($metrics['failed_requests'] / $metrics['total_requests']) * 100;
        } else {
            $metrics['success_rate'] = 0;
            $metrics['error_rate'] = 0;
        }

        return $metrics;
    }

    /**
     * 获取系统指标
     */
    public function getSystemMetrics(): array
    {
        $metrics = [
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'load_average' => $this->getLoadAverage(),
            'uptime' => $this->getSystemUptime(),
        ];

        return $metrics;
    }

    /**
     * 获取并发控制指标
     */
    public function getConcurrencyMetrics(): array
    {
        $config = $this->configService->getDataCollectionConfig();
        $concurrency = $config['concurrency'] ?? 10;
        
        $metrics = [
            'configured_concurrency' => $concurrency,
            'current_active_jobs' => $this->getCurrentActiveJobs(),
            'optimal_concurrency' => $this->calculateOptimalConcurrency(),
            'concurrency_utilization' => 0,
            'recommendations' => [],
        ];

        // 计算并发利用率
        if ($concurrency > 0) {
            $metrics['concurrency_utilization'] = ($metrics['current_active_jobs'] / $concurrency) * 100;
        }

        // 生成并发优化建议
        $metrics['recommendations'] = $this->generateConcurrencyRecommendations($metrics);

        return $metrics;
    }

    /**
     * 获取速率限制指标
     */
    public function getRateLimitMetrics(): array
    {
        $securityConfig = $this->configService->getSecurityConfig();
        $rateLimiting = $securityConfig['rate_limiting'] ?? [];
        
        $metrics = [
            'enabled' => $rateLimiting['enabled'] ?? false,
            'limits' => [
                'per_minute' => $rateLimiting['max_requests_per_minute'] ?? 0,
                'per_hour' => $rateLimiting['max_requests_per_hour'] ?? 0,
            ],
            'current_usage' => $this->getCurrentRateLimitUsage(),
            'violations' => $this->getRateLimitViolations(),
        ];

        return $metrics;
    }

    /**
     * 记录API请求指标
     */
    public function recordApiRequest(string $platform, float $responseTime, bool $success = true, string $endpoint = null): void
    {
        $cacheKey = 'api_metrics:' . date('Y-m-d-H');
        
        $metrics = Cache::get($cacheKey, [
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'avg_response_time' => 0,
            'platforms' => [],
        ]);

        // 更新总体指标
        $metrics['total_requests']++;
        if ($success) {
            $metrics['successful_requests']++;
        } else {
            $metrics['failed_requests']++;
        }

        // 更新平均响应时间
        $oldAvg = $metrics['avg_response_time'];
        $metrics['avg_response_time'] = (($oldAvg * ($metrics['total_requests'] - 1)) + $responseTime) / $metrics['total_requests'];

        // 更新平台特定指标
        if (!isset($metrics['platforms'][$platform])) {
            $metrics['platforms'][$platform] = [
                'requests' => 0,
                'avg_response_time' => 0,
                'success_rate' => 100,
                'endpoints' => [],
            ];
        }

        $platformMetrics = &$metrics['platforms'][$platform];
        $platformMetrics['requests']++;
        $oldPlatformAvg = $platformMetrics['avg_response_time'];
        $platformMetrics['avg_response_time'] = (($oldPlatformAvg * ($platformMetrics['requests'] - 1)) + $responseTime) / $platformMetrics['requests'];

        if ($endpoint) {
            if (!isset($platformMetrics['endpoints'][$endpoint])) {
                $platformMetrics['endpoints'][$endpoint] = [
                    'requests' => 0,
                    'avg_response_time' => 0,
                    'success_rate' => 100,
                ];
            }
            
            $endpointMetrics = &$platformMetrics['endpoints'][$endpoint];
            $endpointMetrics['requests']++;
            $oldEndpointAvg = $endpointMetrics['avg_response_time'];
            $endpointMetrics['avg_response_time'] = (($oldEndpointAvg * ($endpointMetrics['requests'] - 1)) + $responseTime) / $endpointMetrics['requests'];
        }

        // 缓存更新的指标
        Cache::put($cacheKey, $metrics, 3600); // 缓存1小时
    }

    /**
     * 应用速率限制
     */
    public function applyRateLimit(string $key, int $maxRequests, int $windowSeconds): bool
    {
        $cacheKey = "rate_limit:{$key}:" . floor(time() / $windowSeconds);
        
        $currentCount = Cache::get($cacheKey, 0);
        
        if ($currentCount >= $maxRequests) {
            $this->recordRateLimitViolation($key, $currentCount, $maxRequests);
            return false;
        }
        
        Cache::put($cacheKey, $currentCount + 1, $windowSeconds);
        return true;
    }

    /**
     * 获取性能警报
     */
    public function getPerformanceAlerts(): array
    {
        $alerts = [];
        $metrics = $this->collectMetrics();

        // 检查队列积压
        if ($metrics['queue_metrics']['total_pending'] > 1000) {
            $alerts[] = [
                'type' => 'queue_backlog',
                'level' => 'warning',
                'message' => "队列积压严重，待处理任务数：{$metrics['queue_metrics']['total_pending']}",
                'value' => $metrics['queue_metrics']['total_pending'],
                'threshold' => 1000,
            ];
        }

        // 检查API错误率
        if (isset($metrics['api_metrics']['error_rate']) && $metrics['api_metrics']['error_rate'] > 10) {
            $alerts[] = [
                'type' => 'high_error_rate',
                'level' => 'error',
                'message' => "API错误率过高：{$metrics['api_metrics']['error_rate']}%",
                'value' => $metrics['api_metrics']['error_rate'],
                'threshold' => 10,
            ];
        }

        // 检查内存使用
        if (isset($metrics['system_metrics']['memory_usage']['usage_percentage']) && 
            $metrics['system_metrics']['memory_usage']['usage_percentage'] > 80) {
            $alerts[] = [
                'type' => 'high_memory_usage',
                'level' => 'warning',
                'message' => "内存使用率过高：{$metrics['system_metrics']['memory_usage']['usage_percentage']}%",
                'value' => $metrics['system_metrics']['memory_usage']['usage_percentage'],
                'threshold' => 80,
            ];
        }

        // 检查并发利用率
        if (isset($metrics['concurrency_metrics']['concurrency_utilization']) && 
            $metrics['concurrency_metrics']['concurrency_utilization'] > 90) {
            $alerts[] = [
                'type' => 'high_concurrency_usage',
                'level' => 'info',
                'message' => "并发利用率很高：{$metrics['concurrency_metrics']['concurrency_utilization']}%，建议增加并发数",
                'value' => $metrics['concurrency_metrics']['concurrency_utilization'],
                'threshold' => 90,
            ];
        }

        return $alerts;
    }

    /**
     * 优化并发设置
     */
    public function optimizeConcurrency(): array
    {
        $metrics = $this->getConcurrencyMetrics();
        $recommendations = [];

        $currentConcurrency = $metrics['configured_concurrency'];
        $utilization = $metrics['concurrency_utilization'];
        $optimalConcurrency = $metrics['optimal_concurrency'];

        if ($utilization > 90 && $currentConcurrency < 200) {
            $newConcurrency = min(200, $currentConcurrency * 1.5);
            $recommendations[] = [
                'action' => 'increase_concurrency',
                'current' => $currentConcurrency,
                'recommended' => $newConcurrency,
                'reason' => '并发利用率过高，建议增加并发数以提高处理能力',
            ];
        } elseif ($utilization < 50 && $currentConcurrency > 50) {
            $newConcurrency = max(50, $currentConcurrency * 0.8);
            $recommendations[] = [
                'action' => 'decrease_concurrency',
                'current' => $currentConcurrency,
                'recommended' => $newConcurrency,
                'reason' => '并发利用率较低，建议减少并发数以节省资源',
            ];
        }

        return $recommendations;
    }

    // 辅助方法

    protected function getFailedJobsCount(): int
    {
        try {
            return DB::table('failed_jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getProcessedJobsToday(): int
    {
        $cacheKey = 'processed_jobs:' . date('Y-m-d');
        return Cache::get($cacheKey, 0);
    }

    protected function getActiveWorkers(): array
    {
        // 这里可以根据实际情况实现，比如通过进程管理或监控工具获取
        return [];
    }

    protected function getWorkerProcessMetrics(array $worker): array
    {
        return [
            'pid' => $worker['pid'] ?? 0,
            'memory_usage' => $worker['memory'] ?? 0,
            'cpu_usage' => $worker['cpu'] ?? 0,
            'uptime' => $worker['uptime'] ?? 0,
        ];
    }

    protected function getMemoryUsage(): array
    {
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        $peakUsage = memory_get_peak_usage(true);

        return [
            'current' => $memoryUsage,
            'peak' => $peakUsage,
            'limit' => $this->parseMemoryLimit($memoryLimit),
            'usage_percentage' => $memoryLimit ? ($memoryUsage / $this->parseMemoryLimit($memoryLimit)) * 100 : 0,
        ];
    }

    protected function getDiskUsage(): array
    {
        $totalSpace = disk_total_space('/');
        $freeSpace = disk_free_space('/');
        $usedSpace = $totalSpace - $freeSpace;

        return [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'usage_percentage' => $totalSpace ? ($usedSpace / $totalSpace) * 100 : 0,
        ];
    }

    protected function getLoadAverage(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0] ?? 0,
                '5min' => $load[1] ?? 0,
                '15min' => $load[2] ?? 0,
            ];
        }
        
        return ['1min' => 0, '5min' => 0, '15min' => 0];
    }

    protected function getSystemUptime(): int
    {
        if (file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            return (int) floatval(explode(' ', $uptime)[0]);
        }
        
        return 0;
    }

    protected function getCurrentActiveJobs(): int
    {
        $cacheKey = 'active_jobs_count';
        return Cache::get($cacheKey, 0);
    }

    protected function calculateOptimalConcurrency(): int
    {
        // 基于当前系统指标计算最优并发数
        $systemMetrics = $this->getSystemMetrics();
        $memoryUsage = $systemMetrics['memory_usage']['usage_percentage'];
        $loadAverage = $systemMetrics['load_average']['1min'];

        $baseConcurrency = 100;
        
        // 根据内存使用调整
        if ($memoryUsage > 80) {
            $baseConcurrency *= 0.7;
        } elseif ($memoryUsage < 50) {
            $baseConcurrency *= 1.3;
        }

        // 根据负载调整
        if ($loadAverage > 2) {
            $baseConcurrency *= 0.8;
        } elseif ($loadAverage < 1) {
            $baseConcurrency *= 1.2;
        }

        return max(50, min(200, (int) $baseConcurrency));
    }

    protected function generateConcurrencyRecommendations(array $metrics): array
    {
        $recommendations = [];
        
        $utilization = $metrics['concurrency_utilization'];
        $current = $metrics['configured_concurrency'];
        $optimal = $metrics['optimal_concurrency'];

        if ($utilization > 90) {
            $recommendations[] = '并发利用率很高，考虑增加并发数以提高吞吐量';
        } elseif ($utilization < 30) {
            $recommendations[] = '并发利用率较低，可以考虑减少并发数以节省资源';
        }

        if ($optimal != $current) {
            $recommendations[] = "建议将并发数调整为 {$optimal}（当前：{$current}）";
        }

        return $recommendations;
    }

    protected function getCurrentRateLimitUsage(): array
    {
        $usage = [];
        $now = time();
        
        // 每分钟使用量
        $minuteKey = "rate_limit:global:" . floor($now / 60);
        $usage['per_minute'] = Cache::get($minuteKey, 0);
        
        // 每小时使用量
        $hourKey = "rate_limit:global:" . floor($now / 3600);
        $usage['per_hour'] = Cache::get($hourKey, 0);
        
        return $usage;
    }

    protected function getRateLimitViolations(): array
    {
        $cacheKey = 'rate_limit_violations:' . date('Y-m-d-H');
        return Cache::get($cacheKey, []);
    }

    protected function recordRateLimitViolation(string $key, int $current, int $limit): void
    {
        $cacheKey = 'rate_limit_violations:' . date('Y-m-d-H');
        $violations = Cache::get($cacheKey, []);
        
        $violations[] = [
            'key' => $key,
            'timestamp' => time(),
            'current_count' => $current,
            'limit' => $limit,
        ];
        
        Cache::put($cacheKey, $violations, 3600);
        
        Log::warning('速率限制违规', [
            'key' => $key,
            'current' => $current,
            'limit' => $limit,
        ]);
    }

    protected function cacheMetrics(array $metrics, string $cacheKey = null): void
    {
        $cacheKey = $cacheKey ?? ('performance_metrics:' . date('Y-m-d-H-i'));
        Cache::put($cacheKey, $metrics, 300); // 缓存5分钟
        
        // 同时缓存各个组件的指标以便后续使用
        if (isset($metrics['queue_metrics']) && !isset($metrics['queue_metrics']['cached'])) {
            Cache::put('queue_metrics_cache', $metrics['queue_metrics'], 120);
        }
        
        if (isset($metrics['worker_metrics']) && !isset($metrics['worker_metrics']['cached'])) {
            Cache::put('worker_metrics_cache', $metrics['worker_metrics'], 120);
        }
        
        if (isset($metrics['concurrency_metrics']) && !isset($metrics['concurrency_metrics']['cached'])) {
            Cache::put('concurrency_metrics_cache', $metrics['concurrency_metrics'], 120);
        }
        
        if (isset($metrics['rate_limit_metrics']) && !isset($metrics['rate_limit_metrics']['cached'])) {
            Cache::put('rate_limit_metrics_cache', $metrics['rate_limit_metrics'], 120);
        }
    }

    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $value = (int) $limit;

        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }

        return $value;
    }

    // ==================== 新增任务性能监控方法 ====================

    /**
     * 获取任务执行性能统计
     */
    public function getTaskPerformanceStats(): array
    {
        $stats = [
            'execution_times' => $this->getTaskExecutionTimes(),
            'success_rates' => $this->getTaskSuccessRates(),
            'queue_depths' => $this->getQueueDepthAnalysis(),
            'retry_analysis' => $this->getTaskRetryAnalysis(),
            'platform_comparison' => $this->getPlatformPerformanceComparison(),
            'bottleneck_analysis' => $this->getTaskBottleneckAnalysis(),
        ];

        return $stats;
    }

    /**
     * 获取任务执行时间分析
     */
    public function getTaskExecutionTimes(): array
    {
        $cacheKey = 'task_execution_times:' . date('Y-m-d');
        
        $data = Cache::get($cacheKey, [
            'data_collection_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'distribution' => [],
            ],
            'retry_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'distribution' => [],
            ],
            'batch_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'distribution' => [],
            ],
        ]);

        // 计算执行时间分布
        foreach ($data as &$jobType) {
            if ($jobType['total_jobs'] > 0) {
                $jobType['distribution'] = $this->calculateExecutionTimeDistribution($jobType);
            }
        }

        return $data;
    }

    /**
     * 记录任务执行时间
     */
    public function recordTaskExecution(string $jobType, float $executionTime, bool $success = true): void
    {
        $cacheKey = 'task_execution_times:' . date('Y-m-d');
        
        $data = Cache::get($cacheKey, [
            'data_collection_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'successful_jobs' => 0,
                'failed_jobs' => 0,
                'execution_times' => [],
            ],
            'retry_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'successful_jobs' => 0,
                'failed_jobs' => 0,
                'execution_times' => [],
            ],
            'batch_jobs' => [
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'total_jobs' => 0,
                'successful_jobs' => 0,
                'failed_jobs' => 0,
                'execution_times' => [],
            ],
        ]);

        if (!isset($data[$jobType])) {
            return;
        }

        $jobData = &$data[$jobType];
        
        // 更新基础统计
        $jobData['total_jobs']++;
        if ($success) {
            $jobData['successful_jobs']++;
        } else {
            $jobData['failed_jobs']++;
        }

        // 记录执行时间（最多保留1000条记录）
        $jobData['execution_times'][] = $executionTime;
        if (count($jobData['execution_times']) > 1000) {
            array_shift($jobData['execution_times']);
        }

        // 重新计算统计值
        $times = $jobData['execution_times'];
        $jobData['min_execution_time'] = min($times);
        $jobData['max_execution_time'] = max($times);
        $jobData['avg_execution_time'] = array_sum($times) / count($times);

        Cache::put($cacheKey, $data, 86400); // 缓存24小时
    }

    /**
     * 获取任务成功率统计
     */
    public function getTaskSuccessRates(): array
    {
        $cacheKey = 'task_success_rates:' . date('Y-m-d');
        
        return Cache::get($cacheKey, [
            'overall' => [
                'success_rate' => 0,
                'total_tasks' => 0,
                'successful_tasks' => 0,
                'failed_tasks' => 0,
            ],
            'by_platform' => [],
            'by_job_type' => [],
            'hourly_breakdown' => [],
        ]);
    }

    /**
     * 获取队列深度分析
     */
    public function getQueueDepthAnalysis(): array
    {
        $analysis = [
            'current_depths' => [],
            'average_depths' => [],
            'peak_depths' => [],
            'recommendations' => [],
        ];

        $queueConfig = $this->configService->getQueueConfig();
        $queues = $queueConfig['queues'] ?? [];

        foreach ($queues as $priority => $queueName) {
            $currentDepth = $this->getQueueSpecificMetrics($queueName, $queueConfig['connection'])['pending_jobs'];
            $analysis['current_depths'][$priority] = $currentDepth;

            // 获取历史平均深度
            $avgCacheKey = "queue_avg_depth:{$queueName}:" . date('Y-m-d');
            $analysis['average_depths'][$priority] = Cache::get($avgCacheKey, 0);

            // 获取峰值深度
            $peakCacheKey = "queue_peak_depth:{$queueName}:" . date('Y-m-d');
            $analysis['peak_depths'][$priority] = Cache::get($peakCacheKey, 0);

            // 生成建议
            if ($currentDepth > $analysis['average_depths'][$priority] * 3) {
                $analysis['recommendations'][] = "队列 {$queueName} 深度异常，当前: {$currentDepth}，平均: {$analysis['average_depths'][$priority]}";
            }
        }

        return $analysis;
    }

    /**
     * 记录队列深度
     */
    public function recordQueueDepth(string $queueName, int $depth): void
    {
        $date = date('Y-m-d');
        $hour = date('H');
        
        // 更新平均深度
        $avgCacheKey = "queue_avg_depth:{$queueName}:{$date}";
        $avgData = Cache::get($avgCacheKey, ['sum' => 0, 'count' => 0]);
        $avgData['sum'] += $depth;
        $avgData['count']++;
        Cache::put($avgCacheKey, $avgData, 86400);

        // 更新峰值深度
        $peakCacheKey = "queue_peak_depth:{$queueName}:{$date}";
        $currentPeak = Cache::get($peakCacheKey, 0);
        if ($depth > $currentPeak) {
            Cache::put($peakCacheKey, $depth, 86400);
        }

        // 记录小时级别数据
        $hourlyCacheKey = "queue_hourly_depth:{$queueName}:{$date}:{$hour}";
        $hourlyData = Cache::get($hourlyCacheKey, []);
        $hourlyData[] = $depth;
        Cache::put($hourlyCacheKey, $hourlyData, 86400);
    }

    /**
     * 获取任务重试分析
     */
    public function getTaskRetryAnalysis(): array
    {
        $cacheKey = 'task_retry_analysis:' . date('Y-m-d');
        
        return Cache::get($cacheKey, [
            'retry_counts' => [
                'total_retries' => 0,
                'by_attempt' => [1 => 0, 2 => 0, 3 => 0, '4+' => 0],
                'by_platform' => [],
                'by_error_type' => [],
            ],
            'retry_patterns' => [
                'most_retried_platforms' => [],
                'common_failure_reasons' => [],
                'retry_success_rate' => 0,
            ],
            'recommendations' => [],
        ]);
    }

    /**
     * 记录任务重试
     */
    public function recordTaskRetry(string $platform, int $attemptNumber, string $errorType = null, bool $finalSuccess = false): void
    {
        $cacheKey = 'task_retry_analysis:' . date('Y-m-d');
        
        $data = Cache::get($cacheKey, [
            'retry_counts' => [
                'total_retries' => 0,
                'by_attempt' => [1 => 0, 2 => 0, 3 => 0, '4+' => 0],
                'by_platform' => [],
                'by_error_type' => [],
            ],
            'retry_patterns' => [
                'most_retried_platforms' => [],
                'common_failure_reasons' => [],
                'retry_success_rate' => 0,
                'total_retried_tasks' => 0,
                'successful_retries' => 0,
            ],
            'recommendations' => [],
        ]);

        // 更新重试次数统计
        $data['retry_counts']['total_retries']++;
        
        $attemptKey = $attemptNumber <= 3 ? $attemptNumber : '4+';
        $data['retry_counts']['by_attempt'][$attemptKey]++;

        // 按平台统计
        if (!isset($data['retry_counts']['by_platform'][$platform])) {
            $data['retry_counts']['by_platform'][$platform] = 0;
        }
        $data['retry_counts']['by_platform'][$platform]++;

        // 按错误类型统计
        if ($errorType) {
            if (!isset($data['retry_counts']['by_error_type'][$errorType])) {
                $data['retry_counts']['by_error_type'][$errorType] = 0;
            }
            $data['retry_counts']['by_error_type'][$errorType]++;
        }

        // 更新重试成功率
        if ($attemptNumber > 1) {
            $data['retry_patterns']['total_retried_tasks']++;
            if ($finalSuccess) {
                $data['retry_patterns']['successful_retries']++;
            }
            
            if ($data['retry_patterns']['total_retried_tasks'] > 0) {
                $data['retry_patterns']['retry_success_rate'] = 
                    ($data['retry_patterns']['successful_retries'] / $data['retry_patterns']['total_retried_tasks']) * 100;
            }
        }

        Cache::put($cacheKey, $data, 86400);
    }

    /**
     * 获取平台性能比较
     */
    public function getPlatformPerformanceComparison(): array
    {
        $comparison = [];
        $platforms = ['taobao', 'tmall', 'jd', 'pinduoduo']; // 可配置

        foreach ($platforms as $platform) {
            $platformMetrics = $this->getPlatformSpecificMetrics($platform);
            $comparison[$platform] = $platformMetrics;
        }

        // 计算相对性能排名
        $comparison['rankings'] = $this->calculatePlatformRankings($comparison);

        return $comparison;
    }

    /**
     * 获取特定平台的性能指标
     */
    protected function getPlatformSpecificMetrics(string $platform): array
    {
        $cacheKey = "platform_metrics:{$platform}:" . date('Y-m-d');
        
        return Cache::get($cacheKey, [
            'avg_response_time' => 0,
            'success_rate' => 0,
            'total_requests' => 0,
            'error_rate' => 0,
            'rate_limit_hits' => 0,
            'avg_retry_count' => 0,
            'performance_score' => 0,
        ]);
    }

    /**
     * 计算平台性能排名
     */
    protected function calculatePlatformRankings(array $platformData): array
    {
        $rankings = [
            'by_response_time' => [],
            'by_success_rate' => [],
            'by_overall_performance' => [],
        ];

        $platforms = array_keys($platformData);
        $platforms = array_filter($platforms, function($key) {
            return $key !== 'rankings';
        });

        // 按响应时间排名（越低越好）
        usort($platforms, function($a, $b) use ($platformData) {
            return $platformData[$a]['avg_response_time'] <=> $platformData[$b]['avg_response_time'];
        });
        $rankings['by_response_time'] = $platforms;

        // 按成功率排名（越高越好）
        usort($platforms, function($a, $b) use ($platformData) {
            return $platformData[$b]['success_rate'] <=> $platformData[$a]['success_rate'];
        });
        $rankings['by_success_rate'] = $platforms;

        // 计算综合性能分数并排名
        foreach ($platforms as $platform) {
            $data = $platformData[$platform];
            $score = $this->calculatePerformanceScore($data);
            $platformData[$platform]['performance_score'] = $score;
        }

        usort($platforms, function($a, $b) use ($platformData) {
            return $platformData[$b]['performance_score'] <=> $platformData[$a]['performance_score'];
        });
        $rankings['by_overall_performance'] = $platforms;

        return $rankings;
    }

    /**
     * 计算性能分数
     */
    protected function calculatePerformanceScore(array $metrics): float
    {
        $score = 0;
        
        // 成功率权重 40%
        $score += $metrics['success_rate'] * 0.4;
        
        // 响应时间权重 30%（越低越好，转换为分数）
        $responseTimeScore = max(0, 100 - ($metrics['avg_response_time'] / 1000 * 10));
        $score += $responseTimeScore * 0.3;
        
        // 错误率权重 20%（越低越好）
        $errorRateScore = max(0, 100 - $metrics['error_rate']);
        $score += $errorRateScore * 0.2;
        
        // 重试率权重 10%（越低越好）
        $retryScore = max(0, 100 - ($metrics['avg_retry_count'] * 20));
        $score += $retryScore * 0.1;
        
        return round($score, 2);
    }

    /**
     * 获取任务瓶颈分析
     */
    public function getTaskBottleneckAnalysis(): array
    {
        $analysis = [
            'identified_bottlenecks' => [],
            'performance_trends' => $this->getPerformanceTrends(),
            'resource_constraints' => $this->getResourceConstraints(),
            'optimization_suggestions' => [],
        ];

        // 识别瓶颈
        $analysis['identified_bottlenecks'] = $this->identifyBottlenecks();
        
        // 生成优化建议
        $analysis['optimization_suggestions'] = $this->generateOptimizationSuggestions($analysis);

        return $analysis;
    }

    /**
     * 识别系统瓶颈
     */
    protected function identifyBottlenecks(): array
    {
        $bottlenecks = [];
        $metrics = $this->collectMetrics();

        // 检查队列瓶颈
        if ($metrics['queue_metrics']['total_pending'] > 1000) {
            $bottlenecks[] = [
                'type' => 'queue_backlog',
                'severity' => 'high',
                'description' => '队列积压严重，可能影响任务处理效率',
                'metric_value' => $metrics['queue_metrics']['total_pending'],
                'threshold' => 1000,
            ];
        }

        // 检查API响应瓶颈
        if (isset($metrics['api_metrics']['avg_response_time']) && $metrics['api_metrics']['avg_response_time'] > 5000) {
            $bottlenecks[] = [
                'type' => 'api_response_time',
                'severity' => 'medium',
                'description' => 'API响应时间过长，可能影响数据收集效率',
                'metric_value' => $metrics['api_metrics']['avg_response_time'],
                'threshold' => 5000,
            ];
        }

        // 检查并发瓶颈
        if (isset($metrics['concurrency_metrics']['concurrency_utilization']) && 
            $metrics['concurrency_metrics']['concurrency_utilization'] > 95) {
            $bottlenecks[] = [
                'type' => 'concurrency_limit',
                'severity' => 'high',
                'description' => '并发利用率接近上限，需要增加并发数',
                'metric_value' => $metrics['concurrency_metrics']['concurrency_utilization'],
                'threshold' => 95,
            ];
        }

        // 检查内存瓶颈
        if (isset($metrics['system_metrics']['memory_usage']['usage_percentage']) && 
            $metrics['system_metrics']['memory_usage']['usage_percentage'] > 85) {
            $bottlenecks[] = [
                'type' => 'memory_constraint',
                'severity' => 'high',
                'description' => '内存使用率过高，可能导致性能下降',
                'metric_value' => $metrics['system_metrics']['memory_usage']['usage_percentage'],
                'threshold' => 85,
            ];
        }

        return $bottlenecks;
    }

    /**
     * 获取性能趋势
     */
    protected function getPerformanceTrends(): array
    {
        $trends = [
            'queue_depth_trend' => $this->getQueueDepthTrend(),
            'response_time_trend' => $this->getResponseTimeTrend(),
            'success_rate_trend' => $this->getSuccessRateTrend(),
            'throughput_trend' => $this->getThroughputTrend(),
        ];

        return $trends;
    }

    /**
     * 获取队列深度趋势
     */
    protected function getQueueDepthTrend(): array
    {
        $trend = [];
        $hours = 24;
        
        for ($i = $hours - 1; $i >= 0; $i--) {
            $timestamp = time() - ($i * 3600);
            $hour = date('Y-m-d-H', $timestamp);
            $cacheKey = "queue_depth_hourly:{$hour}";
            
            $trend[] = [
                'timestamp' => $timestamp,
                'hour' => $hour,
                'avg_depth' => Cache::get($cacheKey, 0),
            ];
        }
        
        return $trend;
    }

    /**
     * 获取响应时间趋势
     */
    protected function getResponseTimeTrend(): array
    {
        $trend = [];
        $hours = 24;
        
        for ($i = $hours - 1; $i >= 0; $i--) {
            $timestamp = time() - ($i * 3600);
            $hour = date('Y-m-d-H', $timestamp);
            $cacheKey = "response_time_hourly:{$hour}";
            
            $trend[] = [
                'timestamp' => $timestamp,
                'hour' => $hour,
                'avg_response_time' => Cache::get($cacheKey, 0),
            ];
        }
        
        return $trend;
    }

    /**
     * 获取成功率趋势
     */
    protected function getSuccessRateTrend(): array
    {
        $trend = [];
        $hours = 24;
        
        for ($i = $hours - 1; $i >= 0; $i--) {
            $timestamp = time() - ($i * 3600);
            $hour = date('Y-m-d-H', $timestamp);
            $cacheKey = "success_rate_hourly:{$hour}";
            
            $trend[] = [
                'timestamp' => $timestamp,
                'hour' => $hour,
                'success_rate' => Cache::get($cacheKey, 100),
            ];
        }
        
        return $trend;
    }

    /**
     * 获取吞吐量趋势
     */
    protected function getThroughputTrend(): array
    {
        $trend = [];
        $hours = 24;
        
        for ($i = $hours - 1; $i >= 0; $i--) {
            $timestamp = time() - ($i * 3600);
            $hour = date('Y-m-d-H', $timestamp);
            $cacheKey = "throughput_hourly:{$hour}";
            
            $trend[] = [
                'timestamp' => $timestamp,
                'hour' => $hour,
                'throughput' => Cache::get($cacheKey, 0),
            ];
        }
        
        return $trend;
    }

    /**
     * 获取资源约束信息
     */
    protected function getResourceConstraints(): array
    {
        $constraints = [];
        $systemMetrics = $this->getSystemMetrics();

        // CPU约束
        if ($systemMetrics['load_average']['1min'] > 2) {
            $constraints['cpu'] = [
                'constrained' => true,
                'current_load' => $systemMetrics['load_average']['1min'],
                'recommendation' => '考虑减少并发任务数或升级CPU',
            ];
        } else {
            $constraints['cpu'] = ['constrained' => false];
        }

        // 内存约束
        if ($systemMetrics['memory_usage']['usage_percentage'] > 80) {
            $constraints['memory'] = [
                'constrained' => true,
                'usage_percentage' => $systemMetrics['memory_usage']['usage_percentage'],
                'recommendation' => '考虑增加内存或优化内存使用',
            ];
        } else {
            $constraints['memory'] = ['constrained' => false];
        }

        // 磁盘约束
        if ($systemMetrics['disk_usage']['usage_percentage'] > 85) {
            $constraints['disk'] = [
                'constrained' => true,
                'usage_percentage' => $systemMetrics['disk_usage']['usage_percentage'],
                'recommendation' => '考虑清理磁盘空间或增加存储',
            ];
        } else {
            $constraints['disk'] = ['constrained' => false];
        }

        return $constraints;
    }

    /**
     * 生成优化建议
     */
    protected function generateOptimizationSuggestions(array $analysis): array
    {
        $suggestions = [];

        // 基于瓶颈生成建议
        foreach ($analysis['identified_bottlenecks'] as $bottleneck) {
            switch ($bottleneck['type']) {
                case 'queue_backlog':
                    $suggestions[] = [
                        'category' => 'queue_optimization',
                        'priority' => 'high',
                        'suggestion' => '增加工作进程数量或优化任务处理逻辑',
                        'expected_impact' => '减少队列等待时间，提高处理效率',
                    ];
                    break;
                    
                case 'api_response_time':
                    $suggestions[] = [
                        'category' => 'api_optimization',
                        'priority' => 'medium',
                        'suggestion' => '优化API调用策略，考虑增加缓存或连接池',
                        'expected_impact' => '降低API响应时间，提高数据收集速度',
                    ];
                    break;
                    
                case 'concurrency_limit':
                    $suggestions[] = [
                        'category' => 'concurrency_optimization',
                        'priority' => 'high',
                        'suggestion' => '增加并发数配置，但需注意资源限制',
                        'expected_impact' => '提高并行处理能力，增加系统吞吐量',
                    ];
                    break;
                    
                case 'memory_constraint':
                    $suggestions[] = [
                        'category' => 'resource_optimization',
                        'priority' => 'high',
                        'suggestion' => '优化内存使用或增加系统内存',
                        'expected_impact' => '避免内存不足导致的性能下降',
                    ];
                    break;
            }
        }

        // 基于趋势生成建议
        $trends = $analysis['performance_trends'];
        
        // 检查成功率下降趋势
        $recentSuccessRates = array_slice($trends['success_rate_trend'], -6); // 最近6小时
        $avgRecentSuccess = array_sum(array_column($recentSuccessRates, 'success_rate')) / count($recentSuccessRates);
        
        if ($avgRecentSuccess < 90) {
            $suggestions[] = [
                'category' => 'reliability_optimization',
                'priority' => 'high',
                'suggestion' => '检查失败原因并优化错误处理机制',
                'expected_impact' => '提高任务成功率，减少重试开销',
            ];
        }

        return $suggestions;
    }

    /**
     * 计算执行时间分布
     */
    protected function calculateExecutionTimeDistribution(array $jobData): array
    {
        $times = $jobData['execution_times'] ?? [];
        if (empty($times)) {
            return [];
        }

        sort($times);
        $count = count($times);

        return [
            'p50' => $times[intval($count * 0.5)],
            'p75' => $times[intval($count * 0.75)],
            'p90' => $times[intval($count * 0.9)],
            'p95' => $times[intval($count * 0.95)],
            'p99' => $times[intval($count * 0.99)],
        ];
    }

    /**
     * 获取实时任务性能快照
     */
    public function getTaskPerformanceSnapshot(): array
    {
        return [
            'timestamp' => Carbon::now()->toISOString(),
            'queue_status' => $this->getQueueMetrics(),
            'current_bottlenecks' => $this->identifyBottlenecks(),
            'active_tasks' => $this->getCurrentActiveJobs(),
            'recent_performance' => [
                'last_hour_success_rate' => $this->getHourlySuccessRate(),
                'last_hour_avg_response_time' => $this->getHourlyAvgResponseTime(),
                'last_hour_throughput' => $this->getHourlyThroughput(),
            ],
            'alerts' => $this->getPerformanceAlerts(),
        ];
    }

    /**
     * 获取小时级成功率
     */
    protected function getHourlySuccessRate(): float
    {
        $cacheKey = 'success_rate_hourly:' . date('Y-m-d-H');
        return Cache::get($cacheKey, 100.0);
    }

    /**
     * 获取小时级平均响应时间
     */
    protected function getHourlyAvgResponseTime(): float
    {
        $cacheKey = 'response_time_hourly:' . date('Y-m-d-H');
        return Cache::get($cacheKey, 0.0);
    }

    /**
     * 获取小时级吞吐量
     */
    protected function getHourlyThroughput(): int
    {
        $cacheKey = 'throughput_hourly:' . date('Y-m-d-H');
        return Cache::get($cacheKey, 0);
    }

    /**
     * 记录小时级指标
     */
    public function recordHourlyMetrics(float $responseTime, bool $success): void
    {
        $hour = date('Y-m-d-H');
        
        // 记录成功率
        $successKey = "success_rate_hourly:{$hour}";
        $successData = Cache::get($successKey, ['total' => 0, 'successful' => 0]);
        $successData['total']++;
        if ($success) {
            $successData['successful']++;
        }
        $successRate = $successData['total'] > 0 ? ($successData['successful'] / $successData['total']) * 100 : 100;
        Cache::put($successKey, $successData, 3600);
        Cache::put("success_rate_hourly:{$hour}", $successRate, 3600);
        
        // 记录响应时间
        $responseKey = "response_time_hourly:{$hour}";
        $responseData = Cache::get($responseKey, ['sum' => 0, 'count' => 0]);
        $responseData['sum'] += $responseTime;
        $responseData['count']++;
        $avgResponseTime = $responseData['sum'] / $responseData['count'];
        Cache::put($responseKey, $responseData, 3600);
        Cache::put("response_time_hourly:{$hour}", $avgResponseTime, 3600);
        
        // 记录吞吐量
        $throughputKey = "throughput_hourly:{$hour}";
        $currentThroughput = Cache::get($throughputKey, 0);
        Cache::put($throughputKey, $currentThroughput + 1, 3600);
    }
}
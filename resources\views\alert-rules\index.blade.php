@extends('layouts.app')

@section('title', '警报规则管理')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="page-title">
        <i class="fas fa-cogs text-primary me-2"></i>
        警报规则管理
    </h1>
    <a href="{{ route('alert-rules.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        创建新规则
    </a>
</div>

{{-- 统计卡片 --}}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ $stats['total'] }}</h5>
                    <p class="mb-0 small">总规则数</p>
                </div>
                <div class="fa-2x">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ $stats['active'] }}</h5>
                    <p class="mb-0 small">活跃规则</p>
                </div>
                <div class="fa-2x">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ $stats['triggered_today'] }}</h5>
                    <p class="mb-0 small">今日触发</p>
                </div>
                <div class="fa-2x">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1">{{ $stats['inactive'] }}</h5>
                    <p class="mb-0 small">已禁用</p>
                </div>
                <div class="fa-2x">
                    <i class="fas fa-pause-circle"></i>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- 筛选和搜索 --}}
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">搜索规则</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="规则名称或描述">
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">规则类型</label>
                <select class="form-select" id="type" name="type">
                    <option value="">全部类型</option>
                    <option value="price_increase" {{ request('type') == 'price_increase' ? 'selected' : '' }}>价格上涨</option>
                    <option value="price_decrease" {{ request('type') == 'price_decrease' ? 'selected' : '' }}>价格下降</option>
                    <option value="stock_shortage" {{ request('type') == 'stock_shortage' ? 'selected' : '' }}>库存不足</option>
                    <option value="competitor_analysis" {{ request('type') == 'competitor_analysis' ? 'selected' : '' }}>竞争分析</option>
                    <option value="market_trend" {{ request('type') == 'market_trend' ? 'selected' : '' }}>市场趋势</option>
                    <option value="price_deviation" {{ request('type') == 'price_deviation' ? 'selected' : '' }}>价格偏差</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">状态</label>
                <select class="form-select" id="status" name="status">
                    <option value="">全部状态</option>
                    <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>启用</option>
                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>禁用</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="priority" class="form-label">优先级</label>
                <select class="form-select" id="priority" name="priority">
                    <option value="">全部优先级</option>
                    <option value="high" {{ request('priority') == 'high' ? 'selected' : '' }}>高</option>
                    <option value="medium" {{ request('priority') == 'medium' ? 'selected' : '' }}>中</option>
                    <option value="low" {{ request('priority') == 'low' ? 'selected' : '' }}>低</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="{{ route('alert-rules.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>重置
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

{{-- 规则列表 --}}
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                警报规则列表
            </h5>
            @if($rules->count() > 0)
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="selectAll">
                    <label class="form-check-label" for="selectAll">全选</label>
                </div>
            @endif
        </div>
    </div>
    
    <div class="card-body p-0">
        @if($rules->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAllHeader" class="form-check-input">
                            </th>
                            <th>规则名称</th>
                            <th>类型</th>
                            <th>优先级</th>
                            <th>状态</th>
                            <th>关联产品</th>
                            <th>创建时间</th>
                            <th>最后触发</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($rules as $rule)
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input rule-checkbox" value="{{ $rule->id }}">
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $rule->name }}</strong>
                                        @if($rule->description)
                                            <small class="text-muted d-block">{{ Str::limit($rule->description, 50) }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @php
                                        $typeLabels = [
                                            'price_increase' => ['价格上涨', 'danger'],
                                            'price_decrease' => ['价格下降', 'success'],
                                            'stock_shortage' => ['库存不足', 'warning'],
                                            'competitor_analysis' => ['竞争分析', 'info'],
                                            'market_trend' => ['市场趋势', 'primary'],
                                            'price_deviation' => ['价格偏差', 'warning']
                                        ];
                                        $typeInfo = $typeLabels[$rule->type] ?? ['未知', 'secondary'];
                                    @endphp
                                    <span class="badge bg-{{ $typeInfo[1] }}">{{ $typeInfo[0] }}</span>
                                </td>
                                <td>
                                    @php
                                        $priorityLabels = [
                                            'high' => ['高', 'danger'],
                                            'medium' => ['中', 'warning'],
                                            'low' => ['低', 'secondary']
                                        ];
                                        $priorityInfo = $priorityLabels[$rule->priority] ?? ['中', 'warning'];
                                    @endphp
                                    <span class="badge bg-{{ $priorityInfo[1] }}">{{ $priorityInfo[0] }}</span>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input status-toggle" type="checkbox" 
                                               {{ $rule->is_active ? 'checked' : '' }}
                                               data-rule-id="{{ $rule->id }}">
                                        <label class="form-check-label">
                                            {{ $rule->is_active ? '启用' : '禁用' }}
                                        </label>
                                    </div>
                                </td>
                                <td>
                                    @if($rule->product)
                                        <small>{{ Str::limit($rule->product->name, 30) }}</small>
                                    @elseif($rule->productSku)
                                        <small>SKU: {{ $rule->productSku->sku_code }}</small>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <small>{{ $rule->created_at->format('Y-m-d H:i') }}</small>
                                </td>
                                <td>
                                    @if($rule->last_triggered_at)
                                        <small>{{ $rule->last_triggered_at->format('Y-m-d H:i') }}</small>
                                    @else
                                        <span class="text-muted">从未触发</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('alert-rules.edit', $rule) }}" 
                                           class="btn btn-outline-primary btn-sm" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-sm delete-rule"
                                                data-rule-id="{{ $rule->id }}" 
                                                data-rule-name="{{ $rule->name }}" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            {{-- 分页 --}}
            @if($rules->hasPages())
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            显示第 {{ $rules->firstItem() }} 到 {{ $rules->lastItem() }} 条，共 {{ $rules->total() }} 条记录
                        </div>
                        {{ $rules->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
        @else
            <div class="text-center py-5">
                <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                <h5 class="text-muted mb-3">暂无警报规则</h5>
                <p class="text-muted mb-4">开始创建您的第一个警报规则来监控重要的数据变化</p>
                <a href="{{ route('alert-rules.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>创建新规则
                </a>
            </div>
        @endif
    </div>
</div>

{{-- 批量操作工具栏 --}}
<div class="fixed-bottom bg-dark text-white p-3" id="bulkActionBar" style="display: none;">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span id="selectedCount">0</span> 项已选中
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-light btn-sm" id="bulkEnable">
                    <i class="fas fa-check me-1"></i>批量启用
                </button>
                <button type="button" class="btn btn-outline-light btn-sm" id="bulkDisable">
                    <i class="fas fa-pause me-1"></i>批量禁用
                </button>
                <button type="button" class="btn btn-outline-danger btn-sm" id="bulkDelete">
                    <i class="fas fa-trash me-1"></i>批量删除
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="bulkCancel">
                    <i class="fas fa-times me-1"></i>取消
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
// 全局确认删除函数
function confirmDelete(message) {
    return confirm(message);
}

document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllHeaderCheckbox = document.getElementById('selectAllHeader');
    const ruleCheckboxes = document.querySelectorAll('.rule-checkbox');
    const bulkActionBar = document.getElementById('bulkActionBar');
    const selectedCount = document.getElementById('selectedCount');

    // 同步两个全选复选框
    function syncSelectAll() {
        if (selectAllCheckbox) selectAllCheckbox.checked = selectAllHeaderCheckbox?.checked || false;
        if (selectAllHeaderCheckbox) selectAllHeaderCheckbox.checked = selectAllCheckbox?.checked || false;
    }

    selectAllCheckbox?.addEventListener('change', function() {
        selectAllHeaderCheckbox.checked = this.checked;
        ruleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionBar();
    });

    selectAllHeaderCheckbox?.addEventListener('change', function() {
        selectAllCheckbox.checked = this.checked;
        ruleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionBar();
    });

    ruleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 检查是否所有复选框都被选中
            const allChecked = Array.from(ruleCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(ruleCheckboxes).every(cb => !cb.checked);
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
            }
            if (selectAllHeaderCheckbox) {
                selectAllHeaderCheckbox.checked = allChecked;
                selectAllHeaderCheckbox.indeterminate = !allChecked && !noneChecked;
            }
            
            updateBulkActionBar();
        });
    });

    function updateBulkActionBar() {
        const checkedBoxes = document.querySelectorAll('.rule-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0 && bulkActionBar) {
            bulkActionBar.style.display = 'block';
            if (selectedCount) selectedCount.textContent = count;
        } else if (bulkActionBar) {
            bulkActionBar.style.display = 'none';
        }
    }

    // 状态切换
    const statusToggles = document.querySelectorAll('.status-toggle');
    statusToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const ruleId = this.dataset.ruleId;
            const isActive = this.checked;
            
            fetch(`/api/alert-rules/${ruleId}/toggle-status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Authorization': 'Bearer ' + (document.querySelector('meta[name="api-token"]')?.content || '')
                },
                body: JSON.stringify({ is_active: isActive })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const label = this.nextElementSibling;
                    label.textContent = isActive ? '启用' : '禁用';
                    // 显示成功提示
                    showToast('状态更新成功', 'success');
                } else {
                    this.checked = !isActive; // 恢复原状态
                    showToast(data.message || '状态更新失败，请重试', 'error');
                }
            })
            .catch(error => {
                this.checked = !isActive; // 恢复原状态
                showToast('网络错误，请重试', 'error');
                console.error('Error:', error);
            });
        });
    });

    // 删除规则
    const deleteButtons = document.querySelectorAll('.delete-rule');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const ruleId = this.dataset.ruleId;
            const ruleName = this.dataset.ruleName;
            
            if (confirmDelete(`确定要删除规则"${ruleName}"吗？此操作不可恢复。`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/api/alert-rules/${ruleId}`;
                form.innerHTML = `
                    <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').content}">
                    <input type="hidden" name="_method" value="DELETE">
                `;
                
                // 使用fetch进行删除
                fetch(`/api/alert-rules/${ruleId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Authorization': 'Bearer ' + (document.querySelector('meta[name="api-token"]')?.content || '')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('规则删除成功', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showToast(data.message || '删除失败，请重试', 'error');
                    }
                })
                .catch(error => {
                    showToast('网络错误，请重试', 'error');
                    console.error('Error:', error);
                });
            }
        });
    });

    // 批量操作
    document.getElementById('bulkEnable')?.addEventListener('click', function() {
        bulkAction('enable');
    });

    document.getElementById('bulkDisable')?.addEventListener('click', function() {
        bulkAction('disable');
    });

    document.getElementById('bulkDelete')?.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.rule-checkbox:checked');
        if (confirmDelete(`确定要删除选中的 ${checkedBoxes.length} 个规则吗？此操作不可恢复。`)) {
            bulkAction('delete');
        }
    });

    document.getElementById('bulkCancel')?.addEventListener('click', function() {
        if (selectAllCheckbox) selectAllCheckbox.checked = false;
        if (selectAllHeaderCheckbox) selectAllHeaderCheckbox.checked = false;
        ruleCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateBulkActionBar();
    });

    function bulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.rule-checkbox:checked');
        const ruleIds = Array.from(checkedBoxes).map(checkbox => checkbox.value);
        
        if (ruleIds.length === 0) {
            showToast('请先选择要操作的规则', 'warning');
            return;
        }

        fetch('/alert-rules/bulk-action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                action: action,
                rule_ids: ruleIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message || '操作失败，请重试', 'error');
            }
        })
        .catch(error => {
            showToast('网络错误，请重试', 'error');
            console.error('Error:', error);
        });
    }

    // 简单的Toast提示函数
    function showToast(message, type = 'info') {
        // 如果页面有Bootstrap的Toast组件，使用它
        // 否则使用简单的alert
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            // 创建Bootstrap Toast
            const toastHtml = `
                <div class="toast align-items-center text-bg-${type === 'error' ? 'danger' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            // 添加到页面并显示
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = toastContainer.lastElementChild;
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
            
            // 自动移除
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        } else {
            // 简单的alert备选方案
            alert(message);
        }
    }
});
</script>
@endpush 
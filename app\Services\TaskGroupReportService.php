<?php

namespace App\Services;

use App\Models\TaskGroup;
use App\Models\MonitorTask;
use App\Models\PriceHistory;
use App\Models\AlertLog;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TaskGroupReportService
{
    /**
     * 获取任务组的完整报告数据
     */
    public function getGroupReport(int $groupId, array $options = []): array
    {
        $taskGroup = TaskGroup::with(['monitorTasks', 'user'])->findOrFail($groupId);
        
        $startDate = $options['start_date'] ?? Carbon::now()->subDays(30);
        $endDate = $options['end_date'] ?? Carbon::now();
        
        return [
            'group_info' => $this->getGroupInfo($taskGroup),
            'task_overview' => $this->getTaskOverview($taskGroup),
            'performance_metrics' => $this->getPerformanceMetrics($taskGroup, $startDate, $endDate),
            'price_analytics' => $this->getPriceAnalytics($taskGroup, $startDate, $endDate),
            'alert_summary' => $this->getAlertSummary($taskGroup, $startDate, $endDate),
            'trend_data' => $this->getTrendData($taskGroup, $startDate, $endDate),
            'generated_at' => Carbon::now(),
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'days' => Carbon::parse($startDate)->diffInDays($endDate)
            ]
        ];
    }

    /**
     * 获取任务组基本信息
     */
    protected function getGroupInfo(TaskGroup $taskGroup): array
    {
        return [
            'id' => $taskGroup->id,
            'name' => $taskGroup->name,
            'description' => $taskGroup->description,
            'color' => $taskGroup->color,
            'created_at' => $taskGroup->created_at,
            'is_active' => $taskGroup->is_active,
            'owner' => [
                'id' => $taskGroup->user->id,
                'name' => $taskGroup->user->name,
                'email' => $taskGroup->user->email
            ]
        ];
    }

    /**
     * 获取任务概览统计
     */
    protected function getTaskOverview(TaskGroup $taskGroup): array
    {
        $tasks = $taskGroup->monitorTasks();
        
        $statusStats = $tasks->select('status', DB::raw('COUNT(*) as count'))
                           ->groupBy('status')
                           ->pluck('count', 'status')
                           ->toArray();

        $enabledStats = $tasks->select('is_enabled', DB::raw('COUNT(*) as count'))
                            ->groupBy('is_enabled')
                            ->pluck('count', 'is_enabled')
                            ->toArray();

        $platformStats = $tasks->select('platform', DB::raw('COUNT(*) as count'))
                             ->groupBy('platform')
                             ->pluck('count', 'platform')
                             ->toArray();

        return [
            'total_tasks' => $tasks->count(),
            'active_tasks' => $tasks->where('status', 'active')->count(),
            'enabled_tasks' => $tasks->where('is_enabled', true)->count(),
            'disabled_tasks' => $tasks->where('is_enabled', false)->count(),
            'error_tasks' => $tasks->where('status', 'error')->count(),
            'status_breakdown' => $statusStats,
            'enabled_breakdown' => [
                'enabled' => $enabledStats[1] ?? 0,
                'disabled' => $enabledStats[0] ?? 0
            ],
            'platform_breakdown' => $platformStats,
            'health_score' => $this->calculateHealthScore($taskGroup)
        ];
    }

    /**
     * 获取性能指标
     */
    protected function getPerformanceMetrics(TaskGroup $taskGroup, $startDate, $endDate): array
    {
        $taskIds = $taskGroup->monitorTasks()->pluck('id')->toArray();
        
        // 聚合监控任务的执行统计
        $executionStats = MonitorTask::whereIn('id', $taskIds)
            ->select([
                DB::raw('SUM(collection_count) as total_collections'),
                DB::raw('SUM(execution_count) as total_executions'),
                DB::raw('SUM(success_count) as total_successes'),
                DB::raw('SUM(failure_count) as total_failures'),
                DB::raw('AVG(consecutive_errors) as avg_consecutive_errors'),
                DB::raw('MAX(last_collected_at) as last_collection'),
                DB::raw('MIN(created_at) as first_task_created')
            ])
            ->first();

        $successRate = $executionStats->total_executions > 0 
            ? ($executionStats->total_successes / $executionStats->total_executions) * 100 
            : 0;

        return [
            'collection_stats' => [
                'total_collections' => $executionStats->total_collections ?? 0,
                'total_executions' => $executionStats->total_executions ?? 0,
                'total_successes' => $executionStats->total_successes ?? 0,
                'total_failures' => $executionStats->total_failures ?? 0,
                'success_rate' => round($successRate, 2),
                'failure_rate' => round(100 - $successRate, 2)
            ],
            'error_metrics' => [
                'avg_consecutive_errors' => round($executionStats->avg_consecutive_errors ?? 0, 2),
                'tasks_with_errors' => MonitorTask::whereIn('id', $taskIds)
                    ->where('consecutive_errors', '>', 0)->count(),
                'last_collection' => $executionStats->last_collection
            ],
            'uptime_metrics' => $this->calculateUptimeMetrics($taskIds, $startDate, $endDate)
        ];
    }

    /**
     * 获取价格分析数据
     */
    protected function getPriceAnalytics(TaskGroup $taskGroup, $startDate, $endDate): array
    {
        $taskIds = $taskGroup->monitorTasks()->pluck('id')->toArray();
        
        $priceStats = PriceHistory::whereIn('task_id', $taskIds)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->select([
                DB::raw('COUNT(*) as total_records'),
                DB::raw('AVG(price) as avg_price'),
                DB::raw('MIN(price) as min_price'),
                DB::raw('MAX(price) as max_price'),
                DB::raw('AVG(ABS(price_change_rate)) as avg_change_rate'),
                DB::raw('COUNT(CASE WHEN price_change > 0 THEN 1 END) as price_increases'),
                DB::raw('COUNT(CASE WHEN price_change < 0 THEN 1 END) as price_decreases'),
                DB::raw('COUNT(CASE WHEN price_change = 0 THEN 1 END) as price_stable'),
                DB::raw('AVG(promotion_deviation_rate) as avg_promotion_deviation'),
                DB::raw('AVG(channel_deviation_rate) as avg_channel_deviation')
            ])
            ->first();

        // 获取价格波动最大的商品
        $mostVolatile = PriceHistory::whereIn('task_id', $taskIds)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->select('sku_id', DB::raw('AVG(ABS(price_change_rate)) as volatility'))
            ->groupBy('sku_id')
            ->orderBy('volatility', 'desc')
            ->limit(5)
            ->with('sku.product')
            ->get();

        return [
            'price_statistics' => [
                'total_records' => $priceStats->total_records ?? 0,
                'average_price' => round($priceStats->avg_price ?? 0, 2),
                'price_range' => [
                    'min' => round($priceStats->min_price ?? 0, 2),
                    'max' => round($priceStats->max_price ?? 0, 2)
                ],
                'average_change_rate' => round($priceStats->avg_change_rate ?? 0, 2)
            ],
            'price_trends' => [
                'increases' => $priceStats->price_increases ?? 0,
                'decreases' => $priceStats->price_decreases ?? 0,
                'stable' => $priceStats->price_stable ?? 0
            ],
            'deviation_analysis' => [
                'avg_promotion_deviation' => round($priceStats->avg_promotion_deviation ?? 0, 4),
                'avg_channel_deviation' => round($priceStats->avg_channel_deviation ?? 0, 4)
            ],
            'most_volatile_products' => $mostVolatile->map(function ($item) {
                return [
                    'sku_id' => $item->sku_id,
                    'product_name' => $item->sku->product->title ?? 'Unknown',
                    'sku_code' => $item->sku->sku_code ?? 'Unknown',
                    'volatility' => round($item->volatility, 2)
                ];
            })
        ];
    }

    /**
     * 获取告警汇总
     */
    protected function getAlertSummary(TaskGroup $taskGroup, $startDate, $endDate): array
    {
        $taskIds = $taskGroup->monitorTasks()->pluck('id')->toArray();
        
        $alertStats = AlertLog::whereIn('task_id', $taskIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select([
                DB::raw('COUNT(*) as total_alerts'),
                DB::raw('COUNT(CASE WHEN severity = "critical" THEN 1 END) as critical_alerts'),
                DB::raw('COUNT(CASE WHEN severity = "high" THEN 1 END) as high_alerts'),
                DB::raw('COUNT(CASE WHEN severity = "medium" THEN 1 END) as medium_alerts'),
                DB::raw('COUNT(CASE WHEN severity = "low" THEN 1 END) as low_alerts'),
                DB::raw('COUNT(CASE WHEN status = "resolved" THEN 1 END) as resolved_alerts'),
                DB::raw('COUNT(CASE WHEN status = "unread" THEN 1 END) as unread_alerts'),
                DB::raw('AVG(CASE WHEN resolved_at IS NOT NULL THEN TIMESTAMPDIFF(HOUR, created_at, resolved_at) END) as avg_resolution_time')
            ])
            ->first();

        // 获取告警频次最高的任务
        $topAlertTasks = AlertLog::whereIn('task_id', $taskIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('task_id', DB::raw('COUNT(*) as alert_count'))
            ->groupBy('task_id')
            ->orderBy('alert_count', 'desc')
            ->limit(5)
            ->with('task')
            ->get();

        return [
            'alert_statistics' => [
                'total_alerts' => $alertStats->total_alerts ?? 0,
                'unread_alerts' => $alertStats->unread_alerts ?? 0,
                'resolved_alerts' => $alertStats->resolved_alerts ?? 0,
                'avg_resolution_time_hours' => round($alertStats->avg_resolution_time ?? 0, 2)
            ],
            'severity_breakdown' => [
                'critical' => $alertStats->critical_alerts ?? 0,
                'high' => $alertStats->high_alerts ?? 0,
                'medium' => $alertStats->medium_alerts ?? 0,
                'low' => $alertStats->low_alerts ?? 0
            ],
            'top_alert_tasks' => $topAlertTasks->map(function ($item) {
                return [
                    'task_id' => $item->task_id,
                    'task_name' => $item->task->task_name ?? 'Unknown',
                    'alert_count' => $item->alert_count
                ];
            })
        ];
    }

    /**
     * 获取趋势数据
     */
    protected function getTrendData(TaskGroup $taskGroup, $startDate, $endDate): array
    {
        $taskIds = $taskGroup->monitorTasks()->pluck('id')->toArray();
        
        // 按天统计数据收集量
        $collectionTrend = PriceHistory::whereIn('task_id', $taskIds)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(timestamp) as date'),
                DB::raw('COUNT(*) as collection_count'),
                DB::raw('AVG(price) as avg_price')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // 按天统计告警数量
        $alertTrend = AlertLog::whereIn('task_id', $taskIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as alert_count'),
                DB::raw('COUNT(CASE WHEN severity IN ("critical", "high") THEN 1 END) as critical_count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'collection_trend' => $collectionTrend,
            'alert_trend' => $alertTrend,
            'performance_trend' => $this->getPerformanceTrend($taskIds, $startDate, $endDate)
        ];
    }

    /**
     * 计算健康评分
     */
    protected function calculateHealthScore(TaskGroup $taskGroup): float
    {
        $tasks = $taskGroup->monitorTasks;
        
        if ($tasks->isEmpty()) {
            return 0;
        }

        $totalTasks = $tasks->count();
        $activeTasks = $tasks->where('status', 'active')->count();
        $enabledTasks = $tasks->where('is_enabled', true)->count();
        $errorTasks = $tasks->where('status', 'error')->count();
        
        // 基础健康分数
        $healthScore = 0;
        
        // 活跃任务比例 (40%)
        $healthScore += ($activeTasks / $totalTasks) * 40;
        
        // 启用任务比例 (30%)
        $healthScore += ($enabledTasks / $totalTasks) * 30;
        
        // 错误任务惩罚 (30%)
        $errorPenalty = ($errorTasks / $totalTasks) * 30;
        $healthScore += (30 - $errorPenalty);
        
        return round(max(0, min(100, $healthScore)), 1);
    }

    /**
     * 计算正常运行时间指标
     */
    protected function calculateUptimeMetrics(array $taskIds, $startDate, $endDate): array
    {
        // 确保日期参数是Carbon实例
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        
        // 计算期间内的预期执行次数和实际成功次数
        $tasks = MonitorTask::whereIn('id', $taskIds)->get();
        
        $totalExpected = 0;
        $totalSuccesses = 0;
        
        foreach ($tasks as $task) {
            $frequencyMinutes = $task->frequency_minutes;
            $periodMinutes = $startDate->diffInMinutes($endDate);
            $expectedExecutions = intval($periodMinutes / $frequencyMinutes);
            
            $totalExpected += $expectedExecutions;
            $totalSuccesses += $task->success_count;
        }
        
        $uptime = $totalExpected > 0 ? ($totalSuccesses / $totalExpected) * 100 : 100;
        
        return [
            'uptime_percentage' => round(min(100, $uptime), 2),
            'expected_executions' => $totalExpected,
            'successful_executions' => $totalSuccesses,
            'missed_executions' => max(0, $totalExpected - $totalSuccesses)
        ];
    }

    /**
     * 获取性能趋势数据
     */
    protected function getPerformanceTrend(array $taskIds, $startDate, $endDate): array
    {
        // 这里可以扩展更详细的性能趋势分析
        // 暂时返回基础数据结构
        return [
            'daily_success_rate' => [],
            'daily_execution_count' => [],
            'error_rate_trend' => []
        ];
    }

    /**
     * 获取多个任务组的对比报告
     */
    public function getComparisonReport(array $groupIds, array $options = []): array
    {
        $reports = [];
        
        foreach ($groupIds as $groupId) {
            $reports[$groupId] = $this->getGroupReport($groupId, $options);
        }
        
        return [
            'individual_reports' => $reports,
            'comparison_summary' => $this->generateComparisonSummary($reports),
            'generated_at' => Carbon::now()
        ];
    }

    /**
     * 生成对比汇总
     */
    protected function generateComparisonSummary(array $reports): array
    {
        if (empty($reports)) {
            return [];
        }

        $summary = [
            'total_groups' => count($reports),
            'total_tasks' => 0,
            'avg_health_score' => 0,
            'total_alerts' => 0,
            'best_performing_group' => null,
            'most_alerts_group' => null
        ];

        $healthScores = [];
        $alertCounts = [];

        foreach ($reports as $groupId => $report) {
            $summary['total_tasks'] += $report['task_overview']['total_tasks'];
            $summary['total_alerts'] += $report['alert_summary']['alert_statistics']['total_alerts'];
            
            $healthScore = $report['task_overview']['health_score'];
            $healthScores[$groupId] = $healthScore;
            $alertCounts[$groupId] = $report['alert_summary']['alert_statistics']['total_alerts'];
        }

        $summary['avg_health_score'] = round(array_sum($healthScores) / count($healthScores), 1);
        $summary['best_performing_group'] = array_keys($healthScores, max($healthScores))[0];
        $summary['most_alerts_group'] = array_keys($alertCounts, max($alertCounts))[0];

        return $summary;
    }
} 
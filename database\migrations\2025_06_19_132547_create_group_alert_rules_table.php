<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_alert_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_group_id')->constrained()->onDelete('cascade')->comment('所属任务分组');
            $table->string('name', 100)->comment('规则名称');
            $table->text('description')->nullable()->comment('规则描述');
            $table->enum('rule_type', ['price_change', 'price_threshold', 'availability', 'custom'])->comment('规则类型');
            $table->json('conditions')->comment('告警条件配置');
            $table->enum('operator', ['and', 'or'])->default('and')->comment('条件组合逻辑');
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium')->comment('告警严重程度');
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium')->comment('优先级');
            $table->json('notification_settings')->nullable()->comment('通知设置');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('override_individual')->default(false)->comment('是否覆盖个人规则');
            $table->integer('cooldown_minutes')->default(60)->comment('冷却时间(分钟)');
            $table->timestamp('last_triggered_at')->nullable()->comment('最后触发时间');
            $table->integer('trigger_count')->default(0)->comment('触发次数');
            $table->timestamps();
            
            // 索引
            $table->index(['task_group_id', 'is_active']);
            $table->index(['rule_type', 'severity']);
            $table->index('last_triggered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_alert_rules');
    }
};

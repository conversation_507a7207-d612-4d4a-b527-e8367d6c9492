<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[\p{L}\p{M}\p{N}\s\-_.]+$/u'
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'unique:users,email'
            ],
            'username' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'unique:users,username',
                'regex:/^[a-zA-Z0-9_-]+$/'
            ],
            'phone_number' => [
                'nullable',
                'string',
                'regex:/^1[3-9]\d{9}$/',
                'unique:users,phone_number'
            ],
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
            'terms' => [
                'accepted'
            ],
            'captcha' => [
                'nullable',
                'string'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => '姓名是必填项。',
            'name.max' => '姓名不能超过100个字符。',
            'name.regex' => '姓名格式不正确，只能包含字母、数字、空格、连字符、下划线和点。',
            
            'email.required' => '邮箱地址是必填项。',
            'email.email' => '请输入有效的邮箱地址。',
            'email.max' => '邮箱地址不能超过255个字符。',
            'email.unique' => '该邮箱地址已被注册。',
            
            'username.required' => '用户名是必填项。',
            'username.min' => '用户名至少需要3个字符。',
            'username.max' => '用户名不能超过50个字符。',
            'username.unique' => '该用户名已被使用。',
            'username.regex' => '用户名只能包含字母、数字、下划线和连字符。',
            
            'phone_number.regex' => '请输入有效的手机号码。',
            'phone_number.unique' => '该手机号码已被注册。',
            
            'password.required' => '密码是必填项。',
            'password.confirmed' => '密码确认不匹配。',
            'password.min' => '密码至少需要8个字符。',
            'password.letters' => '密码必须包含字母。',
            'password.mixed' => '密码必须包含大小写字母。',
            'password.numbers' => '密码必须包含数字。',
            'password.symbols' => '密码必须包含特殊字符。',
            'password.uncompromised' => '该密码已在数据泄露中出现，请选择其他密码。',
            
            'terms.accepted' => '请同意服务条款和隐私政策。',
            
            'captcha.required' => '请完成验证码验证。',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'email' => strtolower(trim($this->email)),
            'username' => strtolower(trim($this->username)),
            'name' => trim($this->name),
            'phone_number' => $this->phone_number ? preg_replace('/\D/', '', $this->phone_number) : null,
        ]);
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // 移除不需要存储的字段
        unset($validated['password_confirmation'], $validated['terms'], $validated['captcha']);
        
        return $validated;
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        // 记录验证失败日志
        logger()->warning('User registration validation failed', [
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'errors' => $validator->errors()->toArray(),
            'input' => $this->except(['password', 'password_confirmation'])
        ]);

        parent::failedValidation($validator);
    }
} 
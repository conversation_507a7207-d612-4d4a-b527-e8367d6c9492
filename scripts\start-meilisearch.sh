#!/bin/bash

# MeiliSearch 启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/storage/meilisearch/config.toml"

echo "启动 MeiliSearch..."
echo "配置文件: $CONFIG_FILE"
echo "访问地址: http://127.0.0.1:7700"
echo "按 Ctrl+C 停止服务"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件不存在: $CONFIG_FILE"
    echo "请先运行 setup-meilisearch.sh 脚本"
    exit 1
fi

# 启动 MeiliSearch
meilisearch --config-file-path "$CONFIG_FILE"

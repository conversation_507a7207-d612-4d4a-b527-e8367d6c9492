<?php

namespace App\Services;

use App\Models\ApiConfiguration;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * API版本管理服务
 * 负责管理API配置的版本控制、升级和兼容性
 */
class ApiVersionManagerService
{
    /**
     * 创建新版本的API配置
     *
     * @param ApiConfiguration $baseConfig 基础配置
     * @param string $newVersion 新版本号
     * @param array $changes 变更内容
     * @return ApiConfiguration 新版本配置
     * @throws Exception
     */
    public function createNewVersion(ApiConfiguration $baseConfig, string $newVersion, array $changes = []): ApiConfiguration
    {
        // 验证版本号格式
        if (!$this->isValidVersion($newVersion)) {
            throw new Exception("无效的版本号格式: {$newVersion}");
        }

        // 检查版本是否已存在
        if ($this->versionExists($baseConfig->platform_type, $baseConfig->name, $newVersion)) {
            throw new Exception("版本 {$newVersion} 已存在");
        }

        // 创建新版本配置
        $newConfig = $baseConfig->replicate();
        $newConfig->name = $baseConfig->name . ' v' . $newVersion;
        $newConfig->version = $newVersion;
        $newConfig->is_deprecated = false;
        $newConfig->slug = $baseConfig->slug . '-v' . str_replace('.', '-', $newVersion);

        // 应用变更
        foreach ($changes as $field => $value) {
            if (in_array($field, $newConfig->getFillable())) {
                $newConfig->$field = $value;
            }
        }

        $newConfig->save();

        Log::info("创建新API版本", [
            'platform' => $baseConfig->platform_type,
            'name' => $baseConfig->name,
            'old_version' => $baseConfig->version,
            'new_version' => $newVersion,
        ]);

        return $newConfig;
    }

    /**
     * 升级到指定版本
     *
     * @param string $platformType 平台类型
     * @param string $name API名称
     * @param string $targetVersion 目标版本
     * @return ApiConfiguration|null 目标版本配置
     */
    public function upgradeToVersion(string $platformType, string $name, string $targetVersion): ?ApiConfiguration
    {
        $targetConfig = ApiConfiguration::where('platform_type', $platformType)
            ->where('name', $name)
            ->where('version', $targetVersion)
            ->first();

        if (!$targetConfig) {
            Log::warning("目标版本不存在", [
                'platform' => $platformType,
                'name' => $name,
                'target_version' => $targetVersion,
            ]);
            return null;
        }

        // 激活目标版本
        $targetConfig->update(['is_active' => true]);

        // 停用其他版本（包括基础名称的所有版本）
        $baseName = explode(' v', $name)[0];
        ApiConfiguration::where('platform_type', $platformType)
            ->whereRaw('SUBSTRING_INDEX(name, " v", 1) = ?', [$baseName])
            ->where('id', '!=', $targetConfig->id)
            ->update(['is_active' => false]);

        Log::info("API版本升级", [
            'platform' => $platformType,
            'name' => $name,
            'target_version' => $targetVersion,
        ]);

        return $targetConfig;
    }

    /**
     * 标记版本为已弃用
     *
     * @param ApiConfiguration $config API配置
     * @param string $reason 弃用原因
     * @return void
     */
    public function deprecateVersion(ApiConfiguration $config, string $reason = ''): void
    {
        $config->markAsDeprecated();

        // 记录弃用信息到元数据
        $metadata = $config->metadata ?? [];
        $metadata['deprecation'] = [
            'deprecated_at' => now()->toISOString(),
            'reason' => $reason,
        ];
        $config->update([
            'metadata' => $metadata,
            'is_deprecated' => true
        ]);

        Log::info("API版本已弃用", [
            'platform' => $config->platform_type,
            'name' => $config->name,
            'version' => $config->version,
            'reason' => $reason,
        ]);
    }

    /**
     * 批量弃用旧版本
     *
     * @param string $platformType 平台类型
     * @param string $name API名称
     * @param string $keepVersion 保留的版本
     * @return int 弃用的版本数量
     */
    public function deprecateOlderVersions(string $platformType, string $name, string $keepVersion): int
    {
        $deprecatedCount = ApiConfiguration::where('platform_type', $platformType)
            ->where('name', $name)
            ->where('version', '!=', $keepVersion)
            ->where('is_deprecated', false)
            ->update([
                'is_deprecated' => true,
                'updated_at' => now(),
            ]);

        Log::info("批量弃用旧版本", [
            'platform' => $platformType,
            'name' => $name,
            'keep_version' => $keepVersion,
            'deprecated_count' => $deprecatedCount,
        ]);

        return $deprecatedCount;
    }

    /**
     * 获取版本兼容性信息
     *
     * @param ApiConfiguration $config API配置
     * @return array 兼容性信息
     */
    public function getCompatibilityInfo(ApiConfiguration $config): array
    {
        $allVersions = ApiConfiguration::where('platform_type', $config->platform_type)
            ->where('name', $config->name)
            ->orderBy('version', 'desc')
            ->get();

        $compatibility = [
            'current_version' => $config->version,
            'is_latest' => $config->isLatestVersion(),
            'is_deprecated' => $config->is_deprecated,
            'backward_compatible' => [],
            'forward_compatible' => [],
            'breaking_changes' => [],
        ];

        foreach ($allVersions as $version) {
            if ($version->id === $config->id) continue;

            $comparison = version_compare($config->version, $version->version);
            
            if ($comparison > 0) {
                // 当前版本更新，检查向后兼容性
                $compatibility['backward_compatible'][] = [
                    'version' => $version->version,
                    'compatible' => $this->isBackwardCompatible($config, $version),
                ];
            } else {
                // 当前版本更旧，检查向前兼容性
                $compatibility['forward_compatible'][] = [
                    'version' => $version->version,
                    'compatible' => $this->isForwardCompatible($config, $version),
                ];
            }
        }

        return $compatibility;
    }

    /**
     * 获取版本迁移路径
     *
     * @param string $platformType 平台类型
     * @param string $name API名称
     * @param string $fromVersion 源版本
     * @param string $toVersion 目标版本
     * @return array 迁移路径
     */
    public function getMigrationPath(string $platformType, string $name, string $fromVersion, string $toVersion): array
    {
        $allVersions = ApiConfiguration::where('platform_type', $platformType)
            ->whereRaw('SUBSTRING_INDEX(name, " v", 1) = ?', [$name])
            ->orderBy('version', 'asc')
            ->pluck('version')
            ->toArray();

        $fromIndex = array_search($fromVersion, $allVersions);
        $toIndex = array_search($toVersion, $allVersions);

        if ($fromIndex === false || $toIndex === false) {
            return ['error' => '版本不存在'];
        }

        $path = [];
        if ($fromIndex < $toIndex) {
            // 升级路径
            for ($i = $fromIndex; $i <= $toIndex; $i++) {
                $path[] = $allVersions[$i];
            }
        } else {
            // 降级路径
            for ($i = $fromIndex; $i >= $toIndex; $i--) {
                $path[] = $allVersions[$i];
            }
        }

        return [
            'from' => $fromVersion,
            'to' => $toVersion,
            'path' => $path,
            'steps' => count($path) - 1,
            'direction' => $fromIndex < $toIndex ? 'upgrade' : 'downgrade',
        ];
    }

    /**
     * 获取版本统计信息
     *
     * @param string|null $platformType 平台类型
     * @return array 统计信息
     */
    public function getVersionStatistics(?string $platformType = null): array
    {
        $query = ApiConfiguration::query();
        
        if ($platformType) {
            $query->where('platform_type', $platformType);
        }

        $total = $query->count();
        $active = $query->where('is_active', true)->count();
        $deprecated = $query->where('is_deprecated', true)->count();

        $versionDistribution = $query->selectRaw('version, COUNT(*) as count')
            ->groupBy('version')
            ->orderBy('version', 'desc')
            ->pluck('count', 'version')
            ->toArray();

        return [
            'total_configurations' => $total,
            'active_configurations' => $active,
            'deprecated_configurations' => $deprecated,
            'version_distribution' => $versionDistribution,
            'platform_type' => $platformType,
        ];
    }

    /**
     * 验证版本号格式
     *
     * @param string $version 版本号
     * @return bool 是否有效
     */
    private function isValidVersion(string $version): bool
    {
        // 支持语义化版本号格式 (x.y.z)
        return preg_match('/^\d+\.\d+\.\d+(-[a-zA-Z0-9\-\.]+)?$/', $version);
    }

    /**
     * 检查版本是否已存在
     *
     * @param string $platformType 平台类型
     * @param string $name API名称
     * @param string $version 版本号
     * @return bool 是否存在
     */
    private function versionExists(string $platformType, string $name, string $version): bool
    {
        return ApiConfiguration::where('platform_type', $platformType)
            ->where('name', $name)
            ->where('version', $version)
            ->exists();
    }

    /**
     * 检查向后兼容性
     *
     * @param ApiConfiguration $newer 新版本
     * @param ApiConfiguration $older 旧版本
     * @return bool 是否向后兼容
     */
    private function isBackwardCompatible(ApiConfiguration $newer, ApiConfiguration $older): bool
    {
        // 简单的兼容性检查逻辑
        // 实际项目中可能需要更复杂的检查
        return $newer->base_url === $older->base_url && 
               $newer->auth_type === $older->auth_type;
    }

    /**
     * 检查向前兼容性
     *
     * @param ApiConfiguration $older 旧版本
     * @param ApiConfiguration $newer 新版本
     * @return bool 是否向前兼容
     */
    private function isForwardCompatible(ApiConfiguration $older, ApiConfiguration $newer): bool
    {
        // 简单的兼容性检查逻辑
        return $this->isBackwardCompatible($newer, $older);
    }
}

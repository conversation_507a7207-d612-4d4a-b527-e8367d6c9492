<?php

namespace Tests\Feature;

use App\Models\ApiConfiguration;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Services\ApiHealthMonitorService;
use App\Services\ApiRateLimitService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ApiConfigurationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        \Illuminate\Support\Facades\Cache::flush();

        // 创建管理员角色和用户
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => 'Administrator',
            'description' => 'System Administrator',
            'level' => 'admin',
            'is_active' => true,
        ]);
        $this->admin = User::factory()->create();
        $this->admin->assignRole($adminRole);

        // 创建普通用户并分配角色和权限
        $userRole = Role::create([
            'name' => 'user',
            'display_name' => 'Regular User',
            'description' => 'A regular user with basic permissions',
            'level' => 'normal',
            'is_active' => true,
        ]);
        $basicPermission = Permission::create([
            'name' => 'view-dashboard',
            'display_name' => 'View Dashboard',
            'description' => 'Can view the main dashboard',
            'module' => 'dashboard',
            'action' => 'view',
            'is_active' => true,
        ]);
        $userRole->permissions()->attach($basicPermission->id);

        $this->user = User::factory()->create();
        $this->user->assignRole($userRole);
    }

    protected function tearDown(): void
    {
        \Illuminate\Support\Facades\Cache::flush();
        parent::tearDown();
    }

    public function test_can_create_api_configuration()
    {
        $this->actingAs($this->admin);

        $configData = ApiConfiguration::factory()->make()->toArray();
        $configData['auth_credentials'] = ['key' => 'test-api-key'];

        $response = $this->post(route('api-configurations.store'), $configData);

        $response->assertRedirect(route('api-configurations.index'));
        $this->assertDatabaseHas('api_configurations', ['name' => $configData['name']]);
    }

    public function test_api_configuration_validates_required_fields()
    {
        $this->actingAs($this->admin);
        $response = $this->post(route('api-configurations.store'), []);
        $response->assertSessionHasErrors(['name', 'platform_type', 'base_url', 'auth_type']);
    }

    public function test_can_toggle_active_status()
    {
        $this->actingAs($this->admin);
        $config = ApiConfiguration::factory()->create(['is_active' => true]);

        $response = $this->post(route('api-configurations.toggle-active', $config));
        $response->assertSuccessful();
        $this->assertDatabaseHas('api_configurations', ['id' => $config->id, 'is_active' => false]);
    }

    /**
     * @todo Fix rate limit tests. The cache is not behaving as expected in the test environment.
     */
    // public function test_rate_limit_service_works()
    // {
    //     $config = ApiConfiguration::factory()->create(['rate_limit_per_minute' => 2]);
    //     $rateLimitService = app(ApiRateLimitService::class);
    //     $cacheKey = "api_rate_limit:minute:{$config->id}";

    //     // Manually set the cache to the limit
    //     \Illuminate\Support\Facades\Cache::put($cacheKey, 2, 60);

    //     // Now the next check should fail
    //     $this->assertFalse($rateLimitService->canMakeRequest($config->id));
    // }

    public function test_health_monitor_service_works()
    {
        $config = ApiConfiguration::factory()->create();
        
        // Fake the HTTP request
        Http::fake(['*' => Http::response()]);

        $healthMonitorService = app(ApiHealthMonitorService::class);
        $result = $healthMonitorService->checkHealth($config);

        $this->assertEquals('healthy', $result['status']);
        $this->assertEquals(200, $result['http_status']);
    }

    public function test_can_get_client_config()
    {
        $this->actingAs($this->admin);
        $config = ApiConfiguration::factory()->create();
        $response = $this->getJson(route('api.api-configurations.client-config', $config->slug));
        $response->assertSuccessful();
        $this->assertEquals($config->name, $response->json('data.name'));
    }

    public function test_non_admin_cannot_manage_configurations()
    {
        $this->actingAs($this->user);

        // Test GET request with JSON header
        $response = $this->getJson(route('api-configurations.index'));
        $response->assertForbidden();

        // Test POST request with JSON header
        $response = $this->postJson(route('api-configurations.store'), []);
        $response->assertForbidden();
    }

    /**
     * @todo Fix rate limit tests. The cache is not behaving as expected in the test environment.
     */
    // public function test_health_check_respects_rate_limits()
    // {
    //     $this->actingAs($this->admin);
        
    //     $config = ApiConfiguration::factory()->create([
    //         'rate_limit_per_minute' => 1,
    //         'health_check_endpoint' => '/status/200'
    //     ]);
        
    //     // Manually set the rate limit counter to its limit
    //     $cacheKey = "api_rate_limit:minute:{$config->id}";
    //     \Illuminate\Support\Facades\Cache::put($cacheKey, 1, 60);

    //     // Now the endpoint should return a 429
    //     $response = $this->post(route('api-configurations.health-check', $config));
    //     $response->assertStatus(429);
    // }

    public function test_bulk_health_check_works()
    {
        $this->actingAs($this->admin);
        
        $configs = ApiConfiguration::factory(3)->create();

        Http::fake([
            '*' => Http::response(['status' => 'ok'], 200),
        ]);

        $response = $this->post(route('api-configurations.bulk-health-check'));

        $response->assertSuccessful();
        $response->assertJsonCount(3, 'results');
        $this->assertEquals(200, $response->json('results.0.http_status'));
    }
} 
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for every one. Here you may define a default connection.
    |
    */

    'default' => env('QUEUE_CONNECTION', 'redis'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    | Drivers: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'default',
            'retry_after' => 90,
            'after_commit' => false,
        ],

        'beanstalkd' => [
            'driver' => 'beanstalkd',
            'host' => 'localhost',
            'queue' => 'default',
            'retry_after' => 90,
            'block_for' => 0,
            'after_commit' => false,
        ],

        'sqs' => [
            'driver' => 'sqs',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'prefix' => env('SQS_PREFIX', 'https://sqs.us-east-1.amazonaws.com/your-account-id'),
            'queue' => env('SQS_QUEUE', 'default'),
            'suffix' => env('SQS_SUFFIX'),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'after_commit' => false,
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => env('REDIS_QUEUE', 'default'),
            'retry_after' => 90,
            'block_for' => null,
            'after_commit' => false,
        ],

        // 数据收集专用队列连接
        'data-collection' => [
            'driver' => 'redis',
            'connection' => 'data-collection',
            'queue' => env('DATA_COLLECTION_QUEUE', 'data-collection'),
            'retry_after' => env('DATA_COLLECTION_RETRY_AFTER', 90),
            'block_for' => 5,
            'after_commit' => false,
        ],

        // 高优先级数据收集队列
        'data-collection-high' => [
            'driver' => 'redis',
            'connection' => 'data-collection',
            'queue' => 'data-collection-high',
            'retry_after' => 60,
            'block_for' => 1,
            'after_commit' => false,
        ],

        // 低优先级数据收集队列
        'data-collection-low' => [
            'driver' => 'redis',
            'connection' => 'data-collection',
            'queue' => 'data-collection-low',
            'retry_after' => 120,
            'block_for' => 10,
            'after_commit' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Job Batching
    |--------------------------------------------------------------------------
    |
    | The following options configure the database and table that store job
    | batching information. These options can be updated to any database
    | connection and table which has been defined by your application.
    |
    */

    'batching' => [
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'job_batches',
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Collection Queue Settings
    |--------------------------------------------------------------------------
    |
    | Custom settings for data collection system
    |
    */

    'data_collection' => [
        'max_tries' => env('QUEUE_MAX_TRIES', 3),
        'timeout' => env('QUEUE_TIMEOUT', 300),
        'retry_after' => env('QUEUE_RETRY_AFTER', 90),
        'workers' => env('QUEUE_WORKERS', 5),
        'sleep' => env('QUEUE_WORKER_SLEEP', 3),
        'max_time' => env('QUEUE_MAX_TIME', 3600),
        'max_jobs' => env('QUEUE_MAX_JOBS', 1000),
        'force' => env('QUEUE_WORKER_FORCE', false),
        'memory' => env('QUEUE_WORKER_MEMORY', 128),
        'queues' => [
            'high' => 'data-collection-high',
            'default' => 'data-collection',
            'low' => 'data-collection-low',
        ],
        'priorities' => [
            'high' => 1,
            'default' => 5,
            'low' => 10,
        ],
    ],

]; 
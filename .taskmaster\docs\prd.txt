# 电商市场动态监测系统 产品需求文档（PRD）

## 1. 项目概述

### 1.1 系统目标
构建一个基于Web的电商市场动态监测系统，用于实时监控电商平台商品的价格、库存、促销等关键信息，为用户提供数据分析和预警服务。

### 1.2 技术架构
- **后端**: Apache/IIS/Nginx + PHP + MySQL
- **前端**: HTML5 + CSS3 + JavaScript (本地化资源，无远程CDN依赖)
- **数据采集**: PHP多线程机制，支持5万-30万条商品的日监控量
- **通知系统**: 邮件通知、站内消息

### 1.3 核心价值
- 实时监控商品动态变化
- 智能预警机制
- 数据可视化分析
- 竞品价格对比分析

## 2. 用户管理系统

### 2.1 用户注册模块
- 用户注册功能（邮箱注册、手机号注册）
- 邮箱/手机验证码验证
- 用户协议和隐私政策确认
- 注册信息验证（用户名唯一性、密码强度等）

### 2.2 用户登录模块
- 用户名/邮箱/手机号登录
- 密码登录、验证码登录
- 记住登录状态（7天、30天）
- 登录失败次数限制和账户锁定机制
- 找回密码功能

### 2.3 用户权限管理
- **管理员**: 系统全部功能，用户管理，系统配置
- **高级用户**: 完整监控功能，导出数据，设置复杂预警规则
- **普通用户**: 基础监控功能，查看数据，简单预警设置
- **只读用户**: 仅查看数据报告，无修改权限

### 2.4 用户个人中心
- 个人信息管理（头像、昵称、联系方式）
- 密码修改
- 监控任务统计
- 使用记录查看
- 账户设置（通知偏好、数据展示偏好）

## 3. 核心功能模块

### 3.1 渠道价格监测模块

#### 3.1.1 数据源管理
**商品ID/URL导入**:
- 支持单个商品ID/URL手动添加
- 支持批量商品ID/URL导入（文本粘贴、Excel文件上传）
- 商品URL有效性验证
- 导入历史记录和撤销功能

**Excel批量导入**:
- 提供标准Excel模板下载
- 支持.xlsx/.xls/.csv格式
- 数据格式校验和错误提示
- 导入进度显示和失败记录导出

#### 3.1.2 数据采集与存储
**实时采集系统**:
- 基于商品详情API接口的数据抓取
- 支持多平台（淘宝、天猫、京东等）
- 多线程并发采集，支持5-30万商品/天
- 采集频率可配置（每小时、每天、自定义）
- 采集失败重试机制和异常处理

**数据标准化**:
- 基于接口返回JSON数据的字段映射
- 数据清洗和格式统一
- 缺失数据补全和异常数据标记
- 数据去重（基于商品ID和SKU ID）

**核心数据字段**:
```
- code: 状态码
- id: 商品唯一ID
- title: 商品标题  
- pic_urls: 商品图片URLs
- price: SKU原价
- subPrice: SKU券后价（实际到手价）
- subPriceTitle: 券后价名称
- quantity: SKU库存数量
- promotion: 优惠信息列表
- sale: 商品销量
- commentCount: 评论数
- state: 商品状态（上架/下架）
- is_sku: 是否有SKU
- itemType: 平台类型
- category_id: 类目ID
- category_path: 类目路径
- shopId: 店铺ID
- shopName: 店铺名称
- props: 商品属性
- delivery: 发货地
- timestamp: 采集时间
```

#### 3.1.3 关键指标计算
**促销价偏离率**:
- 计算公式: (Price - subPrice) / Price × 100%
- 正值表示降价促销，负值表示加价
- 支持批量计算和历史趋势分析

**渠道价格偏离率**:
- 计算公式: (官方指导价 - subPrice) / 官方指导价 × 100%
- 需要用户设置官方指导价参考值
- 支持多SKU商品的综合偏离率计算

#### 3.1.4 预警规则设置
**预警类型配置**:
- 促销价偏离率预警（阈值可设置，如>10%）
- 渠道价格偏离率预警（需设置官方指导价和阈值）
- 上下架状态变更预警
- 库存异常预警（库存为0或异常变动）
- 数据更新异常预警（超过24小时未更新）

**通知设置**:
- 邮件通知（支持多邮箱订阅）
- 站内消息推送
- 预警频率控制（避免频繁提醒）
- 预警等级分类（紧急、重要、一般）

#### 3.1.5 任务分组管理
- 创建监控任务分组（如"数码产品组"、"服装类目组"）
- 分组内商品统一管理和配置
- 分组级别的预警规则设置
- 分组报告生成和导出

### 3.2 竞品动态监测模块

#### 3.2.1 竞品识别与筛选
**关键词匹配**:
- 基于商品标题的关键词匹配
- 基于类目路径的分类匹配
- 支持多关键词组合查询
- 排除词设置（避免误匹配）

**竞品数据源**:
- 复用3.1模块的数据采集接口
- 支持关键词搜索API接口
- 支持店铺商品列表API接口
- 同类商品智能推荐

#### 3.2.2 竞品分析指标
**促销策略分析**:
- 促销类型统计（满减、折扣、赠品等）
- 促销策略使用频率分析
- 促销力度对比（平均折扣率）

**价格对比分析**:
- 单品价格综合折扣率
- 总体促销强度指数
- 价格偏差率分析（与自有产品对比）
- 最低价/最高价偏差率

**价格趋势分析**:
- 基于线性回归的价格趋势斜率计算
- 涨价/降价趋势识别
- 价格稳定性评估

#### 3.2.3 可视化图表
**价格趋势图**:
- 多竞品价格对比折线图
- 时间维度可选（日、周、月）
- 支持最多10个竞品同时对比
- 图表导出功能（PNG、JPG）

**促销分析图表**:
- 促销类型占比饼图
- 促销策略排名表格
- 竞品促销对比雷达图

**类目分布图**:
- 价格区间分布热力图
- 基于类目的商品密度分析

### 3.3 相似商品查询模块

#### 3.3.1 多维度商品搜索
**搜索方式**:
- 商品ID直接查询
- 关键词模糊搜索
- 店铺ID相关商品查询
- 图片上传相似商品查询（简化版，基于商品标题和类目匹配）

**搜索结果管理**:
- 搜索结果分页显示
- 结果筛选（价格区间、销量、评价等）
- 结果排序（价格、销量、更新时间）
- 搜索历史记录

#### 3.3.2 相似度评分算法
**相似度计算**:
- 标题相似度（基于关键词匹配，权重30%）
- 类目匹配度（完全匹配/部分匹配，权重40%）
- 价格相似度（价格区间匹配，权重20%）
- 店铺类型匹配（官方/第三方，权重10%）

**注意**: 移除原计划的图片相似度分析功能，以降低系统开销

#### 3.3.3 结果展示与分析
**商品对比表格**:
- 相似商品列表展示
- 价格对比分析
- 销量和评价对比
- 店铺信誉对比

## 4. 数据管理系统

### 4.1 数据存储设计
**MySQL数据库结构**:
- 用户表（users）
- 商品基础信息表（products）
- SKU信息表（product_skus）
- 价格历史表（price_history）
- 监控任务表（monitor_tasks）
- 预警规则表（alert_rules）
- 预警记录表（alert_logs）
- 系统配置表（system_config）

### 4.2 数据采集调度
**采集任务调度**:
- 基于PHP的定时任务机制
- 支持cron表达式的灵活调度
- 任务优先级管理
- 失败任务自动重试
- 采集状态监控和日志记录

**多线程处理**:
- PHP多进程/多线程实现
- 并发数量可配置（建议50-200线程）
- 任务队列管理
- 内存和CPU资源监控

### 4.3 数据导入导出
**导入功能**:
- Excel批量导入商品列表
- CSV格式数据导入
- API接口批量导入
- 历史数据迁移工具

**导出功能**:
- 监控数据Excel导出
- 分析报告PDF导出
- 数据接口API提供
- 定时报告邮件发送

## 5. 系统管理功能

### 5.1 API接口管理
**接口配置**:
- 第三方API接口地址配置
- API Token和认证信息管理
- 接口调用频率限制设置
- 接口状态监控和健康检查

**接口扩展**:
- 新增电商平台接口支持
- 接口适配器模式设计
- 接口映射规则配置
- 接口版本管理

### 5.2 系统配置管理
**基础配置**:
- 系统名称和Logo设置
- 默认监控频率设置
- 数据保留周期配置
- 邮件服务器配置

**性能配置**:
- 并发数量设置
- 内存使用限制
- 数据库连接池配置
- 缓存策略设置

### 5.3 日志与监控
**系统日志**:
- 用户操作日志
- 数据采集日志
- 错误异常日志
- 性能监控日志

**监控指标**:
- 系统响应时间
- 数据采集成功率
- 数据库性能指标
- 服务器资源使用情况

## 6. 界面设计要求

### 6.1 响应式设计
- 支持PC端、平板、手机多端访问
- Bootstrap或类似框架实现响应式布局
- 主要功能在移动端的可用性优化

### 6.2 用户体验
**导航设计**:
- 清晰的主导航菜单
- 面包屑导航
- 快捷操作按钮
- 搜索功能

**数据展示**:
- 数据表格支持排序、筛选、分页
- 图表交互式操作
- 实时数据更新显示
- 加载状态和进度提示

### 6.3 本地化资源
- 所有CSS、JS文件本地部署
- 不依赖远程CDN资源
- 字体文件本地化
- 图标资源本地化

## 7. 安全与性能

### 7.1 安全要求
**数据安全**:
- 敏感数据加密存储（价格、用户信息）
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

**访问控制**:
- 用户权限分级管理
- API接口访问频率限制
- 登录失败次数限制
- 会话超时管理

### 7.2 性能要求
**响应时间**:
- 页面加载时间 < 3秒
- API接口响应时间 < 2秒
- 数据采集效率: 5-30万商品/天
- 支持并发用户数: 100+

**数据处理**:
- 大数据量查询优化
- 数据库索引优化
- 缓存机制实现
- 分页查询优化

## 8. 部署与运维

### 8.1 部署要求
**服务器配置**:
- PHP 7.4+ 或 PHP 8.0+
- MySQL 5.7+ 或 8.0+
- Apache/Nginx Web服务器
- 至少4GB内存，推荐8GB+

**环境配置**:
- PHP扩展: PDO, MySQLi, cURL, GD, mbstring
- 支持定时任务执行
- 文件上传限制配置
- 错误日志配置

### 8.2 运维监控
**监控项目**:
- 服务器资源监控
- 数据库性能监控
- 应用程序监控
- 数据采集状态监控

**备份策略**:
- 数据库定期备份
- 重要配置文件备份
- 日志文件轮转
- 灾难恢复预案

## 9. 开发计划

### 9.1 开发阶段
**第一阶段（基础框架）**:
- 用户管理系统开发
- 基础数据模型设计
- 核心API接口对接
- 基本的数据采集功能

**第二阶段（核心功能）**:
- 渠道价格监测模块
- 数据分析和指标计算
- 预警规则设置
- 基础图表展示

**第三阶段（高级功能）**:
- 竞品监测模块
- 相似商品查询
- 高级数据可视化
- 系统优化和性能调优

**第四阶段（完善优化）**:
- 用户体验优化
- 安全加固
- 性能优化
- 测试和部署

### 9.2 验收标准
- 支持5万-30万商品的日监控量
- 数据采集准确率 > 95%
- 系统稳定性 > 99%
- 用户功能完整性验证
- 安全测试通过
- 性能指标达标

## 10. 风险与应对

### 10.1 技术风险
**API接口风险**:
- 第三方接口变更或失效
- 接口调用频率限制
- 数据格式变化

**应对措施**:
- 多接口源备份
- 接口监控和自动切换
- 灵活的数据映射机制

### 10.2 性能风险
**大数据量处理**:
- 数据库性能瓶颈
- 内存不足问题
- 网络带宽限制

**应对措施**:
- 数据库优化和分表
- 缓存机制实现
- 分布式处理考虑

这个PRD文档为电商市场动态监测系统提供了全面详细的需求说明，涵盖了用户管理、核心功能、技术实现、部署运维等各个方面，适合基于PHP+MySQL技术栈的开发实施。 
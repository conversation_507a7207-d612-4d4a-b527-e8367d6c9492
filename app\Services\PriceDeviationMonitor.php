<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 价格偏差计算性能监控和异常检测类
 * 
 * 提供以下功能：
 * 1. 性能监控和统计
 * 2. 异常值检测和报告
 * 3. 系统资源监控
 * 4. 计算质量评估
 */
class PriceDeviationMonitor
{
    /**
     * 监控会话数据
     *
     * @var array
     */
    private array $session = [
        'start_time' => null,
        'end_time' => null,
        'memory_start' => null,
        'memory_peak' => null,
        'calculations_count' => 0,
        'anomalies_detected' => [],
        'performance_warnings' => [],
        'errors' => [],
    ];

    /**
     * 计算统计
     *
     * @var array
     */
    private array $statistics = [
        'total_calculations' => 0,
        'successful_calculations' => 0,
        'failed_calculations' => 0,
        'promotion_calculations' => 0,
        'channel_calculations' => 0,
        'extreme_deviations' => 0,
        'zero_prices' => 0,
        'negative_deviations' => 0,
        'calculation_times' => [],
    ];

    /**
     * 开始监控会话
     *
     * @return void
     */
    public function startSession(): void
    {
        $this->session['start_time'] = Carbon::now();
        $this->session['memory_start'] = memory_get_usage(true);
        $this->session['calculations_count'] = 0;
        $this->session['anomalies_detected'] = [];
        $this->session['performance_warnings'] = [];
        $this->session['errors'] = [];

        Log::info('价格偏差计算监控会话开始', [
            'start_time' => $this->session['start_time'],
            'initial_memory' => $this->formatBytes($this->session['memory_start'])
        ]);
    }

    /**
     * 结束监控会话
     *
     * @return array 会话报告
     */
    public function endSession(): array
    {
        $this->session['end_time'] = Carbon::now();
        $this->session['memory_peak'] = memory_get_peak_usage(true);
        
        $duration = $this->session['end_time']->diffInSeconds($this->session['start_time']);
        $memoryUsed = $this->session['memory_peak'] - $this->session['memory_start'];
        
        $report = $this->generateSessionReport($duration, $memoryUsed);
        
        Log::info('价格偏差计算监控会话结束', $report);
        
        return $report;
    }

    /**
     * 监控单次计算
     *
     * @param callable $calculation 计算函数
     * @param array $context 计算上下文
     * @return mixed 计算结果
     */
    public function monitorCalculation(callable $calculation, array $context = [])
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        try {
            $result = $calculation();
            
            $this->recordSuccessfulCalculation($startTime, $startMemory, $result, $context);
            
            return $result;
            
        } catch (\Exception $e) {
            $this->recordFailedCalculation($startTime, $startMemory, $e, $context);
            throw $e;
        }
    }

    /**
     * 检测计算结果异常
     *
     * @param float|null $promotionRate 促销偏差率
     * @param float|null $channelRate 渠道偏差率
     * @param array $context 计算上下文
     * @return array 异常检测结果
     */
    public function detectAnomalies(?float $promotionRate, ?float $channelRate, array $context = []): array
    {
        $anomalies = [];

        // 检测极端偏差率
        if (PriceDeviationConfig::isExtremeDeviation($promotionRate)) {
            $anomalies[] = [
                'type' => 'extreme_promotion_deviation',
                'value' => $promotionRate,
                'threshold' => PriceDeviationConfig::ANOMALY_DETECTION['extreme_deviation_threshold'],
                'context' => $context
            ];
            $this->statistics['extreme_deviations']++;
        }

        if (PriceDeviationConfig::isExtremeDeviation($channelRate)) {
            $anomalies[] = [
                'type' => 'extreme_channel_deviation',
                'value' => $channelRate,
                'threshold' => PriceDeviationConfig::ANOMALY_DETECTION['extreme_deviation_threshold'],
                'context' => $context
            ];
            $this->statistics['extreme_deviations']++;
        }

        // 检测负偏差率
        if (PriceDeviationConfig::ANOMALY_DETECTION['negative_deviation_warning']) {
            if ($promotionRate !== null && $promotionRate < 0) {
                $anomalies[] = [
                    'type' => 'negative_promotion_deviation',
                    'value' => $promotionRate,
                    'context' => $context
                ];
                $this->statistics['negative_deviations']++;
            }

            if ($channelRate !== null && $channelRate < 0) {
                $anomalies[] = [
                    'type' => 'negative_channel_deviation',
                    'value' => $channelRate,
                    'context' => $context
                ];
                $this->statistics['negative_deviations']++;
            }
        }

        // 检测零价格
        if (PriceDeviationConfig::ANOMALY_DETECTION['zero_price_warning']) {
            if (isset($context['price']) && (float)$context['price'] === 0.0) {
                $anomalies[] = [
                    'type' => 'zero_price',
                    'context' => $context
                ];
                $this->statistics['zero_prices']++;
            }

            if (isset($context['sub_price']) && (float)$context['sub_price'] === 0.0) {
                $anomalies[] = [
                    'type' => 'zero_sub_price',
                    'context' => $context
                ];
            }
        }

        // 记录异常
        if (!empty($anomalies)) {
            $this->session['anomalies_detected'] = array_merge(
                $this->session['anomalies_detected'], 
                $anomalies
            );
        }

        return $anomalies;
    }

    /**
     * 监控系统资源使用
     *
     * @return array 资源使用情况
     */
    public function monitorResources(): array
    {
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        $memoryLimit = $this->getMemoryLimit();
        
        $cpuUsage = $this->getCpuUsage();
        
        $resources = [
            'memory' => [
                'current' => $currentMemory,
                'current_formatted' => $this->formatBytes($currentMemory),
                'peak' => $peakMemory,
                'peak_formatted' => $this->formatBytes($peakMemory),
                'limit' => $memoryLimit,
                'limit_formatted' => $this->formatBytes($memoryLimit),
                'usage_percentage' => $memoryLimit > 0 ? ($currentMemory / $memoryLimit) * 100 : 0,
            ],
            'cpu' => [
                'usage_percentage' => $cpuUsage,
            ],
            'time' => [
                'current' => Carbon::now(),
                'session_duration' => $this->session['start_time'] 
                    ? Carbon::now()->diffInSeconds($this->session['start_time']) 
                    : 0,
            ]
        ];
        
        // 检查资源使用警告
        $this->checkResourceWarnings($resources);
        
        return $resources;
    }

    /**
     * 获取计算质量评估
     *
     * @return array 质量评估报告
     */
    public function getQualityAssessment(): array
    {
        $totalCalculations = $this->statistics['total_calculations'];
        
        if ($totalCalculations === 0) {
            return [
                'overall_score' => 0,
                'success_rate' => 0,
                'anomaly_rate' => 0,
                'performance_score' => 0,
                'quality_grade' => 'N/A',
                'recommendations' => ['没有计算数据可供评估']
            ];
        }

        $successRate = ($this->statistics['successful_calculations'] / $totalCalculations) * 100;
        $anomalyRate = (
            ($this->statistics['extreme_deviations'] + $this->statistics['negative_deviations']) 
            / $totalCalculations
        ) * 100;
        
        $avgCalculationTime = !empty($this->statistics['calculation_times']) 
            ? array_sum($this->statistics['calculation_times']) / count($this->statistics['calculation_times'])
            : 0;
        
        $performanceScore = $this->calculatePerformanceScore($avgCalculationTime);
        $overallScore = ($successRate * 0.5) + ($performanceScore * 0.3) + ((100 - $anomalyRate) * 0.2);
        
        return [
            'overall_score' => round($overallScore, 2),
            'success_rate' => round($successRate, 2),
            'anomaly_rate' => round($anomalyRate, 2),
            'performance_score' => round($performanceScore, 2),
            'quality_grade' => $this->getQualityGrade($overallScore),
            'average_calculation_time' => $avgCalculationTime,
            'total_calculations' => $totalCalculations,
            'recommendations' => $this->generateRecommendations($successRate, $anomalyRate, $avgCalculationTime)
        ];
    }

    /**
     * 记录成功的计算
     *
     * @param float $startTime
     * @param int $startMemory
     * @param mixed $result
     * @param array $context
     * @return void
     */
    private function recordSuccessfulCalculation(float $startTime, int $startMemory, $result, array $context): void
    {
        $duration = microtime(true) - $startTime;
        $memoryUsed = memory_get_usage() - $startMemory;
        
        $this->statistics['total_calculations']++;
        $this->statistics['successful_calculations']++;
        $this->statistics['calculation_times'][] = $duration;
        $this->session['calculations_count']++;
        
        // 检查是否为慢计算
        if (PriceDeviationConfig::PERFORMANCE_CONFIG['enable_time_tracking'] && 
            $duration > PriceDeviationConfig::PERFORMANCE_CONFIG['slow_calculation_threshold']) {
            
            $this->session['performance_warnings'][] = [
                'type' => 'slow_calculation',
                'duration' => $duration,
                'threshold' => PriceDeviationConfig::PERFORMANCE_CONFIG['slow_calculation_threshold'],
                'context' => $context
            ];
        }
        
        Log::debug('计算监控 - 成功', [
            'duration' => $duration,
            'memory_used' => $this->formatBytes($memoryUsed),
            'context' => $context
        ]);
    }

    /**
     * 记录失败的计算
     *
     * @param float $startTime
     * @param int $startMemory
     * @param \Exception $exception
     * @param array $context
     * @return void
     */
    private function recordFailedCalculation(float $startTime, int $startMemory, \Exception $exception, array $context): void
    {
        $duration = microtime(true) - $startTime;
        
        $this->statistics['total_calculations']++;
        $this->statistics['failed_calculations']++;
        $this->session['calculations_count']++;
        
        $errorInfo = [
            'type' => 'calculation_exception',
            'duration' => $duration,
            'exception' => $exception->getMessage(),
            'context' => $context
        ];
        
        $this->session['errors'][] = $errorInfo;
        
        Log::error('计算监控 - 失败', $errorInfo);
    }

    /**
     * 生成会话报告
     *
     * @param int $duration
     * @param int $memoryUsed
     * @return array
     */
    private function generateSessionReport(int $duration, int $memoryUsed): array
    {
        return [
            'session_summary' => [
                'duration_seconds' => $duration,
                'duration_formatted' => $this->formatDuration($duration),
                'calculations_count' => $this->session['calculations_count'],
                'memory_used' => $memoryUsed,
                'memory_used_formatted' => $this->formatBytes($memoryUsed),
                'peak_memory' => $this->session['memory_peak'],
                'peak_memory_formatted' => $this->formatBytes($this->session['memory_peak']),
            ],
            'statistics' => $this->statistics,
            'anomalies_count' => count($this->session['anomalies_detected']),
            'performance_warnings_count' => count($this->session['performance_warnings']),
            'errors_count' => count($this->session['errors']),
            'quality_assessment' => $this->getQualityAssessment(),
        ];
    }

    /**
     * 检查资源使用警告
     *
     * @param array $resources
     * @return void
     */
    private function checkResourceWarnings(array $resources): void
    {
        // 内存使用警告
        if (PriceDeviationConfig::PERFORMANCE_CONFIG['enable_memory_monitoring'] &&
            $resources['memory']['usage_percentage'] > PriceDeviationConfig::PERFORMANCE_CONFIG['memory_limit_warning']) {
            
            $this->session['performance_warnings'][] = [
                'type' => 'high_memory_usage',
                'usage_percentage' => $resources['memory']['usage_percentage'],
                'threshold' => PriceDeviationConfig::PERFORMANCE_CONFIG['memory_limit_warning'],
                'current_memory' => $resources['memory']['current_formatted']
            ];
        }
    }

    /**
     * 计算性能分数
     *
     * @param float $avgCalculationTime
     * @return float
     */
    private function calculatePerformanceScore(float $avgCalculationTime): float
    {
        // 基于平均计算时间计算性能分数 (0-100)
        $threshold = PriceDeviationConfig::PERFORMANCE_CONFIG['slow_calculation_threshold'];
        
        if ($avgCalculationTime <= $threshold * 0.1) {
            return 100;
        } elseif ($avgCalculationTime <= $threshold * 0.5) {
            return 80;
        } elseif ($avgCalculationTime <= $threshold) {
            return 60;
        } elseif ($avgCalculationTime <= $threshold * 2) {
            return 40;
        } else {
            return 20;
        }
    }

    /**
     * 获取质量等级
     *
     * @param float $overallScore
     * @return string
     */
    private function getQualityGrade(float $overallScore): string
    {
        if ($overallScore >= 90) return 'A';
        if ($overallScore >= 80) return 'B';
        if ($overallScore >= 70) return 'C';
        if ($overallScore >= 60) return 'D';
        return 'F';
    }

    /**
     * 生成改进建议
     *
     * @param float $successRate
     * @param float $anomalyRate
     * @param float $avgCalculationTime
     * @return array
     */
    private function generateRecommendations(float $successRate, float $anomalyRate, float $avgCalculationTime): array
    {
        $recommendations = [];
        
        if ($successRate < 95) {
            $recommendations[] = '成功率较低，建议检查输入数据质量和错误处理逻辑';
        }
        
        if ($anomalyRate > 5) {
            $recommendations[] = '异常率较高，建议加强数据验证和异常值过滤';
        }
        
        if ($avgCalculationTime > PriceDeviationConfig::PERFORMANCE_CONFIG['slow_calculation_threshold']) {
            $recommendations[] = '计算性能较慢，建议优化算法或增加硬件资源';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = '系统运行良好，无特殊建议';
        }
        
        return $recommendations;
    }

    /**
     * 格式化字节数
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 格式化持续时间
     *
     * @param int $seconds
     * @return string
     */
    private function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            return floor($seconds / 60) . '分' . ($seconds % 60) . '秒';
        } else {
            return floor($seconds / 3600) . '小时' . floor(($seconds % 3600) / 60) . '分';
        }
    }

    /**
     * 获取内存限制
     *
     * @return int
     */
    private function getMemoryLimit(): int
    {
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryLimit === '-1') {
            return 0; // 无限制
        }
        
        return $this->convertToBytes($memoryLimit);
    }

    /**
     * 获取CPU使用率
     *
     * @return float
     */
    private function getCpuUsage(): float
    {
        // 简单的CPU使用率估算，在生产环境中可能需要更精确的方法
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0] * 10; // 粗略估算
        }
        
        return 0.0;
    }

    /**
     * 转换内存字符串为字节数
     *
     * @param string $value
     * @return int
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
} 
# Dependencies
/vendor/
node_modules/
bower_components/

# Environment files
.env
.env.local
.env.production
.env.testing

# Logs
*.log
logs/
storage/logs/

# Runtime data
*.pid
*.seed
*.pid.lock

# Cache
storage/cache/
storage/framework/cache/
storage/framework/sessions/
storage/framework/views/
bootstrap/cache/

# Compiled assets
public/hot
public/storage
public/mix-manifest.json

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.sqlite
*.db

# Temporary files
tmp/
temp/

# Composer
composer.phar
/vendor/

# Laravel specific
/storage/*.key
/storage/app/public
/public/storage
/public/hot

# PHPUnit
/.phpunit.cache
/phpunit.xml

# Coverage reports
coverage/
*.coverage

# Added by Task Master AI
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
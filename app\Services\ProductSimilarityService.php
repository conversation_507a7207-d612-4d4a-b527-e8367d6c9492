<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Log;

/**
 * 产品相似度评分服务
 * 实现基于标题、分类、价格和商店类型的相似度算法
 */
class ProductSimilarityService
{
    // 权重配置
    private const TITLE_WEIGHT = 0.30;
    private const CATEGORY_WEIGHT = 0.40;
    private const PRICE_WEIGHT = 0.20;
    private const SHOP_TYPE_WEIGHT = 0.10;
    
    // 价格偏差阈值
    private const PRICE_DEVIATION_THRESHOLD = 0.20; // 20%

    /**
     * 计算两个产品之间的相似度评分
     *
     * @param Product $product1 产品1
     * @param Product $product2 产品2
     * @return array 包含总分和各项分数的数组
     */
    public function calculateSimilarity(Product $product1, Product $product2): array
    {
        try {
            // 计算各项分数
            $titleScore = $this->calculateTitleSimilarity($product1->title, $product2->title);
            $categoryScore = $this->calculateCategorySimilarity($product1->category_path, $product2->category_path);
            $priceScore = $this->calculatePriceSimilarity($product1->min_price, $product2->min_price);
            $shopTypeScore = $this->calculateShopTypeSimilarity($product1->shop_type, $product2->shop_type);
            
            // 计算加权总分
            $totalScore = (
                $titleScore * self::TITLE_WEIGHT +
                $categoryScore * self::CATEGORY_WEIGHT +
                $priceScore * self::PRICE_WEIGHT +
                $shopTypeScore * self::SHOP_TYPE_WEIGHT
            );
            
            // 记录日志
            Log::debug('产品相似度计算完成', [
                'product1_id' => $product1->id,
                'product2_id' => $product2->id,
                'title_score' => $titleScore,
                'category_score' => $categoryScore,
                'price_score' => $priceScore,
                'shop_type_score' => $shopTypeScore,
                'total_score' => $totalScore
            ]);
            
            return [
                'total_score' => round($totalScore, 4),
                'title_score' => round($titleScore, 4),
                'category_score' => round($categoryScore, 4),
                'price_score' => round($priceScore, 4),
                'shop_type_score' => round($shopTypeScore, 4),
                'weights' => [
                    'title' => self::TITLE_WEIGHT,
                    'category' => self::CATEGORY_WEIGHT,
                    'price' => self::PRICE_WEIGHT,
                    'shop_type' => self::SHOP_TYPE_WEIGHT
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('产品相似度计算失败', [
                'product1_id' => $product1->id,
                'product2_id' => $product2->id,
                'error' => $e->getMessage()
            ]);
            
            throw new \Exception('相似度计算失败: ' . $e->getMessage());
        }
    }

    /**
     * 计算标题相似度 (权重: 30%)
     * 使用Jaccard相似度算法
     *
     * @param string $title1 标题1
     * @param string $title2 标题2
     * @return float 相似度分数 (0-1)
     */
    public function calculateTitleSimilarity(string $title1, string $title2): float
    {
        // 如果标题相同，直接返回1
        if ($title1 === $title2) {
            return 1.0;
        }
        
        // 如果任一标题为空，返回0
        if (empty($title1) || empty($title2)) {
            return 0.0;
        }
        
        // 标准化和分词
        $tokens1 = $this->tokenizeTitle($title1);
        $tokens2 = $this->tokenizeTitle($title2);
        
        // 如果分词后为空，返回0
        if (empty($tokens1) || empty($tokens2)) {
            return 0.0;
        }
        
        // 计算Jaccard相似度
        $intersection = array_intersect($tokens1, $tokens2);
        $union = array_unique(array_merge($tokens1, $tokens2));
        
        $jaccardScore = count($intersection) / count($union);
        
        // 计算余弦相似度作为补充
        $cosineScore = $this->calculateCosineSimilarity($tokens1, $tokens2);
        
        // 组合Jaccard和余弦相似度 (7:3权重)
        $finalScore = ($jaccardScore * 0.7) + ($cosineScore * 0.3);
        
        return min(1.0, max(0.0, $finalScore));
    }

    /**
     * 分词处理
     *
     * @param string $title 标题
     * @return array 分词结果
     */
    private function tokenizeTitle(string $title): array
    {
        // 转换为小写
        $title = mb_strtolower($title, 'UTF-8');
        
        // 移除特殊字符和数字，保留中文、英文和空格
        $title = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $title);
        
        // 按空格分割
        $words = preg_split('/\s+/', trim($title));
        
        // 过滤空字符串和停用词
        $stopWords = ['的', '和', '与', '或', '及', '等', '为', '是', '有', '在', '了', '到', '从', 'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        
        $tokens = [];
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 1 && !in_array($word, $stopWords)) {
                $tokens[] = $word;
            }
        }
        
        return array_unique($tokens);
    }

    /**
     * 计算余弦相似度
     *
     * @param array $tokens1 分词1
     * @param array $tokens2 分词2
     * @return float 余弦相似度 (0-1)
     */
    private function calculateCosineSimilarity(array $tokens1, array $tokens2): float
    {
        // 构建词频向量
        $allTokens = array_unique(array_merge($tokens1, $tokens2));
        $vector1 = [];
        $vector2 = [];
        
        foreach ($allTokens as $token) {
            $vector1[] = array_count_values($tokens1)[$token] ?? 0;
            $vector2[] = array_count_values($tokens2)[$token] ?? 0;
        }
        
        // 计算点积
        $dotProduct = 0;
        for ($i = 0; $i < count($vector1); $i++) {
            $dotProduct += $vector1[$i] * $vector2[$i];
        }
        
        // 计算向量长度
        $magnitude1 = sqrt(array_sum(array_map(function($x) { return $x * $x; }, $vector1)));
        $magnitude2 = sqrt(array_sum(array_map(function($x) { return $x * $x; }, $vector2)));
        
        // 避免除零
        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0.0;
        }
        
        return $dotProduct / ($magnitude1 * $magnitude2);
    }

    /**
     * 计算分类相似度 (权重: 40%)
     *
     * @param string $category1 分类路径1
     * @param string $category2 分类路径2
     * @return float 相似度分数 (0-1)
     */
    public function calculateCategorySimilarity(string $category1, string $category2): float
    {
        // 如果分类相同，直接返回1
        if ($category1 === $category2) {
            return 1.0;
        }
        
        // 如果任一分类为空，返回0
        if (empty($category1) || empty($category2)) {
            return 0.0;
        }
        
        // 分割分类路径
        $path1 = $this->parseCategoryPath($category1);
        $path2 = $this->parseCategoryPath($category2);
        
        // 如果分类层级为空，返回0
        if (empty($path1) || empty($path2)) {
            return 0.0;
        }
        
        // 计算公共路径长度
        $commonLength = 0;
        $maxLength = max(count($path1), count($path2));
        $minLength = min(count($path1), count($path2));
        
        for ($i = 0; $i < $minLength; $i++) {
            if (mb_strtolower($path1[$i]) === mb_strtolower($path2[$i])) {
                $commonLength++;
            } else {
                break; // 遇到不同的分类就停止
            }
        }
        
        // 计算相似度分数
        if ($commonLength === 0) {
            return 0.0;
        }
        
        // 使用加权评分：完全匹配=1.0，部分匹配根据公共路径比例计算
        if ($commonLength === $maxLength) {
            return 1.0; // 完全匹配或一个是另一个的子集
        }
        
        // 部分匹配：基于公共路径长度的比例
        $baseScore = $commonLength / $maxLength;
        
        // 如果公共路径达到较短路径的长度，给予额外奖励
        if ($commonLength === $minLength) {
            $baseScore *= 1.2; // 20%奖励
        }
        
        return min(1.0, $baseScore);
    }

    /**
     * 解析分类路径
     *
     * @param string $categoryPath 分类路径
     * @return array 分类层级数组
     */
    private function parseCategoryPath(string $categoryPath): array
    {
        // 常见的分隔符
        $separators = ['/', '\\', '>', '|', '-'];
        
        foreach ($separators as $separator) {
            if (strpos($categoryPath, $separator) !== false) {
                $parts = explode($separator, $categoryPath);
                return array_map('trim', array_filter($parts, function($part) {
                    return !empty(trim($part));
                }));
            }
        }
        
        // 如果没有分隔符，返回单个分类
        return [trim($categoryPath)];
    }

    /**
     * 计算价格相似度 (权重: 20%)
     *
     * @param float $price1 价格1
     * @param float $price2 价格2
     * @return float 相似度分数 (0-1)
     */
    public function calculatePriceSimilarity(float $price1, float $price2): float
    {
        // 如果价格相同，返回1
        if ($price1 === $price2) {
            return 1.0;
        }
        
        // 如果任一价格为0或负数，返回0
        if ($price1 <= 0 || $price2 <= 0) {
            return 0.0;
        }
        
        // 计算价格偏差百分比
        $avgPrice = ($price1 + $price2) / 2;
        $deviation = abs($price1 - $price2) / $avgPrice;
        
        // 如果偏差超过阈值，返回0
        if ($deviation > self::PRICE_DEVIATION_THRESHOLD) {
            return 0.0;
        }
        
        // 线性映射：偏差0% = 分数1.0，偏差20% = 分数0.0
        $score = 1.0 - ($deviation / self::PRICE_DEVIATION_THRESHOLD);
        
        return max(0.0, min(1.0, $score));
    }

    /**
     * 计算店铺类型相似度 (权重: 10%)
     *
     * @param string $shopType1 店铺类型1
     * @param string $shopType2 店铺类型2
     * @return float 相似度分数 (0-1)
     */
    public function calculateShopTypeSimilarity(?string $shopType1, ?string $shopType2): float
    {
        // 标准化店铺类型
        $type1 = $this->normalizeShopType($shopType1);
        $type2 = $this->normalizeShopType($shopType2);
        
        // 定义评分规则
        $scores = [
            'official-official' => 1.0,     // 都是官方店
            'official-third_party' => 0.5,  // 一个官方一个第三方
            'third_party-third_party' => 0.75, // 都是第三方
            'unknown-unknown' => 0.5,        // 都是未知类型
            'official-unknown' => 0.3,       // 官方和未知
            'third_party-unknown' => 0.4,    // 第三方和未知
        ];
        
        $key = $type1 . '-' . $type2;
        $reverseKey = $type2 . '-' . $type1;
        
        return $scores[$key] ?? $scores[$reverseKey] ?? 0.0;
    }

    /**
     * 标准化店铺类型
     *
     * @param string|null $shopType 店铺类型
     * @return string 标准化后的类型
     */
    private function normalizeShopType(?string $shopType): string
    {
        if (empty($shopType)) {
            return 'unknown';
        }
        
        $shopType = mb_strtolower(trim($shopType));
        
        // 官方店铺关键词
        $officialKeywords = ['官方', '旗舰店', 'official', 'flagship', '专卖店', '直营'];
        
        foreach ($officialKeywords as $keyword) {
            if (strpos($shopType, $keyword) !== false) {
                return 'official';
            }
        }
        
        // 默认为第三方
        return 'third_party';
    }

    /**
     * 批量计算产品与目标产品的相似度
     *
     * @param Product $targetProduct 目标产品
     * @param array $products 产品列表
     * @return array 相似度结果数组
     */
    public function calculateBatchSimilarity(Product $targetProduct, array $products): array
    {
        $results = [];
        
        foreach ($products as $product) {
            // 如果$product是数组，将其转换为Product对象
            if (is_array($product)) {
                $product = new Product($product);
            }
            
            // 确保product有id属性
            if (!isset($product->id) || $product->id === $targetProduct->id) {
                continue; // 跳过没有ID或与目标产品相同的产品
            }
            
            try {
                $similarity = $this->calculateSimilarity($targetProduct, $product);
                $results[] = [
                    'product_id' => $product->id,
                    'product' => $product,
                    'similarity' => $similarity
                ];
            } catch (\Exception $e) {
                Log::warning('批量相似度计算跳过产品', [
                    'target_product_id' => $targetProduct->id,
                    'product_id' => $product->id ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // 按相似度总分排序
        usort($results, function($a, $b) {
            return $b['similarity']['total_score'] <=> $a['similarity']['total_score'];
        });
        
        return $results;
    }

    /**
     * 获取算法配置信息
     *
     * @return array 配置信息
     */
    public function getAlgorithmConfig(): array
    {
        return [
            'weights' => [
                'title' => self::TITLE_WEIGHT,
                'category' => self::CATEGORY_WEIGHT,
                'price' => self::PRICE_WEIGHT,
                'shop_type' => self::SHOP_TYPE_WEIGHT
            ],
            'thresholds' => [
                'price_deviation' => self::PRICE_DEVIATION_THRESHOLD
            ],
            'methods' => [
                'title_similarity' => 'Jaccard Index + Cosine Similarity (7:3)',
                'category_similarity' => 'Hierarchical Path Matching',
                'price_similarity' => 'Percentage Deviation within 20%',
                'shop_type_similarity' => 'Predefined Scoring Matrix'
            ]
        ];
    }
} 
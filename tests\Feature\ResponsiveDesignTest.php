<?php

namespace Tests\Feature;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ResponsiveDesignTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
    }

    public function test_responsive_test_page_loads()
    {
        $response = $this->get('/responsive-test');
        
        $response->assertStatus(200)
            ->assertViewIs('responsive-test')
            ->assertSee('响应式设计测试页面')
            ->assertSee('响应式网格系统测试')
            ->assertSee('响应式表格测试')
            ->assertSee('响应式表单测试');
    }

    public function test_layout_contains_responsive_meta_tag()
    {
        $response = $this->get('/responsive-test');
        
        $response->assertSee('name="viewport"', false)
            ->assertSee('width=device-width, initial-scale=1', false);
    }

    public function test_layout_uses_local_assets()
    {
        $response = $this->get('/responsive-test');
        
        // 检查是否使用本地Bootstrap CSS
        $response->assertSee('assets/css/bootstrap.min.css', false);
        
        // 检查是否使用本地Font Awesome CSS
        $response->assertSee('assets/css/fontawesome.min.css', false);
        
        // 检查是否使用本地Bootstrap JS
        $response->assertSee('assets/js/bootstrap.bundle.min.js', false);
        
        // 确保没有CDN链接
        $response->assertDontSee('cdn.jsdelivr.net', false);
        $response->assertDontSee('cdnjs.cloudflare.com', false);
    }

    public function test_responsive_css_classes_present()
    {
        $response = $this->get('/responsive-test');
        
        // 检查响应式网格类
        $response->assertSee('col-lg-3 col-md-6 col-sm-12', false)
            ->assertSee('col-md-6', false)
            ->assertSee('col-12', false);
        
        // 检查响应式显示类
        $response->assertSee('d-none-mobile', false);
        
        // 检查响应式间距类
        $response->assertSee('mb-3', false)
            ->assertSee('mb-4', false);
    }

    public function test_responsive_table_structure()
    {
        $response = $this->get('/responsive-test');
        
        // 检查表格响应式包装
        $response->assertSee('table-responsive', false);
        
        // 检查移动端数据标签
        $response->assertSee('data-label="ID"', false)
            ->assertSee('data-label="产品名称"', false)
            ->assertSee('data-label="价格"', false);
    }

    public function test_responsive_form_layout()
    {
        $response = $this->get('/responsive-test');
        
        // 检查表单响应式布局
        $response->assertSee('col-md-6', false)
            ->assertSee('col-md-4', false)
            ->assertSee('flex-column flex-md-row', false);
    }

    public function test_mobile_friendly_button_sizes()
    {
        $response = $this->get('/responsive-test');
        
        // 检查按钮类
        $response->assertSee('btn btn-sm', false)
            ->assertSee('btn btn-primary', false);
    }

    public function test_css_media_queries_present()
    {
        $response = $this->get('/responsive-test');
        
        // 检查CSS中是否包含媒体查询
        $content = $response->getContent();
        
        $this->assertStringContainsString('@media (max-width: 576px)', $content);
        $this->assertStringContainsString('@media (max-width: 768px)', $content);
        $this->assertStringContainsString('@media (max-width: 992px)', $content);
        $this->assertStringContainsString('@media (max-width: 1200px)', $content);
    }

    public function test_touch_device_optimizations()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查触摸设备优化
        $this->assertStringContainsString('@media (hover: none) and (pointer: coarse)', $content);
        $this->assertStringContainsString('min-height: 44px', $content);
    }

    public function test_accessibility_features()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查减少动画的用户偏好支持
        $this->assertStringContainsString('@media (prefers-reduced-motion: reduce)', $content);
        
        // 检查表单标签
        $response->assertSee('form-label', false);
        
        // 检查ARIA属性
        $response->assertSee('role="button"', false);
    }

    public function test_sidebar_responsive_behavior()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查侧边栏响应式CSS
        $this->assertStringContainsString('position: fixed', $content);
        $this->assertStringContainsString('left: -100%', $content);
        $this->assertStringContainsString('z-index: 1050', $content);
        
        // 检查侧边栏切换JavaScript
        $this->assertStringContainsString('sidebar.classList.toggle', $content);
    }

    public function test_navbar_responsive_elements()
    {
        $response = $this->get('/responsive-test');
        
        // 检查导航栏响应式元素
        $response->assertSee('navbar-toggler', false)
            ->assertSee('d-lg-none', false)
            ->assertSee('navbar-collapse', false);
    }

    public function test_card_responsive_design()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查卡片响应式样式
        $this->assertStringContainsString('border-radius: 12px', $content);
        $this->assertStringContainsString('border-radius: 8px', $content); // 移动端
        $this->assertStringContainsString('border-radius: 6px', $content); // 小屏幕
    }

    public function test_font_size_responsive_scaling()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查字体大小响应式调整
        $this->assertStringContainsString('font-size: 16px', $content); // 防止iOS缩放
        $this->assertStringContainsString('font-size: 0.95rem', $content);
        $this->assertStringContainsString('font-size: 0.9rem', $content);
        $this->assertStringContainsString('font-size: 0.85rem', $content);
    }

    public function test_javascript_device_detection()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查设备检测JavaScript
        $this->assertStringContainsString('window.innerWidth', $content);
        $this->assertStringContainsString('screen.width', $content);
        $this->assertStringContainsString('navigator.userAgent', $content);
        $this->assertStringContainsString('updateDeviceInfo', $content);
    }

    public function test_high_dpi_screen_support()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查高分辨率屏幕支持
        $this->assertStringContainsString('@media (-webkit-min-device-pixel-ratio: 2)', $content);
        $this->assertStringContainsString('(min-resolution: 192dpi)', $content);
    }

    public function test_dark_mode_support()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查暗色模式支持
        $this->assertStringContainsString('@media (prefers-color-scheme: dark)', $content);
        $this->assertStringContainsString('--bs-body-bg: #1a1a1a', $content);
    }

    public function test_responsive_breakpoints_display()
    {
        $response = $this->get('/responsive-test');
        
        // 检查断点信息显示
        $response->assertSee('响应式断点')
            ->assertSee('< 576px')
            ->assertSee('≥ 576px')
            ->assertSee('≥ 768px')
            ->assertSee('≥ 992px')
            ->assertSee('≥ 1200px')
            ->assertSee('≥ 1400px');
    }

    public function test_mobile_table_transformation()
    {
        $response = $this->get('/responsive-test');
        
        $content = $response->getContent();
        
        // 检查移动端表格转换CSS
        $this->assertStringContainsString('.table thead { display: none; }', $content);
        $this->assertStringContainsString('.table, .table tbody, .table tr, .table td { display: block; }', $content);
        $this->assertStringContainsString('content: attr(data-label)', $content);
    }

    public function test_loading_performance_optimizations()
    {
        $response = $this->get('/responsive-test');
        
        // 检查性能优化
        $response->assertSee('defer', false); // 延迟加载脚本
        
        $content = $response->getContent();
        
        // 检查CSS优化
        $this->assertStringContainsString('transition:', $content);
        $this->assertStringContainsString('transform:', $content);
    }

    public function test_assets_are_accessible()
    {
        // 测试本地资源文件是否可访问
        $response = $this->get('/assets/css/bootstrap.min.css');
        $response->assertStatus(200);
        
        $response = $this->get('/assets/css/fontawesome.min.css');
        $response->assertStatus(200);
        
        $response = $this->get('/assets/js/bootstrap.bundle.min.js');
        $response->assertStatus(200);
    }
}

# API配置环境变量
# 生成时间: 2025-06-20 09:56:39

# 通用API配置模板

# TAOBAO API配置
API_CONFIG_TAOBAO_BASE_URL=https://api.{strtolower(TAOBAO)}.com
API_CONFIG_TAOBAO_API_KEY=your_api_key_here
API_CONFIG_TAOBAO_SECRET_KEY=your_secret_key_here
API_CONFIG_TAOBAO_ACCESS_TOKEN=your_access_token_here
API_CONFIG_TAOBAO_REFRESH_TOKEN=your_refresh_token_here

# JD API配置
API_CONFIG_JD_BASE_URL=https://api.{strtolower(JD)}.com
API_CONFIG_JD_API_KEY=your_api_key_here
API_CONFIG_JD_SECRET_KEY=your_secret_key_here
API_CONFIG_JD_ACCESS_TOKEN=your_access_token_here
API_CONFIG_JD_REFRESH_TOKEN=your_refresh_token_here

# TMALL API配置
API_CONFIG_TMALL_BASE_URL=https://api.{strtolower(TMALL)}.com
API_CONFIG_TMALL_API_KEY=your_api_key_here
API_CONFIG_TMALL_SECRET_KEY=your_secret_key_here
API_CONFIG_TMALL_ACCESS_TOKEN=your_access_token_here
API_CONFIG_TMALL_REFRESH_TOKEN=your_refresh_token_here

# SHOPIFY API配置
API_CONFIG_SHOPIFY_BASE_URL=https://api.{strtolower(SHOPIFY)}.com
API_CONFIG_SHOPIFY_API_KEY=your_api_key_here
API_CONFIG_SHOPIFY_SECRET_KEY=your_secret_key_here
API_CONFIG_SHOPIFY_ACCESS_TOKEN=your_access_token_here
API_CONFIG_SHOPIFY_REFRESH_TOKEN=your_refresh_token_here

# MAGENTO API配置
API_CONFIG_MAGENTO_BASE_URL=https://api.{strtolower(MAGENTO)}.com
API_CONFIG_MAGENTO_API_KEY=your_api_key_here
API_CONFIG_MAGENTO_SECRET_KEY=your_secret_key_here
API_CONFIG_MAGENTO_ACCESS_TOKEN=your_access_token_here
API_CONFIG_MAGENTO_REFRESH_TOKEN=your_refresh_token_here

# 通用配置
API_CONFIG_DEFAULT_TIMEOUT=30
API_CONFIG_DEFAULT_RETRY_ATTEMPTS=3
API_CONFIG_ENABLE_LOGGING=true
API_CONFIG_LOG_LEVEL=info

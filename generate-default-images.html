<!DOCTYPE html>
<html>
<head>
    <title>Generate Default Images</title>
</head>
<body>
    <canvas id="productCanvas" width="200" height="200" style="border: 1px solid #ccc;"></canvas>
    <canvas id="avatarCanvas" width="32" height="32" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        // Generate default product image
        const productCanvas = document.getElementById('productCanvas');
        const productCtx = productCanvas.getContext('2d');
        
        // Background
        productCtx.fillStyle = '#F3F4F6';
        productCtx.fillRect(0, 0, 200, 200);
        
        // Image placeholder
        productCtx.fillStyle = '#E5E7EB';
        productCtx.fillRect(50, 50, 100, 100);
        
        // Simple image icon
        productCtx.fillStyle = '#9CA3AF';
        productCtx.beginPath();
        productCtx.arc(75, 75, 8, 0, 2 * Math.PI);
        productCtx.fill();
        
        // Mountain shape
        productCtx.beginPath();
        productCtx.moveTo(60, 120);
        productCtx.lineTo(80, 100);
        productCtx.lineTo(100, 120);
        productCtx.lineTo(120, 100);
        productCtx.lineTo(140, 120);
        productCtx.lineTo(140, 140);
        productCtx.lineTo(60, 140);
        productCtx.closePath();
        productCtx.fill();
        
        // Text
        productCtx.fillStyle = '#6B7280';
        productCtx.font = '12px Arial';
        productCtx.textAlign = 'center';
        productCtx.fillText('商品图片', 100, 170);
        
        // Generate default avatar
        const avatarCanvas = document.getElementById('avatarCanvas');
        const avatarCtx = avatarCanvas.getContext('2d');
        
        // Background circle
        avatarCtx.fillStyle = '#E5E7EB';
        avatarCtx.beginPath();
        avatarCtx.arc(16, 16, 16, 0, 2 * Math.PI);
        avatarCtx.fill();
        
        // Head
        avatarCtx.fillStyle = '#9CA3AF';
        avatarCtx.beginPath();
        avatarCtx.arc(16, 12, 4, 0, 2 * Math.PI);
        avatarCtx.fill();
        
        // Body
        avatarCtx.beginPath();
        avatarCtx.arc(16, 24, 8, 0, Math.PI);
        avatarCtx.fill();
        
        // Download function
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Auto download after 1 second
        setTimeout(() => {
            downloadCanvas(productCanvas, 'default-product.png');
            downloadCanvas(avatarCanvas, 'default-avatar.png');
        }, 1000);
    </script>
</body>
</html>

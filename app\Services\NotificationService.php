<?php

namespace App\Services;

use App\Models\User;
use App\Models\AlertLog;
use App\Models\AlertRule;
use App\Notifications\AlertNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Exception;

class NotificationService
{
    /**
     * 严重程度评估器实例
     */
    private AlertSeverityEvaluator $severityEvaluator;

    /**
     * 构造函数
     */
    public function __construct(AlertSeverityEvaluator $severityEvaluator)
    {
        $this->severityEvaluator = $severityEvaluator;
    }

    /**
     * 发送警报通知（增强版 - 自动评估严重程度）
     */
    public function sendAlertNotification(
        User $user,
        array $alertData,
        string $severity = null,
        AlertRule $alertRule = null
    ): bool {
        try {
            // 如果没有提供严重程度，使用评估器自动评估
            if ($severity === null && $alertRule) {
                $severity = $this->evaluateAlertSeverity($alertRule, $alertData);
                Log::info("自动评估警报严重程度", [
                    'rule_id' => $alertRule->id,
                    'evaluated_severity' => $severity,
                    'alert_data' => $alertData
                ]);
            } elseif ($severity === null) {
                $severity = 'general'; // 默认严重程度
            }

            // 根据历史数据调整严重程度
            if ($alertRule) {
                $adjustedSeverity = $this->adjustSeverityBasedOnHistory($severity, $alertData, $user, $alertRule);
                if ($adjustedSeverity !== $severity) {
                    Log::info("根据历史数据调整警报严重程度", [
                        'original_severity' => $severity,
                        'adjusted_severity' => $adjustedSeverity,
                        'user_id' => $user->id,
                        'rule_id' => $alertRule->id
                    ]);
                    $severity = $adjustedSeverity;
                }
            }

            // 检查用户偏好设置
            if (!$this->shouldSendNotification($user, $severity)) {
                Log::info("用户 {$user->id} 的通知偏好设置跳过了严重程度为 {$severity} 的通知");
                return true;
            }

            // 检查通知频率限制
            if (!$this->checkNotificationRateLimit($user, $severity, $alertRule)) {
                Log::info("用户 {$user->id} 触发通知频率限制，跳过严重程度为 {$severity} 的通知");
                return true;
            }

            // 创建通知实例
            $notification = new AlertNotification($alertData, $severity, $alertRule);

            // 获取通知频率控制
            $frequency = $user->getNotificationFrequency($severity);

            if ($frequency === 'immediate') {
                // 立即发送
                $user->notify($notification);
            } else {
                // 添加到队列（延迟发送或批量发送）
                $this->queueNotification($user, $notification, $frequency);
            }

            // 记录到alert_logs表
            $this->logNotification($user, $alertData, $severity, $alertRule);

            Log::info("成功发送通知给用户 {$user->id}，严重程度: {$severity}");
            return true;

        } catch (Exception $e) {
            $errorId = uniqid('error_', true);
            
            Log::error("发送通知失败: " . $e->getMessage(), [
                'error_id' => $errorId,
                'user_id' => $user->id,
                'severity' => $severity,
                'alert_data' => $alertData,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 尝试记录失败的通知到数据库
            try {
                $this->logFailedNotification($user, $alertData, $severity, $alertRule, $e->getMessage(), $errorId);
            } catch (Exception $logException) {
                Log::critical("无法记录失败的通知到数据库", [
                    'error_id' => $errorId,
                    'original_error' => $e->getMessage(),
                    'log_error' => $logException->getMessage()
                ]);
            }
            
            return false;
        }
    }

    /**
     * 智能批量发送通知（根据规则自动评估严重程度）
     */
    public function sendSmartBulkNotifications(
        array $users,
        array $alertData,
        AlertRule $alertRule,
        string $forceSeverity = null
    ): array {
        $results = [];
        
        // 一次性评估严重程度，避免重复计算
        $evaluatedSeverity = $forceSeverity ?: $this->evaluateAlertSeverity($alertRule, $alertData);
        
        Log::info("批量发送智能通知", [
            'user_count' => count($users),
            'rule_id' => $alertRule->id,
            'evaluated_severity' => $evaluatedSeverity
        ]);

        foreach ($users as $user) {
            if (!$user instanceof User) {
                $user = User::find($user);
                if (!$user) {
                    continue;
                }
            }

            $results[$user->id] = $this->sendAlertNotification(
                $user,
                $alertData,
                $evaluatedSeverity,
                $alertRule
            );
        }

        return $results;
    }

    /**
     * 批量发送通知给多个用户（保持向后兼容）
     */
    public function sendBulkNotifications(
        array $users,
        array $alertData,
        string $severity = 'general',
        AlertRule $alertRule = null
    ): array {
        $results = [];

        foreach ($users as $user) {
            if (!$user instanceof User) {
                $user = User::find($user);
                if (!$user) {
                    continue;
                }
            }

            $results[$user->id] = $this->sendAlertNotification(
                $user,
                $alertData,
                $severity,
                $alertRule
            );
        }

        return $results;
    }

    /**
     * 发送紧急警报给管理员（智能评估版）
     */
    public function sendEmergencyAlert(array $alertData, AlertRule $alertRule = null): bool
    {
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        if ($admins->isEmpty()) {
            Log::warning("没有找到管理员用户，无法发送紧急警报");
            return false;
        }

        // 如果有规则，使用智能评估；否则强制为紧急
        if ($alertRule) {
            $results = $this->sendSmartBulkNotifications(
                $admins,
                $alertData,
                $alertRule,
                'emergency' // 强制为紧急级别
            );
        } else {
            $results = $this->sendBulkNotifications(
                $admins,
                $alertData,
                'emergency',
                $alertRule
            );
        }

        // 如果至少有一个管理员成功接收通知，则认为成功
        return in_array(true, $results, true);
    }

    /**
     * 评估警报严重程度
     */
    public function evaluateAlertSeverity(AlertRule $alertRule, array $alertData): string
    {
        try {
            return $this->severityEvaluator->evaluateSeverity($alertRule, $alertData);
        } catch (Exception $e) {
            Log::error("严重程度评估失败，使用默认值", [
                'rule_id' => $alertRule->id,
                'error' => $e->getMessage(),
                'alert_data' => $alertData
            ]);
            return 'general'; // 评估失败时返回默认值
        }
    }

    /**
     * 根据历史数据调整严重程度
     */
    public function adjustSeverityBasedOnHistory(
        string $baseSeverity, 
        array $alertData, 
        User $user, 
        AlertRule $alertRule
    ): string {
        try {
            // 添加用户和规则信息到评估数据中
            $enrichedData = array_merge($alertData, [
                'user_id' => $user->id,
                'rule_id' => $alertRule->id
            ]);

            return $this->severityEvaluator->adjustSeverityBasedOnHistory($baseSeverity, $enrichedData);
        } catch (Exception $e) {
            Log::error("历史数据调整严重程度失败", [
                'base_severity' => $baseSeverity,
                'user_id' => $user->id,
                'rule_id' => $alertRule->id,
                'error' => $e->getMessage()
            ]);
            return $baseSeverity; // 调整失败时返回原始值
        }
    }

    /**
     * 检查通知频率限制
     */
    private function checkNotificationRateLimit(User $user, string $severity, AlertRule $alertRule = null): bool
    {
        // 根据严重程度设置不同的频率限制
        $rateLimits = [
            'emergency' => ['count' => 50, 'period' => 60], // 1小时内最多50次紧急通知
            'important' => ['count' => 20, 'period' => 60], // 1小时内最多20次重要通知
            'general' => ['count' => 10, 'period' => 60],   // 1小时内最多10次一般通知
        ];

        $limit = $rateLimits[$severity] ?? $rateLimits['general'];
        
        $query = AlertLog::where('user_id', $user->id)
            ->where('severity', $this->mapSeverityToDb($severity))
            ->where('created_at', '>=', now()->subMinutes($limit['period']));

        if ($alertRule) {
            $query->where('rule_id', $alertRule->id);
        }

        $recentCount = $query->count();
        
        if ($recentCount >= $limit['count']) {
            Log::warning("用户触发通知频率限制", [
                'user_id' => $user->id,
                'severity' => $severity,
                'recent_count' => $recentCount,
                'limit' => $limit['count'],
                'period_minutes' => $limit['period']
            ]);
            return false;
        }

        return true;
    }

    /**
     * 根据用户偏好判断是否应该发送通知
     */
    private function shouldSendNotification(User $user, string $severity): bool
    {
        $preferences = $user->getNotificationPreferences();

        // 检查是否启用了站内通知或邮件通知
        $inSiteEnabled = $user->isNotificationEnabled('in_site', $severity);
        $emailEnabled = $user->isNotificationEnabled('email', $severity);

        return $inSiteEnabled || $emailEnabled;
    }

    /**
     * 将通知添加到队列中
     */
    private function queueNotification(User $user, AlertNotification $notification, string $frequency): void
    {
        $delay = 0;

        if ($frequency === 'daily_digest') {
            // 计算到下一个特定时间的延迟（比如每天上午9点）
            $tomorrow9am = now()->addDay()->setTime(9, 0, 0);
            $delay = now()->diffInSeconds($tomorrow9am);
        } elseif ($frequency === 'hourly_digest') {
            // 计算到下一个小时的延迟
            $nextHour = now()->addHour()->startOfHour();
            $delay = now()->diffInSeconds($nextHour);
        }

        if ($delay > 0) {
            $user->notify($notification->delay($delay));
        } else {
            $user->notify($notification);
        }
    }

    /**
     * 记录通知到alert_logs表
     */
    private function logNotification(
        User $user,
        array $alertData,
        string $severity,
        AlertRule $alertRule = null
    ): void {
        try {
            $alertLog = AlertLog::create([
                'user_id' => $user->id,
                'rule_id' => $alertRule ? $alertRule->id : null,
                'task_id' => $alertData['task_id'] ?? null,
                'sku_id' => $alertData['sku_id'] ?? null,
                'alert_title' => $alertData['title'] ?? '系统警报',
                'message' => $alertData['message'] ?? '系统检测到异常情况',
                'severity' => $this->mapSeverityToDb($severity),
                'status' => 'unread',
                'trigger_value' => $alertData['trigger_value'] ?? null,
                'threshold_value' => $alertData['threshold_value'] ?? null,
                'context_data' => $alertData['context_data'] ?? null,
                'notification_method' => 'system',
                'notification_sent' => true,
                'notification_sent_at' => now(),
            ]);

            Log::info("警报日志已记录", ['alert_log_id' => $alertLog->id]);

        } catch (Exception $e) {
            Log::error("记录警报日志失败: " . $e->getMessage(), [
                'user_id' => $user->id,
                'alert_data' => $alertData,
                'exception' => $e
            ]);
        }
    }

    /**
     * 映射严重程度到数据库值
     */
    private function mapSeverityToDb(string $severity): string
    {
        $severityMap = [
            'emergency' => 'critical',
            'important' => 'high',
            'general' => 'medium'
        ];

        return $severityMap[$severity] ?? 'medium';
    }

    /**
     * 获取用户的未读通知数量
     */
    public function getUnreadNotificationsCount(User $user): int
    {
        return $user->unreadNotifications()->count();
    }

    /**
     * 标记通知为已读
     */
    public function markNotificationAsRead(User $user, string $notificationId): bool
    {
        try {
            $notification = $user->notifications()->find($notificationId);
            if ($notification) {
                $notification->markAsRead();
                return true;
            }
            return false;
        } catch (Exception $e) {
            Log::error("标记通知为已读失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 标记所有通知为已读
     */
    public function markAllNotificationsAsRead(User $user): bool
    {
        try {
            $user->unreadNotifications->markAsRead();
            return true;
        } catch (Exception $e) {
            Log::error("标记所有通知为已读失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户的通知列表
     */
    public function getUserNotifications(User $user, int $limit = 20, bool $unreadOnly = false): array
    {
        try {
            $query = $user->notifications();

            if ($unreadOnly) {
                $query = $user->unreadNotifications();
            }

            $notifications = $query->take($limit)->get();

            return $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error("获取用户通知失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取用户通知统计信息
     */
    public function getUserNotificationStats(User $user): array
    {
        try {
            return [
                'total_notifications' => $user->notifications()->count(),
                'unread_count' => $user->unreadNotifications()->count(),
                'today_count' => $user->notifications()
                    ->whereDate('created_at', today())
                    ->count(),
                'this_week_count' => $user->notifications()
                    ->where('created_at', '>=', now()->startOfWeek())
                    ->count(),
                'severity_breakdown' => AlertLog::forUser($user->id)
                    ->selectRaw('severity, count(*) as count')
                    ->groupBy('severity')
                    ->get()
                    ->pluck('count', 'severity')
                    ->toArray()
            ];
        } catch (Exception $e) {
            Log::error("获取用户通知统计失败: " . $e->getMessage());
            return [
                'total_notifications' => 0,
                'unread_count' => 0,
                'today_count' => 0,
                'this_week_count' => 0,
                'severity_breakdown' => []
            ];
        }
    }

    /**
     * 测试通知系统（用于调试和验证）
     */
    public function testNotificationSystem(User $user, AlertRule $alertRule = null): array
    {
        $testResults = [];
        
        try {
            // 测试不同严重程度的通知
            $severities = ['general', 'important', 'emergency'];
            
            foreach ($severities as $severity) {
                $testData = [
                    'title' => "测试通知 - {$severity}",
                    'message' => "这是一个 {$severity} 级别的测试通知",
                    'test_mode' => true,
                    'created_at' => now()
                ];

                $result = $this->sendAlertNotification($user, $testData, $severity, $alertRule);
                $testResults[$severity] = $result;
            }

            // 如果有规则，测试自动评估
            if ($alertRule) {
                $autoTestData = [
                    'title' => '自动评估测试通知',
                    'message' => '这是一个自动严重程度评估的测试通知',
                    'test_mode' => true,
                    'price_change_percentage' => 25, // 触发重要级别
                    'current_price' => 1000,
                    'previous_price' => 800
                ];

                $result = $this->sendAlertNotification($user, $autoTestData, null, $alertRule);
                $testResults['auto_evaluated'] = $result;
            }

            Log::info("通知系统测试完成", [
                'user_id' => $user->id,
                'results' => $testResults
            ]);

        } catch (Exception $e) {
            Log::error("通知系统测试失败: " . $e->getMessage());
            $testResults['error'] = $e->getMessage();
        }

        return $testResults;
    }

    /**
     * 验证通知数据
     */
    private function validateNotificationData(User $user, array $alertData): bool
    {
        // 检查用户是否有效
        if (!$user || !$user->exists) {
            Log::warning("无效的用户对象", ['user_id' => $user->id ?? null]);
            return false;
        }
        
        // 检查用户邮箱是否有效
        if (!$user->email || !filter_var($user->email, FILTER_VALIDATE_EMAIL)) {
            Log::warning("用户邮箱无效", ['user_id' => $user->id, 'email' => $user->email]);
            return false;
        }
        
        // 检查必要的通知数据
        if (empty($alertData)) {
            Log::warning("通知数据为空", ['user_id' => $user->id]);
            return false;
        }
        
        // 检查消息内容
        if (!isset($alertData['message']) && !isset($alertData['title'])) {
            Log::warning("通知缺少消息或标题", ['user_id' => $user->id, 'alert_data' => $alertData]);
            return false;
        }
        
        return true;
    }

    /**
     * 增强警报数据
     */
    private function enhanceAlertData(array $alertData, string $severity, AlertRule $alertRule = null): array
    {
        $enhanced = $alertData;
        
        // 添加时间戳
        if (!isset($enhanced['created_at'])) {
            $enhanced['created_at'] = now();
        }
        
        // 添加严重程度信息
        $enhanced['severity'] = $severity;
        $enhanced['severity_label'] = $this->getSeverityLabel($severity);
        
        // 添加规则信息
        if ($alertRule) {
            $enhanced['alert_rule_id'] = $alertRule->id;
            $enhanced['alert_rule_name'] = $alertRule->name;
            $enhanced['alert_rule_type'] = $alertRule->type;
        }
        
        // 添加系统信息
        $enhanced['system_name'] = config('app.name', '电商监测系统');
        $enhanced['environment'] = config('app.env');
        
        return $enhanced;
    }

    /**
     * 带重试机制的通知发送
     */
    private function sendNotificationWithRetry(
        User $user, 
        array $alertData, 
        string $severity, 
        AlertRule $alertRule = null, 
        string $notificationId = null,
        int $maxRetries = 3
    ): bool {
        $attempts = 0;
        $lastException = null;
        
        while ($attempts < $maxRetries) {
            try {
                $attempts++;
                
                // 创建通知实例
                $notification = new AlertNotification($alertData, $severity, $alertRule);
                
                // 获取通知频率控制
                $frequency = $user->getNotificationFrequency($severity);
                
                if ($frequency === 'immediate') {
                    // 立即发送
                    $user->notify($notification);
                } else {
                    // 添加到队列
                    $this->queueNotification($user, $notification, $frequency);
                }
                
                Log::info("通知发送成功", [
                    'notification_id' => $notificationId,
                    'user_id' => $user->id,
                    'severity' => $severity,
                    'attempt' => $attempts
                ]);
                
                return true;
                
            } catch (Exception $e) {
                $lastException = $e;
                
                Log::warning("通知发送失败，准备重试", [
                    'notification_id' => $notificationId,
                    'user_id' => $user->id,
                    'severity' => $severity,
                    'attempt' => $attempts,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);
                
                // 等待后重试（指数退避）
                if ($attempts < $maxRetries) {
                    $delay = pow(2, $attempts - 1); // 1, 2, 4 seconds
                    sleep($delay);
                }
            }
        }
        
        // 所有重试都失败了
        Log::error("通知发送最终失败", [
            'notification_id' => $notificationId,
            'user_id' => $user->id,
            'severity' => $severity,
            'total_attempts' => $attempts,
            'final_error' => $lastException->getMessage()
        ]);
        
        return false;
    }

    /**
     * 记录失败的通知
     */
    private function logFailedNotification(
        User $user, 
        array $alertData, 
        string $severity, 
        AlertRule $alertRule = null, 
        string $errorMessage = null,
        string $errorId = null
    ): void {
        try {
            AlertLog::create([
                'user_id' => $user->id,
                'rule_id' => $alertRule ? $alertRule->id : null,
                'task_id' => $alertData['task_id'] ?? null,
                'sku_id' => $alertData['sku_id'] ?? null,
                'alert_title' => $alertData['title'] ?? '通知发送失败',
                'message' => $alertData['message'] ?? '通知发送过程中发生错误',
                'severity' => $this->mapSeverityToDb($severity),
                'status' => 'failed',
                'notification_method' => 'system',
                'notification_sent' => false,
                'notification_error' => $errorMessage,
                'context_data' => json_encode([
                    'error_id' => $errorId,
                    'failure_reason' => $errorMessage,
                    'failed_at' => now(),
                    'alert_data' => $alertData
                ])
            ]);
        } catch (Exception $e) {
            // 如果连数据库记录都失败了，只能记录到日志
            Log::critical("无法将失败的通知记录到数据库", [
                'error_id' => $errorId,
                'user_id' => $user->id,
                'database_error' => $e->getMessage(),
                'original_error' => $errorMessage
            ]);
        }
    }

    /**
     * 获取严重程度标签
     */
    private function getSeverityLabel(string $severity): string
    {
        switch ($severity) {
            case 'emergency':
                return '紧急';
            case 'important':
                return '重要';
            case 'general':
            default:
                return '一般';
        }
    }

    /**
     * 处理邮件发送失败的回调
     */
    public function handleMailFailure(User $user, array $alertData, string $severity, string $errorMessage): void
    {
        Log::error("邮件发送失败回调", [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'severity' => $severity,
            'error' => $errorMessage,
            'alert_data' => $alertData
        ]);
        
        // 可以在这里实现备用通知方式
        // 例如：发送短信、推送到即时消息等
        
        // 更新用户的邮件发送状态（如果用户表有这些字段）
        try {
            // 这里只是示例，实际需要根据用户表结构调整
            /*
            $user->update([
                'last_email_error' => $errorMessage,
                'last_email_error_at' => now()
            ]);
            */
        } catch (Exception $e) {
            Log::warning("无法更新用户邮件错误状态", [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查邮件服务连通性
     */
    public function checkMailServiceConnectivity(): array
    {
        $status = [
            'smtp_reachable' => false,
            'auth_valid' => false,
            'error_message' => null,
            'tested_at' => now()
        ];
        
        try {
            // 获取邮件配置
            $config = config('mail.mailers.smtp');
            
            if (!$config) {
                $status['error_message'] = '未找到SMTP配置';
                return $status;
            }
            
            // 测试SMTP连接
            $connection = fsockopen(
                $config['host'], 
                $config['port'], 
                $errno, 
                $errstr, 
                10 // 10秒超时
            );
            
            if ($connection) {
                $status['smtp_reachable'] = true;
                fclose($connection);
                
                // 可以进一步测试认证，但这里简化处理
                $status['auth_valid'] = true;
            } else {
                $status['error_message'] = "无法连接到SMTP服务器: $errstr ($errno)";
            }
            
        } catch (Exception $e) {
            $status['error_message'] = '邮件服务连通性检查失败: ' . $e->getMessage();
        }
        
        return $status;
    }
} 
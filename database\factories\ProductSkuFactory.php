<?php

namespace Database\Factories;

use App\Models\ProductSku;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Product;

class ProductSkuFactory extends Factory
{
    protected $model = ProductSku::class;

    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'sku_code' => $this->faker->unique()->bothify('SKU-####-????'),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'quantity' => $this->faker->numberBetween(0, 100),
            'props' => json_encode([
                'color' => $this->faker->colorName(),
                'size' => $this->faker->randomElement(['S', 'M', 'L', 'XL'])
            ]),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 
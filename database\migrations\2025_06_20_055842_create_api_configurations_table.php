<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // API configuration name
            $table->string('slug')->unique(); // URL-friendly identifier
            $table->text('description')->nullable(); // Description of the API
            $table->string('platform_type'); // e.g., 'shopify', 'magento', 'woocommerce'
            $table->string('base_url'); // API base URL
            $table->string('auth_type'); // 'api_key', 'oauth', 'bearer_token', 'basic_auth'
            $table->text('auth_credentials'); // Encrypted JSON storage for credentials
            $table->string('version')->default('v1'); // API version
            $table->boolean('is_active')->default(true); // Enable/disable configuration
            $table->boolean('is_deprecated')->default(false); // Mark as deprecated
            
            // Rate limiting configuration
            $table->integer('rate_limit_per_minute')->default(60); // Requests per minute
            $table->integer('rate_limit_per_hour')->default(3600); // Requests per hour
            $table->integer('rate_limit_per_day')->default(86400); // Requests per day
            
            // Health monitoring
            $table->string('health_check_endpoint')->nullable(); // Endpoint for health checks
            $table->integer('health_check_interval')->default(300); // Health check interval in seconds
            $table->timestamp('last_health_check_at')->nullable(); // Last health check timestamp
            $table->enum('health_status', ['healthy', 'unhealthy', 'unknown'])->default('unknown');
            $table->text('health_check_response')->nullable(); // Last health check response
            
            // Additional configuration
            $table->json('headers')->nullable(); // Default headers for requests
            $table->json('timeout_settings')->nullable(); // Connection and read timeouts
            $table->json('retry_settings')->nullable(); // Retry configuration
            $table->json('custom_config')->nullable(); // Platform-specific configurations
            
            // Metadata
            $table->timestamp('last_used_at')->nullable(); // Last time this configuration was used
            $table->integer('request_count')->default(0); // Total requests made
            $table->integer('error_count')->default(0); // Total errors encountered
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['platform_type', 'is_active']);
            $table->index(['is_active', 'is_deprecated']);
            $table->index('health_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_configurations');
    }
};

<?php

namespace App\Services;

use App\Models\ProductSku;
use App\Models\PriceHistory;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class PriceComparisonService
{
    // 在这里实现价格比较逻辑

    /**
     * 计算在特定日期，竞争对手SKU相对于自有SKU的价格偏离率。
     * 公式: ((竞争对手价格 - 自有产品价格) / 自有产品价格) * 100
     *
     * @param ProductSku $competitorSku
     * @param ProductSku $ownSku
     * @param Carbon $date
     * @return float|null 返回偏离率百分比，如果任一价格缺失则返回null
     */
    public function calculatePriceDeviationRate(ProductSku $competitorSku, ProductSku $ownSku, Carbon $date): ?float
    {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay = $date->copy()->endOfDay();

        // 获取当天的最新价格
        $ownPriceRecord = PriceHistory::where('sku_id', $ownSku->id)
            ->whereBetween('timestamp', [$startOfDay, $endOfDay])
            ->orderBy('timestamp', 'desc')
            ->first();

        $competitorPriceRecord = PriceHistory::where('sku_id', $competitorSku->id)
            ->whereBetween('timestamp', [$startOfDay, $endOfDay])
            ->orderBy('timestamp', 'desc')
            ->first();

        if (!$ownPriceRecord || !$competitorPriceRecord || $ownPriceRecord->price == 0) {
            return null; // 如果任一价格缺失，或自有价格为0，则无法计算
        }

        $ownPrice = $ownPriceRecord->price;
        $competitorPrice = $competitorPriceRecord->price;

        $deviation = (($competitorPrice - $ownPrice) / $ownPrice) * 100;

        return round($deviation, 2);
    }

    /**
     * 分析在给定时间范围内，竞争对手SKU相对于自有SKU的最低和最高价格偏离率。
     *
     * @param ProductSku $competitorSku
     * @param ProductSku $ownSku
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array 包含'min_deviation_rate'和'max_deviation_rate'的数组
     */
    public function analyzePriceDeviationRange(ProductSku $competitorSku, ProductSku $ownSku, Carbon $startDate, Carbon $endDate): array
    {
        $ownPrices = PriceHistory::where('sku_id', $ownSku->id)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->orderBy('timestamp', 'asc')
            ->get()
            ->keyBy(function ($item) {
                return $item->timestamp->toDateString();
            });

        $competitorPrices = PriceHistory::where('sku_id', $competitorSku->id)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->orderBy('timestamp', 'asc')
            ->get()
            ->keyBy(function ($item) {
                return $item->timestamp->toDateString();
            });

        $deviationRates = [];

        // 找出共同有价格记录的日期
        $commonDates = $ownPrices->keys()->intersect($competitorPrices->keys());

        foreach ($commonDates as $dateString) {
            $ownPrice = $ownPrices[$dateString]->price;
            $competitorPrice = $competitorPrices[$dateString]->price;

            if ($ownPrice > 0) {
                $rate = (($competitorPrice - $ownPrice) / $ownPrice) * 100;
                $deviationRates[] = $rate;
            }
        }
        
        if (empty($deviationRates)) {
            return [
                'min_deviation_rate' => null,
                'max_deviation_rate' => null,
            ];
        }

        return [
            'min_deviation_rate' => round(min($deviationRates), 2),
            'max_deviation_rate' => round(max($deviationRates), 2),
        ];
    }

    /**
     * 计算单个产品在特定日期的综合折扣率。
     * 此方法会解析promotion_info，并根据不同促销类型计算一个等效的折扣率。
     *
     * @param ProductSku $sku
     * @param Carbon $date
     * @return float
     */
    public function calculateSingleProductDiscountRate(ProductSku $sku, Carbon $date): float
    {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay = $date->copy()->endOfDay();

        $priceRecord = PriceHistory::where('sku_id', $sku->id)
            ->whereBetween('timestamp', [$startOfDay, $endOfDay])
            ->orderBy('timestamp', 'desc')
            ->first();

        if (!$priceRecord || !$priceRecord->promotion_info || $priceRecord->price == 0) {
            return 0.0;
        }

        $price = $priceRecord->price;
        $promotions = $priceRecord->promotion_info;
        $totalReduction = 0.0;

        foreach ($promotions as $promotion) {
            $type = $promotion['type'] ?? null;
            
            switch ($type) {
                case 'discount_rate':
                    $rate = $promotion['discount_rate'] ?? 0;
                    $totalReduction += $price * ($rate / 100);
                    break;
                
                case 'full_reduction':
                    $threshold = $promotion['threshold'] ?? 0;
                    $reduction = $promotion['reduction'] ?? 0;
                    if ($price >= $threshold) {
                        $totalReduction += $reduction;
                    }
                    break;
                
                // 未来可在此处添加更多促销类型的处理逻辑
            }
        }
        
        if ($totalReduction === 0.0) {
            return 0.0;
        }

        $effectiveDiscountRate = ($totalReduction / $price) * 100;
        
        return round($effectiveDiscountRate, 2);
    }

    /**
     * 计算一组产品在给定时间范围内的整体促销强度指数。
     * 当前实现为所有有效促销活动的"平均综合折扣率"。
     *
     * @param Collection $skus
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return float
     */
    public function calculateOverallPromotionIntensity(Collection $skus, Carbon $startDate, Carbon $endDate): float
    {
        $skuIds = $skus->pluck('id');

        $priceHistoryWithPromos = PriceHistory::whereIn('sku_id', $skuIds)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->whereNotNull('promotion_info')
            ->whereJsonLength('promotion_info', '>', 0)
            ->get();
        
        if ($priceHistoryWithPromos->isEmpty()) {
            return 0.0;
        }

        $allDiscountRates = [];
        foreach ($priceHistoryWithPromos as $record) {
            $sku = $skus->firstWhere('id', $record->sku_id);
            if ($sku && $record->price > 0) {
                 $rate = $this->calculateSingleProductDiscountRateForRecord($record);
                 if ($rate > 0) {
                     $allDiscountRates[] = $rate;
                 }
            }
        }
        
        if (empty($allDiscountRates)) {
            return 0.0;
        }

        return round(array_sum($allDiscountRates) / count($allDiscountRates), 2);
    }
    
    /**
     * 为单个价格历史记录计算综合折扣率的辅助方法。
     *
     * @param PriceHistory $priceRecord
     * @return float
     */
    private function calculateSingleProductDiscountRateForRecord(PriceHistory $priceRecord): float
    {
        if (!$priceRecord->promotion_info || $priceRecord->price == 0) {
            return 0.0;
        }

        $price = $priceRecord->price;
        $promotions = $priceRecord->promotion_info;
        $totalReduction = 0.0;

        foreach ($promotions as $promotion) {
            $type = $promotion['type'] ?? null;
            
            switch ($type) {
                case 'discount_rate':
                    $rate = $promotion['discount_rate'] ?? 0;
                    $totalReduction += $price * ($rate / 100);
                    break;
                
                case 'full_reduction':
                    $threshold = $promotion['threshold'] ?? 0;
                    $reduction = $promotion['reduction'] ?? 0;
                    if ($price >= $threshold) {
                        $totalReduction += $reduction;
                    }
                    break;
            }
        }
        
        if ($totalReduction === 0.0) {
            return 0.0;
        }

        return ($totalReduction / $price) * 100;
    }
} 
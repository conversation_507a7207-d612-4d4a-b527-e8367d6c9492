<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'phone_number',
        'username',
        'password',
        'avatar',
        'status',
        'email_verified_at',
        'phone_verified_at',
        'last_login_at',
        'last_login_ip',
        'login_attempts',
        'locked_until',
        'phone',
        'bio',
        'location',
        'website',
        'preferences',
        'notification_preferences',
        'password_changed_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'locked_until' => 'datetime',
        'password_changed_at' => 'datetime',
        'password' => 'hashed',
        'preferences' => 'array',
        'notification_preferences' => 'array',
    ];

    /**
     * 用户状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';

    /**
     * 获取用户的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_user')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * 获取用户的操作日志
     */
    public function actionLogs(): HasMany
    {
        return $this->hasMany(UserActionLog::class);
    }

    /**
     * 获取用户的数据源
     */
    public function dataSources(): HasMany
    {
        return $this->hasMany(DataSource::class);
    }

    /**
     * 获取用户的导入日志
     */
    public function importLogs(): HasMany
    {
        return $this->hasMany(ImportLog::class);
    }

    /**
     * 获取用户的监控任务
     */
    public function monitorTasks(): HasMany
    {
        return $this->hasMany(MonitorTask::class);
    }

    /**
     * 检查用户是否有特定权限
     */
    public function hasPermission(string $permission): bool
    {
        return $this->roles()->whereHas('permissions', function ($query) use ($permission) {
            $query->where('name', $permission)->where('is_active', true);
        })->exists();
    }

    /**
     * 检查用户是否有特定角色
     */
    public function hasRole(string $role): bool
    {
        return $this->roles()->where('name', $role)->where('is_active', true)->exists();
    }

    /**
     * 获取用户的所有权限
     */
    public function getAllPermissions()
    {
        return $this->roles()
                    ->with('permissions')
                    ->where('is_active', true)
                    ->get()
                    ->pluck('permissions')
                    ->flatten()
                    ->where('is_active', true)
                    ->unique('id');
    }

    /**
     * 检查用户是否被锁定
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * 检查用户是否激活
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 增加登录尝试次数
     */
    public function incrementLoginAttempts(): void
    {
        $this->increment('login_attempts');
    }

    /**
     * 重置登录尝试次数
     */
    public function resetLoginAttempts(): void
    {
        $this->update([
            'login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * 锁定用户账户
     */
    public function lockAccount(int $minutes = 30): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
        ]);
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin(string $ipAddress): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ipAddress,
        ]);
    }

    /**
     * 验证手机号码
     */
    public function markPhoneAsVerified(): void
    {
        $this->update([
            'phone_verified_at' => now(),
        ]);
    }

    /**
     * 检查手机是否已验证
     */
    public function hasVerifiedPhone(): bool
    {
        return !is_null($this->phone_verified_at);
    }

    /**
     * 生成用户头像URL
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        // 生成默认头像（使用用户名首字母）
        $initial = mb_substr($this->name, 0, 1);
        return "https://ui-avatars.com/api/?name={$initial}&background=6366f1&color=ffffff&size=200";
    }

    /**
     * 获取用户显示名称
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name ?: $this->username;
    }

    /**
     * 给用户分配角色
     */
    public function assignRole(string|Role $role, ?int $assignedBy = null): void
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        $this->roles()->syncWithoutDetaching([
            $role->id => [
                'assigned_at' => now(),
                'assigned_by' => $assignedBy,
            ]
        ]);
    }

    /**
     * 给用户分配多个角色
     */
    public function assignRoles(array $roles, ?int $assignedBy = null): void
    {
        $roleData = [];
        foreach ($roles as $role) {
            if (is_string($role)) {
                $role = Role::where('name', $role)->firstOrFail();
            }
            $roleData[$role->id] = [
                'assigned_at' => now(),
                'assigned_by' => $assignedBy,
            ];
        }

        $this->roles()->syncWithoutDetaching($roleData);
    }

    /**
     * 撤销用户的角色
     */
    public function removeRole(string|Role $role): void
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        $this->roles()->detach($role->id);
    }

    /**
     * 同步用户角色（替换所有角色）
     */
    public function syncRoles(array $roles, ?int $assignedBy = null): void
    {
        $roleData = [];
        foreach ($roles as $role) {
            if (is_string($role)) {
                $role = Role::where('name', $role)->firstOrFail();
            }
            $roleData[$role->id] = [
                'assigned_at' => now(),
                'assigned_by' => $assignedBy,
            ];
        }

        $this->roles()->sync($roleData);
    }

    /**
     * 检查用户是否有任一角色
     */
    public function hasAnyRole(array $roles): bool
    {
        foreach ($roles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否有所有角色
     */
    public function hasAllRoles(array $roles): bool
    {
        foreach ($roles as $role) {
            if (!$this->hasRole($role)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查用户是否有任一权限
     */
    public function hasAnyPermission(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否有所有权限
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查用户是否是管理员
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * 获取用户的主要角色（权限级别最高的角色）
     */
    public function getPrimaryRole(): ?Role
    {
        $roles = $this->roles()->where('is_active', true)->get();
        
        if ($roles->isEmpty()) {
            return null;
        }

        // 按角色级别排序，返回权限最高的角色
        $roleOrder = ['admin', 'advanced', 'normal', 'readonly'];
        
        foreach ($roleOrder as $level) {
            $role = $roles->where('level', $level)->first();
            if ($role) {
                return $role;
            }
        }

        return $roles->first();
    }

    /**
     * 获取用户角色名称数组
     */
    public function getRoleNames(): array
    {
        return $this->roles()->where('is_active', true)->pluck('name')->toArray();
    }

    /**
     * 获取用户权限名称数组
     */
    public function getPermissionNames(): array
    {
        return $this->getAllPermissions()->pluck('name')->toArray();
    }

    /**
     * 获取带默认值的用户偏好设置
     */
    public function getPreferencesWithDefaults(): array
    {
        $defaults = [
            'theme' => 'light',
            'language' => 'zh-CN',
            'timezone' => 'Asia/Shanghai',
            'notifications' => [
                'email' => true,
                'push' => true,
                'sms' => false,
            ],
        ];

        return array_merge($defaults, $this->preferences ?? []);
    }

    /**
     * 批量插入用户数据（优化性能）
     */
    public static function bulkInsert(array $users, int $chunkSize = 500): bool
    {
        try {
            DB::beginTransaction();
            
            // 分块处理以避免内存问题
            $chunks = array_chunk($users, $chunkSize);
            
            foreach ($chunks as $chunk) {
                // 为每个用户添加时间戳
                $processedChunk = array_map(function ($user) {
                    return array_merge($user, [
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }, $chunk);
                
                // 使用批量插入
                DB::table('users')->insert($processedChunk);
            }
            
            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 批量更新用户数据（优化性能）
     */
    public static function bulkUpdate(array $updates): bool
    {
        try {
            DB::beginTransaction();
            
            foreach ($updates as $update) {
                if (!isset($update['id'])) {
                    continue;
                }
                
                $id = $update['id'];
                unset($update['id']);
                $update['updated_at'] = now();
                
                DB::table('users')->where('id', $id)->update($update);
            }
            
            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取用户通知偏好设置
     */
    public function getNotificationPreferences(): array
    {
        $defaults = [
            'email_enabled' => true,
            'emergency_email' => true,
            'important_email' => true,
            'general_email' => false,
            'in_site_enabled' => true,
            'emergency_in_site' => true,
            'important_in_site' => true,
            'general_in_site' => true,
            'frequency_control' => [
                'emergency' => 'immediate',
                'important' => 'immediate',
                'general' => 'daily_digest'
            ]
        ];

        return array_merge($defaults, $this->notification_preferences ?? []);
    }

    /**
     * 更新用户通知偏好设置
     */
    public function updateNotificationPreferences(array $preferences): bool
    {
        $current = $this->getNotificationPreferences();
        $updated = array_merge($current, $preferences);
        
        return $this->update(['notification_preferences' => $updated]);
    }

    /**
     * 检查是否启用了特定类型的通知
     */
    public function isNotificationEnabled(string $type, string $severity = 'general'): bool
    {
        $preferences = $this->getNotificationPreferences();
        
        if ($type === 'email') {
            if (!$preferences['email_enabled']) {
                return false;
            }
            return $preferences["{$severity}_email"] ?? false;
        }
        
        if ($type === 'in_site') {
            if (!$preferences['in_site_enabled']) {
                return false;
            }
            return $preferences["{$severity}_in_site"] ?? true;
        }
        
        return false;
    }

    /**
     * 获取通知频率控制设置
     */
    public function getNotificationFrequency(string $severity): string
    {
        $preferences = $this->getNotificationPreferences();
        return $preferences['frequency_control'][$severity] ?? 'immediate';
    }

    /**
     * 获取用户的警报日志
     */
    public function alertLogs(): HasMany
    {
        return $this->hasMany(AlertLog::class);
    }

    /**
     * Find the user for passport authentication.
     *
     * @param  string  $username
     * @return \App\Models\User
     */
    public function findForPassport($username)
    {
        return $this->where('username', $username)->orWhere('email', $username)->first();
    }
} 
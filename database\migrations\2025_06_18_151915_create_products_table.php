<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('产品标题');
            $table->unsignedBigInteger('category_id')->nullable()->comment('分类ID');
            $table->string('category_path')->nullable()->comment('分类路径');
            $table->unsignedBigInteger('shop_id')->nullable()->comment('店铺ID');
            $table->string('shop_name')->nullable()->comment('店铺名称');
            $table->enum('item_type', ['normal', 'virtual', 'service'])->default('normal')->comment('商品类型');
            $table->enum('state', ['active', 'inactive', 'deleted'])->default('active')->comment('产品状态');
            $table->string('source_platform')->default('taobao')->comment('来源平台');
            $table->string('source_url')->nullable()->comment('原始商品链接');
            $table->string('source_id')->nullable()->comment('平台商品ID');
            $table->text('description')->nullable()->comment('产品描述');
            $table->string('image_url')->nullable()->comment('主图URL');
            $table->json('images')->nullable()->comment('商品图片集合');
            $table->decimal('min_price', 10, 2)->nullable()->comment('最低价格');
            $table->decimal('max_price', 10, 2)->nullable()->comment('最高价格');
            $table->unsignedInteger('total_sales')->default(0)->comment('总销量');
            $table->decimal('rating', 3, 2)->nullable()->comment('评分');
            $table->unsignedInteger('review_count')->default(0)->comment('评价数量');
            $table->timestamps();
            
            // 索引
            $table->index(['shop_id', 'state']);
            $table->index(['category_id', 'state']);
            $table->index(['source_platform', 'source_id']);
            $table->index(['state', 'created_at']);
            $table->index('title');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};

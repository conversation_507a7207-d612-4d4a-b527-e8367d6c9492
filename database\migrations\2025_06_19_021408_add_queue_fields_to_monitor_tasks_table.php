<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            // 添加队列相关字段
            $table->string('queue_name')->default('data_collection')->comment('队列名称')->after('platform');
            $table->json('maintenance_window')->nullable()->comment('维护窗口配置')->after('parameters');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->dropColumn(['queue_name', 'maintenance_window']);
        });
    }
};

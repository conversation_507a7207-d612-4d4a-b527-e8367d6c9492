<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 只添加个人中心需要的新字段，其他字段已在基础users表中
            $table->string('phone', 20)->nullable()->after('phone_number')->comment('个人手机号');
            $table->text('bio')->nullable()->after('avatar')->comment('个人简介');
            $table->string('location')->nullable()->after('bio')->comment('所在地区');
            $table->string('website')->nullable()->after('location')->comment('个人网站');
            $table->json('preferences')->nullable()->after('website')->comment('用户偏好设置');
            $table->timestamp('password_changed_at')->nullable()->after('preferences')->comment('密码修改时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'bio',
                'location',
                'website',
                'preferences',
                'password_changed_at',
            ]);
        });
    }
}; 
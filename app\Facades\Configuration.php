<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * 配置管理 Facade
 * 
 * @method static mixed get(string $key, $default = null, bool $useCache = true)
 * @method static void set(string $key, $value)
 * @method static array getDataCollectionConfig(?string $platform = null)
 * @method static array getPlatformConfig(string $platform)
 * @method static array getQueueConfig()
 * @method static array getHttpConfig()
 * @method static array getMonitoringConfig()
 * @method static array getCacheConfig()
 * @method static array getSecurityConfig()
 * @method static array validateConfiguration()
 * @method static array getAllConfigs(bool $includeSensitive = false)
 * @method static void reload()
 * @method static bool has(string $key)
 * @method static mixed env(string $key, $default = null)
 */
class Configuration extends Facade
{
    /**
     * 获取 Facade 对应的服务容器绑定名称
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'configuration';
    }
} 
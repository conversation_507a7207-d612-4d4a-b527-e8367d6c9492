#!/bin/bash

# MeiliSearch 停止脚本

echo "正在停止 MeiliSearch..."

# 查找 MeiliSearch 进程
PIDS=$(pgrep -f "meilisearch")

if [ -z "$PIDS" ]; then
    echo "未找到运行中的 MeiliSearch 进程"
else
    echo "找到 MeiliSearch 进程: $PIDS"
    
    # 优雅停止
    for PID in $PIDS; do
        echo "正在停止进程 $PID..."
        kill -TERM $PID
        
        # 等待进程结束
        sleep 2
        
        # 检查进程是否仍在运行
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程 $PID..."
            kill -KILL $PID
        fi
    done
    
    echo "MeiliSearch 已停止"
fi

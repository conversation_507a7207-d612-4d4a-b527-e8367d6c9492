<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\MonitorTask;
use App\Models\Product;
use App\Models\AlertLog;
use App\Models\PriceHistory;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // 获取统计数据（带默认值）
        $taskCount = MonitorTask::where('status', 'active')->count();
        $productCount = Product::count();
        $priceAlertCount = 0; // 暂时设为0，因为警报系统较复杂
        $todayUpdateCount = PriceHistory::whereDate('created_at', Carbon::today())->count();

        // 获取最新警报（暂时使用示例数据）
        $recentAlerts = collect([
            [
                'title' => '系统运行正常',
                'description' => '所有监控任务正常运行',
                'type' => '系统状态',
                'created_at' => now(),
            ]
        ]);

        return view('dashboard', compact(
            'taskCount',
            'productCount', 
            'priceAlertCount',
            'todayUpdateCount',
            'recentAlerts'
        ));
    }
}

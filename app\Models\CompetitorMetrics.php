<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CompetitorMetrics extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'sku_id',
        'competitor_product_id',
        'competitor_platform',
        
        // 促销策略分析指标
        'promotion_types',
        'total_promotions',
        'promotion_frequency',
        'avg_discount_rate',
        'max_discount_rate',
        'min_discount_rate',
        'promotion_duration_avg',
        
        // 价格对比指标
        'single_product_discount_rate',
        'promotion_intensity_index',
        'price_deviation_rate',
        'min_price_deviation',
        'max_price_deviation',
        'avg_price_deviation',
        
        // 价格趋势分析指标
        'price_trend_slope',
        'price_trend_direction',
        'price_volatility',
        'trend_correlation',
        'trend_analysis_days',
        'price_change_30d',
        'price_change_7d',
        
        // 竞争分析指标
        'market_share_estimate',
        'competitive_advantage_score',
        'price_position',
        'sales_performance_ratio',
        
        // 数据质量和元信息
        'analysis_date',
        'data_period_start',
        'data_period_end',
        'data_points_count',
        'data_quality_score',
        'calculation_metadata',
        'calculation_status',
        'calculation_notes',
    ];

    protected $casts = [
        'promotion_types' => 'array',
        'total_promotions' => 'integer',
        'promotion_frequency' => 'decimal:2',
        'avg_discount_rate' => 'decimal:2',
        'max_discount_rate' => 'decimal:2',
        'min_discount_rate' => 'decimal:2',
        'promotion_duration_avg' => 'integer',
        
        'single_product_discount_rate' => 'decimal:2',
        'promotion_intensity_index' => 'decimal:2',
        'price_deviation_rate' => 'decimal:2',
        'min_price_deviation' => 'decimal:2',
        'max_price_deviation' => 'decimal:2',
        'avg_price_deviation' => 'decimal:2',
        
        'price_trend_slope' => 'decimal:4',
        'price_volatility' => 'decimal:2',
        'trend_correlation' => 'decimal:2',
        'trend_analysis_days' => 'integer',
        'price_change_30d' => 'decimal:2',
        'price_change_7d' => 'decimal:2',
        
        'market_share_estimate' => 'decimal:2',
        'competitive_advantage_score' => 'decimal:2',
        'sales_performance_ratio' => 'decimal:2',
        
        'analysis_date' => 'datetime',
        'data_period_start' => 'datetime',
        'data_period_end' => 'datetime',
        'data_points_count' => 'integer',
        'data_quality_score' => 'decimal:2',
        'calculation_metadata' => 'array',
    ];

    /**
     * 关联产品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联SKU
     */
    public function sku(): BelongsTo
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    /**
     * 获取促销类型统计文本
     */
    public function getPromotionTypesTextAttribute(): string
    {
        if (!$this->promotion_types) {
            return '无促销数据';
        }

        $types = [];
        foreach ($this->promotion_types as $type => $count) {
            $typeName = $this->getPromotionTypeName($type);
            $types[] = "{$typeName}: {$count}次";
        }

        return implode(', ', $types);
    }

    /**
     * 获取价格趋势描述
     */
    public function getPriceTrendDescriptionAttribute(): string
    {
        $direction = match($this->price_trend_direction) {
            'rising' => '上涨',
            'falling' => '下跌',
            'stable' => '稳定',
            default => '未知'
        };

        $slope = abs($this->price_trend_slope);
        $intensity = $slope > 1 ? '强烈' : ($slope > 0.5 ? '明显' : '轻微');

        return "{$intensity}{$direction}";
    }

    /**
     * 获取竞争优势等级
     */
    public function getCompetitiveAdvantageGradeAttribute(): string
    {
        $score = $this->competitive_advantage_score;
        
        return match(true) {
            $score >= 80 => 'A级 (优势明显)',
            $score >= 60 => 'B级 (有一定优势)',
            $score >= 40 => 'C级 (竞争激烈)',
            $score >= 20 => 'D级 (处于劣势)',
            default => 'E级 (严重劣势)'
        };
    }

    /**
     * 获取价格定位描述
     */
    public function getPricePositionTextAttribute(): string
    {
        return match($this->price_position) {
            'premium' => '高端定位',
            'mid-range' => '中端定位',
            'budget' => '低端定位',
            default => '定位未知'
        };
    }

    /**
     * 获取数据质量等级
     */
    public function getDataQualityGradeAttribute(): string
    {
        $score = $this->data_quality_score;
        
        return match(true) {
            $score >= 0.9 => '优秀',
            $score >= 0.7 => '良好',
            $score >= 0.5 => '一般',
            $score >= 0.3 => '较差',
            default => '很差'
        };
    }

    /**
     * 检查是否有促销活动
     */
    public function hasPromotions(): bool
    {
        return $this->total_promotions > 0;
    }

    /**
     * 检查价格是否处于上涨趋势
     */
    public function isPriceRising(): bool
    {
        return $this->price_trend_direction === 'rising';
    }

    /**
     * 检查价格是否处于下跌趋势
     */
    public function isPriceFalling(): bool
    {
        return $this->price_trend_direction === 'falling';
    }

    /**
     * 检查是否具有竞争优势
     */
    public function hasCompetitiveAdvantage(): bool
    {
        return $this->competitive_advantage_score >= 60;
    }

    /**
     * 获取促销类型名称
     */
    private function getPromotionTypeName(string $type): string
    {
        return match($type) {
            'full_reduction' => '满减',
            'discount' => '折扣',
            'gifts' => '赠品',
            'coupon' => '优惠券',
            'flash_sale' => '秒杀',
            'bundle' => '套装',
            default => $type
        };
    }

    /**
     * 作用域：按产品筛选
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * 作用域：按SKU筛选
     */
    public function scopeForSku($query, $skuId)
    {
        return $query->where('sku_id', $skuId);
    }

    /**
     * 作用域：按平台筛选
     */
    public function scopeForPlatform($query, $platform)
    {
        return $query->where('competitor_platform', $platform);
    }

    /**
     * 作用域：按分析日期范围筛选
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('analysis_date', [$startDate, $endDate]);
    }

    /**
     * 作用域：只获取计算完成的记录
     */
    public function scopeCompleted($query)
    {
        return $query->where('calculation_status', 'completed');
    }

    /**
     * 作用域：按竞争优势评分排序
     */
    public function scopeOrderByCompetitiveAdvantage($query, $direction = 'desc')
    {
        return $query->orderBy('competitive_advantage_score', $direction);
    }

    /**
     * 作用域：按促销频率排序
     */
    public function scopeOrderByPromotionFrequency($query, $direction = 'desc')
    {
        return $query->orderBy('promotion_frequency', $direction);
    }
}

<?php

namespace App\Jobs;

use App\Services\DataCollectionService;
use App\Services\RetryService;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 数据收集重试任务
 * 
 * 用于重试失败的数据收集操作
 */
class DataCollectionRetryJob extends RetryableJob
{
    /**
     * 执行具体的任务逻辑
     *
     * @return mixed
     * @throws Exception
     */
    protected function executeJob()
    {
        $platform = $this->jobData['platform'] ?? null;
        $parameters = $this->jobData['parameters'] ?? [];
        $method = $this->jobData['method'] ?? 'collectData';

        if (!$platform) {
            throw new Exception('平台名称不能为空');
        }

        Log::info('开始执行数据收集重试任务', [
            'platform' => $platform,
            'method' => $method,
            'parameters' => $parameters
        ]);

        // 创建数据收集服务实例
        $dataCollectionService = app(\App\Services\DataCollection\DataCollectionService::class);

        // 根据方法执行不同的操作
        switch ($method) {
            case 'collectData':
                return $dataCollectionService->collectData($parameters);
                
            case 'searchProducts':
                $keyword = $parameters['keyword'] ?? '';
                $page = $parameters['page'] ?? 1;
                return $dataCollectionService->searchProducts($platform, $keyword, $page);
                
            case 'collectSimilarProducts':
                $itemId = $parameters['item_id'] ?? '';
                return $dataCollectionService->collectSimilarProducts($platform, $itemId);
                
            case 'collectShopItems':
                $shopId = $parameters['shop_id'] ?? '';
                $page = $parameters['page'] ?? 1;
                return $dataCollectionService->collectShopItems($platform, $shopId, $page);
                
            default:
                throw new Exception("不支持的方法: {$method}");
        }
    }

    /**
     * 检查异常是否应该重试
     *
     * @param Exception $exception 异常
     * @return bool
     */
    protected function shouldRetryException(Exception $exception): bool
    {
        // 调用父类的默认重试逻辑
        $shouldRetry = parent::shouldRetryException($exception);

        // 添加特定的重试逻辑
        $message = $exception->getMessage();
        
        // 这些错误不应该重试
        $nonRetryableMessages = [
            '平台名称不能为空',
            '不支持的方法',
            '无效的商品ID',
            '无效的店铺ID',
            '参数验证失败',
            '找不到任务ID',
        ];

        foreach ($nonRetryableMessages as $nonRetryableMessage) {
            if (strpos($message, $nonRetryableMessage) !== false) {
                Log::info('检测到不可重试的错误', [
                    'exception' => $message,
                    'reason' => $nonRetryableMessage
                ]);
                return false;
            }
        }

        // 网络相关错误应该重试
        $retryableMessages = [
            'Connection timed out',
            'Connection refused',
            'Network is unreachable',
            'Too many requests',
            'Service unavailable',
            'Gateway timeout',
            'Internal server error',
        ];

        foreach ($retryableMessages as $retryableMessage) {
            if (stripos($message, $retryableMessage) !== false) {
                Log::info('检测到可重试的网络错误', [
                    'exception' => $message,
                    'reason' => $retryableMessage
                ]);
                return true;
            }
        }

        return $shouldRetry;
    }

    /**
     * 处理最终失败
     *
     * @param Exception $exception 异常
     * @return void
     */
    protected function handleFinalFailure(Exception $exception): void
    {
        parent::handleFinalFailure($exception);

        // 记录失败的数据收集任务
        Log::critical('数据收集任务最终失败', [
            'platform' => $this->jobData['platform'] ?? 'unknown',
            'method' => $this->jobData['method'] ?? 'unknown',
            'parameters' => $this->jobData['parameters'] ?? [],
            'exception' => $exception->getMessage(),
            'total_attempts' => $this->attempts()
        ]);

        // 可以在这里添加失败通知逻辑
        // 例如：发送邮件、推送通知、记录到监控系统等
    }

    /**
     * 最终失败时的回调
     *
     * @param Exception $exception 异常
     * @return void
     */
    protected function onFinalFailure(Exception $exception): void
    {
        // 可以在这里实现特定的失败处理逻辑
        // 例如：更新数据库状态、发送告警等
        
        $platform = $this->jobData['platform'] ?? 'unknown';
        $method = $this->jobData['method'] ?? 'unknown';
        
        Log::alert('数据收集重试任务彻底失败', [
            'platform' => $platform,
            'method' => $method,
            'job_data' => $this->jobData,
            'exception_message' => $exception->getMessage(),
            'suggestion' => '请检查平台配置和网络连接'
        ]);
    }

    /**
     * 创建数据收集重试任务
     *
     * @param string $platform 平台名称
     * @param string $method 方法名称
     * @param array $parameters 参数
     * @param array $retryConfig 重试配置
     * @return static
     */
    public static function create(
        string $platform, 
        string $method = 'collectData', 
        array $parameters = [], 
        array $retryConfig = []
    ): self {
        $jobData = [
            'platform' => $platform,
            'method' => $method,
            'parameters' => $parameters,
            'created_at' => now()->toISOString()
        ];

        // 为数据收集任务设置特定的重试配置
        $defaultRetryConfig = [
            'max_retries' => 5, // 数据收集可能需要更多重试
            'base_delay' => 2000, // 2秒基础延迟
            'max_delay' => 60000, // 最大1分钟延迟
            'backoff_multiplier' => 2,
            'jitter_factor' => 0.1,
        ];

        $finalRetryConfig = array_merge($defaultRetryConfig, $retryConfig);

        return new static($jobData, $finalRetryConfig);
    }

    /**
     * 批量创建数据收集重试任务
     *
     * @param array $tasks 任务列表
     * @param array $retryConfig 重试配置
     * @return array
     */
    public static function createBatch(array $tasks, array $retryConfig = []): array
    {
        $jobs = [];

        foreach ($tasks as $task) {
            $platform = $task['platform'] ?? '';
            $method = $task['method'] ?? 'collectData';
            $parameters = $task['parameters'] ?? [];

            if ($platform) {
                $jobs[] = static::create($platform, $method, $parameters, $retryConfig);
            }
        }

        return $jobs;
    }
} 
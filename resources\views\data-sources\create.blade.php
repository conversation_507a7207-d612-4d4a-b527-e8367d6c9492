@extends('layouts.app')

@section('title', '添加数据源')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('data-sources.index') }}">数据源管理</a></li>
    <li class="breadcrumb-item active">添加数据源</li>
@endsection

@section('page-title', '添加数据源')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-plus me-2"></i>新建数据源
                </h6>
            </div>
            <div class="card-body">
                <form id="createDataSourceForm" method="POST" action="{{ route('data-sources.store') }}">
                    @csrf
                    
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">基本信息</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">数据源名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">请输入易于识别的数据源名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="platform" class="form-label">平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="淘宝">淘宝</option>
                                    <option value="京东">京东</option>
                                    <option value="天猫">天猫</option>
                                    <option value="拼多多">拼多多</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">描述</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入数据源的详细描述"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 数据源类型 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">数据源类型</h6>
                        </div>
                        <div class="col-12">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-primary h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="typeAPI" name="type" value="api" checked>
                                            <label for="typeAPI" class="form-check-label d-block mt-2">
                                                <i class="fas fa-cloud fa-2x text-primary mb-2"></i>
                                                <h6>API接口</h6>
                                                <small class="text-muted">通过API接口获取数据</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="typeExcel" name="type" value="excel">
                                            <label for="typeExcel" class="form-check-label d-block mt-2">
                                                <i class="fas fa-file-excel fa-2x text-success mb-2"></i>
                                                <h6>Excel文件</h6>
                                                <small class="text-muted">上传Excel文件导入数据</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-warning h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="typeCSV" name="type" value="csv">
                                            <label for="typeCSV" class="form-check-label d-block mt-2">
                                                <i class="fas fa-file-csv fa-2x text-warning mb-2"></i>
                                                <h6>CSV文件</h6>
                                                <small class="text-muted">上传CSV文件导入数据</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API配置 -->
                    <div id="apiConfig" class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">API配置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiUrl" class="form-label">API地址 <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="apiUrl" name="api_url" placeholder="https://api.example.com/products">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiMethod" class="form-label">请求方法</label>
                                <select class="form-select" id="apiMethod" name="api_method">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="apiKey" class="form-label">API密钥</label>
                                <input type="password" class="form-control" id="apiKey" name="api_key" placeholder="请输入API密钥">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="updateInterval" class="form-label">更新频率（分钟）</label>
                                <input type="number" class="form-control" id="updateInterval" name="update_interval" value="60" min="1">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="apiHeaders" class="form-label">请求头（JSON格式）</label>
                                <textarea class="form-control" id="apiHeaders" name="api_headers" rows="3" placeholder='{"Content-Type": "application/json"}'></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 文件配置 -->
                    <div id="fileConfig" class="row mb-4" style="display: none;">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">文件配置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fileEncoding" class="form-label">文件编码</label>
                                <select class="form-select" id="fileEncoding" name="file_encoding">
                                    <option value="utf-8">UTF-8</option>
                                    <option value="gbk">GBK</option>
                                    <option value="gb2312">GB2312</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="delimiter" class="form-label">分隔符（仅CSV）</label>
                                <select class="form-select" id="delimiter" name="delimiter">
                                    <option value=",">逗号 (,)</option>
                                    <option value=";">分号 (;)</option>
                                    <option value="\t">制表符 (\t)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="columnMapping" class="form-label">列映射配置（JSON格式）</label>
                                <textarea class="form-control" id="columnMapping" name="column_mapping" rows="4" placeholder='{"商品名称": "name", "价格": "price", "库存": "stock"}'></textarea>
                                <div class="form-text">定义文件列名与系统字段的映射关系</div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据处理选项 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2">数据处理选项</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="autoUpdate" name="auto_update" checked>
                                <label class="form-check-label" for="autoUpdate">
                                    自动更新数据
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="deduplication" name="deduplication" checked>
                                <label class="form-check-label" for="deduplication">
                                    自动去重
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="dataValidation" name="data_validation" checked>
                                <label class="form-check-label" for="dataValidation">
                                    数据验证
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="errorNotification" name="error_notification" checked>
                                <label class="form-check-label" for="errorNotification">
                                    错误通知
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ route('data-sources.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <button type="button" class="btn btn-info" onclick="testConnection()">
                                    <i class="fas fa-plug me-1"></i>测试连接
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存数据源
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 监听数据源类型变化
    const typeRadios = document.querySelectorAll('input[name="type"]');
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleConfigSections(this.value);
        });
    });
    
    // 初始化显示API配置
    toggleConfigSections('api');
});

// 切换配置区域显示
function toggleConfigSections(type) {
    const apiConfig = document.getElementById('apiConfig');
    const fileConfig = document.getElementById('fileConfig');
    
    if (type === 'api') {
        apiConfig.style.display = 'block';
        fileConfig.style.display = 'none';
        
        // 设置API字段为必填
        document.getElementById('apiUrl').required = true;
    } else {
        apiConfig.style.display = 'none';
        fileConfig.style.display = 'block';
        
        // 移除API字段必填
        document.getElementById('apiUrl').required = false;
    }
}

// 测试连接
function testConnection() {
    const type = document.querySelector('input[name="type"]:checked').value;
    
    if (type === 'api') {
        const apiUrl = document.getElementById('apiUrl').value;
        if (!apiUrl) {
            alert('请先输入API地址');
            return;
        }
        
        // 模拟测试连接
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>测试中...';
        btn.disabled = true;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('连接测试成功！');
        }, 2000);
    } else {
        alert('文件类型数据源无需测试连接');
    }
}

// 表单提交处理
document.getElementById('createDataSourceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 模拟提交
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        alert('数据源创建成功！');
        window.location.href = '{{ route("data-sources.index") }}';
    }, 2000);
});
</script>
@endsection

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class MonitorTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'sku_id',
        'task_group_id',
        'task_name',
        'platform',
        'queue_name',
        'description',
        'monitor_type',
        'frequency',
        'cron_expression',
        'min_interval',
        'last_collected_at',
        'last_executed_at',
        'next_collection_at',
        'status',
        'collection_count',
        'execution_count',
        'success_count',
        'failure_count',
        'monitor_settings',
        'dependencies',
        'retry_config',
        'parameters',
        'maintenance_window',
        'error_message',
        'error_occurred_at',
        'consecutive_errors',
        'is_public',
        'is_enabled',
    ];

    protected $casts = [
        'last_collected_at' => 'datetime',
        'last_executed_at' => 'datetime',
        'next_collection_at' => 'datetime',
        'error_occurred_at' => 'datetime',
        'collection_count' => 'integer',
        'execution_count' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'consecutive_errors' => 'integer',
        'min_interval' => 'integer',
        'monitor_settings' => 'array',
        'dependencies' => 'array',
        'retry_config' => 'array',
        'parameters' => 'array',
        'maintenance_window' => 'array',
        'is_public' => 'boolean',
        'is_enabled' => 'boolean',
    ];

    /**
     * 任务创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 监控的产品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 监控的SKU
     */
    public function sku(): BelongsTo
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    /**
     * 所属任务分组
     */
    public function taskGroup(): BelongsTo
    {
        return $this->belongsTo(TaskGroup::class, 'task_group_id');
    }

    /**
     * 告警规则
     */
    public function alertRules(): HasMany
    {
        return $this->hasMany(AlertRule::class, 'task_id');
    }

    /**
     * 告警日志
     */
    public function alertLogs(): HasMany
    {
        return $this->hasMany(AlertLog::class, 'task_id');
    }

    /**
     * 价格历史记录
     */
    public function priceHistories(): HasMany
    {
        return $this->hasMany(PriceHistory::class, 'task_id');
    }

    /**
     * 活跃的告警规则
     */
    public function activeAlertRules(): HasMany
    {
        return $this->alertRules()->where('is_active', true);
    }

    /**
     * 获取频率对应的分钟数
     */
    public function getFrequencyMinutesAttribute(): int
    {
        $frequencies = [
            '5min' => 5,
            '15min' => 15,
            '30min' => 30,
            '1hour' => 60,
            '6hour' => 360,
            '12hour' => 720,
            '24hour' => 1440,
        ];

        return $frequencies[$this->frequency] ?? 60;
    }

    /**
     * 检查是否需要执行采集
     */
    public function shouldCollect(): bool
    {
        if ($this->status !== 'active' || !$this->is_enabled) {
            return false;
        }

        return !$this->next_collection_at || Carbon::now() >= $this->next_collection_at;
    }

    /**
     * 更新下次采集时间
     */
    public function updateNextCollectionTime(): void
    {
        $this->next_collection_at = Carbon::now()->addMinutes($this->frequency_minutes);
        $this->save();
    }

    /**
     * 标记采集完成
     */
    public function markCollectionCompleted(): void
    {
        $this->last_collected_at = Carbon::now();
        $this->collection_count++;
        $this->consecutive_errors = 0; // 重置错误计数
        $this->error_message = null;
        $this->updateNextCollectionTime();
        $this->save();
    }

    /**
     * 记录采集错误
     */
    public function recordError(string $errorMessage): void
    {
        $this->error_message = $errorMessage;
        $this->error_occurred_at = Carbon::now();
        $this->consecutive_errors++;
        
        // 如果连续错误超过3次，暂停任务
        if ($this->consecutive_errors >= 3) {
            $this->status = 'error';
        }
        
        $this->updateNextCollectionTime(); // 即使出错也要更新下次尝试时间
        $this->save();
    }

    /**
     * 获取监控目标显示名称
     */
    public function getTargetNameAttribute(): string
    {
        if ($this->sku) {
            return $this->sku->display_name;
        } elseif ($this->product) {
            return $this->product->title;
        }
        
        return '未知目标';
    }

    /**
     * 获取状态中文
     */
    public function getStatusTextAttribute(): string
    {
        $statusMap = [
            'active' => '运行中',
            'paused' => '已暂停',
            'stopped' => '已停止',
            'error' => '错误'
        ];

        return $statusMap[$this->status] ?? $this->status;
    }

    /**
     * 获取频率中文
     */
    public function getFrequencyTextAttribute(): string
    {
        $frequencyMap = [
            '5min' => '每5分钟',
            '15min' => '每15分钟',
            '30min' => '每30分钟',
            '1hour' => '每小时',
            '6hour' => '每6小时',
            '12hour' => '每12小时',
            '24hour' => '每天'
        ];

        return $frequencyMap[$this->frequency] ?? $this->frequency;
    }

    /**
     * 检查是否健康（无错误）
     */
    public function isHealthy(): bool
    {
        return $this->consecutive_errors == 0 && $this->status !== 'error';
    }

    /**
     * 按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 需要执行采集的任务
     */
    public function scopeReadyForCollection($query)
    {
        return $query->where('status', 'active')
                    ->where(function($q) {
                        $q->whereNull('next_collection_at')
                          ->orWhere('next_collection_at', '<=', Carbon::now());
                    });
    }

    /**
     * 有错误的任务
     */
    public function scopeWithErrors($query)
    {
        return $query->where('consecutive_errors', '>', 0);
    }

    /**
     * 公开的任务
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 按频率筛选
     */
    public function scopeWithFrequency($query, $frequency)
    {
        return $query->where('frequency', $frequency);
    }
}

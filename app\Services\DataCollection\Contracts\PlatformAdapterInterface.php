<?php

namespace App\Services\DataCollection\Contracts;

use App\Models\ApiConfiguration;

/**
 * 平台适配器接口
 * 定义所有电商平台适配器必须实现的方法
 */
interface PlatformAdapterInterface
{
    /**
     * 获取商品详情
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 商品详情数据
     */
    public function getItemDetail(string $itemId, array $options = []): array;

    /**
     * 获取同款商品
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 同款商品列表
     */
    public function getSimilarProducts(string $itemId, array $options = []): array;

    /**
     * 获取店铺商品列表
     *
     * @param string $shopId 店铺ID
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 店铺商品列表
     */
    public function getShopItems(string $shopId, int $page = 1, array $options = []): array;

    /**
     * 搜索商品
     *
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 搜索结果
     */
    public function searchItems(string $keyword, int $page = 1, array $options = []): array;

    /**
     * 获取平台名称
     *
     * @return string 平台名称
     */
    public function getPlatformName(): string;

    /**
     * 验证API连接
     *
     * @return bool 连接是否成功
     */
    public function testConnection(): bool;

    /**
     * 获取平台支持的功能列表
     *
     * @return array 支持的功能列表
     */
    public function getSupportedFeatures(): array;

    /**
     * 获取平台的API版本信息
     *
     * @return string API版本
     */
    public function getApiVersion(): string;

    /**
     * 设置API配置
     *
     * @param ApiConfiguration $config API配置对象
     * @return void
     */
    public function setApiConfiguration(ApiConfiguration $config): void;

    /**
     * 获取当前API配置
     *
     * @return ApiConfiguration|null 当前API配置
     */
    public function getApiConfiguration(): ?ApiConfiguration;

    /**
     * 执行健康检查
     *
     * @return array 健康检查结果
     */
    public function performHealthCheck(): array;

    /**
     * 获取适配器的统计信息
     *
     * @return array 统计信息
     */
    public function getStatistics(): array;
}
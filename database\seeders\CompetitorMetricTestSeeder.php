<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductSku;
use App\Models\PriceHistory;
use Carbon\Carbon;

class CompetitorMetricTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建产品
        $ownProduct = Product::factory()->create(['title' => 'Own Brand Product']);
        $competitorProductA = Product::factory()->create(['title' => 'Competitor A Product']);
        $competitorProductB = Product::factory()->create(['title' => 'Competitor B Product']);

        // 创建 SKUs
        $ownSkus = ProductSku::factory(2)->create(['product_id' => $ownProduct->id, 'is_own' => true]);
        $competitorSkus = ProductSku::factory(3)->create(['product_id' => $competitorProductA->id, 'is_own' => false]);
        $competitorSkus->push(...ProductSku::factory(2)->create(['product_id' => $competitorProductB->id, 'is_own' => false]));

        // 创建价格历史
        $allSkus = $ownSkus->merge($competitorSkus);
        $endDate = Carbon::today();
        $startDate = $endDate->copy()->subDays(30);

        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
            foreach ($allSkus as $sku) {
                $promotionInfo = null;
                // 为非自有产品随机添加促销
                if (!$sku->is_own && rand(1, 10) <= 3) { // 30% 的几率有促销
                    $promotionInfo = [['type' => 'discount_rate', 'discount_rate' => rand(5, 25)]];
                }

                PriceHistory::factory()->create([
                    'sku_id' => $sku->id,
                    'price' => $sku->is_own ? rand(100, 150) : rand(90, 160),
                    'timestamp' => $date,
                    'promotion_info' => $promotionInfo,
                ]);
            }
        }
    }
}

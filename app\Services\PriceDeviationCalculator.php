<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Exception;

/**
 * 价格偏差率计算服务
 * 
 * 提供高精度的价格偏差率计算功能，支持：
 * 1. 促销价格偏差率计算
 * 2. 渠道价格偏差率计算
 * 3. 批量计算
 * 4. 边界情况处理
 * 5. 性能监控和异常检测
 */
class PriceDeviationCalculator
{
    /**
     * 性能监控器
     *
     * @var PriceDeviationMonitor|null
     */
    private ?PriceDeviationMonitor $monitor = null;

    /**
     * 构造函数
     * 设置bcmath计算精度
     */
    public function __construct(?PriceDeviationMonitor $monitor = null)
    {
        // 初始化bcmath精度
        PriceDeviationConfig::initializeBcmathPrecision();
        
        // 设置监控器
        $this->monitor = $monitor;
    }

    /**
     * 设置监控器
     *
     * @param PriceDeviationMonitor $monitor
     * @return void
     */
    public function setMonitor(PriceDeviationMonitor $monitor): void
    {
        $this->monitor = $monitor;
    }

    /**
     * 计算促销价格偏差率
     * 
     * 公式: (price - sub_price) / price × 100%
     * 
     * @param float|string $price 原价
     * @param float|string $subPrice 促销价/副价格
     * @return float|null 偏差率（百分比），null表示无法计算
     */
    public function calculatePromotionDeviation($price, $subPrice): ?float
    {
        $context = [
            'type' => 'promotion_deviation',
            'price' => $price,
            'sub_price' => $subPrice
        ];

        // 如果有监控器，使用监控器执行计算
        if ($this->monitor) {
            return $this->monitor->monitorCalculation(function() use ($price, $subPrice, $context) {
                return $this->doCalculatePromotionDeviation($price, $subPrice, $context);
            }, $context);
        }

        return $this->doCalculatePromotionDeviation($price, $subPrice, $context);
    }

    /**
     * 执行促销价格偏差率计算
     */
    private function doCalculatePromotionDeviation($price, $subPrice, array $context): ?float
    {
        try {
            // 参数验证和转换
            $price = $this->validateAndConvertPrice($price, 'price');
            $subPrice = $this->validateAndConvertPrice($subPrice, 'sub_price');

            // 检查除零情况
            if (bccomp($price, '0', PriceDeviationConfig::PRECISION) === 0) {
                Log::warning('促销价格偏差率计算: 原价为0，无法计算', [
                    'price' => $price,
                    'sub_price' => $subPrice
                ]);
                return null;
            }

            // 如果促销价为null或0，偏差率为100%
            if ($subPrice === null || bccomp($subPrice, '0', PriceDeviationConfig::PRECISION) === 0) {
                return 100.0;
            }

            // 计算: (price - sub_price) / price × 100
            $difference = bcsub($price, $subPrice, PriceDeviationConfig::PRECISION);
            $rate = bcdiv($difference, $price, PriceDeviationConfig::PRECISION);
            $percentage = bcmul($rate, PriceDeviationConfig::PERCENTAGE_MULTIPLIER, PriceDeviationConfig::PRECISION);

            $result = (float) $percentage;

            // 验证结果有效性
            if (!PriceDeviationConfig::isValidDeviationRate($result)) {
                Log::warning('促销价格偏差率超出有效范围', [
                    'price' => $price,
                    'sub_price' => $subPrice,
                    'deviation_rate' => $result,
                    'valid_range' => [PriceDeviationConfig::MIN_DEVIATION_RATE, PriceDeviationConfig::MAX_DEVIATION_RATE]
                ]);
            }

            // 检测异常值
            if ($this->monitor) {
                $anomalies = $this->monitor->detectAnomalies($result, null, $context);
                if (!empty($anomalies)) {
                    Log::warning('促销价格偏差率异常检测', [
                        'anomalies' => $anomalies,
                        'context' => $context
                    ]);
                }
            }

            Log::debug('促销价格偏差率计算成功', [
                'price' => $price,
                'sub_price' => $subPrice,
                'deviation_rate' => $result
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('促销价格偏差率计算失败', [
                'price' => $price ?? 'invalid',
                'sub_price' => $subPrice ?? 'invalid',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 计算渠道价格偏差率
     * 
     * 公式: (official_guide_price - sub_price) / official_guide_price × 100%
     * 
     * @param float|string $officialGuidePrice 官方指导价
     * @param float|string $subPrice 促销价/副价格
     * @return float|null 偏差率（百分比），null表示无法计算
     */
    public function calculateChannelDeviation($officialGuidePrice, $subPrice): ?float
    {
        $context = [
            'type' => 'channel_deviation',
            'official_guide_price' => $officialGuidePrice,
            'sub_price' => $subPrice
        ];

        // 如果有监控器，使用监控器执行计算
        if ($this->monitor) {
            return $this->monitor->monitorCalculation(function() use ($officialGuidePrice, $subPrice, $context) {
                return $this->doCalculateChannelDeviation($officialGuidePrice, $subPrice, $context);
            }, $context);
        }

        return $this->doCalculateChannelDeviation($officialGuidePrice, $subPrice, $context);
    }

    /**
     * 执行渠道价格偏差率计算
     */
    private function doCalculateChannelDeviation($officialGuidePrice, $subPrice, array $context): ?float
    {
        try {
            // 参数验证和转换
            $officialGuidePrice = $this->validateAndConvertPrice($officialGuidePrice, 'official_guide_price');
            $subPrice = $this->validateAndConvertPrice($subPrice, 'sub_price');

            // 检查官方指导价是否存在
            if ($officialGuidePrice === null) {
                Log::info('渠道价格偏差率计算: 官方指导价未设置');
                return null;
            }

            // 检查除零情况
            if (bccomp($officialGuidePrice, '0', PriceDeviationConfig::PRECISION) === 0) {
                Log::warning('渠道价格偏差率计算: 官方指导价为0，无法计算', [
                    'official_guide_price' => $officialGuidePrice,
                    'sub_price' => $subPrice
                ]);
                return null;
            }

            // 如果促销价为null或0，偏差率为100%
            if ($subPrice === null || bccomp($subPrice, '0', PriceDeviationConfig::PRECISION) === 0) {
                return 100.0;
            }

            // 计算: (official_guide_price - sub_price) / official_guide_price × 100
            $difference = bcsub($officialGuidePrice, $subPrice, PriceDeviationConfig::PRECISION);
            $rate = bcdiv($difference, $officialGuidePrice, PriceDeviationConfig::PRECISION);
            $percentage = bcmul($rate, PriceDeviationConfig::PERCENTAGE_MULTIPLIER, PriceDeviationConfig::PRECISION);

            $result = (float) $percentage;

            // 验证结果有效性
            if (!PriceDeviationConfig::isValidDeviationRate($result)) {
                Log::warning('渠道价格偏差率超出有效范围', [
                    'official_guide_price' => $officialGuidePrice,
                    'sub_price' => $subPrice,
                    'deviation_rate' => $result,
                    'valid_range' => [PriceDeviationConfig::MIN_DEVIATION_RATE, PriceDeviationConfig::MAX_DEVIATION_RATE]
                ]);
            }

            // 检测异常值
            if ($this->monitor) {
                $anomalies = $this->monitor->detectAnomalies(null, $result, $context);
                if (!empty($anomalies)) {
                    Log::warning('渠道价格偏差率异常检测', [
                        'anomalies' => $anomalies,
                        'context' => $context
                    ]);
                }
            }

            Log::debug('渠道价格偏差率计算成功', [
                'official_guide_price' => $officialGuidePrice,
                'sub_price' => $subPrice,
                'deviation_rate' => $result
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('渠道价格偏差率计算失败', [
                'official_guide_price' => $officialGuidePrice ?? 'invalid',
                'sub_price' => $subPrice ?? 'invalid',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 批量计算偏差率
     * 
     * @param array $priceData 价格数据数组，每个元素应包含必要的价格字段
     * @return array 计算结果数组
     */
    public function calculateBatchDeviations(array $priceData): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($priceData as $index => $data) {
            try {
                $result = [
                    'index' => $index,
                    'original_data' => $data,
                    'promotion_deviation_rate' => null,
                    'channel_deviation_rate' => null,
                    'calculation_status' => 'failed',
                    'error' => null
                ];

                // 计算促销价格偏差率
                if (isset($data['price']) && isset($data['sub_price'])) {
                    $result['promotion_deviation_rate'] = $this->calculatePromotionDeviation(
                        $data['price'],
                        $data['sub_price']
                    );
                }

                // 计算渠道价格偏差率
                if (isset($data['official_guide_price']) && isset($data['sub_price'])) {
                    $result['channel_deviation_rate'] = $this->calculateChannelDeviation(
                        $data['official_guide_price'],
                        $data['sub_price']
                    );
                }

                // 判断计算状态
                if ($result['promotion_deviation_rate'] !== null || $result['channel_deviation_rate'] !== null) {
                    $result['calculation_status'] = 'calculated';
                    $successCount++;
                } else {
                    $result['calculation_status'] = 'failed';
                    $result['error'] = '无法计算任何偏差率';
                    $failureCount++;
                }

                $results[] = $result;

            } catch (Exception $e) {
                $results[] = [
                    'index' => $index,
                    'original_data' => $data,
                    'promotion_deviation_rate' => null,
                    'channel_deviation_rate' => null,
                    'calculation_status' => 'failed',
                    'error' => $e->getMessage()
                ];
                $failureCount++;
            }
        }

        Log::info('批量偏差率计算完成', [
            'total' => count($priceData),
            'success' => $successCount,
            'failed' => $failureCount
        ]);

        return [
            'results' => $results,
            'summary' => [
                'total' => count($priceData),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'success_rate' => count($priceData) > 0 ? round($successCount / count($priceData) * 100, 2) : 0
            ]
        ];
    }

    /**
     * 验证价格参数并转换为字符串
     * 
     * @param mixed $price 价格值
     * @param string $fieldName 字段名称（用于错误日志）
     * @return string|null 验证后的价格字符串，null表示空值
     * @throws InvalidArgumentException 当价格值无效时
     */
    private function validateAndConvertPrice($price, string $fieldName): ?string
    {
        // null值处理
        if ($price === null) {
            return null;
        }

        // 空字符串处理
        if ($price === '' || $price === '0.00') {
            return '0';
        }

        // 数值转换
        if (is_numeric($price)) {
            $converted = (string) $price;
            
            // 验证转换后的值是否为有效数字
            if (!is_numeric($converted)) {
                throw new InvalidArgumentException("Invalid {$fieldName} value after conversion: {$converted}");
            }

            // 验证是否为负数
            if (bccomp($converted, '0', PriceDeviationConfig::PRECISION) < 0) {
                throw new InvalidArgumentException("Negative {$fieldName} value not allowed: {$converted}");
            }

            // 验证价格范围
            if (!PriceDeviationConfig::isValidPrice($converted)) {
                throw new InvalidArgumentException("Price {$fieldName} value out of valid range: {$converted}");
            }

            return $converted;
        }

        throw new InvalidArgumentException("Invalid {$fieldName} value: must be numeric, got " . gettype($price));
    }

    /**
     * 获取计算结果统计信息
     * 
     * @param array $results 批量计算结果
     * @return array 统计信息
     */
    public function getCalculationStats(array $results): array
    {
        $promotionDeviations = [];
        $channelDeviations = [];
        $calculatedCount = 0;

        foreach ($results['results'] as $result) {
            if ($result['calculation_status'] === 'calculated') {
                $calculatedCount++;
                
                if ($result['promotion_deviation_rate'] !== null) {
                    $promotionDeviations[] = $result['promotion_deviation_rate'];
                }
                
                if ($result['channel_deviation_rate'] !== null) {
                    $channelDeviations[] = $result['channel_deviation_rate'];
                }
            }
        }

        return [
            'total_calculated' => $calculatedCount,
            'promotion_deviation_stats' => $this->calculateStatistics($promotionDeviations),
            'channel_deviation_stats' => $this->calculateStatistics($channelDeviations)
        ];
    }

    /**
     * 计算数组的统计信息
     * 
     * @param array $values 数值数组
     * @return array 统计信息
     */
    private function calculateStatistics(array $values): array
    {
        if (empty($values)) {
            return [
                'count' => 0,
                'min' => null,
                'max' => null,
                'avg' => null
            ];
        }

        return [
            'count' => count($values),
            'min' => min($values),
            'max' => max($values),
            'avg' => round(array_sum($values) / count($values), 2)
        ];
    }
} 
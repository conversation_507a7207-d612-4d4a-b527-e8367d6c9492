<!-- 侧边栏导航 -->
<nav id="sidebar">
    <div class="sidebar-header">
        <h3><i class="fas fa-chart-line me-2"></i> 商品监控平台</h3>
        <button type="button" id="sidebarCollapse" class="btn btn-sm btn-outline-light">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <ul class="list-unstyled components">
        <!-- 首页 -->
        <li class="{{ request()->routeIs('dashboard') ? 'active' : '' }}">
            <a href="{{ route('dashboard') }}">
                <i class="fas fa-home me-2"></i>
                <span class="nav-text">首页</span>
            </a>
        </li>

        <!-- 商品监控 -->
        <li class="{{ request()->routeIs('products.*') ? 'active' : '' }}">
            <a href="{{ route('products.index') }}">
                <i class="fas fa-box me-2"></i>
                <span class="nav-text">商品监控</span>
            </a>
        </li>

        <!-- 数据分析 -->
        <li class="{{ request()->routeIs('analytics.*') ? 'active' : '' }}">
            <a href="{{ route('analytics.index') }}">
                <i class="fas fa-chart-area me-2"></i>
                <span class="nav-text">数据分析</span>
            </a>
        </li>

        <!-- 用户管理 (仅管理员可见) -->
        @can('manage_users')
        <li class="{{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
            <a href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-users me-2"></i>
                <span class="nav-text">用户管理</span>
            </a>
            <ul class="collapse list-unstyled {{ request()->routeIs('admin.users.*') ? 'show' : '' }}" id="userSubmenu">
                <li><a href="{{ route('admin.users.index') }}">用户列表</a></li>
                <li><a href="{{ route('admin.roles.index') }}">角色管理</a></li>
            </ul>
        </li>
        @endcan

        <!-- 任务管理 -->
        <li class="{{ request()->routeIs('task-groups.*') ? 'active' : '' }}">
            <a href="#taskSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-tasks me-2"></i>
                <span class="nav-text">任务管理</span>
            </a>
            <ul class="collapse list-unstyled {{ request()->routeIs('task-groups.*') ? 'show' : '' }}" id="taskSubmenu">
                <li><a href="{{ route('task-groups.index') }}">任务分组</a></li>
            </ul>
        </li>

        <!-- 数据源管理 -->
        <li class="{{ request()->routeIs('data-sources.*') ? 'active' : '' }}">
            <a href="#dataSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-database me-2"></i>
                <span class="nav-text">数据源管理</span>
            </a>
            <ul class="collapse list-unstyled {{ request()->routeIs('data-sources.*') ? 'show' : '' }}" id="dataSubmenu">
                <li><a href="{{ route('data-sources.index') }}">数据源列表</a></li>
                <li><a href="{{ route('data-sources.create') }}">添加数据源</a></li>
                <li><a href="{{ route('data-sources.excel-create') }}">Excel导入</a></li>
                <li><a href="{{ route('data-sources.import-history') }}">导入历史</a></li>
            </ul>
        </li>

        <!-- 预警管理 -->
        <li class="{{ request()->routeIs('alert-rules.*') ? 'active' : '' }}">
            <a href="#alertSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-bell me-2"></i>
                <span class="nav-text">预警管理</span>
            </a>
            <ul class="collapse list-unstyled {{ request()->routeIs('alert-rules.*') ? 'show' : '' }}" id="alertSubmenu">
                <li><a href="{{ route('alert-rules.index') }}">预警规则</a></li>
                <li><a href="{{ route('alert-rules.create') }}">新建规则</a></li>
            </ul>
        </li>

        <!-- 竞品分析 -->
        <li class="{{ request()->routeIs('competitors.*') ? 'active' : '' }}">
            <a href="{{ route('competitors.index') }}">
                <i class="fas fa-search me-2"></i>
                <span class="nav-text">竞品分析</span>
            </a>
        </li>

        <!-- 系统管理 (仅管理员可见) -->
        @can('manage_system')
        <li class="{{ request()->routeIs('api-configurations.*', 'monitoring.*') ? 'active' : '' }}">
            <a href="#systemSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-cog me-2"></i>
                <span class="nav-text">系统管理</span>
            </a>
            <ul class="collapse list-unstyled {{ request()->routeIs('api-configurations.*', 'monitoring.*') ? 'show' : '' }}" id="systemSubmenu">
                <li><a href="{{ route('api-configurations.index') }}">API配置</a></li>
                <li><a href="{{ route('monitoring.dashboard') }}">系统监控</a></li>
            </ul>
        </li>
        @endcan
    </ul>

    <!-- 底部用户操作 -->
    <ul class="list-unstyled CTAs">
        <li>
            <a href="{{ route('profile.index') }}" class="download">
                <i class="fas fa-user me-2"></i>
                <span class="nav-text">个人中心</span>
            </a>
        </li>
        <li>
            <a href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="article">
                <i class="fas fa-sign-out-alt me-2"></i>
                <span class="nav-text">退出登录</span>
            </a>
            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                @csrf
            </form>
        </li>
    </ul>
</nav>
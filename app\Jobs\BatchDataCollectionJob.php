<?php

namespace App\Jobs;

use App\Services\SchedulingService;
use App\Services\PerformanceMonitoringService;
use App\Models\MonitorTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use Exception;

/**
 * 批量数据收集任务
 * 
 * 用于批量执行多个相关的数据收集任务
 * 支持性能监控和智能并发控制
 */
class BatchDataCollectionJob extends RetryableJob
{
    /**
     * 批次ID
     *
     * @var string
     */
    private $batchId;

    /**
     * 任务ID列表
     *
     * @var array
     */
    private $taskIds;

    /**
     * 批量执行配置
     *
     * @var array
     */
    private $batchConfig;

    /**
     * 性能监控服务
     *
     * @var PerformanceMonitoringService|null
     */
    protected $performanceMonitor;

    /**
     * 构造函数
     *
     * @param array $taskIds 任务ID列表
     * @param array $batchConfig 批量配置
     * @param array $retryConfig 重试配置
     */
    public function __construct(array $taskIds, array $batchConfig = [], array $retryConfig = [])
    {
        $this->batchId = 'batch_' . uniqid();
        $this->taskIds = $taskIds;
        $this->batchConfig = array_merge($this->getDefaultBatchConfig(), $batchConfig);
        
        parent::__construct([
            'batch_id' => $this->batchId,
            'task_ids' => $taskIds,
            'batch_config' => $this->batchConfig
        ], $retryConfig);

        // 设置队列名称
        $this->onQueue($this->batchConfig['queue_name'] ?? 'batch_collection');
    }

    /**
     * 执行具体的任务逻辑
     *
     * @return mixed
     * @throws Exception
     */
    protected function executeJob()
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // 获取性能监控服务
        $this->performanceMonitor = app(PerformanceMonitoringService::class);
        
        // 记录批量作业开始
        $this->recordBatchStart($startTime, $startMemory);
        
        Log::info('开始执行批量数据收集任务', [
            'batch_id' => $this->batchId,
            'task_count' => count($this->taskIds),
            'batch_config' => $this->batchConfig,
            'memory_start' => $this->formatBytes($startMemory)
        ]);

        // 检查批量并发限制
        $this->checkBatchConcurrencyLimits();

        $schedulingService = new SchedulingService();
        $results = [];
        $successCount = 0;
        $failureCount = 0;
        $taskExecutionTimes = [];

        try {
            // 获取任务并按优先级排序
            $tasks = $this->getTasksWithPriority();
            
            foreach ($tasks as $index => $task) {
                $taskStartTime = microtime(true);
                
                try {
                    Log::debug('执行批量任务中的单个任务', [
                        'batch_id' => $this->batchId,
                        'task_id' => $task->id,
                        'platform' => $task->platform,
                        'progress' => ($index + 1) . '/' . count($tasks)
                    ]);

                    // 检查任务间延迟和平台限流
                    $this->applyTaskDelay($task, $index);

                    // 检查单个任务的并发限制
                    $this->checkTaskConcurrencyLimits($task);

                    // 执行任务
                    $schedulingService->executeDataCollectionTask($task);
                    
                    $taskExecutionTime = round(microtime(true) - $taskStartTime, 2);
                    $taskExecutionTimes[] = $taskExecutionTime;
                    
                    $results[$task->id] = [
                        'status' => 'success',
                        'executed_at' => now()->toISOString(),
                        'execution_time' => $taskExecutionTime,
                        'task_name' => $task->task_name,
                        'platform' => $task->platform,
                        'memory_usage' => memory_get_usage(true)
                    ];
                    
                    $successCount++;
                    
                    // 记录任务性能指标
                    $this->recordTaskMetrics($task, $taskExecutionTime, true);
                    
                    Log::debug('批量任务中的单个任务执行成功', [
                        'batch_id' => $this->batchId,
                        'task_id' => $task->id,
                        'execution_time' => $taskExecutionTime
                    ]);

                } catch (Exception $e) {
                    $taskExecutionTime = round(microtime(true) - $taskStartTime, 2);
                    $taskExecutionTimes[] = $taskExecutionTime;
                    
                    $results[$task->id] = [
                        'status' => 'failed',
                        'error' => $e->getMessage(),
                        'executed_at' => now()->toISOString(),
                        'execution_time' => $taskExecutionTime,
                        'memory_usage' => memory_get_usage(true)
                    ];
                    
                    $failureCount++;
                    
                    // 记录任务性能指标
                    $this->recordTaskMetrics($task, $taskExecutionTime, false);
                    
                    Log::error('批量任务中的单个任务执行失败', [
                        'batch_id' => $this->batchId,
                        'task_id' => $task->id,
                        'error' => $e->getMessage(),
                        'execution_time' => $taskExecutionTime
                    ]);

                    // 根据配置决定是否继续执行
                    if (!$this->batchConfig['continue_on_failure']) {
                        throw new Exception("批量任务在任务ID {$task->id} 处失败，停止执行: " . $e->getMessage());
                    }
                }
                
                // 检查内存使用情况
                $this->checkMemoryUsage();
            }

            // 计算批量作业总体性能指标
            $totalExecutionTime = round(microtime(true) - $startTime, 2);
            $endMemory = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);

            // 记录批量执行结果
            $batchResult = [
                'batch_id' => $this->batchId,
                'total_tasks' => count($this->taskIds),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'success_rate' => count($this->taskIds) > 0 ? ($successCount / count($this->taskIds)) * 100 : 0,
                'total_execution_time' => $totalExecutionTime,
                'average_task_time' => count($taskExecutionTimes) > 0 ? round(array_sum($taskExecutionTimes) / count($taskExecutionTimes), 2) : 0,
                'memory_usage' => [
                    'start' => $startMemory,
                    'end' => $endMemory,
                    'peak' => $peakMemory,
                    'formatted' => [
                        'start' => $this->formatBytes($startMemory),
                        'end' => $this->formatBytes($endMemory),
                        'peak' => $this->formatBytes($peakMemory)
                    ]
                ],
                'results' => $results,
                'completed_at' => now()->toISOString()
            ];

            // 记录批量作业性能指标
            $this->recordBatchMetrics($batchResult);

            Log::info('批量数据收集任务完成', $batchResult);

            // 检查是否满足最小成功率要求
            $minSuccessRate = $this->batchConfig['min_success_rate'] ?? 0;
            if ($batchResult['success_rate'] < $minSuccessRate) {
                throw new Exception("批量任务成功率 {$batchResult['success_rate']}% 低于要求的 {$minSuccessRate}%");
            }

            return $batchResult;

        } finally {
            // 释放批量并发控制
            $this->releaseBatchConcurrencyControl();
        }
    }

    /**
     * 检查批量并发限制
     *
     * @throws Exception
     */
    protected function checkBatchConcurrencyLimits()
    {
        $maxBatchConcurrency = $this->batchConfig['max_batch_concurrency'] ?? 3;
        $currentBatches = Redis::scard('batch_concurrency:global');
        
        if ($currentBatches >= $maxBatchConcurrency) {
            throw new Exception("批量作业并发限制达到上限: {$currentBatches}/{$maxBatchConcurrency}");
        }
        
        // 获取批量并发控制锁
        Redis::sadd('batch_concurrency:global', $this->batchId);
        Redis::expire('batch_concurrency:global', 3600); // 1小时过期
        
        Log::debug('批量并发检查通过', [
            'batch_id' => $this->batchId,
            'current_batches' => $currentBatches,
            'max_batches' => $maxBatchConcurrency
        ]);
    }

    /**
     * 检查单个任务的并发限制
     *
     * @param MonitorTask $task
     * @throws Exception
     */
    protected function checkTaskConcurrencyLimits(MonitorTask $task)
    {
        $platform = $task->platform;
        $platformKey = "concurrency:platform:{$platform}";
        $platformLimits = [
            'taobao' => 3,
            'tmall' => 3,
            'jd' => 2,
            'douyin' => 2,
            'kuaishou' => 1,
        ];
        
        $maxPlatformConcurrency = $platformLimits[$platform] ?? 2;
        $currentPlatform = Redis::scard($platformKey);
        
        if ($currentPlatform >= $maxPlatformConcurrency) {
            // 等待一段时间再重试
            sleep(rand(1, 3));
            $currentPlatform = Redis::scard($platformKey);
            
            if ($currentPlatform >= $maxPlatformConcurrency) {
                throw new Exception("平台 {$platform} 并发限制达到上限: {$currentPlatform}/{$maxPlatformConcurrency}");
            }
        }
    }

    /**
     * 应用任务间延迟
     *
     * @param MonitorTask $task
     * @param int $index
     */
    protected function applyTaskDelay(MonitorTask $task, int $index)
    {
        $baseDelay = $this->batchConfig['task_delay'] ?? 1;
        
        // 根据平台调整延迟
        $platformDelays = [
            'taobao' => 2,
            'tmall' => 2,
            'jd' => 3,
            'douyin' => 3,
            'kuaishou' => 5,
        ];
        
        $platformDelay = $platformDelays[$task->platform] ?? $baseDelay;
        
        // 如果不是第一个任务，则应用延迟
        if ($index > 0) {
            $actualDelay = $platformDelay + rand(0, 2); // 添加随机延迟避免同时执行
            sleep($actualDelay);
        }
    }

    /**
     * 检查内存使用情况
     */
    protected function checkMemoryUsage()
    {
        $currentMemory = memory_get_usage(true);
        $memoryLimit = $this->batchConfig['memory_limit'] ?? (128 * 1024 * 1024); // 128MB
        
        if ($currentMemory > $memoryLimit) {
            Log::warning('批量作业内存使用过高', [
                'batch_id' => $this->batchId,
                'current_memory' => $this->formatBytes($currentMemory),
                'memory_limit' => $this->formatBytes($memoryLimit)
            ]);
            
            // 强制垃圾回收
            gc_collect_cycles();
        }
    }

    /**
     * 获取带优先级的任务列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getTasksWithPriority()
    {
        return MonitorTask::whereIn('id', $this->taskIds)
                         ->where('status', 'active')
                         ->where('is_enabled', true)
                         ->orderByRaw("CASE 
                             WHEN platform = 'kuaishou' THEN 1
                             WHEN platform = 'douyin' THEN 2  
                             WHEN platform = 'jd' THEN 3
                             WHEN platform = 'tmall' THEN 4
                             WHEN platform = 'taobao' THEN 5
                             ELSE 6 END")
                         ->orderBy('last_executed_at', 'asc')
                         ->get();
    }

    /**
     * 记录批量作业开始
     *
     * @param float $startTime
     * @param int $startMemory
     */
    protected function recordBatchStart($startTime, $startMemory)
    {
        if ($this->performanceMonitor) {
            $this->performanceMonitor->recordBatchJobStart([
                'batch_id' => $this->batchId,
                'task_count' => count($this->taskIds),
                'started_at' => $startTime,
                'memory_start' => $startMemory,
                'config' => $this->batchConfig
            ]);
        }
    }

    /**
     * 记录单个任务性能指标
     *
     * @param MonitorTask $task
     * @param float $executionTime
     * @param bool $success
     */
    protected function recordTaskMetrics(MonitorTask $task, $executionTime, $success)
    {
        if (!$this->performanceMonitor) {
            return;
        }

        $this->performanceMonitor->recordBatchTaskMetrics([
            'batch_id' => $this->batchId,
            'task_id' => $task->id,
            'platform' => $task->platform,
            'execution_time' => $executionTime,
            'success' => $success,
            'memory_usage' => memory_get_usage(true),
            'timestamp' => time()
        ]);
    }

    /**
     * 记录批量作业性能指标
     *
     * @param array $batchResult
     */
    protected function recordBatchMetrics(array $batchResult)
    {
        if (!$this->performanceMonitor) {
            return;
        }

        $this->performanceMonitor->recordBatchJobMetrics([
            'batch_id' => $this->batchId,
            'total_tasks' => $batchResult['total_tasks'],
            'success_count' => $batchResult['success_count'],
            'failure_count' => $batchResult['failure_count'],
            'success_rate' => $batchResult['success_rate'],
            'total_execution_time' => $batchResult['total_execution_time'],
            'average_task_time' => $batchResult['average_task_time'],
            'memory_metrics' => $batchResult['memory_usage'],
            'timestamp' => time()
        ]);

        // 记录性能警告
        if ($batchResult['success_rate'] < 80) {
            $this->performanceMonitor->recordAlert([
                'type' => 'low_batch_success_rate',
                'severity' => 'warning',
                'batch_id' => $this->batchId,
                'success_rate' => $batchResult['success_rate'],
                'message' => "批量作业成功率较低: {$batchResult['success_rate']}%"
            ]);
        }

        if ($batchResult['total_execution_time'] > 1800) { // 30分钟
            $this->performanceMonitor->recordAlert([
                'type' => 'long_running_batch',
                'severity' => 'warning',
                'batch_id' => $this->batchId,
                'execution_time' => $batchResult['total_execution_time'],
                'message' => "批量作业执行时间过长: {$batchResult['total_execution_time']}秒"
            ]);
        }
    }

    /**
     * 释放批量并发控制
     */
    protected function releaseBatchConcurrencyControl()
    {
        Redis::srem('batch_concurrency:global', $this->batchId);
    }

    /**
     * 获取默认批量配置
     *
     * @return array
     */
    private function getDefaultBatchConfig(): array
    {
        return [
            'continue_on_failure' => true,      // 单个任务失败时是否继续执行
            'task_delay' => 1,                  // 任务间延迟（秒）
            'min_success_rate' => 0,            // 最小成功率（百分比）
            'queue_name' => 'batch_collection', // 队列名称
            'timeout' => 1800,                  // 批量任务超时时间（秒）
            'max_batch_concurrency' => 3,      // 最大批量并发数
            'memory_limit' => 128 * 1024 * 1024, // 内存限制（字节）
        ];
    }

    /**
     * 格式化字节
     *
     * @param int $bytes
     * @return string
     */
    protected function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 创建批量数据收集任务
     *
     * @param array $taskIds 任务ID列表
     * @param array $batchConfig 批量配置
     * @param array $retryConfig 重试配置
     * @return static
     */
    public static function create(array $taskIds, array $batchConfig = [], array $retryConfig = []): self
    {
        return new static($taskIds, $batchConfig, $retryConfig);
    }

    /**
     * 根据平台创建批量任务
     *
     * @param string $platform 平台名称
     * @param array $batchConfig 批量配置
     * @param array $retryConfig 重试配置
     * @return static
     */
    public static function createByPlatform(string $platform, array $batchConfig = [], array $retryConfig = []): self
    {
        $taskIds = MonitorTask::where('platform', $platform)
                             ->where('status', 'active')
                             ->where('is_enabled', true)
                             ->pluck('id')
                             ->toArray();

        if (empty($taskIds)) {
            throw new Exception("平台 {$platform} 没有可执行的任务");
        }

        return new static($taskIds, $batchConfig, $retryConfig);
    }

    /**
     * 根据标签创建批量任务
     *
     * @param string $tag 任务标签
     * @param array $batchConfig 批量配置
     * @param array $retryConfig 重试配置
     * @return static
     */
    public static function createByTag(string $tag, array $batchConfig = [], array $retryConfig = []): self
    {
        $taskIds = MonitorTask::whereJsonContains('tags', $tag)
                             ->where('status', 'active')
                             ->where('is_enabled', true)
                             ->pluck('id')
                             ->toArray();

        if (empty($taskIds)) {
            throw new Exception("标签 {$tag} 没有可执行的任务");
        }

        return new static($taskIds, $batchConfig, $retryConfig);
    }

    /**
     * 获取批次ID
     *
     * @return string
     */
    public function getBatchId(): string
    {
        return $this->batchId;
    }

    /**
     * 获取任务ID列表
     *
     * @return array
     */
    public function getTaskIds(): array
    {
        return $this->taskIds;
    }

    /**
     * 获取批量配置
     *
     * @return array
     */
    public function getBatchConfig(): array
    {
        return $this->batchConfig;
    }
} 
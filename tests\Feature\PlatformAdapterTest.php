<?php

namespace Tests\Feature;

use App\Models\ApiConfiguration;
use App\Services\DataCollection\PlatformAdapterFactory;
use App\Services\DataCollection\AdapterManagerService;
use App\Services\DataCollection\Adapters\TaobaoAdapter;
use App\Services\DataCollection\Adapters\JdAdapter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PlatformAdapterTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_platform_adapter_factory_supports_multiple_platforms()
    {
        $supportedPlatforms = PlatformAdapterFactory::getSupportedPlatforms();
        
        $this->assertContains('taobao', $supportedPlatforms);
        $this->assertContains('jd', $supportedPlatforms);
    }

    public function test_can_create_taobao_adapter()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        
        $this->assertInstanceOf(TaobaoAdapter::class, $adapter);
        $this->assertEquals('taobao', $adapter->getPlatformName());
    }

    public function test_can_create_jd_adapter()
    {
        $adapter = PlatformAdapterFactory::create('jd');
        
        $this->assertInstanceOf(JdAdapter::class, $adapter);
        $this->assertEquals('jd', $adapter->getPlatformName());
    }

    public function test_adapter_has_required_methods()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        
        $this->assertTrue(method_exists($adapter, 'getItemDetail'));
        $this->assertTrue(method_exists($adapter, 'getSimilarProducts'));
        $this->assertTrue(method_exists($adapter, 'getShopItems'));
        $this->assertTrue(method_exists($adapter, 'searchItems'));
        $this->assertTrue(method_exists($adapter, 'testConnection'));
        $this->assertTrue(method_exists($adapter, 'getSupportedFeatures'));
        $this->assertTrue(method_exists($adapter, 'getApiVersion'));
        $this->assertTrue(method_exists($adapter, 'performHealthCheck'));
        $this->assertTrue(method_exists($adapter, 'getStatistics'));
    }

    public function test_adapter_supported_features()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        $features = $adapter->getSupportedFeatures();
        
        $this->assertIsArray($features);
        $this->assertArrayHasKey('item_detail', $features);
        $this->assertArrayHasKey('similar_products', $features);
        $this->assertArrayHasKey('shop_items', $features);
        $this->assertArrayHasKey('search_items', $features);
        $this->assertArrayHasKey('health_check', $features);
    }

    public function test_adapter_api_version()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        $version = $adapter->getApiVersion();
        
        $this->assertIsString($version);
        $this->assertNotEmpty($version);
    }

    public function test_adapter_statistics()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        $statistics = $adapter->getStatistics();
        
        $this->assertIsArray($statistics);
        $this->assertArrayHasKey('platform', $statistics);
        $this->assertArrayHasKey('requests_made', $statistics);
        $this->assertArrayHasKey('successful_requests', $statistics);
        $this->assertArrayHasKey('failed_requests', $statistics);
        $this->assertEquals('taobao', $statistics['platform']);
    }

    public function test_adapter_health_check()
    {
        $adapter = PlatformAdapterFactory::create('taobao');
        $healthCheck = $adapter->performHealthCheck();
        
        $this->assertIsArray($healthCheck);
        $this->assertArrayHasKey('status', $healthCheck);
        $this->assertArrayHasKey('platform', $healthCheck);
        $this->assertArrayHasKey('timestamp', $healthCheck);
        $this->assertEquals('taobao', $healthCheck['platform']);
        $this->assertContains($healthCheck['status'], ['healthy', 'unhealthy']);
    }

    public function test_can_create_adapter_from_api_configuration()
    {
        $apiConfig = ApiConfiguration::create([
            'name' => 'Test Taobao API',
            'slug' => 'test-taobao',
            'platform_type' => 'taobao',
            'base_url' => 'http://test-api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'is_active' => true,
        ]);

        $adapter = PlatformAdapterFactory::createFromApiConfiguration($apiConfig);
        
        $this->assertInstanceOf(TaobaoAdapter::class, $adapter);
        $this->assertEquals('taobao', $adapter->getPlatformName());
        $this->assertEquals($apiConfig->id, $adapter->getApiConfiguration()->id);
    }

    public function test_adapter_manager_service()
    {
        // 创建API配置
        $apiConfig = ApiConfiguration::create([
            'name' => 'Test Taobao API',
            'slug' => 'test-taobao',
            'platform_type' => 'taobao',
            'base_url' => 'http://test-api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'is_active' => true,
        ]);

        $adapterManager = new AdapterManagerService();
        
        // 测试获取适配器
        $adapter = $adapterManager->getAdapter('taobao');
        $this->assertInstanceOf(TaobaoAdapter::class, $adapter);
        
        // 测试获取所有适配器
        $adapters = $adapterManager->getAllAdapters();
        $this->assertArrayHasKey('taobao', $adapters);
        
        // 测试健康检查
        $healthResults = $adapterManager->performHealthCheckAll();
        $this->assertArrayHasKey('taobao', $healthResults);
        
        // 测试统计信息
        $statistics = $adapterManager->getStatisticsAll();
        $this->assertArrayHasKey('taobao', $statistics);
        
        // 测试性能报告
        $report = $adapterManager->getPerformanceReport();
        $this->assertArrayHasKey('platforms', $report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('taobao', $report['platforms']);
    }

    public function test_adapter_manager_batch_tasks()
    {
        // 创建API配置
        $apiConfig = ApiConfiguration::create([
            'name' => 'Test Taobao API',
            'slug' => 'test-taobao',
            'platform_type' => 'taobao',
            'base_url' => 'http://test-api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'is_active' => true,
        ]);

        $adapterManager = new AdapterManagerService();
        
        $tasks = [
            [
                'platform' => 'taobao',
                'method' => 'getItemDetail',
                'params' => ['item_id' => '123456'],
            ],
        ];

        $results = $adapterManager->executeBatchTasks($tasks);
        
        $this->assertIsArray($results);
        $this->assertCount(1, $results);
        $this->assertArrayHasKey('status', $results[0]);
        $this->assertArrayHasKey('task', $results[0]);
    }

    public function test_platform_config_retrieval()
    {
        $config = PlatformAdapterFactory::getPlatformConfig('taobao');
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('adapter_class', $config);
        $this->assertArrayHasKey('base_url', $config);
        $this->assertArrayHasKey('timeout', $config);
        $this->assertArrayHasKey('retry_attempts', $config);
    }

    public function test_unsupported_platform_throws_exception()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('不支持的平台: unsupported_platform');
        
        PlatformAdapterFactory::create('unsupported_platform');
    }

    public function test_jd_adapter_specific_features()
    {
        $adapter = PlatformAdapterFactory::create('jd');
        $features = $adapter->getSupportedFeatures();
        
        // 京东适配器应该支持特有功能
        $this->assertArrayHasKey('price_history', $features);
        $this->assertArrayHasKey('promotion_info', $features);
        $this->assertTrue($features['price_history']);
        $this->assertTrue($features['promotion_info']);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}

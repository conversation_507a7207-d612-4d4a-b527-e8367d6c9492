<?php

namespace App\Services\DataCollection;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\HandlerStack;
use App\Services\RetryService;
use App\Services\PerformanceMonitoringService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class HttpClientService
{
    private Client $client;
    private array $config;
    private array $metrics;
    private RetryService $retryService;
    private ?PerformanceMonitoringService $performanceMonitor = null;

    public function __construct(array $config = [], ?PerformanceMonitoringService $performanceMonitor = null)
    {
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->metrics = [
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'total_response_time' => 0,
            'average_response_time' => 0,
        ];
        
        // 初始化重试服务
        $this->retryService = new RetryService($this->config['retry_config'] ?? []);
        
        // 初始化性能监控服务
        $this->performanceMonitor = $performanceMonitor;
        
        $this->initializeClient();
    }

    private function getDefaultConfig(): array
    {
        return [
            'timeout' => 15, // 减少超时时间以提高响应性
            'connect_timeout' => 5, // 减少连接超时时间
            'verify' => true,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'User-Agent' => 'E-commerce Monitor/1.0',
                'Accept-Encoding' => 'gzip, deflate', // 启用压缩
                'Connection' => 'keep-alive', // 启用连接复用
            ],
            // 连接池配置（优化性能）
            'pool_size' => 50, // 连接池大小
            'pool_max_idle' => 10, // 最大空闲连接数
            'pool_timeout' => 60, // 连接池超时时间
            // 重试配置（优化重试策略）
            'retry_config' => [
                'max_retries' => 2, // 减少重试次数以提高响应速度
                'base_delay' => 500, // 减少基础延迟
                'max_delay' => 10000, // 减少最大延迟
                'backoff_multiplier' => 1.5, // 减少退避倍数
                'jitter_factor' => 0.1,
            ],
            // 向后兼容的旧配置
            'retry_attempts' => 2,
            'retry_delay' => [0.5, 1, 2],
            // HTTP/2支持
            'version' => '2.0',
            // 缓存配置
            'cache_responses' => true,
            'cache_ttl' => 300, // 5分钟缓存
        ];
    }

    private function initializeClient(): void
    {
        // 创建处理器栈并添加重试中间件
        $stack = HandlerStack::create();
        $stack->push($this->retryService->createGuzzleRetryMiddleware());

        $this->client = new Client([
            'handler' => $stack,
            'timeout' => $this->config['timeout'],
            'connect_timeout' => $this->config['connect_timeout'],
            'verify' => $this->config['verify'],
            'headers' => $this->config['headers'],
        ]);
    }

    public function request(string $method, string $url, array $options = []): array
    {
        $startTime = microtime(true);
        $platform = $this->extractPlatformFromUrl($url);
        $endpoint = $this->extractEndpointFromUrl($url);
        
        // 检查缓存（如果启用）
        if ($this->config['cache_responses'] && $method === 'GET') {
            $cacheKey = 'http_response:' . md5($url . serialize($options));
            $cached = Cache::get($cacheKey);
            if ($cached) {
                $responseTime = (microtime(true) - $startTime) * 1000;
                $this->recordMetrics($startTime, true);
                $this->recordPerformanceMetrics($platform, $responseTime, true, $endpoint);
                
                Log::debug('HTTP请求命中缓存', [
                    'method' => $method,
                    'url' => $url,
                    'platform' => $platform,
                    'endpoint' => $endpoint,
                    'response_time' => round($responseTime, 2) . 'ms'
                ]);
                
                return $cached;
            }
        }
        
        // 应用速率限制
        if ($this->performanceMonitor && !$this->applyRateLimit($platform)) {
            throw new Exception("Rate limit exceeded for platform: {$platform}");
        }
        
        try {
            Log::debug('发起HTTP请求', [
                'method' => $method,
                'url' => $url,
                'platform' => $platform,
                'endpoint' => $endpoint,
                'options' => $options
            ]);

            // 使用重试服务执行请求
            $result = $this->retryService->execute(function() use ($method, $url, $options) {
                $response = $this->client->request($method, $url, $options);
                $body = $response->getBody()->getContents();
                
                $data = json_decode($body, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception('Invalid JSON response: ' . json_last_error_msg());
                }

                return $data;
            });

            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->recordMetrics($startTime, true);
            $this->recordPerformanceMetrics($platform, $responseTime, true, $endpoint);
            
            // 缓存成功的GET请求响应
            if ($this->config['cache_responses'] && $method === 'GET') {
                $cacheKey = 'http_response:' . md5($url . serialize($options));
                Cache::put($cacheKey, $result, $this->config['cache_ttl']);
            }
            
            Log::debug('HTTP请求成功', [
                'method' => $method,
                'url' => $url,
                'platform' => $platform,
                'endpoint' => $endpoint,
                'response_time' => round($responseTime, 2) . 'ms'
            ]);

            return $result;

        } catch (Exception $e) {
            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->recordMetrics($startTime, false);
            $this->recordPerformanceMetrics($platform, $responseTime, false, $endpoint);
            
            Log::error('HTTP请求失败', [
                'method' => $method,
                'url' => $url,
                'platform' => $platform,
                'endpoint' => $endpoint,
                'exception' => $e->getMessage(),
                'response_time' => round($responseTime, 2) . 'ms'
            ]);

            throw new Exception("HTTP request failed: " . $e->getMessage());
        }
    }

    public function get(string $url, array $options = []): array
    {
        return $this->request('GET', $url, $options);
    }

    public function post(string $url, array $data = [], array $options = []): array
    {
        if (!empty($data)) {
            $options['json'] = $data;
        }
        return $this->request('POST', $url, $options);
    }

    public function testConnection(string $url): array
    {
        try {
            $startTime = microtime(true);
            $response = $this->client->get($url, ['timeout' => 10]);
            $responseTime = (microtime(true) - $startTime) * 1000;

            return [
                'status' => 'success',
                'response_time' => round($responseTime, 2),
                'status_code' => $response->getStatusCode(),
                'error' => null,
            ];

        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'response_time' => null,
                'status_code' => null,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function getMetrics(): array
    {
        return $this->metrics;
    }

    /**
     * 获取重试统计信息
     *
     * @return array
     */
    public function getRetryStats(): array
    {
        return $this->retryService->getRetryStats(static::class);
    }

    /**
     * 清除重试统计信息
     *
     * @return void
     */
    public function clearRetryStats(): void
    {
        $this->retryService->clearRetryStats(static::class);
    }

    /**
     * 获取重试配置
     *
     * @return array
     */
    public function getRetryConfig(): array
    {
        return $this->retryService->getConfig();
    }

    /**
     * 更新重试配置
     *
     * @param array $config 新配置
     * @return void
     */
    public function updateRetryConfig(array $config): void
    {
        $this->retryService->updateConfig($config);
        // 重新初始化客户端以应用新配置
        $this->initializeClient();
    }

    /**
     * 使用重试机制执行自定义操作
     *
     * @param callable $operation 要执行的操作
     * @param array $retryOptions 重试选项
     * @return mixed
     */
    public function executeWithRetry(callable $operation, array $retryOptions = [])
    {
        return $this->retryService->execute($operation, $retryOptions);
    }

    private function recordMetrics(float $startTime, bool $success): void
    {
        $responseTime = (microtime(true) - $startTime) * 1000;
        
        $this->metrics['total_requests']++;
        $this->metrics['total_response_time'] += $responseTime;
        $this->metrics['average_response_time'] = $this->metrics['total_response_time'] / $this->metrics['total_requests'];
        
        if ($success) {
            $this->metrics['successful_requests']++;
        } else {
            $this->metrics['failed_requests']++;
        }
    }

    /**
     * 记录性能监控指标
     */
    private function recordPerformanceMetrics(string $platform, float $responseTime, bool $success, ?string $endpoint = null): void
    {
        if ($this->performanceMonitor) {
            $this->performanceMonitor->recordApiRequest($platform, $responseTime, $success, $endpoint);
        }
    }

    /**
     * 应用速率限制
     */
    private function applyRateLimit(string $platform): bool
    {
        if (!$this->performanceMonitor) {
            return true;
        }

        // 全局速率限制
        $globalKey = "global";
        if (!$this->performanceMonitor->applyRateLimit($globalKey, 120, 60)) { // 每分钟120请求
            return false;
        }
        
        if (!$this->performanceMonitor->applyRateLimit($globalKey, 5000, 3600)) { // 每小时5000请求
            return false;
        }

        // 平台特定速率限制
        $platformKey = "platform:{$platform}";
        if (!$this->performanceMonitor->applyRateLimit($platformKey, 60, 60)) { // 每分钟60请求
            return false;
        }

        return true;
    }

    /**
     * 从URL提取平台名称
     */
    private function extractPlatformFromUrl(string $url): string
    {
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'] ?? '';
        
        // 根据主机名或路径判断平台
        if (strpos($host, 'taobao') !== false || strpos($url, '/tb/') !== false) {
            return 'taobao';
        } elseif (strpos($host, 'jd') !== false) {
            return 'jd';
        } elseif (strpos($host, 'pdd') !== false || strpos($host, 'pinduoduo') !== false) {
            return 'pdd';
        } elseif (strpos($url, '/taobao/') !== false) {
            return 'taobao';
        }
        
        return 'unknown';
    }

    /**
     * 从URL提取端点信息
     */
    private function extractEndpointFromUrl(string $url): ?string
    {
        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'] ?? '';
        
        // 提取关键的端点信息
        if (strpos($path, '/item_detail') !== false) {
            return 'item_detail';
        } elseif (strpos($path, '/SimilarProduct') !== false) {
            return 'similar_products';
        } elseif (strpos($path, '/shop_item') !== false) {
            return 'shop_items';
        } elseif (strpos($path, '/search') !== false) {
            return 'search';
        }
        
        return $path;
    }

    /**
     * 获取性能统计信息
     */
    public function getPerformanceStats(): array
    {
        return [
            'local_metrics' => $this->getMetrics(),
            'retry_stats' => $this->getRetryStats(),
            'performance_monitor_available' => $this->performanceMonitor !== null,
        ];
    }

    /**
     * 设置性能监控服务
     */
    public function setPerformanceMonitor(PerformanceMonitoringService $performanceMonitor): void
    {
        $this->performanceMonitor = $performanceMonitor;
    }

    /**
     * 增加活跃任务计数（用于并发控制）
     */
    public function incrementActiveJobs(): void
    {
        Cache::increment('active_jobs_count', 1);
    }

    /**
     * 减少活跃任务计数（用于并发控制）
     */
    public function decrementActiveJobs(): void
    {
        $count = Cache::get('active_jobs_count', 0);
        if ($count > 0) {
            Cache::decrement('active_jobs_count', 1);
        }
    }

    /**
     * 增加处理任务计数（用于统计）
     */
    public function incrementProcessedJobs(): void
    {
        $cacheKey = 'processed_jobs:' . date('Y-m-d');
        Cache::increment($cacheKey, 1, 86400); // 缓存24小时
    }
} 
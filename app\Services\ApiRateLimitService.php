<?php

namespace App\Services;

use App\Models\ApiConfiguration;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ApiRateLimitService
{
    private const LIMITS = [
        'minute' => ['property' => 'rate_limit_per_minute', 'ttl' => 60],
        'hour' => ['property' => 'rate_limit_per_hour', 'ttl' => 3600],
        'day' => ['property' => 'rate_limit_per_day', 'ttl' => 86400],
    ];

    /**
     * Check if API call is within rate limits
     *
     * @param ApiConfiguration $config
     * @param string $identifier (optional custom identifier)
     * @return bool
     */
    public function isWithinLimit(ApiConfiguration $config, string $identifier = null): bool
    {
        $identifier = $identifier ?? $config->id;

        $limits = [
            'minute' => $config->rate_limit_per_minute,
            'hour' => $config->rate_limit_per_hour,
            'day' => $config->rate_limit_per_day,
        ];

        foreach ($limits as $period => $limit) {
            if ($limit > 0) {
                $cacheKey = "api_rate_limit:{$period}:{$identifier}";
                $currentCount = \Illuminate\Support\Facades\Cache::get($cacheKey, 0);
                if ($currentCount >= $limit) {
                    return false; // Exceeded limit
                }
            }
        }

        return true; // Within all limits
    }
    
    /**
     * Increment rate limit counters
     *
     * @param ApiConfiguration $config
     * @param string $identifier
     * @return void
     */
    public function incrementCounter(ApiConfiguration $config, string $identifier = null): void
    {
        $identifier = $identifier ?? $config->id;
        $now = now();

        if ($config->rate_limit_per_minute > 0) {
            $minuteKey = "api_rate_limit:minute:{$identifier}";
            $currentCount = \Illuminate\Support\Facades\Cache::get($minuteKey, 0);
            \Illuminate\Support\Facades\Cache::put($minuteKey, $currentCount + 1, 60);
        }

        if ($config->rate_limit_per_hour > 0) {
            $hourKey = "api_rate_limit:hour:{$identifier}";
            $ttl = 3600 - ($now->minute * 60 + $now->second);
            $currentCount = \Illuminate\Support\Facades\Cache::get($hourKey, 0);
            \Illuminate\Support\Facades\Cache::put($hourKey, $currentCount + 1, $ttl);
        }

        if ($config->rate_limit_per_day > 0) {
            $dayKey = "api_rate_limit:day:{$identifier}";
            $ttl = $now->copy()->endOfDay()->diffInSeconds($now);
            $currentCount = \Illuminate\Support\Facades\Cache::get($dayKey, 0);
            \Illuminate\Support\Facades\Cache::put($dayKey, $currentCount + 1, $ttl);
        }
    }
    
    /**
     * Get current usage for rate limits
     *
     * @param ApiConfiguration $config
     * @param string $identifier
     * @return array
     */
    public function getCurrentUsage(ApiConfiguration $config, string $identifier = null): array
    {
        $identifier = $identifier ?? $config->id;
        $usage = [];
        foreach (self::LIMITS as $period => $details) {
            $usage[$period] = $this->getPeriodUsage($config, $identifier, $period, $details['property']);
        }
        return $usage;
    }
    
    /**
     * Reset rate limit counters for a configuration
     *
     * @param ApiConfiguration $config
     * @param string $identifier
     * @return void
     */
    public function resetCounters(ApiConfiguration $config, string $identifier = null): void
    {
        $identifier = $identifier ?? $config->id;
        foreach (array_keys(self::LIMITS) as $period) {
            Cache::forget($this->getCacheKey($identifier, $period));
        }
        Log::info("Rate limit counters reset", ['config_id' => $config->id, 'config_name' => $config->name]);
    }
    
    /**
     * Get time until rate limit resets
     *
     * @param ApiConfiguration $config
     * @param string $identifier
     * @return array
     */
    public function getResetTimes(ApiConfiguration $config, string $identifier = null): array
    {
        $now = now();
        return [
            'minute_resets_in' => 60 - $now->second,
            'hour_resets_in' => (60 - $now->minute) * 60 - $now->second,
            'daily_resets_in' => $now->copy()->addDay()->startOfDay()->diffInSeconds($now)
        ];
    }

    /**
     * Check if a request can be made (alias for isWithinLimit)
     *
     * @param int $configId
     * @return bool
     */
    public function canMakeRequest(int $configId): bool
    {
        $config = ApiConfiguration::find($configId);
        return $config ? $this->isWithinLimit($config) : false;
    }

    /**
     * Record a request (alias for incrementCounter)
     *
     * @param int $configId
     * @return void
     */
    public function recordRequest(int $configId): void
    {
        if ($config = ApiConfiguration::find($configId)) {
            $this->incrementCounter($config);
        }
    }

    /**
     * Get remaining limits
     *
     * @param int $configId
     * @return array
     */
    public function getRemainingLimits(int $configId): array
    {
        $config = ApiConfiguration::find($configId);
        if (!$config) return [];

        $identifier = $config->id;
        $limits = [];

        if ($config->rate_limit_per_minute > 0) {
            $minuteKey = "api_rate_limit:minute:{$identifier}";
            $limits['per_minute'] = [
                'limit' => $config->rate_limit_per_minute,
                'remaining' => max(0, $config->rate_limit_per_minute - \Illuminate\Support\Facades\Cache::get($minuteKey, 0)),
            ];
        }

        if ($config->rate_limit_per_hour > 0) {
            $hourKey = "api_rate_limit:hour:{$identifier}";
            $limits['per_hour'] = [
                'limit' => $config->rate_limit_per_hour,
                'remaining' => max(0, $config->rate_limit_per_hour - \Illuminate\Support\Facades\Cache::get($hourKey, 0)),
            ];
        }

        if ($config->rate_limit_per_day > 0) {
            $dayKey = "api_rate_limit:day:{$identifier}";
            $limits['per_day'] = [
                'limit' => $config->rate_limit_per_day,
                'remaining' => max(0, $config->rate_limit_per_day - \Illuminate\Support\Facades\Cache::get($dayKey, 0)),
            ];
        }

        return $limits;
    }

    /**
     * Reset limits for a configuration
     *
     * @param int $configId
     * @return void
     */
    public function resetLimits(int $configId): void
    {
        if ($config = ApiConfiguration::find($configId)) {
            $identifier = $config->id;
            \Illuminate\Support\Facades\Cache::forget("api_rate_limit:minute:{$identifier}");
            \Illuminate\Support\Facades\Cache::forget("api_rate_limit:hour:{$identifier}");
            \Illuminate\Support\Facades\Cache::forget("api_rate_limit:day:{$identifier}");
        }
    }

    /**
     * Get usage statistics
     *
     * @param int $configId
     * @return array
     */
    public function getUsageStats(int $configId): array
    {
        $config = ApiConfiguration::find($configId);
        if (!$config) {
            return [
                'total_requests' => 0,
                'requests_per_minute' => 0,
                'requests_per_hour' => 0,
                'requests_per_day' => 0,
            ];
        }

        $identifier = $config->id;
        $minuteUsage = \Illuminate\Support\Facades\Cache::get("api_rate_limit:minute:{$identifier}", 0);
        $hourUsage = \Illuminate\Support\Facades\Cache::get("api_rate_limit:hour:{$identifier}", 0);
        $dayUsage = \Illuminate\Support\Facades\Cache::get("api_rate_limit:day:{$identifier}", 0);

        return [
            'total_requests' => $dayUsage, // Use day usage as total for current day
            'requests_per_minute' => $minuteUsage,
            'requests_per_hour' => $hourUsage,
            'requests_per_day' => $dayUsage,
        ];
    }

    // --- Private Helper Methods ---

    private function getCacheKey(string $identifier, string $period): string
    {
        $key = "api_rate_limit:{$period}:{$identifier}";
        Log::info("Generated cache key: {$key}");
        return $key;
    }

    private function checkPeriodLimit(ApiConfiguration $config, string $identifier, string $period, string $property): bool
    {
        $limit = $config->{$property};
        if ($limit <= 0) {
            return true;
        }

        $key = $this->getCacheKey($identifier, $period);
        $count = Cache::get($key, 0);

        if ($count >= $limit) {
            Log::warning("API rate limit exceeded ({$period})", [
                'config_id' => $config->id,
                'limit' => $limit,
                'current' => $count
            ]);
            return false;
        }
        return true;
    }
    
    private function incrementPeriodCounter(ApiConfiguration $config, string $identifier, string $period, string $property, int $ttl): void
    {
        if ($config->{$property} > 0) {
            $key = $this->getCacheKey($identifier, $period);
            Cache::remember($key, $ttl, fn() => 0);
            Cache::increment($key);
        }
    }

    private function getPeriodUsage(ApiConfiguration $config, string $identifier, string $period, string $property): array
    {
        $limit = $config->{$property} ?? 0;
        $current = Cache::get($this->getCacheKey($identifier, $period), 0);
        return [
            'current' => $current,
            'limit' => $limit,
            'remaining' => max(0, $limit - $current)
        ];
    }
    
    private function getDefaultLimits(): array
    {
        $limits = [];
        foreach (array_keys(self::LIMITS) as $period) {
            $limits[$period] = ['limit' => 0, 'remaining' => 0, 'used' => 0];
        }
        return $limits;
    }
} 
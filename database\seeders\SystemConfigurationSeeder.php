<?php

namespace Database\Seeders;

use App\Models\SystemConfiguration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configurations = [
            // 通用配置
            [
                'key' => 'system_name',
                'category' => 'general',
                'value' => '电商市场动态监控系统',
                'type' => 'string',
                'label' => '系统名称',
                'description' => '系统的显示名称',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'system_description',
                'category' => 'general',
                'value' => '专业的电商平台数据监控与分析系统',
                'type' => 'textarea',
                'label' => '系统描述',
                'description' => '系统的详细描述',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'default_monitoring_frequency',
                'category' => 'general',
                'value' => '60',
                'type' => 'integer',
                'label' => '默认监控频率（分钟）',
                'description' => '新建监控任务的默认执行频率',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:1440'],
                'sort_order' => 3,
            ],
            [
                'key' => 'data_retention_period',
                'category' => 'general',
                'value' => '365',
                'type' => 'integer',
                'label' => '数据保留期（天）',
                'description' => '监控数据的保留天数',
                'validation_rules' => ['required', 'integer', 'min:30', 'max:3650'],
                'sort_order' => 4,
            ],

            // 性能配置
            [
                'key' => 'concurrent_worker_count',
                'category' => 'performance',
                'value' => '4',
                'type' => 'integer',
                'label' => '并发工作进程数',
                'description' => '同时运行的数据收集工作进程数量',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:20'],
                'requires_restart' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'memory_limit',
                'category' => 'performance',
                'value' => '512M',
                'type' => 'select',
                'label' => '内存限制',
                'description' => 'PHP进程的内存使用限制',
                'options' => ['256M', '512M', '1G', '2G'],
                'requires_restart' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'database_connection_pool_size',
                'category' => 'performance',
                'value' => '10',
                'type' => 'integer',
                'label' => '数据库连接池大小',
                'description' => '数据库连接池的最大连接数',
                'validation_rules' => ['required', 'integer', 'min:5', 'max:100'],
                'requires_restart' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'cache_ttl',
                'category' => 'performance',
                'value' => '3600',
                'type' => 'integer',
                'label' => '缓存生存时间（秒）',
                'description' => '默认缓存数据的生存时间',
                'validation_rules' => ['required', 'integer', 'min:60', 'max:86400'],
                'sort_order' => 4,
            ],

            // 邮件配置
            [
                'key' => 'smtp_host',
                'category' => 'email',
                'value' => '',
                'type' => 'string',
                'label' => 'SMTP服务器',
                'description' => 'SMTP邮件服务器地址',
                'validation_rules' => ['required', 'string'],
                'sort_order' => 1,
            ],
            [
                'key' => 'smtp_port',
                'category' => 'email',
                'value' => '587',
                'type' => 'integer',
                'label' => 'SMTP端口',
                'description' => 'SMTP服务器端口号',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:65535'],
                'sort_order' => 2,
            ],
            [
                'key' => 'smtp_username',
                'category' => 'email',
                'value' => '',
                'type' => 'string',
                'label' => 'SMTP用户名',
                'description' => 'SMTP服务器登录用户名',
                'validation_rules' => ['required', 'string'],
                'sort_order' => 3,
            ],
            [
                'key' => 'smtp_password',
                'category' => 'email',
                'value' => '',
                'type' => 'password',
                'label' => 'SMTP密码',
                'description' => 'SMTP服务器登录密码',
                'validation_rules' => ['required', 'string'],
                'is_encrypted' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'smtp_encryption',
                'category' => 'email',
                'value' => 'tls',
                'type' => 'select',
                'label' => '加密方式',
                'description' => 'SMTP连接加密方式',
                'options' => ['tls', 'ssl', 'none'],
                'sort_order' => 5,
            ],
            [
                'key' => 'mail_from_address',
                'category' => 'email',
                'value' => '',
                'type' => 'string',
                'label' => '发件人邮箱',
                'description' => '系统邮件的发件人邮箱地址',
                'validation_rules' => ['required', 'email'],
                'sort_order' => 6,
            ],
            [
                'key' => 'mail_from_name',
                'category' => 'email',
                'value' => '电商监控系统',
                'type' => 'string',
                'label' => '发件人名称',
                'description' => '系统邮件的发件人显示名称',
                'validation_rules' => ['required', 'string'],
                'sort_order' => 7,
            ],

            // 监控配置
            [
                'key' => 'enable_health_monitoring',
                'category' => 'monitoring',
                'value' => 'true',
                'type' => 'boolean',
                'label' => '启用健康监控',
                'description' => '是否启用系统健康状态监控',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'health_check_interval',
                'category' => 'monitoring',
                'value' => '300',
                'type' => 'integer',
                'label' => '健康检查间隔（秒）',
                'description' => '系统健康检查的执行间隔',
                'validation_rules' => ['required', 'integer', 'min:60', 'max:3600'],
                'sort_order' => 2,
            ],
            [
                'key' => 'alert_email_recipients',
                'category' => 'monitoring',
                'value' => '',
                'type' => 'textarea',
                'label' => '告警邮件接收人',
                'description' => '接收系统告警邮件的邮箱地址，多个邮箱用换行分隔',
                'sort_order' => 3,
            ],

            // 安全配置
            [
                'key' => 'enable_api_rate_limiting',
                'category' => 'security',
                'value' => 'true',
                'type' => 'boolean',
                'label' => '启用API速率限制',
                'description' => '是否启用API接口的速率限制功能',
                'sort_order' => 1,
            ],
            [
                'key' => 'session_timeout',
                'category' => 'security',
                'value' => '7200',
                'type' => 'integer',
                'label' => '会话超时时间（秒）',
                'description' => '用户会话的超时时间',
                'validation_rules' => ['required', 'integer', 'min:300', 'max:86400'],
                'sort_order' => 2,
            ],
            [
                'key' => 'password_min_length',
                'category' => 'security',
                'value' => '8',
                'type' => 'integer',
                'label' => '密码最小长度',
                'description' => '用户密码的最小长度要求',
                'validation_rules' => ['required', 'integer', 'min:6', 'max:32'],
                'sort_order' => 3,
            ],
        ];

        foreach ($configurations as $config) {
            SystemConfiguration::updateOrCreate(
                ['key' => $config['key']],
                $config
            );
        }
    }
}

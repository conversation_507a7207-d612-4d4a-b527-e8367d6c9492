@extends('layouts.app')

@section('title', '响应式设计测试')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">响应式设计测试页面</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') ?? '/' }}">首页</a></li>
                        <li class="breadcrumb-item active">响应式测试</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 响应式网格测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">响应式网格系统测试</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6>大屏: 3列</h6>
                                    <p class="mb-0">中屏: 2列<br>小屏: 1列</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6>响应式卡片</h6>
                                    <p class="mb-0">自适应布局</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6>移动优先</h6>
                                    <p class="mb-0">触摸友好</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6>跨设备</h6>
                                    <p class="mb-0">一致体验</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 响应式表格测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">响应式表格测试</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>产品名称</th>
                                    <th class="d-none-mobile">价格</th>
                                    <th class="d-none-mobile">库存</th>
                                    <th class="d-none-mobile">状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td data-label="ID">1</td>
                                    <td data-label="产品名称">iPhone 15 Pro</td>
                                    <td data-label="价格" class="d-none-mobile">¥8,999</td>
                                    <td data-label="库存" class="d-none-mobile">50</td>
                                    <td data-label="状态" class="d-none-mobile">
                                        <span class="badge bg-success">有库存</span>
                                    </td>
                                    <td data-label="操作">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td data-label="ID">2</td>
                                    <td data-label="产品名称">Samsung Galaxy S24</td>
                                    <td data-label="价格" class="d-none-mobile">¥7,999</td>
                                    <td data-label="库存" class="d-none-mobile">30</td>
                                    <td data-label="状态" class="d-none-mobile">
                                        <span class="badge bg-warning">库存不足</span>
                                    </td>
                                    <td data-label="操作">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td data-label="ID">3</td>
                                    <td data-label="产品名称">MacBook Pro 16"</td>
                                    <td data-label="价格" class="d-none-mobile">¥18,999</td>
                                    <td data-label="库存" class="d-none-mobile">0</td>
                                    <td data-label="状态" class="d-none-mobile">
                                        <span class="badge bg-danger">缺货</span>
                                    </td>
                                    <td data-label="操作">
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 响应式表单测试 -->
    <div class="row mb-4">
        <div class="col-lg-8 col-md-10 col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">响应式表单测试</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="productName" class="form-label">产品名称</label>
                                <input type="text" class="form-control" id="productName" placeholder="请输入产品名称">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="productPrice" class="form-label">价格</label>
                                <input type="number" class="form-control" id="productPrice" placeholder="0.00">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">分类</label>
                                <select class="form-select" id="category">
                                    <option selected>选择分类</option>
                                    <option value="1">电子产品</option>
                                    <option value="2">服装</option>
                                    <option value="3">家居</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="stock" class="form-label">库存</label>
                                <input type="number" class="form-control" id="stock" placeholder="0">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status">
                                    <option value="active">启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" rows="3" placeholder="请输入产品描述"></textarea>
                        </div>
                        <div class="d-flex flex-column flex-md-row gap-2">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <button type="button" class="btn btn-secondary">取消</button>
                            <button type="button" class="btn btn-outline-danger">删除</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 侧边信息卡片 -->
        <div class="col-lg-4 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">设备信息</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>屏幕宽度:</strong>
                        <span id="screenWidth">-</span>px
                    </div>
                    <div class="mb-2">
                        <strong>视口宽度:</strong>
                        <span id="viewportWidth">-</span>px
                    </div>
                    <div class="mb-2">
                        <strong>设备类型:</strong>
                        <span id="deviceType">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>用户代理:</strong>
                        <small id="userAgent" class="text-muted">-</small>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">响应式断点</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-primary">XS</span> < 576px
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary">SM</span> ≥ 576px
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-success">MD</span> ≥ 768px
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info">LG</span> ≥ 992px
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">XL</span> ≥ 1200px
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-danger">XXL</span> ≥ 1400px
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    function updateDeviceInfo() {
        document.getElementById('screenWidth').textContent = screen.width;
        document.getElementById('viewportWidth').textContent = window.innerWidth;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        let deviceType = 'Desktop';
        if (window.innerWidth < 576) {
            deviceType = 'Mobile (XS)';
        } else if (window.innerWidth < 768) {
            deviceType = 'Mobile (SM)';
        } else if (window.innerWidth < 992) {
            deviceType = 'Tablet (MD)';
        } else if (window.innerWidth < 1200) {
            deviceType = 'Desktop (LG)';
        } else if (window.innerWidth < 1400) {
            deviceType = 'Desktop (XL)';
        } else {
            deviceType = 'Desktop (XXL)';
        }
        
        document.getElementById('deviceType').textContent = deviceType;
    }
    
    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
});
</script>
@endsection

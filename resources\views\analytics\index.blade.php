@extends('layouts.app')

@section('title', '数据分析')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item active">数据分析</li>
@endsection

@section('page-title', '数据分析中心')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="refreshAnalytics()">
            <i class="fas fa-sync-alt me-1"></i>刷新数据
        </button>
        <button type="button" class="btn btn-success" onclick="exportAnalytics()">
            <i class="fas fa-download me-1"></i>导出报告
        </button>
        <div class="btn-group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-calendar me-1"></i>时间范围
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="setTimeRange('today')">今天</a></li>
                <li><a class="dropdown-item" href="#" onclick="setTimeRange('week')">本周</a></li>
                <li><a class="dropdown-item" href="#" onclick="setTimeRange('month')">本月</a></li>
                <li><a class="dropdown-item" href="#" onclick="setTimeRange('quarter')">本季度</a></li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
<div class="row">
    <!-- 左侧图表区域 -->
    <div class="col-lg-8">
        <!-- 类目分布热力图 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-2"></i>商品类目分布热力图
                </h6>
            </div>
            <div class="card-body">
                <canvas id="categoryHeatmapChart" height="100"></canvas>
            </div>
        </div>

        <!-- 价格区间分布 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-bar me-2"></i>价格区间分布
                </h6>
            </div>
            <div class="card-body">
                <canvas id="priceRangeChart" height="80"></canvas>
            </div>
        </div>

        <!-- 平台分布对比 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>平台分布对比
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="platformChart" height="120"></canvas>
                    </div>
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>平台</th>
                                        <th>商品数</th>
                                        <th>占比</th>
                                        <th>平均价格</th>
                                    </tr>
                                </thead>
                                <tbody id="platformStatsTable">
                                    <!-- 数据将通过JS填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧统计面板 -->
    <div class="col-lg-4">
        <!-- 类目TOP排行 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-trophy me-2"></i>热门类目TOP10
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush" id="topCategoriesList">
                    <!-- 数据将通过JS填充 -->
                </div>
            </div>
        </div>

        <!-- 价格统计 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calculator me-2"></i>价格统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <h4 class="text-success" id="avgPrice">¥0</h4>
                        <small class="text-muted">平均价格</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info" id="medianPrice">¥0</h4>
                        <small class="text-muted">中位数价格</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <h5 class="text-danger" id="maxPrice">¥0</h5>
                        <small class="text-muted">最高价格</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning" id="minPrice">¥0</h5>
                        <small class="text-muted">最低价格</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-heartbeat me-2"></i>监控状态
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>正常监控</span>
                    <span class="badge bg-success" id="normalCount">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>价格异常</span>
                    <span class="badge bg-warning" id="priceAbnormalCount">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>库存不足</span>
                    <span class="badge bg-danger" id="stockLowCount">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>已下架</span>
                    <span class="badge bg-secondary" id="offlineCount">0</span>
                </div>
            </div>
        </div>

        <!-- 数据更新时间 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clock me-2"></i>数据更新
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-2">
                        <i class="fas fa-sync-alt text-success fa-2x"></i>
                    </div>
                    <div class="text-muted">最后更新时间</div>
                    <div class="fw-bold" id="lastUpdateTime">--</div>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshAnalytics()">
                            <i class="fas fa-sync-alt me-1"></i>立即更新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
<script>
let categoryHeatmapChart, priceRangeChart, platformChart;
let currentTimeRange = 'month';

document.addEventListener('DOMContentLoaded', function() {
    initializeAnalyticsCharts();
    loadAnalyticsData();
});

// 初始化分析图表
function initializeAnalyticsCharts() {
    // 类目分布热力图（使用条形图模拟）
    const categoryHeatmapCtx = document.getElementById('categoryHeatmapChart').getContext('2d');
    categoryHeatmapChart = new Chart(categoryHeatmapCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '商品数量',
                data: [],
                backgroundColor: function(context) {
                    if (!context.parsed || context.parsed.y === undefined) {
                        return 'rgba(78, 115, 223, 0.5)';
                    }
                    const value = context.parsed.y;
                    const max = Math.max(...context.dataset.data.filter(d => d !== null && d !== undefined));
                    if (max === 0) return 'rgba(78, 115, 223, 0.3)';
                    const intensity = value / max;
                    return `rgba(78, 115, 223, ${0.3 + intensity * 0.7})`;
                },
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '商品数量'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '商品类目'
                    }
                }
            }
        }
    });

    // 价格区间分布
    const priceRangeCtx = document.getElementById('priceRangeChart').getContext('2d');
    priceRangeChart = new Chart(priceRangeCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '商品数量',
                data: [],
                backgroundColor: 'rgba(28, 200, 138, 0.8)',
                borderColor: 'rgba(28, 200, 138, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '价格区间'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '商品数量'
                    }
                }
            }
        }
    });

    // 平台分布图
    const platformCtx = document.getElementById('platformChart').getContext('2d');
    platformChart = new Chart(platformCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 加载分析数据
function loadAnalyticsData() {
    // 模拟API调用
    setTimeout(() => {
        // 类目分布数据
        const categoryData = {
            labels: ['数码电器', '服装鞋包', '家居用品', '美妆护肤', '食品饮料', '运动户外', '汽车用品', '母婴用品'],
            data: [456, 389, 267, 234, 189, 156, 123, 89]
        };
        updateCategoryHeatmap(categoryData);

        // 价格区间数据
        const priceRangeData = {
            labels: ['0-100', '100-500', '500-1000', '1000-2000', '2000-5000', '5000+'],
            data: [234, 567, 432, 298, 156, 67]
        };
        updatePriceRangeChart(priceRangeData);

        // 平台分布数据
        const platformData = {
            labels: ['天猫', '淘宝', '京东', '拼多多', '其他'],
            data: [1245, 876, 543, 321, 156],
            avgPrices: [1250, 980, 1180, 650, 890]
        };
        updatePlatformChart(platformData);

        // 更新统计数据
        updateAnalyticsStats({
            avgPrice: 1125,
            medianPrice: 980,
            maxPrice: 15680,
            minPrice: 25,
            normalCount: 2456,
            priceAbnormalCount: 89,
            stockLowCount: 34,
            offlineCount: 67
        });

        // 更新热门类目列表
        updateTopCategories(categoryData);

        // 更新时间
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString();
    }, 1000);
}

// 更新类目热力图
function updateCategoryHeatmap(data) {
    categoryHeatmapChart.data.labels = data.labels;
    categoryHeatmapChart.data.datasets[0].data = data.data;
    categoryHeatmapChart.update();
}

// 更新价格区间图表
function updatePriceRangeChart(data) {
    priceRangeChart.data.labels = data.labels;
    priceRangeChart.data.datasets[0].data = data.data;
    priceRangeChart.update();
}

// 更新平台分布图表
function updatePlatformChart(data) {
    platformChart.data.labels = data.labels;
    platformChart.data.datasets[0].data = data.data;
    platformChart.update();

    // 更新平台统计表格
    const tableBody = document.getElementById('platformStatsTable');
    tableBody.innerHTML = data.labels.map((label, index) => {
        const count = data.data[index];
        const total = data.data.reduce((a, b) => a + b, 0);
        const percentage = ((count / total) * 100).toFixed(1);
        const avgPrice = data.avgPrices[index];

        return `
            <tr>
                <td><span class="badge bg-primary">${label}</span></td>
                <td>${count.toLocaleString()}</td>
                <td>${percentage}%</td>
                <td>¥${avgPrice.toLocaleString()}</td>
            </tr>
        `;
    }).join('');
}

// 更新统计数据
function updateAnalyticsStats(stats) {
    document.getElementById('avgPrice').textContent = `¥${stats.avgPrice.toLocaleString()}`;
    document.getElementById('medianPrice').textContent = `¥${stats.medianPrice.toLocaleString()}`;
    document.getElementById('maxPrice').textContent = `¥${stats.maxPrice.toLocaleString()}`;
    document.getElementById('minPrice').textContent = `¥${stats.minPrice.toLocaleString()}`;

    document.getElementById('normalCount').textContent = stats.normalCount.toLocaleString();
    document.getElementById('priceAbnormalCount').textContent = stats.priceAbnormalCount.toLocaleString();
    document.getElementById('stockLowCount').textContent = stats.stockLowCount.toLocaleString();
    document.getElementById('offlineCount').textContent = stats.offlineCount.toLocaleString();
}

// 更新热门类目列表
function updateTopCategories(data) {
    const listContainer = document.getElementById('topCategoriesList');
    const sortedData = data.labels.map((label, index) => ({
        name: label,
        count: data.data[index]
    })).sort((a, b) => b.count - a.count);

    listContainer.innerHTML = sortedData.slice(0, 10).map((item, index) => `
        <div class="list-group-item d-flex justify-content-between align-items-center">
            <div>
                <span class="badge bg-secondary me-2">${index + 1}</span>
                ${item.name}
            </div>
            <span class="badge bg-primary">${item.count}</span>
        </div>
    `).join('');
}

// 设置时间范围
function setTimeRange(range) {
    currentTimeRange = range;
    loadAnalyticsData();

    // 更新按钮文本
    const rangeNames = {
        'today': '今天',
        'week': '本周',
        'month': '本月',
        'quarter': '本季度'
    };

    document.querySelector('[data-bs-toggle="dropdown"]').innerHTML =
        `<i class="fas fa-calendar me-1"></i>${rangeNames[range]}`;
}

// 刷新分析数据
function refreshAnalytics() {
    loadAnalyticsData();
}

// 导出分析报告
function exportAnalytics() {
    console.log('导出分析报告');
    alert('分析报告导出功能开发中...');
}
</script>
@endsection

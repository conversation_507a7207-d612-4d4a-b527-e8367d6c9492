<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 更新警报类型枚举，添加新的警报类型
            $table->string('type')->change()->comment('告警类型');
            
            // 添加新的字段以支持不同类型的警报
            $table->string('rule_title')->nullable()->after('rule_name')->comment('规则标题');
            $table->foreignId('product_id')->nullable()->after('task_id')
                  ->constrained('products')->onDelete('cascade')->comment('关联的产品ID');
            $table->foreignId('product_sku_id')->nullable()->after('product_id')
                  ->constrained('product_skus')->onDelete('cascade')->comment('关联的SKU ID');
            $table->foreignId('category_id')->nullable()->after('product_sku_id')
                  ->constrained('categories')->onDelete('set null')->comment('关联的分类ID');
            $table->string('channel_id')->nullable()->after('category_id')->comment('渠道ID');
            
            // 扩展阈值字段以支持更多类型
            $table->decimal('promotion_deviation_threshold', 5, 2)->nullable()->after('percentage_threshold')
                  ->comment('促销价偏差率阈值');
            $table->decimal('channel_deviation_threshold', 5, 2)->nullable()->after('promotion_deviation_threshold')
                  ->comment('渠道价偏差率阈值');
            $table->integer('inventory_threshold')->nullable()->after('channel_deviation_threshold')
                  ->comment('库存异常阈值');
            $table->integer('data_update_hours_threshold')->nullable()->after('inventory_threshold')
                  ->comment('数据更新异常阈值(小时)');
            
            // 添加状态变化相关字段
            $table->enum('status_change_type', ['on_shelf', 'off_shelf', 'any'])->nullable()
                  ->after('data_update_hours_threshold')->comment('状态变化类型');
            
            // 扩展条件配置
            $table->json('alert_parameters')->nullable()->after('conditions')
                  ->comment('警报参数配置(灵活存储各种参数)');
            
            // 添加规则适用范围
            $table->enum('scope', ['global', 'product', 'sku', 'category', 'brand'])->default('global')
                  ->after('alert_parameters')->comment('规则适用范围');
            
            // 添加新的索引
            $table->index(['product_id', 'is_active']);
            $table->index(['product_sku_id', 'is_active']);
            $table->index(['category_id', 'is_active']);
            $table->index(['scope', 'is_active']);
            $table->index(['status_change_type', 'is_active']);
        });
        
        // 在迁移后更新type字段的约束（如果需要）
        DB::statement("ALTER TABLE alert_rules MODIFY COLUMN type ENUM('promotion_price_deviation', 'channel_price_deviation', 'product_status_change', 'inventory_anomaly', 'data_update_anomaly', 'price_drop', 'price_rise', 'stock_change', 'custom') NOT NULL COMMENT '告警类型'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('alert_rules', function (Blueprint $table) {
            // 删除新添加的字段
            $table->dropForeign(['product_id']);
            $table->dropForeign(['product_sku_id']);
            $table->dropForeign(['category_id']);
            $table->dropColumn([
                'rule_title',
                'product_id',
                'product_sku_id',
                'category_id',
                'channel_id',
                'promotion_deviation_threshold',
                'channel_deviation_threshold',
                'inventory_threshold',
                'data_update_hours_threshold',
                'status_change_type',
                'alert_parameters',
                'scope'
            ]);
            
            // 删除新添加的索引
            $table->dropIndex(['product_id', 'is_active']);
            $table->dropIndex(['product_sku_id', 'is_active']);
            $table->dropIndex(['category_id', 'is_active']);
            $table->dropIndex(['scope', 'is_active']);
            $table->dropIndex(['status_change_type', 'is_active']);
        });
        
        // 恢复原始的type枚举
        DB::statement("ALTER TABLE alert_rules MODIFY COLUMN type ENUM('price_drop', 'price_rise', 'stock_change', 'custom') NOT NULL COMMENT '告警类型'");
    }
}; 
<?php

namespace Database\Factories;

use App\Models\ApiConfiguration;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ApiConfiguration>
 */
class ApiConfigurationFactory extends Factory
{
    protected $model = ApiConfiguration::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $platforms = ['shopify', 'magento', 'woocommerce', 'opencart', 'prestashop'];
        $authTypes = ['api_key', 'bearer_token', 'oauth', 'basic_auth'];
        $platform = $this->faker->randomElement($platforms);
        
        return [
            'name' => $this->faker->company . ' API',
            'slug' => $this->faker->unique()->slug,
            'description' => $this->faker->sentence,
            'platform_type' => $platform,
            'base_url' => $this->faker->url,
            'auth_type' => $this->faker->randomElement($authTypes),
            'auth_credentials' => ['key' => $this->faker->sha256],
            'is_active' => true,
            'rate_limit_per_minute' => $this->faker->numberBetween(100, 1000),
            'health_check_endpoint' => '/health'
        ];
    }

    /**
     * Generate a base URL based on platform type
     */
    private function generateBaseUrl(string $platform): string
    {
        return match ($platform) {
            'shopify' => 'https://' . $this->faker->domainWord . '.myshopify.com/admin/api/2023-01',
            'magento' => 'https://' . $this->faker->domainName . '/rest/V1',
            'woocommerce' => 'https://' . $this->faker->domainName . '/wp-json/wc/v3',
            'opencart' => 'https://' . $this->faker->domainName . '/index.php?route=api',
            'prestashop' => 'https://' . $this->faker->domainName . '/api',
            default => 'https://' . $this->faker->domainName . '/api/v1'
        };
    }

    /**
     * Generate auth credentials based on auth type
     */
    private function generateAuthCredentials(): array
    {
        $authTypes = ['api_key', 'bearer_token', 'oauth', 'basic_auth'];
        $authType = $this->faker->randomElement($authTypes);

        return match ($authType) {
            'api_key' => [
                'api_key' => $this->faker->sha256,
                'key_name' => $this->faker->randomElement(['X-API-Key', 'Authorization', 'X-Auth-Token'])
            ],
            'bearer_token' => [
                'token' => $this->faker->sha256
            ],
            'oauth' => [
                'client_id' => $this->faker->uuid,
                'client_secret' => $this->faker->sha256,
                'access_token' => $this->faker->sha256,
                'refresh_token' => $this->faker->sha256,
                'token_url' => 'https://' . $this->faker->domainName . '/oauth/token'
            ],
            'basic_auth' => [
                'username' => $this->faker->userName,
                'password' => $this->faker->password
            ],
            default => []
        };
    }

    /**
     * Generate health check endpoint based on platform
     */
    private function generateHealthCheckEndpoint(string $platform): string
    {
        return match ($platform) {
            'shopify' => '/admin/api/2023-01/shop.json',
            'magento' => '/rest/V1/store/storeConfigs',
            'woocommerce' => '/wp-json/wc/v3/system_status',
            'opencart' => '/index.php?route=api/login',
            'prestashop' => '/api/configurations',
            default => '/health'
        };
    }

    /**
     * Generate default headers
     */
    private function generateHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'E-commerce-Monitor/1.0'
        ];
    }

    /**
     * Generate custom configuration based on platform
     */
    private function generateCustomConfig(string $platform): array
    {
        return match ($platform) {
            'shopify' => [
                'api_version' => '2023-01',
                'webhook_verification' => true,
                'rate_limit_header' => 'X-Shopify-Shop-Api-Call-Limit'
            ],
            'magento' => [
                'store_code' => 'default',
                'currency_code' => 'USD',
                'integration_token' => $this->faker->sha256
            ],
            'woocommerce' => [
                'consumer_key' => $this->faker->bothify('ck_???????????????????'),
                'consumer_secret' => $this->faker->bothify('cs_???????????????????'),
                'wp_api' => true
            ],
            'opencart' => [
                'store_id' => 0,
                'language' => 'en-gb',
                'currency' => 'USD'
            ],
            'prestashop' => [
                'shop_id' => 1,
                'language_id' => 1,
                'output_format' => 'JSON'
            ],
            default => []
        };
    }

    /**
     * Indicate that the configuration is active
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'is_deprecated' => false,
        ]);
    }

    /**
     * Indicate that the configuration is inactive
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the configuration is deprecated
     */
    public function deprecated(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_deprecated' => true,
        ]);
    }

    /**
     * Set a specific platform type
     */
    public function platform(string $platform): static
    {
        return $this->state(fn (array $attributes) => [
            'platform_type' => $platform,
            'base_url' => $this->generateBaseUrl($platform),
            'health_check_endpoint' => $this->generateHealthCheckEndpoint($platform),
            'custom_config' => $this->generateCustomConfig($platform),
        ]);
    }

    /**
     * Set healthy status
     */
    public function healthy(): static
    {
        return $this->state(fn (array $attributes) => [
            'health_status' => 'healthy',
            'last_health_check_at' => now(),
            'health_check_response' => 'OK',
        ]);
    }

    /**
     * Set unhealthy status
     */
    public function unhealthy(): static
    {
        return $this->state(fn (array $attributes) => [
            'health_status' => 'unhealthy',
            'last_health_check_at' => now(),
            'health_check_response' => 'Connection timeout',
        ]);
    }
} 
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | 数据收集系统配置
    |--------------------------------------------------------------------------
    |
    | 这个配置文件包含数据收集系统的所有配置选项
    |
    */

    /*
    |--------------------------------------------------------------------------
    | HTTP客户端配置
    |--------------------------------------------------------------------------
    */
    
    'http' => [
        'timeout' => env('API_TIMEOUT', 30),
        'connect_timeout' => env('API_CONNECT_TIMEOUT', 10),
        'retry_attempts' => env('API_RETRY_ATTEMPTS', 3),
        'retry_delay' => [1, 2, 4], // 指数退避延迟（秒）
        'user_agent' => env('API_USER_AGENT', 'E-commerce Market Dynamics Monitor/1.0'),
        'verify' => env('API_VERIFY_SSL', true),
        'headers' => [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 平台API配置
    |--------------------------------------------------------------------------
    */
    
    'platforms' => [
        'taobao' => [
            'base_url' => env('TAOBAO_API_BASE_URL', 'http://60.247.148.208:5001'),
            'token' => env('TAOBAO_API_TOKEN', ''),
            'timeout' => env('TAOBAO_API_TIMEOUT', 30),
            'retry_attempts' => env('TAOBAO_API_RETRY_ATTEMPTS', 3),
            'endpoints' => [
                'item_detail' => '/tb/new/item_detail_base',
                'similar_products' => '/taobao/SimilarProduct',
                'shop_items' => '/tb/new/shop_item',
                'search' => '/tb/new/search',
            ],
        ],
        
        'jd' => [
            'base_url' => env('JD_API_BASE_URL', ''),
            'token' => env('JD_API_TOKEN', ''),
            'timeout' => env('JD_API_TIMEOUT', 30),
            'retry_attempts' => env('JD_API_RETRY_ATTEMPTS', 3),
            'endpoints' => [
                // JD的接口端点（待实现）
            ],
        ],
        
        'pdd' => [
            'base_url' => env('PDD_API_BASE_URL', ''),
            'token' => env('PDD_API_TOKEN', ''),
            'timeout' => env('PDD_API_TIMEOUT', 30),
            'retry_attempts' => env('PDD_API_RETRY_ATTEMPTS', 3),
            'endpoints' => [
                // 拼多多的接口端点（待实现）
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 数据收集配置
    |--------------------------------------------------------------------------
    */
    
    'collection' => [
        'batch_size' => env('COLLECTION_BATCH_SIZE', 50),
        'frequency' => env('COLLECTION_FREQUENCY', 'hourly'),
        'concurrency' => env('COLLECTION_CONCURRENCY', 10),
        'max_processing_time' => env('COLLECTION_MAX_TIME', 3600), // 1小时
        'enable_real_time' => env('COLLECTION_REAL_TIME', true),
        'enable_batch' => env('COLLECTION_BATCH', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | 队列配置
    |--------------------------------------------------------------------------
    */
    
    'queue' => [
        'connection' => env('QUEUE_CONNECTION', 'redis'),
        'workers' => env('QUEUE_WORKERS', 5),
        'max_tries' => env('QUEUE_MAX_TRIES', 3),
        'timeout' => env('QUEUE_TIMEOUT', 300),
        'retry_after' => env('QUEUE_RETRY_AFTER', 90),
        'queues' => [
            'high' => 'data-collection-high',
            'default' => 'data-collection',
            'low' => 'data-collection-low',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 监控和日志配置
    |--------------------------------------------------------------------------
    */
    
    'monitoring' => [
        'enabled' => env('MONITORING_ENABLED', true),
        'log_level' => env('MONITORING_LOG_LEVEL', 'info'),
        'performance_tracking' => env('PERFORMANCE_MONITORING', true),
        'error_tracking' => env('ERROR_TRACKING', true),
        'metrics' => [
            'collect_request_metrics' => true,
            'collect_response_time' => true,
            'collect_error_rates' => true,
            'collect_memory_usage' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 缓存配置
    |--------------------------------------------------------------------------
    */
    
    'cache' => [
        'ttl' => env('CACHE_TTL', 3600), // 1小时
        'prefix' => env('CACHE_PREFIX', 'datacollection:'),
        'store' => env('CACHE_STORE', 'redis'),
        'cache_responses' => env('CACHE_RESPONSES', true),
        'cache_similar_products' => env('CACHE_SIMILAR_PRODUCTS', true),
        'cache_shop_items' => env('CACHE_SHOP_ITEMS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | 安全配置
    |--------------------------------------------------------------------------
    */
    
    'security' => [
        'rate_limiting' => [
            'enabled' => env('RATE_LIMITING_ENABLED', true),
            'max_requests_per_minute' => env('RATE_LIMIT_PER_MINUTE', 120),
            'max_requests_per_hour' => env('RATE_LIMIT_PER_HOUR', 5000),
        ],
        'ip_whitelist' => array_filter(explode(',', env('API_IP_WHITELIST', ''))),
        'encryption' => [
            'encrypt_tokens' => env('ENCRYPT_API_TOKENS', true),
            'encrypt_logs' => env('ENCRYPT_LOGS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 数据验证配置
    |--------------------------------------------------------------------------
    */
    
    'validation' => [
        'strict_mode' => env('VALIDATION_STRICT_MODE', false),
        'validate_prices' => env('VALIDATE_PRICES', true),
        'validate_urls' => env('VALIDATE_URLS', true),
        'validate_required_fields' => env('VALIDATE_REQUIRED_FIELDS', true),
        'max_title_length' => env('MAX_TITLE_LENGTH', 500),
        'max_description_length' => env('MAX_DESCRIPTION_LENGTH', 5000),
    ],

    /*
    |--------------------------------------------------------------------------
    | 调度配置
    |--------------------------------------------------------------------------
    */
    
    'scheduling' => [
        'default_frequency' => env('DATACOLLECTION_DEFAULT_FREQUENCY', 'hourly'),
        'use_queue' => env('DATACOLLECTION_USE_QUEUE', true),
        'queues' => [
            'high' => env('DATACOLLECTION_QUEUE_HIGH', 'data_collection_high'),
            'medium' => env('DATACOLLECTION_QUEUE_MEDIUM', 'data_collection'),
            'low' => env('DATACOLLECTION_QUEUE_LOW', 'data_collection_low'),
        ],
        'health_check_frequency' => env('DATACOLLECTION_HEALTH_CHECK_FREQUENCY', 5), // 分钟
        'max_execution_time' => env('DATACOLLECTION_MAX_EXECUTION_TIME', 300), // 5分钟
        'retry_delays' => [60, 120, 240], // 重试延迟（秒）
        'overlap_prevention' => env('DATACOLLECTION_PREVENT_OVERLAP', true),
        'background_execution' => env('DATACOLLECTION_BACKGROUND_EXEC', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | 调试配置
    |--------------------------------------------------------------------------
    */
    
    'debug' => [
        'log_requests' => env('DEBUG_LOG_REQUESTS', false),
        'log_responses' => env('DEBUG_LOG_RESPONSES', false),
        'dump_errors' => env('DEBUG_DUMP_ERRORS', true),
        'simulate_requests' => env('DEBUG_SIMULATE_REQUESTS', false),
    ],

]; 
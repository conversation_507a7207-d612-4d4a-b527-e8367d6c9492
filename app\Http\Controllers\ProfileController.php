<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Password;
// use Intervention\Image\Facades\Image; // 暂时注释掉，可以后续安装

class ProfileController extends Controller
{
    /**
     * 显示用户个人中心
     */
    public function index(): View
    {
        $user = Auth::user();
        
        // 获取用户统计数据
        $stats = $this->getUserStats($user);
        
        // 获取最近的活动日志
        $recentActivities = $this->getRecentActivities($user);
        
        return view('profile.index', compact('user', 'stats', 'recentActivities'));
    }

    /**
     * 显示个人信息编辑页面
     */
    public function edit(): View
    {
        $user = Auth::user();
        return view('profile.edit', compact('user'));
    }

    /**
     * 更新个人信息
     */
    public function update(Request $request): JsonResponse|RedirectResponse
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'username' => ['required', 'string', 'max:50', 'unique:users,username,' . $user->id, 'regex:/^[a-zA-Z0-9_]+$/'],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
            'bio' => ['nullable', 'string', 'max:500'],
            'location' => ['nullable', 'string', 'max:100'],
            'website' => ['nullable', 'url', 'max:255'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ], [
            'name.required' => '姓名是必填项',
            'name.max' => '姓名不能超过255个字符',
            'email.required' => '邮箱是必填项',
            'email.email' => '请输入有效的邮箱地址',
            'email.unique' => '该邮箱已被其他用户使用',
            'username.required' => '用户名是必填项',
            'username.unique' => '该用户名已被其他用户使用',
            'username.regex' => '用户名只能包含字母、数字和下划线',
            'phone.regex' => '请输入有效的手机号码',
            'bio.max' => '个人简介不能超过500个字符',
            'location.max' => '所在地不能超过100个字符',
            'website.url' => '请输入有效的网站地址',
            'avatar.image' => '头像必须是图片文件',
            'avatar.mimes' => '头像只支持jpeg、png、jpg、gif格式',
            'avatar.max' => '头像文件大小不能超过2MB',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // 处理头像上传
            if ($request->hasFile('avatar')) {
                $avatarPath = $this->handleAvatarUpload($request->file('avatar'), $user);
                $user->avatar = $avatarPath;
            }

            // 更新用户信息
            $user->update([
                'name' => $request->name,
                'email' => $request->email,
                'username' => $request->username,
                'phone' => $request->phone,
                'bio' => $request->bio,
                'location' => $request->location,
                'website' => $request->website,
            ]);

            // 记录活动日志
            $this->logActivity('profile_updated', '更新了个人信息', $user);

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '个人信息更新成功',
                    'user' => $user->fresh()
                ]);
            }

            return redirect()->route('profile.index')
                ->with('success', '个人信息更新成功');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '更新失败，请重试'
                ], 500);
            }

            return redirect()->back()
                ->with('error', '更新失败，请重试')
                ->withInput();
        }
    }

    /**
     * 显示密码修改页面
     */
    public function showChangePassword(): View
    {
        return view('profile.change-password');
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', Password::min(8)->letters()->mixedCase()->numbers()->symbols(), 'confirmed'],
            'password_confirmation' => ['required', 'string'],
        ], [
            'current_password.required' => '请输入当前密码',
            'password.required' => '请输入新密码',
            'password.min' => '密码至少8位',
            'password.letters' => '密码必须包含字母',
            'password.mixed' => '密码必须包含大小写字母',
            'password.numbers' => '密码必须包含数字',
            'password.symbols' => '密码必须包含特殊字符',
            'password.confirmed' => '两次输入的密码不一致',
            'password_confirmation.required' => '请确认新密码',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator);
        }

        $user = Auth::user();

        // 验证当前密码
        if (!Hash::check($request->current_password, $user->password)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '当前密码不正确'
                ], 422);
            }

            return redirect()->back()
                ->withErrors(['current_password' => '当前密码不正确']);
        }

        try {
            // 更新密码
            $user->update([
                'password' => Hash::make($request->password),
                'password_changed_at' => now(),
            ]);

            // 记录活动日志
            $this->logActivity('password_changed', '修改了登录密码', $user);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '密码修改成功'
                ]);
            }

            return redirect()->route('profile.index')
                ->with('success', '密码修改成功');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '密码修改失败，请重试'
                ], 500);
            }

            return redirect()->back()
                ->with('error', '密码修改失败，请重试');
        }
    }

    /**
     * 显示偏好设置页面
     */
    public function showPreferences(): View
    {
        $user = Auth::user();
        $preferences = $this->getUserPreferences($user);
        $notificationPreferences = $user->getNotificationPreferences();
        
        return view('profile.preferences', compact('user', 'preferences', 'notificationPreferences'));
    }

    /**
     * 更新偏好设置
     */
    public function updatePreferences(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'timezone' => ['nullable', 'string', 'max:50'],
            'language' => ['nullable', 'string', 'max:10'],
            'theme' => ['nullable', 'string', 'in:light,dark,auto'],
            'notifications' => ['nullable', 'array'],
            'notifications.email' => ['boolean'],
            'notifications.sms' => ['boolean'],
            'notifications.browser' => ['boolean'],
            // 详细通知偏好设置验证
            'notification_preferences' => ['nullable', 'array'],
            'notification_preferences.email_enabled' => ['boolean'],
            'notification_preferences.in_site_enabled' => ['boolean'],
            'notification_preferences.emergency_email' => ['boolean'],
            'notification_preferences.important_email' => ['boolean'],
            'notification_preferences.general_email' => ['boolean'],
            'notification_preferences.emergency_in_site' => ['boolean'],
            'notification_preferences.important_in_site' => ['boolean'],
            'notification_preferences.general_in_site' => ['boolean'],
            'notification_preferences.frequency_control' => ['nullable', 'array'],
            'notification_preferences.frequency_control.emergency' => ['string', 'in:immediate,hourly_digest,daily_digest'],
            'notification_preferences.frequency_control.important' => ['string', 'in:immediate,hourly_digest,daily_digest'],
            'notification_preferences.frequency_control.general' => ['string', 'in:immediate,hourly_digest,daily_digest'],
            'data_display' => ['nullable', 'array'],
            'data_display.items_per_page' => ['integer', 'min:10', 'max:100'],
            'data_display.date_format' => ['string', 'max:20'],
            'data_display.time_format' => ['string', 'max:20'],
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            return redirect()->back()
                ->withErrors($validator);
        }

        try {
            $user = Auth::user();
            
            // 保存偏好设置
            $preferences = [
                'timezone' => $request->timezone ?? 'Asia/Shanghai',
                'language' => $request->language ?? 'zh-CN',
                'theme' => $request->theme ?? 'light',
                'notifications' => [
                    'email' => $request->input('notifications.email', true),
                    'sms' => $request->input('notifications.sms', false),
                    'browser' => $request->input('notifications.browser', true),
                ],
                'data_display' => [
                    'items_per_page' => $request->input('data_display.items_per_page', 25),
                    'date_format' => $request->input('data_display.date_format', 'Y-m-d'),
                    'time_format' => $request->input('data_display.time_format', 'H:i:s'),
                ],
            ];

            // 保存详细通知偏好设置
            if ($request->has('notification_preferences')) {
                $notificationPreferences = [
                    'email_enabled' => $request->input('notification_preferences.email_enabled', true),
                    'in_site_enabled' => $request->input('notification_preferences.in_site_enabled', true),
                    'emergency_email' => $request->input('notification_preferences.emergency_email', true),
                    'important_email' => $request->input('notification_preferences.important_email', true),
                    'general_email' => $request->input('notification_preferences.general_email', false),
                    'emergency_in_site' => $request->input('notification_preferences.emergency_in_site', true),
                    'important_in_site' => $request->input('notification_preferences.important_in_site', true),
                    'general_in_site' => $request->input('notification_preferences.general_in_site', true),
                    'frequency_control' => [
                        'emergency' => $request->input('notification_preferences.frequency_control.emergency', 'immediate'),
                        'important' => $request->input('notification_preferences.frequency_control.important', 'immediate'),
                        'general' => $request->input('notification_preferences.frequency_control.general', 'daily_digest'),
                    ]
                ];
                
                $user->updateNotificationPreferences($notificationPreferences);
            }

            $user->update([
                'preferences' => json_encode($preferences),
            ]);

            // 记录活动日志
            $this->logActivity('preferences_updated', '更新了偏好设置', $user);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => '偏好设置保存成功',
                    'preferences' => $preferences
                ]);
            }

            return redirect()->route('profile.preferences')
                ->with('success', '偏好设置保存成功');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '保存失败，请重试'
                ], 500);
            }

            return redirect()->back()
                ->with('error', '保存失败，请重试');
        }
    }

    /**
     * 显示活动日志
     */
    public function showActivityLog(Request $request): View|JsonResponse
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 20);
        
        $activities = $this->getUserActivities($user, $perPage);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'activities' => $activities
            ]);
        }

        return view('profile.activity-log', compact('activities'));
    }

    /**
     * 处理头像上传
     */
    private function handleAvatarUpload($file, $user): string
    {
        // 删除旧头像
        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        // 生成文件名
        $filename = 'avatar_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
        $path = 'avatars/' . $filename;

        // 简单保存文件（不进行图片处理）
        // 后续可以安装 intervention/image 包来实现图片缩放和压缩
        Storage::disk('public')->putFileAs('avatars', $file, $filename);

        return $path;
    }

    /**
     * 获取用户统计数据
     */
    private function getUserStats($user): array
    {
        // 这里应该根据实际的监控任务模型来获取统计数据
        // 暂时返回模拟数据
        return [
            'total_tasks' => 0,
            'active_tasks' => 0,
            'completed_tasks' => 0,
            'pending_alerts' => 0,
            'login_count' => $user->login_attempts ?? 0,
            'last_login' => $user->last_login_at,
            'account_created' => $user->created_at,
        ];
    }

    /**
     * 获取用户偏好设置
     */
    private function getUserPreferences($user): array
    {
        $defaultPreferences = [
            'timezone' => 'Asia/Shanghai',
            'language' => 'zh-CN',
            'theme' => 'light',
            'notifications' => [
                'email' => true,
                'sms' => false,
                'browser' => true,
            ],
            'data_display' => [
                'items_per_page' => 25,
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
            ],
        ];

        if ($user->preferences) {
            return array_merge($defaultPreferences, json_decode($user->preferences, true));
        }

        return $defaultPreferences;
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities($user, $limit = 5): array
    {
        // 这里应该从活动日志表获取数据
        // 暂时返回模拟数据
        return [
            [
                'action' => 'login',
                'description' => '用户登录',
                'ip' => $user->last_login_ip ?? '127.0.0.1',
                'created_at' => $user->last_login_at ?? now(),
            ]
        ];
    }

    /**
     * 获取用户活动日志
     */
    private function getUserActivities($user, $perPage = 20): array
    {
        // 这里应该从活动日志表获取分页数据
        // 暂时返回模拟数据
        return [
            'data' => $this->getRecentActivities($user, $perPage),
            'current_page' => 1,
            'per_page' => $perPage,
            'total' => 1,
        ];
    }

    /**
     * 记录活动日志
     */
    private function logActivity(string $action, string $description, $user): void
    {
        // 这里应该记录到活动日志表
        // 暂时为空实现
    }
} 
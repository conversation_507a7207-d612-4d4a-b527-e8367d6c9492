<?php

namespace App\Console\Commands;

use App\Models\ApiConfiguration;
use App\Services\SecureConfigurationService;
use Illuminate\Console\Command;

/**
 * API安全验证命令
 */
class ValidateApiSecurity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api-config:validate-security 
                            {--platform= : Specific platform to validate}
                            {--fix : Attempt to fix security issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate security of API configurations';

    /**
     * 安全配置服务
     *
     * @var SecureConfigurationService
     */
    private SecureConfigurationService $secureConfigService;

    /**
     * Create a new command instance.
     */
    public function __construct(SecureConfigurationService $secureConfigService)
    {
        parent::__construct();
        $this->secureConfigService = $secureConfigService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $platform = $this->option('platform');
        $fix = $this->option('fix');

        $this->info('开始验证API配置安全性...');
        $this->info('');

        $query = ApiConfiguration::query();
        if ($platform) {
            $query->where('platform_type', $platform);
        }

        $configurations = $query->get();

        if ($configurations->isEmpty()) {
            $this->warn('未找到API配置');
            return;
        }

        $totalConfigs = $configurations->count();
        $secureConfigs = 0;
        $issuesFound = 0;

        foreach ($configurations as $config) {
            $this->validateConfiguration($config, $fix, $secureConfigs, $issuesFound);
        }

        $this->displaySummary($totalConfigs, $secureConfigs, $issuesFound);
    }

    /**
     * 验证单个配置
     *
     * @param ApiConfiguration $config 配置实例
     * @param bool $fix 是否修复问题
     * @param int &$secureConfigs 安全配置计数
     * @param int &$issuesFound 问题计数
     */
    private function validateConfiguration(ApiConfiguration $config, bool $fix, int &$secureConfigs, int &$issuesFound): void
    {
        $this->line("验证配置: {$config->name} ({$config->platform_type})");

        // 获取解密的配置数据进行验证
        $configData = $this->secureConfigService->getDecryptedConfiguration($config);
        $validation = $this->secureConfigService->validateSecurity($configData);

        if ($validation['is_secure']) {
            $this->info('  ✅ 配置安全');
            $secureConfigs++;
        } else {
            $this->error('  ❌ 发现安全问题:');
            foreach ($validation['issues'] as $issue) {
                $this->line("    - {$issue}");
                $issuesFound++;
            }

            if ($fix) {
                $this->attemptFix($config, $validation['issues']);
            }
        }

        // 显示建议
        if (!empty($validation['recommendations'])) {
            $this->line('  💡 安全建议:');
            foreach ($validation['recommendations'] as $recommendation) {
                $this->line("    - {$recommendation}");
            }
        }

        // 检查环境变量覆盖
        $this->checkEnvironmentOverrides($config);

        $this->line('');
    }

    /**
     * 尝试修复安全问题
     *
     * @param ApiConfiguration $config 配置实例
     * @param array $issues 问题列表
     */
    private function attemptFix(ApiConfiguration $config, array $issues): void
    {
        $this->line('  🔧 尝试修复问题...');

        foreach ($issues as $issue) {
            if (str_contains($issue, 'HTTPS')) {
                // 尝试将HTTP转换为HTTPS
                if (str_starts_with($config->base_url, 'http://')) {
                    $newUrl = str_replace('http://', 'https://', $config->base_url);
                    $config->update(['base_url' => $newUrl]);
                    $this->info("    ✅ 已将URL更新为HTTPS: {$newUrl}");
                }
            }
        }
    }

    /**
     * 检查环境变量覆盖
     *
     * @param ApiConfiguration $config 配置实例
     */
    private function checkEnvironmentOverrides(ApiConfiguration $config): void
    {
        $platformUpper = strtoupper($config->platform_type);
        $envKeys = [
            "API_CONFIG_{$platformUpper}_API_KEY",
            "API_CONFIG_{$platformUpper}_SECRET_KEY",
            "API_CONFIG_{$platformUpper}_ACCESS_TOKEN",
            "API_CONFIG_{$platformUpper}_BASE_URL",
        ];

        $overrides = [];
        foreach ($envKeys as $envKey) {
            if (env($envKey) !== null) {
                $overrides[] = $envKey;
            }
        }

        if (!empty($overrides)) {
            $this->line('  🌍 环境变量覆盖:');
            foreach ($overrides as $override) {
                $this->line("    - {$override}");
            }
        }
    }

    /**
     * 显示验证摘要
     *
     * @param int $totalConfigs 总配置数
     * @param int $secureConfigs 安全配置数
     * @param int $issuesFound 发现的问题数
     */
    private function displaySummary(int $totalConfigs, int $secureConfigs, int $issuesFound): void
    {
        $this->info('');
        $this->info('📊 验证摘要:');
        $this->line("总配置数: {$totalConfigs}");
        $this->line("安全配置: {$secureConfigs}");
        $this->line("不安全配置: " . ($totalConfigs - $secureConfigs));
        $this->line("发现问题: {$issuesFound}");

        $securityScore = $totalConfigs > 0 ? round(($secureConfigs / $totalConfigs) * 100, 1) : 0;
        $this->line("安全评分: {$securityScore}%");

        if ($securityScore < 80) {
            $this->error('');
            $this->error('⚠️  安全评分较低，建议立即处理安全问题！');
        } elseif ($securityScore < 95) {
            $this->warn('');
            $this->warn('⚠️  安全评分中等，建议优化配置安全性');
        } else {
            $this->info('');
            $this->info('✅ 安全评分良好！');
        }

        $this->info('');
        $this->info('💡 提高安全性的建议:');
        $this->line('1. 使用环境变量存储敏感信息');
        $this->line('2. 启用HTTPS协议');
        $this->line('3. 定期轮换API密钥');
        $this->line('4. 设置适当的速率限制');
        $this->line('5. 监控API访问日志');
    }
}

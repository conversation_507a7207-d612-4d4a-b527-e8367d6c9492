<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PriceComparisonService;
use App\Models\ProductSku;
use App\Models\PriceHistory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class PriceComparisonServiceTest extends TestCase
{
    use RefreshDatabase;

    private $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PriceComparisonService();
    }

    // 在这里添加测试用例

    /**
     * @test
     */
    public function it_calculates_price_deviation_rate_correctly()
    {
        $date = Carbon::now();
        
        // 创建自有产品和竞争对手产品
        $ownSku = ProductSku::factory()->create(['is_own' => true]);
        $competitorSku = ProductSku::factory()->create(['is_own' => false]);

        // 创建价格历史
        PriceHistory::factory()->create([
            'sku_id' => $ownSku->id,
            'timestamp' => $date,
            'price' => 100.00,
        ]);
        PriceHistory::factory()->create([
            'sku_id' => $competitorSku->id,
            'timestamp' => $date,
            'price' => 120.00,
        ]);

        $deviationRate = $this->service->calculatePriceDeviationRate($competitorSku, $ownSku, $date);

        // 期望值 = ((120 - 100) / 100) * 100 = 20%
        $this->assertEquals(20.0, $deviationRate);
    }

    /**
     * @test
     */
    public function it_analyzes_min_max_price_deviation_range()
    {
        $startDate = Carbon::now()->subDays(10);
        $endDate = Carbon::now();
        
        $ownSku = ProductSku::factory()->create(['is_own' => true]);
        $competitorSku = ProductSku::factory()->create(['is_own' => false]);

        // Day 1: 竞争对手价格高20% (max)
        PriceHistory::factory()->create(['sku_id' => $ownSku->id, 'timestamp' => $startDate, 'price' => 100]);
        PriceHistory::factory()->create(['sku_id' => $competitorSku->id, 'timestamp' => $startDate, 'price' => 120]);

        // Day 5: 价格相同 (0% 偏离)
        PriceHistory::factory()->create(['sku_id' => $ownSku->id, 'timestamp' => $startDate->copy()->addDays(4), 'price' => 110]);
        PriceHistory::factory()->create(['sku_id' => $competitorSku->id, 'timestamp' => $startDate->copy()->addDays(4), 'price' => 110]);

        // Day 10: 竞争对手价格低10% (min)
        PriceHistory::factory()->create(['sku_id' => $ownSku->id, 'timestamp' => $endDate, 'price' => 100]);
        PriceHistory::factory()->create(['sku_id' => $competitorSku->id, 'timestamp' => $endDate, 'price' => 90]);

        $result = $this->service->analyzePriceDeviationRange($competitorSku, $ownSku, $startDate, $endDate);

        $this->assertEquals([
            'min_deviation_rate' => -10.0,
            'max_deviation_rate' => 20.0,
        ], $result);
    }

    /**
     * @test
     */
    public function it_calculates_single_product_discount_rate()
    {
        $date = Carbon::now();
        $sku = ProductSku::factory()->create();

        // 场景: 价格150, 促销为 "满100减20"
        PriceHistory::factory()->create([
            'sku_id' => $sku->id,
            'timestamp' => $date,
            'price' => 150,
            'promotion_info' => [['type' => 'full_reduction', 'threshold' => 100, 'reduction' => 20]]
        ]);
        
        $rate = $this->service->calculateSingleProductDiscountRate($sku, $date);

        // 期望折扣率: (20 / 150) * 100 = 13.33%
        $this->assertEquals(13.33, $rate);

        // 场景2: 价格80, 不满足 "满100减20", 折扣率为0
        $date2 = $date->copy()->addDay();
        PriceHistory::factory()->create([
            'sku_id' => $sku->id,
            'timestamp' => $date2,
            'price' => 80,
            'promotion_info' => [['type' => 'full_reduction', 'threshold' => 100, 'reduction' => 20]]
        ]);

        $rate2 = $this->service->calculateSingleProductDiscountRate($sku, $date2);
        $this->assertEquals(0, $rate2);
    }

    /**
     * @test
     */
    public function it_calculates_overall_promotion_intensity()
    {
        $startDate = Carbon::now()->subDays(10);
        $endDate = Carbon::now();

        $sku1 = ProductSku::factory()->create();
        $sku2 = ProductSku::factory()->create();

        // SKU1, Day 1: 折扣 10%
        PriceHistory::factory()->create(['sku_id' => $sku1->id, 'timestamp' => $startDate, 'price' => 100, 'promotion_info' => [['type' => 'discount_rate', 'discount_rate' => 10]]]);
        
        // SKU1, Day 5: 无促销
        PriceHistory::factory()->create(['sku_id' => $sku1->id, 'timestamp' => $startDate->copy()->addDays(4), 'price' => 100, 'promotion_info' => null]);
        
        // SKU2, Day 3: 折扣 20%
        PriceHistory::factory()->create(['sku_id' => $sku2->id, 'timestamp' => $startDate->copy()->addDays(2), 'price' => 200, 'promotion_info' => [['type' => 'discount_rate', 'discount_rate' => 20]]]);
        
        // SKU2, Day 8: 满150减30 (等效折扣 30/200 = 15%)
        PriceHistory::factory()->create(['sku_id' => $sku2->id, 'timestamp' => $startDate->copy()->addDays(7), 'price' => 200, 'promotion_info' => [['type' => 'full_reduction', 'threshold' => 150, 'reduction' => 30]]]);

        $skus = new \Illuminate\Database\Eloquent\Collection([$sku1, $sku2]);
        $intensity = $this->service->calculateOverallPromotionIntensity($skus, $startDate, $endDate);

        // 预期: (10% + 20% + 15%) / 3个促销活动 = 15%
        $this->assertEquals(15.0, $intensity);
    }
} 
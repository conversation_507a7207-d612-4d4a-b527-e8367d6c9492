<?php

namespace App\Http\Controllers;

use App\Services\CompetitorSearchService;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Exception;

class CompetitorController extends Controller
{
    private CompetitorSearchService $competitorSearchService;

    public function __construct(CompetitorSearchService $competitorSearchService)
    {
        $this->competitorSearchService = $competitorSearchService;
        
        // 应用速率限制中间件
        $this->middleware('throttle:competitor_search,60,1')->only(['search', 'findSimilar']);
        $this->middleware('throttle:competitor_suggestions,120,1')->only(['suggestions']);
    }

    public function index(): View
    {
        try {
            return view('competitors.index');
        } catch (Exception $e) {
            Log::error('竞争对手页面加载失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);
            
            // 返回错误页面或重定向
            return view('errors.500')->with('message', '页面加载失败，请稍后重试');
        }
    }

    public function search(Request $request): JsonResponse
    {
        try {
            // 记录搜索请求
            Log::info('竞争对手搜索请求', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'params' => $request->except(['_token'])
            ]);

            // 输入验证
            $validator = Validator::make($request->all(), [
                'keywords' => 'array|max:20',
                'keywords.*' => 'string|max:100',
                'exclude_words' => 'array|max:10',
                'exclude_words.*' => 'string|max:100',
                'categories' => 'array|max:10',
                'categories.*' => 'string|max:200',
                'operator' => 'in:AND,OR',
                'limit' => 'integer|min:1|max:500',
                'filters' => 'array',
                'filters.platform' => 'string|in:taobao,tmall,jd,pdd',
                'filters.min_price' => 'numeric|min:0|max:999999.99',
                'filters.max_price' => 'numeric|min:0|max:999999.99',
                'filters.min_sales' => 'integer|min:0|max:999999999',
                'filters.min_rating' => 'numeric|min:0|max:5',
            ], [
                'keywords.max' => '关键词数量不能超过20个',
                'keywords.*.max' => '单个关键词长度不能超过100字符',
                'exclude_words.max' => '排除词数量不能超过10个',
                'exclude_words.*.max' => '单个排除词长度不能超过100字符',
                'categories.max' => '分类数量不能超过10个',
                'categories.*.max' => '单个分类长度不能超过200字符',
                'operator.in' => '操作符必须是AND或OR',
                'limit.min' => '结果数量不能少于1',
                'limit.max' => '结果数量不能超过500',
                'filters.platform.in' => '平台必须是taobao、tmall、jd或pdd之一',
                'filters.min_price.numeric' => '最低价格必须是数字',
                'filters.max_price.numeric' => '最高价格必须是数字',
                'filters.min_sales.integer' => '最低销量必须是整数',
                'filters.min_rating.numeric' => '最低评分必须是数字',
                'filters.min_rating.max' => '最低评分不能超过5',
            ]);

            if ($validator->fails()) {
                Log::warning('竞争对手搜索参数验证失败', [
                    'user_id' => auth()->id(),
                    'errors' => $validator->errors()->toArray(),
                    'input' => $request->all()
                ]);

                return response()->json([
                    'success' => false,
                    'error' => '参数验证失败',
                    'error_code' => 'VALIDATION_ERROR',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // 检查是否有有效的搜索条件
            $keywords = $request->input('keywords', []);
            $categories = $request->input('categories', []);
            
            if (empty($keywords) && empty($categories)) {
                return response()->json([
                    'success' => false,
                    'error' => '请输入搜索关键词或选择分类',
                    'error_code' => 'NO_SEARCH_CRITERIA',
                ], 400);
            }

            // 执行搜索
            $result = $this->competitorSearchService->searchCompetitors(
                keywords: $keywords,
                excludeWords: $request->input('exclude_words', []),
                categories: $categories,
                filters: $request->input('filters', []),
                operator: $request->input('operator', 'OR'),
                limit: $request->input('limit', 100)
            );

            // 记录搜索结果
            Log::info('竞争对手搜索完成', [
                'user_id' => auth()->id(),
                'success' => $result['success'],
                'results_count' => $result['success'] ? count($result['data']) : 0,
                'search_time' => $result['meta']['search_time'] ?? 0,
            ]);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('竞争对手搜索异常', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'input' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => '搜索服务暂时不可用，请稍后重试',
                'error_code' => 'SERVICE_UNAVAILABLE',
                'debug' => app()->environment('local') ? [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ] : null
            ], 500);
        }
    }

    public function findSimilar(Request $request, int $productId): JsonResponse
    {
        try {
            // 记录相似产品搜索请求
            Log::info('相似产品搜索请求', [
                'user_id' => auth()->id(),
                'product_id' => $productId,
                'ip' => $request->ip(),
            ]);

            // 验证产品ID
            if ($productId <= 0) {
                return response()->json([
                    'success' => false,
                    'error' => '无效的产品ID',
                    'error_code' => 'INVALID_PRODUCT_ID',
                ], 400);
            }

            // 查找产品
            $product = Product::find($productId);
            
            if (!$product) {
                Log::warning('相似产品搜索：产品不存在', [
                    'user_id' => auth()->id(),
                    'product_id' => $productId,
                ]);

                return response()->json([
                    'success' => false,
                    'error' => '产品不存在',
                    'error_code' => 'PRODUCT_NOT_FOUND',
                ], 404);
            }

            // 输入验证
            $validator = Validator::make($request->all(), [
                'exclude_words' => 'array|max:10',
                'exclude_words.*' => 'string|max:100',
                'operator' => 'in:AND,OR',
                'limit' => 'integer|min:1|max:200',
                'filters' => 'array',
                'filters.platform' => 'string|in:taobao,tmall,jd,pdd',
                'filters.min_price' => 'numeric|min:0',
                'filters.max_price' => 'numeric|min:0',
                'filters.min_sales' => 'integer|min:0',
                'filters.min_rating' => 'numeric|min:0|max:5',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => '参数验证失败',
                    'error_code' => 'VALIDATION_ERROR',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // 构建搜索选项
            $options = [
                'exclude_words' => $request->input('exclude_words', []),
                'operator' => $request->input('operator', 'OR'),
                'limit' => $request->input('limit', 50),
                'filters' => $request->input('filters', []),
            ];

            // 执行相似产品搜索
            $result = $this->competitorSearchService->findSimilarCompetitors($product, $options);

            // 记录搜索结果
            Log::info('相似产品搜索完成', [
                'user_id' => auth()->id(),
                'product_id' => $productId,
                'success' => $result['success'],
                'results_count' => $result['success'] ? count($result['data']) : 0,
            ]);

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('相似产品搜索异常', [
                'user_id' => auth()->id(),
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return response()->json([
                'success' => false,
                'error' => '相似产品搜索服务暂时不可用，请稍后重试',
                'error_code' => 'SERVICE_UNAVAILABLE',
            ], 500);
        }
    }

    /**
     * 高级相似度搜索接口
     */
    public function searchSimilar(Request $request): JsonResponse
    {
        try {
            // 记录请求
            Log::info('高级相似度搜索请求', [
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'params' => $request->except(['_token'])
            ]);

            // 输入验证
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'limit' => 'integer|min:1|max:100',
                'min_similarity' => 'numeric|min:0|max:1',
                'platform' => 'string|in:taobao,tmall,jd,pdd',
                'min_price' => 'numeric|min:0',
                'max_price' => 'numeric|min:0',
                'include_algorithm_info' => 'boolean'
            ], [
                'product_id.required' => '产品ID为必填项',
                'product_id.exists' => '产品不存在',
                'limit.min' => '结果数量不能少于1',
                'limit.max' => '结果数量不能超过100',
                'min_similarity.numeric' => '相似度阈值必须是数字',
                'min_similarity.max' => '相似度阈值不能超过1',
                'platform.in' => '平台必须是taobao、tmall、jd或pdd之一',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => '参数验证失败',
                    'error_code' => 'VALIDATION_ERROR',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $product = Product::findOrFail($request->product_id);
            
            // 使用依赖注入创建服务（如果构造函数需要ProductSimilarityService）
            $similarityService = app(\App\Services\ProductSimilarityService::class);
            $searchService = new \App\Services\CompetitorSearchService($similarityService);

            $options = [
                'limit' => $request->get('limit', 20),
                'min_similarity' => $request->get('min_similarity', 0.3),
                'filters' => array_filter([
                    'platform' => $request->platform,
                    'min_price' => $request->min_price,
                    'max_price' => $request->max_price,
                ])
            ];

            $result = $searchService->findSimilarCompetitorsAdvanced($product, $options);

            // 如果不需要算法信息，移除以减少响应大小
            if (!$request->get('include_algorithm_info', false)) {
                unset($result['algorithm_info']);
            }

            // 记录搜索结果
            Log::info('高级相似度搜索完成', [
                'user_id' => auth()->id(),
                'product_id' => $request->product_id,
                'success' => $result['success'],
                'results_count' => $result['success'] ? count($result['data'] ?? []) : 0,
            ]);

            return response()->json($result, $result['success'] ? 200 : 400);

        } catch (Exception $e) {
            Log::error('高级相似度搜索异常', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'input' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => '相似度搜索服务暂时不可用，请稍后重试',
                'error_code' => 'SERVICE_UNAVAILABLE',
            ], 500);
        }
    }

    public function suggestions(Request $request): JsonResponse
    {
        try {
            // 输入验证
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:2|max:200',
                'limit' => 'integer|min:1|max:20',
            ], [
                'query.required' => '查询字符串不能为空',
                'query.min' => '查询字符串至少需要2个字符',
                'query.max' => '查询字符串不能超过200个字符',
                'limit.min' => '建议数量不能少于1',
                'limit.max' => '建议数量不能超过20',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => '参数验证失败',
                    'error_code' => 'VALIDATION_ERROR',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // 检查速率限制
            $key = 'suggestions:' . $request->ip();
            if (RateLimiter::tooManyAttempts($key, 60)) {
                $seconds = RateLimiter::availableIn($key);
                
                Log::warning('搜索建议请求过于频繁', [
                    'ip' => $request->ip(),
                    'user_id' => auth()->id(),
                    'retry_after' => $seconds,
                ]);

                return response()->json([
                    'success' => false,
                    'error' => '请求过于频繁，请稍后重试',
                    'error_code' => 'RATE_LIMIT_EXCEEDED',
                    'retry_after' => $seconds,
                ], 429);
            }

            // 记录请求
            RateLimiter::hit($key, 60);

            // 获取搜索建议
            $result = $this->competitorSearchService->getSearchSuggestions(
                $request->input('query'),
                $request->input('limit', 10)
            );

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('搜索建议异常', [
                'user_id' => auth()->id(),
                'query' => $request->input('query'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => '搜索建议服务暂时不可用',
                'error_code' => 'SERVICE_UNAVAILABLE',
                'suggestions' => [],
            ], 500);
        }
    }

    /**
     * 清理搜索缓存（管理员功能）
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            // 检查权限
            if (!auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'error' => '权限不足',
                    'error_code' => 'INSUFFICIENT_PERMISSIONS',
                ], 403);
            }

            $pattern = $request->input('pattern');
            $result = $this->competitorSearchService->clearSearchCache($pattern);

            Log::info('搜索缓存清理', [
                'user_id' => auth()->id(),
                'pattern' => $pattern,
                'success' => $result,
            ]);

            return response()->json([
                'success' => $result,
                'message' => $result ? '缓存清理成功' : '缓存清理失败',
            ]);

        } catch (Exception $e) {
            Log::error('缓存清理异常', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => '缓存清理失败',
                'error_code' => 'CACHE_CLEAR_FAILED',
            ], 500);
        }
    }

    /**
     * 获取搜索统计信息（管理员功能）
     */
    public function getStats(Request $request): JsonResponse
    {
        try {
            // 检查权限
            if (!auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'error' => '权限不足',
                    'error_code' => 'INSUFFICIENT_PERMISSIONS',
                ], 403);
            }

            $result = $this->competitorSearchService->getSearchStats();

            return response()->json($result);

        } catch (Exception $e) {
            Log::error('获取搜索统计异常', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => '获取统计信息失败',
                'error_code' => 'STATS_FETCH_FAILED',
            ], 500);
        }
    }

    /**
     * 导出搜索结果
     */
    public function exportResults(Request $request): JsonResponse
    {
        try {
            // 输入验证
            $validator = Validator::make($request->all(), [
                'results' => 'required|array|min:1|max:10000',
                'format' => 'required|in:csv,json,xlsx',
                'include_images' => 'boolean',
                'search_params' => 'array',
            ], [
                'results.required' => '导出数据不能为空',
                'results.min' => '至少需要1条数据',
                'results.max' => '单次导出数据不能超过10000条',
                'format.required' => '导出格式不能为空',
                'format.in' => '导出格式必须是csv、json或xlsx',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => '参数验证失败',
                    'error_code' => 'VALIDATION_ERROR',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $results = $request->input('results');
            $format = $request->input('format');
            $includeImages = $request->input('include_images', false);
            $searchParams = $request->input('search_params', []);

            // 记录导出请求
            Log::info('竞争对手数据导出请求', [
                'user_id' => auth()->id(),
                'format' => $format,
                'count' => count($results),
                'include_images' => $includeImages,
            ]);

            // 处理导出数据
            $exportData = $this->prepareExportData($results, $includeImages, $searchParams);

            // 生成文件
            $filename = 'competitor_search_' . date('Y-m-d_H-i-s') . '.' . $format;
            
            switch ($format) {
                case 'csv':
                    return $this->exportToCsv($exportData, $filename);
                case 'json':
                    return $this->exportToJson($exportData, $filename);
                case 'xlsx':
                    return $this->exportToExcel($exportData, $filename);
                default:
                    throw new \InvalidArgumentException('不支持的导出格式');
            }

        } catch (Exception $e) {
            Log::error('导出搜索结果异常', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return response()->json([
                'success' => false,
                'error' => '导出失败，请稍后重试',
                'error_code' => 'EXPORT_FAILED',
            ], 500);
        }
    }

    /**
     * 准备导出数据
     */
    private function prepareExportData(array $results, bool $includeImages, array $searchParams): array
    {
        $exportData = [
            'metadata' => [
                'export_time' => now()->toISOString(),
                'user_id' => auth()->id(),
                'total_records' => count($results),
                'search_params' => $searchParams,
                'include_images' => $includeImages,
            ],
            'data' => []
        ];

        foreach ($results as $product) {
            $row = [
                'ID' => $product['id'] ?? '',
                '产品标题' => $product['title'] ?? '',
                '分类路径' => $product['category_path'] ?? '',
                '最低价格' => $product['min_price'] ?? '',
                '最高价格' => $product['max_price'] ?? '',
                '总销量' => $product['total_sales'] ?? '',
                '评分' => $product['rating'] ?? '',
                '店铺名称' => $product['shop_name'] ?? '',
                '来源平台' => $product['source_platform'] ?? '',
                '相关性评分' => $product['relevance_score'] ?? '',
                '产品链接' => $product['product_url'] ?? '',
                '更新时间' => $product['updated_at'] ?? '',
            ];

            if ($includeImages && isset($product['main_image'])) {
                $row['主图链接'] = $product['main_image'];
            }

            $exportData['data'][] = $row;
        }

        return $exportData;
    }

    /**
     * 导出为CSV格式
     */
    private function exportToCsv(array $data, string $filename)
    {
        $output = fopen('php://temp', 'w');
        
        // 写入BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");

        // 写入表头
        if (!empty($data['data'])) {
            fputcsv($output, array_keys($data['data'][0]));
            
            // 写入数据
            foreach ($data['data'] as $row) {
                fputcsv($output, $row);
            }
        }

        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);

        return response($content)
            ->header('Content-Type', 'text/csv; charset=utf-8')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * 导出为JSON格式
     */
    private function exportToJson(array $data, string $filename)
    {
        $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        return response($content)
            ->header('Content-Type', 'application/json; charset=utf-8')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * 导出为Excel格式（简化版本，实际项目中可使用PhpSpreadsheet）
     */
    private function exportToExcel(array $data, string $filename)
    {
        // 简化实现：转换为CSV格式但使用xlsx扩展名
        // 实际项目中应该使用PhpSpreadsheet库
        $output = fopen('php://temp', 'w');
        
        // 写入BOM
        fwrite($output, "\xEF\xBB\xBF");

        if (!empty($data['data'])) {
            fputcsv($output, array_keys($data['data'][0]));
            
            foreach ($data['data'] as $row) {
                fputcsv($output, $row);
            }
        }

        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);

        return response($content)
            ->header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }
}
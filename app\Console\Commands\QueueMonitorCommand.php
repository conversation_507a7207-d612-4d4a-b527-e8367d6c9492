<?php

namespace App\Console\Commands;

use App\Services\QueueManagementService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class QueueMonitorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:monitor 
                            {--interval=30 : 监控间隔（秒）}
                            {--continuous : 持续监控模式}
                            {--stats : 显示详细统计信息}
                            {--workers : 显示工作进程信息}
                            {--clear-failed : 清理失败任务}
                            {--retry-failed : 重试失败任务}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '监控Laravel队列状态和性能指标';

    /**
     * Queue management service instance
     */
    protected $queueService;

    /**
     * Create a new command instance.
     */
    public function __construct(QueueManagementService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 处理特定操作
        if ($this->option('clear-failed')) {
            return $this->clearFailedJobs();
        }

        if ($this->option('retry-failed')) {
            return $this->retryFailedJobs();
        }

        // 显示队列监控信息
        if ($this->option('continuous')) {
            $this->continuousMonitor();
        } else {
            $this->singleMonitor();
        }

        return 0;
    }

    /**
     * 单次监控显示
     */
    protected function singleMonitor()
    {
        $this->info('=== Laravel 队列监控 ===');
        $this->newLine();

        // 显示基本统计信息
        $this->displayQueueStats();

        if ($this->option('stats')) {
            $this->newLine();
            $this->displayDetailedStats();
        }

        if ($this->option('workers')) {
            $this->newLine();
            $this->displayWorkerInfo();
        }
    }

    /**
     * 持续监控模式
     */
    protected function continuousMonitor()
    {
        $interval = (int) $this->option('interval');
        
        $this->info("开始持续监控模式 (间隔: {$interval}秒)");
        $this->info('按 Ctrl+C 停止监控');
        $this->newLine();

        while (true) {
            // 清屏
            if (PHP_OS_FAMILY === 'Windows') {
                system('cls');
            } else {
                system('clear');
            }

            $this->info('=== Laravel 队列持续监控 === ' . now()->format('Y-m-d H:i:s'));
            $this->newLine();

            $this->displayQueueStats();
            $this->displayWorkerInfo();

            sleep($interval);
        }
    }

    /**
     * 显示队列统计信息
     */
    protected function displayQueueStats()
    {
        $result = $this->queueService->getQueueStats();
        
        if (!$result['success']) {
            $this->error('获取队列统计失败: ' . $result['error']);
            return;
        }

        $stats = $result['stats'];

        // 总体统计
        $this->table(['指标', '数值'], [
            ['总待处理任务', $stats['total_pending']],
            ['正在处理任务', $stats['processing'] ?? 0],
            ['失败任务', $stats['failed_jobs']],
        ]);

        // 各队列详情
        if (!empty($stats['queues'])) {
            $this->newLine();
            $this->info('各队列详情:');
            
            $queueData = [];
            foreach ($stats['queues'] as $queueName => $queueInfo) {
                $queueData[] = [
                    $queueName,
                    $queueInfo['size'],
                    $this->getQueueStatus($queueName)
                ];
            }

            $this->table(['队列名称', '待处理任务', '状态'], $queueData);
        }
    }

    /**
     * 显示详细统计信息
     */
    protected function displayDetailedStats()
    {
        $this->info('=== 详细统计信息 ===');

        try {
            // 数据库队列统计
            $jobsStats = $this->getDatabaseJobsStats();
            if ($jobsStats) {
                $this->table(['统计项', '数值'], $jobsStats);
            }

            // Redis队列统计（如果使用Redis）
            if (config('queue.default') === 'redis') {
                $this->newLine();
                $this->displayRedisStats();
            }

        } catch (\Exception $e) {
            $this->error('获取详细统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示工作进程信息
     */
    protected function displayWorkerInfo()
    {
        $this->info('=== 工作进程信息 ===');

        try {
            // 检查是否有活跃的队列工作进程
            $workers = $this->getActiveWorkers();
            
            if (empty($workers)) {
                $this->warn('未检测到活跃的队列工作进程');
                $this->line('建议运行: php artisan queue:work');
            } else {
                $this->table(['进程ID', '队列', '状态', '运行时间'], $workers);
            }

        } catch (\Exception $e) {
            $this->error('获取工作进程信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理失败任务
     */
    protected function clearFailedJobs()
    {
        if ($this->confirm('确定要清理所有失败任务吗？')) {
            $result = $this->queueService->clearFailedJobs();
            
            if ($result['success']) {
                $this->info($result['message']);
            } else {
                $this->error('清理失败: ' . $result['error']);
            }
        }

        return 0;
    }

    /**
     * 重试失败任务
     */
    protected function retryFailedJobs()
    {
        $limit = $this->ask('要重试多少个失败任务？(留空重试全部)', null);
        $limit = $limit ? (int) $limit : null;

        if ($this->confirm("确定要重试" . ($limit ? $limit . "个" : "所有") . "失败任务吗？")) {
            $result = $this->queueService->retryFailedJobs($limit);
            
            if ($result['success']) {
                $this->info($result['message']);
            } else {
                $this->error('重试失败: ' . $result['error']);
            }
        }

        return 0;
    }

    /**
     * 获取队列状态
     */
    protected function getQueueStatus($queueName)
    {
        try {
            // 这里可以实现更复杂的状态检测逻辑
            // 例如检查队列是否暂停、工作进程是否运行等
            return '运行中';
        } catch (\Exception $e) {
            return '未知';
        }
    }

    /**
     * 获取数据库任务统计
     */
    protected function getDatabaseJobsStats()
    {
        try {
            if (config('queue.default') !== 'database') {
                return null;
            }

            $stats = [];
            
            // 总任务数
            $totalJobs = DB::table('jobs')->count();
            $stats[] = ['数据库队列总任务', $totalJobs];

            // 按队列分组统计
            $queueStats = DB::table('jobs')
                ->select('queue', DB::raw('count(*) as count'))
                ->groupBy('queue')
                ->get();

            foreach ($queueStats as $stat) {
                $stats[] = ["队列 '{$stat->queue}' 任务数", $stat->count];
            }

            return $stats;

        } catch (\Exception $e) {
            $this->warn('无法获取数据库队列统计: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 显示Redis统计信息
     */
    protected function displayRedisStats()
    {
        try {
            $this->info('Redis 队列统计:');
            
            $redis = Redis::connection();
            $info = $redis->info();
            
            $redisStats = [
                ['Redis 连接状态', '已连接'],
                ['Redis 内存使用', $info['used_memory_human'] ?? 'N/A'],
                ['Redis 连接数', $info['connected_clients'] ?? 'N/A'],
            ];

            $this->table(['Redis 指标', '值'], $redisStats);

        } catch (\Exception $e) {
            $this->warn('无法获取Redis统计: ' . $e->getMessage());
        }
    }

    /**
     * 获取活跃的工作进程信息
     */
    protected function getActiveWorkers()
    {
        $workers = [];
        
        try {
            // 在实际环境中，这里可以通过系统命令或其他方式
            // 检测正在运行的 queue:work 进程
            // 这是一个简化的实现
            
            if (PHP_OS_FAMILY === 'Windows') {
                $output = shell_exec('tasklist /FI "IMAGENAME eq php.exe" /FO CSV 2>NUL');
            } else {
                $output = shell_exec('ps aux | grep "queue:work" | grep -v grep');
            }

            if ($output) {
                // 解析输出并格式化为表格数据
                // 这里是一个简化的实现
                $workers[] = ['检测到', 'queue:work', '运行中', '未知'];
            }

        } catch (\Exception $e) {
            // 如果无法检测进程，返回空数组
        }

        return $workers;
    }
} 
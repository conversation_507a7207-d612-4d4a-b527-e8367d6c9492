<?php

namespace App\Services;

use App\Models\TaskGroup;
use Illuminate\Support\Facades\DB;

class GroupReportingService
{
    /**
     * 为指定的任务组生成聚合数据报告
     *
     * @param int $groupId
     * @return array
     */
    public function generateReport(int $groupId): array
    {
        $taskGroup = TaskGroup::with('monitorTasks')->findOrFail($groupId);

        $taskIds = $taskGroup->monitorTasks->pluck('id');

        // 聚合数据计算
        $totalTasks = $taskIds->count();
        $enabledTasks = $taskGroup->monitorTasks->where('is_enabled', true)->count();

        // 价格历史聚合
        $priceHistoryStats = DB::table('price_history')
            ->whereIn('task_id', $taskIds)
            ->select(
                DB::raw('COUNT(*) as price_data_points'),
                DB::raw('AVG(price) as average_price'),
                DB::raw('MIN(price) as min_price'),
                DB::raw('MAX(price) as max_price')
            )
            ->first();

        // 预警日志聚合
        $alertLogsCount = DB::table('alert_logs')
            ->whereIn('task_id', $taskIds)
            ->count();
            
        // 如果需要，可以按严重性聚合
        $alertsBySeverity = DB::table('alert_logs')
            ->whereIn('task_id', $taskIds)
            ->groupBy('severity')
            ->select('severity', DB::raw('COUNT(*) as count'))
            ->pluck('count', 'severity')
            ->all();

        return [
            'group_id' => $taskGroup->id,
            'group_name' => $taskGroup->name,
            'total_tasks' => $totalTasks,
            'enabled_tasks' => $enabledTasks,
            'price_data_points' => $priceHistoryStats->price_data_points ?? 0,
            'average_price' => (float) ($priceHistoryStats->average_price ?? 0),
            'min_price' => (float) ($priceHistoryStats->min_price ?? 0),
            'max_price' => (float) ($priceHistoryStats->max_price ?? 0),
            'total_alerts' => $alertLogsCount,
            'alerts_by_severity' => [
                'low' => $alertsBySeverity['low'] ?? 0,
                'medium' => $alertsBySeverity['medium'] ?? 0,
                'high' => $alertsBySeverity['high'] ?? 0,
                'critical' => $alertsBySeverity['critical'] ?? 0,
            ],
            'last_updated' => now()->toDateTimeString(),
        ];
    }
} 
@extends('layouts.app')

@section('title', '商品搜索')

@section('page-title', '商品搜索')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="showAdvancedSearch()">
            <i class="fas fa-filter me-1"></i>高级搜索
        </button>
        <button type="button" class="btn btn-success" onclick="exportSearchResults()">
            <i class="fas fa-download me-1"></i>导出结果
        </button>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- 搜索表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-search me-2"></i>
                商品搜索
            </h5>
        </div>
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-6">
                    <label for="keyword" class="form-label">关键词</label>
                    <input type="text" class="form-control" id="keyword" name="keyword" placeholder="输入商品名称、品牌或关键词...">
                </div>
                <div class="col-md-3">
                    <label for="platform" class="form-label">平台</label>
                    <select class="form-select" id="platform" name="platform">
                        <option value="">全部平台</option>
                        <option value="taobao">淘宝</option>
                        <option value="tmall">天猫</option>
                        <option value="jd">京东</option>
                        <option value="pdd">拼多多</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">分类</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">全部分类</option>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装鞋帽</option>
                        <option value="home">家居用品</option>
                        <option value="food">食品饮料</option>
                        <option value="beauty">美妆护肤</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="clearSearch()">
                        <i class="fas fa-times me-1"></i>清空
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 高级搜索面板 -->
    <div class="card mb-4" id="advancedSearchPanel" style="display: none;">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                高级搜索
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="priceMin" class="form-label">最低价格</label>
                    <input type="number" class="form-control" id="priceMin" name="priceMin" placeholder="0">
                </div>
                <div class="col-md-4">
                    <label for="priceMax" class="form-label">最高价格</label>
                    <input type="number" class="form-control" id="priceMax" name="priceMax" placeholder="999999">
                </div>
                <div class="col-md-4">
                    <label for="sortBy" class="form-label">排序方式</label>
                    <select class="form-select" id="sortBy" name="sortBy">
                        <option value="relevance">相关度</option>
                        <option value="price_asc">价格从低到高</option>
                        <option value="price_desc">价格从高到低</option>
                        <option value="sales">销量</option>
                        <option value="rating">评分</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索结果 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                搜索结果 <span id="resultCount" class="badge bg-primary">0</span>
            </h5>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                    <i class="fas fa-th"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary active" onclick="toggleView('list')" id="listViewBtn">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载状态 -->
            <div id="loadingIndicator" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">搜索中...</span>
                </div>
                <p class="mt-2 text-muted">正在搜索商品...</p>
            </div>

            <!-- 搜索结果容器 -->
            <div id="searchResults">
                <div class="text-center py-5 text-muted">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>输入关键词开始搜索商品</p>
                </div>
            </div>

            <!-- 分页 -->
            <nav id="pagination" style="display: none;">
                <ul class="pagination justify-content-center">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* 搜索结果样式 */
.product-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.product-card:hover {
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.35rem 0.35rem 0 0;
}

.product-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #e74a3b;
}

.product-platform {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
}

.platform-taobao { background-color: #ff6900; color: white; }
.platform-tmall { background-color: #ff0036; color: white; }
.platform-jd { background-color: #e1251b; color: white; }
.platform-pdd { background-color: #e02e24; color: white; }

/* 列表视图样式 */
.list-view .product-card {
    display: flex;
    align-items: center;
    padding: 1rem;
}

.list-view .product-image {
    width: 120px;
    height: 120px;
    margin-right: 1rem;
    border-radius: 0.35rem;
}

.list-view .product-info {
    flex: 1;
}

/* 网格视图样式 */
.grid-view .product-card {
    height: 100%;
}

.grid-view .product-info {
    padding: 1rem;
}

/* 加载动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-skeleton {
    animation: pulse 1.5s ease-in-out infinite;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}
</style>
@endpush

@section('scripts')
<script src="{{ asset('js/api-client.js') }}"></script>
<script>
let currentView = 'list';
let currentPage = 1;
let totalPages = 1;
let searchResults = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
});

function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    searchForm.addEventListener('submit', handleSearch);

    // 绑定回车键搜索
    document.getElementById('keyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSearch(e);
        }
    });
}

async function handleSearch(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const searchParams = {
        keyword: formData.get('keyword'),
        platform: formData.get('platform'),
        category: formData.get('category'),
        priceMin: formData.get('priceMin'),
        priceMax: formData.get('priceMax'),
        sortBy: formData.get('sortBy'),
        page: currentPage
    };

    // 过滤空值
    Object.keys(searchParams).forEach(key => {
        if (!searchParams[key]) {
            delete searchParams[key];
        }
    });

    if (!searchParams.keyword) {
        alert('请输入搜索关键词');
        return;
    }

    showLoading();

    try {
        // 模拟API调用
        const results = await simulateSearch(searchParams);
        displayResults(results);
    } catch (error) {
        console.error('搜索失败:', error);
        showError('搜索失败，请重试');
    } finally {
        hideLoading();
    }
}

async function simulateSearch(params) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟搜索结果
    const mockResults = [
        {
            id: 1,
            title: `${params.keyword} 高品质商品`,
            price: 299.99,
            originalPrice: 399.99,
            platform: params.platform || 'taobao',
            image: '{{ asset("images/default-product.svg") }}',
            sales: 1234,
            rating: 4.8,
            url: '#'
        },
        {
            id: 2,
            title: `精选 ${params.keyword} 热销款`,
            price: 199.99,
            originalPrice: 299.99,
            platform: params.platform || 'tmall',
            image: '{{ asset("images/default-product.svg") }}',
            sales: 856,
            rating: 4.6,
            url: '#'
        },
        {
            id: 3,
            title: `${params.keyword} 限时特价`,
            price: 159.99,
            originalPrice: 199.99,
            platform: params.platform || 'jd',
            image: '{{ asset("images/default-product.svg") }}',
            sales: 567,
            rating: 4.5,
            url: '#'
        }
    ];

    return {
        data: mockResults,
        total: mockResults.length,
        page: params.page || 1,
        perPage: 10
    };
}

function displayResults(results) {
    searchResults = results.data;
    totalPages = Math.ceil(results.total / results.perPage);

    document.getElementById('resultCount').textContent = results.total;

    const container = document.getElementById('searchResults');
    container.innerHTML = '';

    if (results.data.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5 text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>未找到相关商品</p>
            </div>
        `;
        return;
    }

    const viewClass = currentView === 'grid' ? 'grid-view' : 'list-view';
    container.className = viewClass;

    if (currentView === 'grid') {
        container.innerHTML = '<div class="row"></div>';
        const row = container.querySelector('.row');

        results.data.forEach(product => {
            const col = document.createElement('div');
            col.className = 'col-md-4 col-lg-3 mb-4';
            col.innerHTML = createProductCard(product, 'grid');
            row.appendChild(col);
        });
    } else {
        results.data.forEach(product => {
            const card = document.createElement('div');
            card.innerHTML = createProductCard(product, 'list');
            container.appendChild(card);
        });
    }

    updatePagination();
}

function createProductCard(product, view) {
    const platformClass = `platform-${product.platform}`;

    if (view === 'grid') {
        return `
            <div class="product-card">
                <img src="${product.image}" alt="${product.title}" class="product-image">
                <div class="product-info">
                    <h6 class="product-title">${product.title}</h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="product-price">¥${product.price}</span>
                        <span class="product-platform ${platformClass}">${getPlatformName(product.platform)}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">销量: ${product.sales}</small>
                        <small class="text-warning">
                            <i class="fas fa-star"></i> ${product.rating}
                        </small>
                    </div>
                    <div class="mt-2">
                        <a href="${product.url}" class="btn btn-primary btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>查看详情
                        </a>
                        <button class="btn btn-outline-success btn-sm ms-1" onclick="addToMonitor(${product.id})">
                            <i class="fas fa-plus me-1"></i>监控
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else {
        return `
            <div class="product-card">
                <img src="${product.image}" alt="${product.title}" class="product-image">
                <div class="product-info">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="product-title flex-grow-1">${product.title}</h6>
                        <span class="product-platform ${platformClass} ms-2">${getPlatformName(product.platform)}</span>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <span class="product-price">¥${product.price}</span>
                            ${product.originalPrice ? `<small class="text-muted text-decoration-line-through ms-2">¥${product.originalPrice}</small>` : ''}
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">销量: ${product.sales}</small>
                        </div>
                        <div class="col-md-3">
                            <small class="text-warning">
                                <i class="fas fa-star"></i> ${product.rating}
                            </small>
                        </div>
                        <div class="col-md-3">
                            <a href="${product.url}" class="btn btn-primary btn-sm me-1" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>查看
                            </a>
                            <button class="btn btn-outline-success btn-sm" onclick="addToMonitor(${product.id})">
                                <i class="fas fa-plus me-1"></i>监控
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

function getPlatformName(platform) {
    const names = {
        'taobao': '淘宝',
        'tmall': '天猫',
        'jd': '京东',
        'pdd': '拼多多'
    };
    return names[platform] || platform;
}

function showAdvancedSearch() {
    const panel = document.getElementById('advancedSearchPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

function clearSearch() {
    document.getElementById('searchForm').reset();
    document.getElementById('searchResults').innerHTML = `
        <div class="text-center py-5 text-muted">
            <i class="fas fa-search fa-3x mb-3"></i>
            <p>输入关键词开始搜索商品</p>
        </div>
    `;
    document.getElementById('resultCount').textContent = '0';
    document.getElementById('pagination').style.display = 'none';
}

function toggleView(view) {
    currentView = view;

    // 更新按钮状态
    document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
    document.getElementById('listViewBtn').classList.toggle('active', view === 'list');

    // 重新渲染结果
    if (searchResults.length > 0) {
        displayResults({ data: searchResults, total: searchResults.length, page: currentPage, perPage: 10 });
    }
}

function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('searchResults').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
    document.getElementById('searchResults').style.display = 'block';
}

function showError(message) {
    document.getElementById('searchResults').innerHTML = `
        <div class="text-center py-5 text-danger">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <p>${message}</p>
        </div>
    `;
}

function updatePagination() {
    const pagination = document.getElementById('pagination');
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }

    pagination.style.display = 'block';
    const ul = pagination.querySelector('ul');
    ul.innerHTML = '';

    // 添加分页按钮逻辑
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        ul.appendChild(li);
    }
}

function changePage(page) {
    currentPage = page;
    // 重新搜索
    document.getElementById('searchForm').dispatchEvent(new Event('submit'));
}

function addToMonitor(productId) {
    // 添加到监控列表的逻辑
    alert(`商品 ${productId} 已添加到监控列表`);
}

function exportSearchResults() {
    if (searchResults.length === 0) {
        alert('没有搜索结果可以导出');
        return;
    }

    // 导出搜索结果的逻辑
    alert('导出功能开发中...');
}
</script>
@endsection
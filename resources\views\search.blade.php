<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Search</title>
</head>
<body>
    <h1>Search for Products</h1>
    <form action="/products/search" method="GET">
        <input type="text" name="keyword" placeholder="Enter keyword...">
        <button type="submit">Search</button>
    </form>

    <div id="results"></div>

    <script>
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            const keyword = this.querySelector('input[name="keyword"]').value;
            
            fetch(`/api/v1/products/search?keyword=${keyword}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = '';
                    if (data.data.length) {
                        data.data.forEach(product => {
                            resultsDiv.innerHTML += `<p>${product.title}</p>`;
                        });
                    } else {
                        resultsDiv.innerHTML = '<p>No products found.</p>';
                    }
                });
        });
    </script>
</body>
</html> 
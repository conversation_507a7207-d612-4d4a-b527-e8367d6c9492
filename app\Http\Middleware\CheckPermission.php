<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        if (!Auth::check()) {
            return $this->handleUnauthorized($request);
        }

        $user = Auth::user();

        // 检查用户是否有任一所需权限
        if ($this->hasAnyPermission($user, $permissions)) {
            return $next($request);
        }

        return $this->handleForbidden($request);
    }

    /**
     * 检查用户是否有任一所需权限
     */
    private function hasAnyPermission($user, array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($user->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理未认证的情况
     */
    private function handleUnauthorized(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => '请先登录',
                'code' => 401
            ], 401);
        }

        return redirect()->route('login')->with('error', '请先登录');
    }

    /**
     * 处理权限不足的情况
     */
    private function handleForbidden(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => '您没有权限访问此资源',
                'code' => 403
            ], 403);
        }

        return abort(403, '您没有权限访问此页面');
    }
} 
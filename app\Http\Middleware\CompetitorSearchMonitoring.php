<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;
use Exception;

class CompetitorSearchMonitoring
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $memoryStart = memory_get_usage(true);
        
        // 生成请求ID用于追踪
        $requestId = uniqid('req_', true);
        $request->headers->set('X-Request-ID', $requestId);
        
        // 记录请求开始
        Log::info('竞争对手搜索请求开始', [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => auth()->id(),
            'start_time' => date('Y-m-d H:i:s'),
            'memory_start' => $this->formatBytes($memoryStart),
        ]);

        try {
            // 检查系统状态
            $this->checkSystemHealth();
            
            // 执行请求
            $response = $next($request);
            
            // 记录成功响应
            $this->logResponse($request, $response, $startTime, $memoryStart, $requestId);
            
            return $response;
            
        } catch (Exception $e) {
            // 记录异常
            $this->logException($request, $e, $startTime, $memoryStart, $requestId);
            
            // 返回友好的错误响应
            return $this->handleException($e);
        }
    }

    /**
     * 检查系统健康状态
     */
    private function checkSystemHealth(): void
    {
        // 检查内存使用情况
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        $memoryLimitBytes = $this->parseBytes($memoryLimit);
        
        if ($memoryUsage > ($memoryLimitBytes * 0.8)) {
            Log::warning('内存使用率过高', [
                'memory_usage' => $this->formatBytes($memoryUsage),
                'memory_limit' => $memoryLimit,
                'usage_percentage' => round(($memoryUsage / $memoryLimitBytes) * 100, 2),
            ]);
        }

        // 检查缓存连接
        try {
            Cache::get('health_check');
        } catch (Exception $e) {
            Log::error('缓存连接异常', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 记录响应信息
     */
    private function logResponse(Request $request, Response $response, float $startTime, int $memoryStart, string $requestId): void
    {
        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        $memoryEnd = memory_get_usage(true);
        $memoryUsed = $memoryEnd - $memoryStart;
        
        $logData = [
            'request_id' => $requestId,
            'status_code' => $response->getStatusCode(),
            'execution_time_ms' => $executionTime,
            'memory_used' => $this->formatBytes($memoryUsed),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
            'response_size' => strlen($response->getContent()),
        ];

        // 记录性能指标
        if ($executionTime > 5000) { // 超过5秒
            Log::warning('竞争对手搜索响应缓慢', $logData);
        } else {
            Log::info('竞争对手搜索请求完成', $logData);
        }

        // 更新性能统计
        $this->updatePerformanceStats($executionTime, $memoryUsed, $response->getStatusCode());
    }

    /**
     * 记录异常信息
     */
    private function logException(Request $request, Exception $e, float $startTime, int $memoryStart, string $requestId): void
    {
        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        $memoryEnd = memory_get_usage(true);
        $memoryUsed = $memoryEnd - $memoryStart;

        Log::error('竞争对手搜索请求异常', [
            'request_id' => $requestId,
            'exception' => get_class($e),
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'execution_time_ms' => $executionTime,
            'memory_used' => $this->formatBytes($memoryUsed),
            'trace' => $e->getTraceAsString(),
        ]);

        // 更新错误统计
        $this->updateErrorStats(get_class($e));
    }

    /**
     * 处理异常并返回友好的错误响应
     */
    private function handleException(Exception $e): Response
    {
        $statusCode = 500;
        $errorCode = 'INTERNAL_SERVER_ERROR';
        $message = '服务暂时不可用，请稍后重试';

        // 根据异常类型设置不同的响应
        if ($e instanceof \Illuminate\Database\QueryException) {
            $statusCode = 503;
            $errorCode = 'DATABASE_ERROR';
            $message = '数据库服务暂时不可用';
        } elseif ($e instanceof \Illuminate\Http\Client\RequestException) {
            $statusCode = 502;
            $errorCode = 'EXTERNAL_SERVICE_ERROR';
            $message = '外部服务连接失败';
        } elseif ($e instanceof \Laravel\Scout\Engines\EngineException) {
            $statusCode = 503;
            $errorCode = 'SEARCH_ENGINE_ERROR';
            $message = '搜索引擎服务异常';
        }

        $responseData = [
            'success' => false,
            'error' => $message,
            'error_code' => $errorCode,
            'timestamp' => now()->toISOString(),
        ];

        // 在开发环境中包含详细错误信息
        if (app()->environment('local')) {
            $responseData['debug'] = [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ];
        }

        return response()->json($responseData, $statusCode);
    }

    /**
     * 更新性能统计
     */
    private function updatePerformanceStats(float $executionTime, int $memoryUsed, int $statusCode): void
    {
        $date = date('Y-m-d');
        $hour = date('H');
        
        // 使用Redis计数器记录统计信息
        try {
            $redis = Cache::getRedis();
            
            // 每日统计
            $redis->hincrby("competitor_stats:daily:{$date}", 'total_requests', 1);
            $redis->hincrby("competitor_stats:daily:{$date}", 'total_execution_time', $executionTime);
            $redis->hincrby("competitor_stats:daily:{$date}", 'total_memory_used', $memoryUsed);
            
            if ($statusCode >= 400) {
                $redis->hincrby("competitor_stats:daily:{$date}", 'error_count', 1);
            }
            
            // 每小时统计
            $redis->hincrby("competitor_stats:hourly:{$date}:{$hour}", 'requests', 1);
            $redis->hincrby("competitor_stats:hourly:{$date}:{$hour}", 'execution_time', $executionTime);
            
            // 设置过期时间（保留30天）
            $redis->expire("competitor_stats:daily:{$date}", 30 * 24 * 3600);
            $redis->expire("competitor_stats:hourly:{$date}:{$hour}", 30 * 24 * 3600);
            
        } catch (Exception $e) {
            Log::error('更新性能统计失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 更新错误统计
     */
    private function updateErrorStats(string $exceptionClass): void
    {
        $date = date('Y-m-d');
        
        try {
            $redis = Cache::getRedis();
            $redis->hincrby("competitor_errors:daily:{$date}", $exceptionClass, 1);
            $redis->expire("competitor_errors:daily:{$date}", 30 * 24 * 3600);
        } catch (Exception $e) {
            Log::error('更新错误统计失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 解析字节数
     */
    private function parseBytes(string $val): int
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        $val = (int) $val;
        
        switch ($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        
        return $val;
    }
} 
# 官方指导价管理功能

## 概述

官方指导价管理功能为电商价格监控系统提供了设置和管理商品SKU官方指导价的能力。通过对比实际价格与官方指导价，系统可以计算价格偏差率，帮助商家监控价格合规性。

## 功能特性

### 1. 多种设置方式
- **手动设置**: 通过Web界面或API单独设置
- **批量导入**: 通过CSV文件批量导入
- **自动计算**: 基于历史价格数据自动计算
- **API集成**: 通过外部API获取官方指导价

### 2. 数据源追踪
系统记录每个官方指导价的来源：
- `manual`: 手动输入
- `import`: 批量导入
- `auto`: 自动计算
- `api`: API接口

### 3. 自动计算方法
- **最高价法** (`max`): 取历史价格最高值作为指导价
- **平均价法** (`avg`): 取历史价格平均值作为指导价
- **百分位数法** (`percentile`): 取指定百分位数价格作为指导价

## 数据库结构

官方指导价相关字段已添加到 `product_skus` 表：

```sql
ALTER TABLE product_skus ADD COLUMN official_guide_price DECIMAL(10,2) NULL COMMENT '官方指导价';
ALTER TABLE product_skus ADD COLUMN official_guide_price_set_at TIMESTAMP NULL COMMENT '官方指导价设置时间';
ALTER TABLE product_skus ADD COLUMN official_guide_price_source ENUM('manual','api','import','auto') NULL COMMENT '官方指导价数据源';
```

## API接口

### 1. 获取统计信息
```http
GET /api/official-guide-prices/stats
```

响应示例：
```json
{
  "success": true,
  "data": {
    "total_active_skus": 1000,
    "with_guide_price": 750,
    "without_guide_price": 250,
    "coverage_percentage": 75.0,
    "by_source": {
      "manual": 200,
      "import": 400,
      "auto": 150
    },
    "stale_count": 50
  }
}
```

### 2. 设置单个SKU官方指导价
```http
POST /api/official-guide-prices/set-single
Content-Type: application/json

{
  "sku_id": 123,
  "guide_price": 99.99,
  "source": "manual"
}
```

### 3. 批量设置官方指导价
```http
POST /api/official-guide-prices/set-batch
Content-Type: application/json

{
  "price_data": [
    {"sku_id": 123, "price": 99.99},
    {"sku_id": 124, "price": 199.99}
  ],
  "source": "import"
}
```

### 4. CSV文件导入
```http
POST /api/official-guide-prices/import-csv
Content-Type: multipart/form-data

file: [CSV文件]
mapping: {"sku_id": 0, "price": 1}
```

CSV文件格式：
```csv
sku_id,guide_price,notes
123,99.99,iPhone 14 Pro
124,199.99,Samsung Galaxy S23
```

### 5. 自动计算官方指导价
```http
POST /api/official-guide-prices/calculate-auto
Content-Type: application/json

{
  "sku_id": 123,
  "days": 90,
  "method": "max",
  "percentile": 95.0
}
```

### 6. 获取需要设置官方指导价的SKU列表
```http
GET /api/official-guide-prices/skus-needing?limit=100&price_min=10.00
```

### 7. 下载CSV模板
```http
GET /api/official-guide-prices/download-template
```

### 8. 清除官方指导价
```http
DELETE /api/official-guide-prices/clear
Content-Type: application/json

{
  "sku_ids": [123, 124, 125]
}
```

## 命令行工具

系统提供了命令行工具来管理官方指导价：

### 1. 查看统计信息
```bash
php artisan guide-price:manage stats
```

### 2. 设置单个SKU官方指导价
```bash
php artisan guide-price:manage set --sku-id=123 --price=99.99 --source=manual
```

### 3. 自动计算官方指导价
```bash
# 为特定SKU计算
php artisan guide-price:manage auto-calculate --sku-id=123 --days=90 --method=max

# 批量处理所有需要的SKU
php artisan guide-price:manage auto-calculate --limit=100 --method=percentile --percentile=95
```

### 4. 从CSV文件导入
```bash
php artisan guide-price:manage import --file=/path/to/prices.csv
```

### 5. 清除过期的官方指导价
```bash
php artisan guide-price:manage clear --limit=100
```

### 6. 模拟运行（不实际修改数据）
```bash
php artisan guide-price:manage auto-calculate --dry-run --limit=10
```

## Model 使用方法

### 1. 查询方法

```php
use App\Models\ProductSku;

// 获取有官方指导价的SKU
$skusWithGuidePrice = ProductSku::withOfficialGuidePrice()->get();

// 获取没有官方指导价的SKU
$skusWithoutGuidePrice = ProductSku::whereNull('official_guide_price')->get();

// 获取过期的官方指导价（超过90天）
$staleGuidePrices = ProductSku::staleGuidePrice(90)->get();

// 获取特定数据源的官方指导价
$manualGuidePrices = ProductSku::where('official_guide_price_source', 'manual')->get();
```

### 2. 访问器

```php
$sku = ProductSku::find(1);

// 检查是否有官方指导价
if ($sku->hasOfficialGuidePrice()) {
    echo "官方指导价: " . $sku->official_guide_price;
    echo "设置时间: " . $sku->official_guide_price_set_at;
    echo "数据源: " . $sku->official_guide_price_source;
}

// 检查官方指导价是否过期
if ($sku->isOfficialGuidePriceStale()) {
    echo "官方指导价已过期，建议更新";
}
```

## Service 使用方法

```php
use App\Services\OfficialGuidePriceService;

$service = app(OfficialGuidePriceService::class);

// 设置单个SKU官方指导价
$result = $service->setGuidePriceForSku(123, 99.99, 'manual');

// 批量设置
$priceData = [
    ['sku_id' => 123, 'price' => 99.99],
    ['sku_id' => 124, 'price' => 199.99]
];
$result = $service->setBatchGuidePrices($priceData, 'import');

// 自动计算
$result = $service->calculateAutoGuidePrice(123, 90, 'max');

// 从CSV导入
$result = $service->importFromCsv('/path/to/file.csv');

// 获取统计信息
$stats = $service->getGuidePriceStats();

// 获取需要设置指导价的SKU
$skus = $service->getSkusNeedingGuidePrice(['price_min' => 10.00]);
```

## 价格偏差率计算

设置官方指导价后，系统会自动触发相关价格历史记录的偏差率重新计算。偏差率计算公式：

```
偏差率 = (实际价格 - 官方指导价) / 官方指导价 * 100%
```

示例：
- 官方指导价: 100.00元
- 实际价格: 85.00元
- 偏差率: (85 - 100) / 100 * 100% = -15%

## 最佳实践

### 1. 数据源选择
- **手动设置**: 适用于少量重要商品的精确定价
- **CSV导入**: 适用于大批量商品的初始化设置
- **自动计算**: 适用于定期更新和维护
- **API集成**: 适用于与外部系统同步

### 2. 计算方法选择
- **最高价法**: 适用于防止价格过低的场景
- **平均价法**: 适用于价格相对稳定的商品
- **百分位数法**: 适用于排除异常价格影响，通常使用95%分位数

### 3. 维护策略
- 定期检查过期的官方指导价（建议每月）
- 监控价格偏差率，及时调整不合理的指导价
- 建立官方指导价更新的审批流程

### 4. 性能优化
- 批量操作时使用事务来保证数据一致性
- 大量数据导入时分批处理，避免内存溢出
- 定期清理过期的官方指导价数据

## 错误处理

常见错误及解决方案：

### 1. SKU不存在
```
错误: SKU not found: 123
解决: 确认SKU ID正确且SKU处于活跃状态
```

### 2. 价格验证失败
```
错误: The price must be at least 0.01
解决: 确保价格为正数且不超过999999.99
```

### 3. 无价格历史数据
```
错误: No price history found for SKU 123 in the last 90 days
解决: 增加分析天数或先收集足够的价格历史数据
```

### 4. CSV格式错误
```
错误: CSV行 5: 列数不足
解决: 检查CSV文件格式，确保列数匹配映射配置
```

## 监控和日志

系统会记录所有官方指导价操作的详细日志：

- 设置成功/失败的记录
- 批量操作的汇总信息
- 自动计算的详细过程
- 导入操作的错误详情

可以通过 `storage/logs/laravel.log` 查看详细日志信息。 
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('导入用户ID');
            $table->enum('import_type', ['manual', 'batch'])->comment('导入类型');
            $table->enum('data_type', ['url', 'id', 'mixed'])->comment('数据类型');
            $table->text('source_data')->comment('原始导入数据');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending')->comment('导入状态');
            $table->json('import_summary')->nullable()->comment('导入摘要统计');
            $table->json('import_results')->nullable()->comment('详细导入结果');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->unsignedInteger('total_items')->default(0)->comment('总项目数');
            $table->unsignedInteger('success_count')->default(0)->comment('成功数量');
            $table->unsignedInteger('failed_count')->default(0)->comment('失败数量');
            $table->unsignedInteger('duplicate_count')->default(0)->comment('重复数量');
            $table->timestamp('started_at')->nullable()->comment('开始处理时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->boolean('is_undoable')->default(true)->comment('是否可撤销');
            $table->timestamp('undone_at')->nullable()->comment('撤销时间');
            $table->text('notes')->nullable()->comment('备注信息');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['import_type', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index('started_at');
            $table->index('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_logs');
    }
};

<?php

namespace App\Http\Controllers;

use App\Services\OfficialGuidePriceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

/**
 * 官方指导价管理控制器
 */
class OfficialGuidePriceController extends Controller
{
    private OfficialGuidePriceService $guidePriceService;

    public function __construct(OfficialGuidePriceService $guidePriceService)
    {
        $this->guidePriceService = $guidePriceService;
    }

    /**
     * 获取官方指导价统计信息
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = $this->guidePriceService->getGuidePriceStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 为单个SKU设置官方指导价
     */
    public function setSingle(Request $request): JsonResponse
    {
        $request->validate([
            'sku_id' => 'required|integer|exists:product_skus,id',
            'guide_price' => 'required|numeric|min:0.01|max:999999.99',
            'source' => ['required', Rule::in([
                OfficialGuidePriceService::SOURCE_MANUAL,
                OfficialGuidePriceService::SOURCE_API,
                OfficialGuidePriceService::SOURCE_IMPORT,
                OfficialGuidePriceService::SOURCE_AUTO
            ])]
        ]);

        $result = $this->guidePriceService->setGuidePriceForSku(
            $request->sku_id,
            $request->guide_price,
            $request->source
        );

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * 批量设置官方指导价
     */
    public function setBatch(Request $request): JsonResponse
    {
        $request->validate([
            'price_data' => 'required|array|min:1',
            'price_data.*.sku_id' => 'required|integer|exists:product_skus,id',
            'price_data.*.price' => 'required|numeric|min:0.01|max:999999.99',
            'source' => ['required', Rule::in([
                OfficialGuidePriceService::SOURCE_MANUAL,
                OfficialGuidePriceService::SOURCE_API,
                OfficialGuidePriceService::SOURCE_IMPORT,
                OfficialGuidePriceService::SOURCE_AUTO
            ])]
        ]);

        $result = $this->guidePriceService->setBatchGuidePrices(
            $request->price_data,
            $request->source
        );

        return response()->json($result);
    }

    /**
     * 从CSV文件导入官方指导价
     */
    public function importCsv(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt|max:10240', // 10MB
            'mapping' => 'array',
            'mapping.sku_id' => 'integer|min:0',
            'mapping.price' => 'integer|min:0'
        ]);

        try {
            $file = $request->file('file');
            $tempPath = $file->store('temp');
            $fullPath = storage_path('app/' . $tempPath);

            // 获取列映射配置
            $mapping = $request->input('mapping', ['sku_id' => 0, 'price' => 1]);

            $result = $this->guidePriceService->importFromCsv($fullPath, $mapping);

            // 清理临时文件
            unlink($fullPath);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Import failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 自动计算官方指导价
     */
    public function calculateAuto(Request $request): JsonResponse
    {
        $request->validate([
            'sku_id' => 'required|integer|exists:product_skus,id',
            'days' => 'integer|min:1|max:365',
            'method' => 'string|in:max,avg,percentile',
            'percentile' => 'numeric|min:1|max:100'
        ]);

        $result = $this->guidePriceService->calculateAutoGuidePrice(
            $request->sku_id,
            $request->input('days', 90),
            $request->input('method', 'max'),
            $request->input('percentile', 95.0)
        );

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取需要设置官方指导价的SKU列表
     */
    public function getSkusNeeding(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'integer|exists:products,id',
            'price_min' => 'numeric|min:0',
            'price_max' => 'numeric|min:0',
            'limit' => 'integer|min:1|max:1000'
        ]);

        try {
            $filters = array_filter([
                'product_id' => $request->product_id,
                'price_min' => $request->price_min,
                'price_max' => $request->price_max,
            ]);

            $skus = $this->guidePriceService->getSkusNeedingGuidePrice($filters);
            
            // 应用分页限制
            $limit = $request->input('limit', 100);
            $skus = array_slice($skus, 0, $limit);

            return response()->json([
                'success' => true,
                'data' => $skus,
                'count' => count($skus)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 下载CSV模板文件
     */
    public function downloadTemplate(): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="official_guide_price_template.csv"',
        ];

        return response()->stream(function() {
            $handle = fopen('php://output', 'w');
            
            // 写入BOM以支持中文
            fwrite($handle, "\xEF\xBB\xBF");
            
            // 写入标题行
            fputcsv($handle, ['sku_id', 'guide_price', 'notes']);
            
            // 写入示例数据
            fputcsv($handle, [1, 99.99, 'Example SKU 1']);
            fputcsv($handle, [2, 199.99, 'Example SKU 2']);
            
            fclose($handle);
        }, 200, $headers);
    }

    /**
     * 批量自动计算官方指导价
     */
    public function batchCalculateAuto(Request $request): JsonResponse
    {
        $request->validate([
            'sku_ids' => 'required|array|min:1|max:100',
            'sku_ids.*' => 'integer|exists:product_skus,id',
            'days' => 'integer|min:1|max:365',
            'method' => 'string|in:max,avg,percentile',
            'percentile' => 'numeric|min:1|max:100'
        ]);

        $days = $request->input('days', 90);
        $method = $request->input('method', 'max');
        $percentile = $request->input('percentile', 95.0);

        $results = [
            'total' => count($request->sku_ids),
            'success' => 0,
            'failed' => 0,
            'results' => []
        ];

        foreach ($request->sku_ids as $skuId) {
            $result = $this->guidePriceService->calculateAutoGuidePrice(
                $skuId,
                $days,
                $method,
                $percentile
            );

            $results['results'][] = array_merge(['sku_id' => $skuId], $result);

            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
            }
        }

        return response()->json($results);
    }

    /**
     * 清除SKU的官方指导价
     */
    public function clear(Request $request): JsonResponse
    {
        $request->validate([
            'sku_ids' => 'required|array|min:1',
            'sku_ids.*' => 'integer|exists:product_skus,id'
        ]);

        try {
            $updated = \App\Models\ProductSku::whereIn('id', $request->sku_ids)
                                          ->update([
                                              'official_guide_price' => null,
                                              'official_guide_price_set_at' => null,
                                              'official_guide_price_source' => null
                                          ]);

            return response()->json([
                'success' => true,
                'updated_count' => $updated
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 
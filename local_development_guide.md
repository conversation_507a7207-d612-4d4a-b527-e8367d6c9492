# 本地开发环境启动与调试指南

本文档旨在指导开发者如何快速地在本地环境中启动并调试"电商市场动态监测系统"。请按照以下步骤操作。

## 1. 环境要求

在开始之前，请确保您的开发环境中已经安装了以下软件：
- PHP (推荐版本 >= 8.1)
- Composer (PHP 依赖管理工具)
- Node.js & npm (JavaScript 运行时及包管理器)
- 一个数据库服务 (例如：MySQL, MariaDB, PostgreSQL)

## 2. 项目配置

### 第一步：安装 PHP 依赖

打开项目根目录的终端，运行 Composer 来安装所有后端的依赖包。

```bash
composer install
```

### 第二步：配置环境变量

项目中包含一个 `.env.example` 文件，它是环境变量的模板。我们需要复制它来创建自己的本地配置文件 `.env`。

```bash
# 在 Windows 上使用 copy, 在 Linux/macOS 上使用 cp
copy .env.example .env
```

创建完成后，您需要**编辑 `.env` 文件**，配置以下关键信息，特别是数据库连接部分：

```dotenv
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name  # 替换成您的数据库名
DB_USERNAME=your_username       # 替换成您的数据库用户名
DB_PASSWORD=your_password       # 替换成您的数据库密码
```

**重要**：请确保您填写的数据库已经存在，否则下一步会失败。

### 第三步：生成应用密钥

Laravel 应用需要一个唯一的应用密钥，用于加密 Session 和其他敏感数据。运行以下 Artisan 命令生成密钥：

```bash
php artisan key:generate
```

该命令会自动将生成的密钥写入到您的 `.env` 文件中的 `APP_KEY` 变量。

## 3. 数据库设置

### 第四步：运行数据库迁移

配置好数据库连接后，我们需要创建应用所需的所有数据表。运行数据库迁移命令：

```bash
php artisan migrate
```

### 第五步：填充初始数据 (可选但推荐)

为了让系统能够正常运行并包含一些基础数据（如管理员用户、角色、权限等），建议运行数据填充程序：

```bash
php artisan db:seed
```

这将运行 `database/seeders/` 目录下的所有 Seeder 文件，为您的数据库装载初始数据。

## 4. 前端资源构建

### 第六步：安装 JavaScript 依赖

项目的前端资源（CSS, JS）需要通过 npm 来管理。

```bash
npm install
```

### 第七步：编译前端资源

安装完依赖后，编译前端资源文件。在开发环境下，建议使用 `npm run dev`，它会在文件发生变化时自动重新编译。

```bash
npm run dev
```

## 5. 启动开发服务器

### 第八步：启动 Laravel 开发服务器

现在，一切准备就绪。运行以下命令来启动 PHP 内置的开发服务器：

```bash
php artisan serve
```

启动成功后，您会在终端看到类似以下的输出：

```
  INFO  Server running on [http://127.0.0.1:8000].

  Press Ctrl+C to stop the server
```

现在，您可以打开浏览器，访问 `http://127.0.0.1:8000` 来查看和调试您的应用程序了。

## 总结

完整的启动流程命令如下：

1.  `composer install`
2.  `copy .env.example .env` (并修改数据库配置)
3.  `php artisan key:generate`
4.  `php artisan migrate`
5.  `php artisan db:seed`
6.  `npm install`
7.  `npm run dev` (保持此终端运行)
8.  `php artisan serve` (在新的终端中运行)

祝您调试顺利！ 
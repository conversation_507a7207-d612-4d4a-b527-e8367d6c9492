<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\AlertLog;
use App\Notifications\AlertNotification;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SendDailyNotificationDigestCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'notifications:daily-digest 
                           {--users= : Comma-separated list of user IDs to send digest to}
                           {--dry-run : Show what would be sent without actually sending}';

    /**
     * The console command description.
     */
    protected $description = '发送每日通知摘要给用户';

    /**
     * @var NotificationService
     */
    private $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始生成每日通知摘要...');

        $dryRun = $this->option('dry-run');
        $userIds = $this->option('users');

        // 获取用户列表
        $users = $this->getTargetUsers($userIds);

        if ($users->isEmpty()) {
            $this->warn('没有找到需要发送每日摘要的用户');
            return 0;
        }

        $this->info("找到 {$users->count()} 个用户需要发送每日摘要");

        $successCount = 0;
        $failCount = 0;

        foreach ($users as $user) {
            try {
                $digestData = $this->generateDigestForUser($user);

                if (empty($digestData['alerts'])) {
                    $this->line("用户 {$user->name} ({$user->id}) 没有未处理的警报，跳过");
                    continue;
                }

                if ($dryRun) {
                    $this->line("【预览模式】用户 {$user->name} 的摘要:");
                    $this->line("  - 未读警报数量: {$digestData['unread_count']}");
                    $this->line("  - 重要警报数量: {$digestData['important_count']}");
                    $this->line("  - 一般警报数量: {$digestData['general_count']}");
                } else {
                    $sent = $this->sendDigestToUser($user, $digestData);
                    if ($sent) {
                        $successCount++;
                        $this->info("✓ 成功发送摘要给用户 {$user->name} ({$user->id})");
                    } else {
                        $failCount++;
                        $this->error("✗ 发送摘要失败给用户 {$user->name} ({$user->id})");
                    }
                }

            } catch (\Exception $e) {
                $failCount++;
                $this->error("处理用户 {$user->name} ({$user->id}) 时发生错误: " . $e->getMessage());
                Log::error("每日摘要处理失败", [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        if (!$dryRun) {
            $this->info("摘要发送完成: 成功 {$successCount}, 失败 {$failCount}");
            Log::info("每日通知摘要发送完成", [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'total_users' => $users->count()
            ]);
        }

        return 0;
    }

    /**
     * 获取目标用户列表
     */
    private function getTargetUsers(?string $userIds)
    {
        if ($userIds) {
            $ids = explode(',', $userIds);
            return User::whereIn('id', $ids)->get();
        }

        // 获取启用每日摘要的用户
        return User::whereJsonContains('notification_preferences->frequency_control->general', 'daily_digest')
                   ->orWhereJsonContains('notification_preferences->frequency_control->important', 'daily_digest')
                   ->get();
    }

    /**
     * 为用户生成摘要数据
     */
    private function generateDigestForUser(User $user): array
    {
        // 获取昨天的警报
        $yesterday = Carbon::yesterday();
        $alerts = AlertLog::where('user_id', $user->id)
                         ->where('status', 'unread')
                         ->whereDate('created_at', '>=', $yesterday)
                         ->orderBy('severity', 'desc')
                         ->orderBy('created_at', 'desc')
                         ->get();

        // 按严重程度分类
        $emergencyAlerts = $alerts->where('severity', 'critical');
        $importantAlerts = $alerts->where('severity', 'high');
        $generalAlerts = $alerts->where('severity', 'medium')->concat($alerts->where('severity', 'low'));

        return [
            'alerts' => $alerts,
            'unread_count' => $alerts->count(),
            'emergency_count' => $emergencyAlerts->count(),
            'important_count' => $importantAlerts->count(),
            'general_count' => $generalAlerts->count(),
            'emergency_alerts' => $emergencyAlerts->take(5), // 最多显示5个紧急警报
            'important_alerts' => $importantAlerts->take(10), // 最多显示10个重要警报
            'general_alerts' => $generalAlerts->take(20), // 最多显示20个一般警报
            'date_range' => $yesterday->format('Y-m-d') . ' - ' . Carbon::today()->format('Y-m-d')
        ];
    }

    /**
     * 发送摘要给用户
     */
    private function sendDigestToUser(User $user, array $digestData): bool
    {
        $alertData = [
            'title' => '每日警报摘要',
            'message' => $this->buildDigestMessage($digestData),
            'type' => 'daily_digest',
            'digest_data' => $digestData,
            'date_range' => $digestData['date_range']
        ];

        return $this->notificationService->sendAlertNotification(
            $user,
            $alertData,
            'general'
        );
    }

    /**
     * 构建摘要消息内容
     */
    private function buildDigestMessage(array $digestData): string
    {
        $message = "您有 {$digestData['unread_count']} 条未读警报需要关注：\n\n";

        if ($digestData['emergency_count'] > 0) {
            $message .= "🚨 紧急警报: {$digestData['emergency_count']} 条\n";
        }

        if ($digestData['important_count'] > 0) {
            $message .= "⚠️ 重要警报: {$digestData['important_count']} 条\n";
        }

        if ($digestData['general_count'] > 0) {
            $message .= "ℹ️ 一般警报: {$digestData['general_count']} 条\n";
        }

        $message .= "\n请登录系统查看详细信息并及时处理。";

        return $message;
    }
} 
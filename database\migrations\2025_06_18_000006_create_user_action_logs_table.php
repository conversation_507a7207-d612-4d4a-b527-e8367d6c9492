<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_action_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null')->comment('用户ID');
            $table->string('action', 100)->comment('操作类型');
            $table->string('module', 50)->nullable()->comment('操作模块');
            $table->text('description')->comment('操作描述');
            $table->json('data_before')->nullable()->comment('操作前数据');
            $table->json('data_after')->nullable()->comment('操作后数据');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->string('request_id', 100)->nullable()->comment('请求ID');
            $table->enum('status', ['success', 'failed', 'error'])->default('success')->comment('操作状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'module']);
            $table->index('created_at');
            $table->index('ip_address');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_action_logs');
    }
}; 
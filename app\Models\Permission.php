<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'module',
        'action',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 权限模块常量
     */
    const MODULE_USER = 'user';
    const MODULE_ROLE = 'role';
    const MODULE_MONITOR = 'monitor';
    const MODULE_SYSTEM = 'system';

    /**
     * 获取权限的角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'permission_role')
                    ->withTimestamps();
    }

    /**
     * 检查权限是否激活
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * 获取权限模块的显示名称
     */
    public function getModuleDisplayAttribute(): string
    {
        return match($this->module) {
            self::MODULE_USER => '用户管理',
            self::MODULE_ROLE => '角色管理',
            self::MODULE_MONITOR => '监控管理',
            self::MODULE_SYSTEM => '系统设置',
            default => '未知模块',
        };
    }

    /**
     * 获取权限的角色数量
     */
    public function getRoleCountAttribute(): int
    {
        return $this->roles()->where('is_active', true)->count();
    }

    /**
     * 根据模块获取权限
     */
    public static function byModule(string $module)
    {
        return static::where('module', $module)->where('is_active', true);
    }

    /**
     * 获取所有激活的权限
     */
    public static function active()
    {
        return static::where('is_active', true);
    }
} 
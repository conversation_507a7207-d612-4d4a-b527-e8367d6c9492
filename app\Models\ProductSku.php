<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductSku extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'sku_code',
        'price',
        'sub_price',
        'sub_price_title',
        'quantity',
        'is_sku',
        'props',
        'delivery',
        'sku_image',
        'original_price',
        'discount_price',
        'official_guide_price',
        'official_guide_price_set_at',
        'official_guide_price_source',
        'promotion_type',
        'promotion_info',
        'sales_count',
        'status',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sub_price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'official_guide_price' => 'decimal:2',
        'official_guide_price_set_at' => 'datetime',
        'quantity' => 'integer',
        'sales_count' => 'integer',
        'is_sku' => 'boolean',
        'props' => 'array',
        'delivery' => 'array',
    ];

    /**
     * SKU所属产品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * SKU的价格历史记录
     */
    public function priceHistory(): HasMany
    {
        return $this->hasMany(PriceHistory::class, 'sku_id');
    }

    /**
     * SKU的监控任务
     */
    public function monitorTasks(): HasMany
    {
        return $this->hasMany(MonitorTask::class, 'sku_id');
    }

    /**
     * SKU的告警日志
     */
    public function alertLogs(): HasMany
    {
        return $this->hasMany(AlertLog::class, 'sku_id');
    }

    /**
     * 获取最新价格记录
     */
    public function latestPriceHistory(): BelongsTo
    {
        return $this->belongsTo(PriceHistory::class, 'id', 'sku_id')
                    ->orderBy('timestamp', 'desc');
    }

    /**
     * 计算折扣率
     */
    public function getDiscountRateAttribute(): float
    {
        if (!$this->original_price || $this->original_price <= 0) {
            return 0;
        }
        
        return round((($this->original_price - $this->price) / $this->original_price) * 100, 2);
    }

    /**
     * 检查是否有促销
     */
    public function hasPromotion(): bool
    {
        return !empty($this->promotion_type) || !empty($this->promotion_info);
    }

    /**
     * 检查是否有库存
     */
    public function inStock(): bool
    {
        return $this->quantity > 0 && $this->status === 'active';
    }

    /**
     * 获取SKU显示名称
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->is_sku && !empty($this->props)) {
            $propStrings = [];
            foreach ($this->props as $key => $value) {
                $propStrings[] = "{$key}:{$value}";
            }
            return $this->product->title . ' (' . implode(', ', $propStrings) . ')';
        }
        
        return $this->product->title;
    }

    /**
     * 按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 按库存筛选
     */
    public function scopeInStock($query)
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * 按价格范围筛选
     */
    public function scopePriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('price', [$minPrice, $maxPrice]);
    }

    /**
     * 有促销的SKU
     */
    public function scopeWithPromotion($query)
    {
        return $query->where(function($q) {
            $q->whereNotNull('promotion_type')
              ->orWhereNotNull('promotion_info');
        });
    }

    /**
     * 检查是否设置了官方指导价
     */
    public function hasOfficialGuidePrice(): bool
    {
        return !is_null($this->official_guide_price) && $this->official_guide_price > 0;
    }

    /**
     * 获取格式化的官方指导价
     */
    public function getFormattedOfficialGuidePriceAttribute(): ?string
    {
        if (!$this->hasOfficialGuidePrice()) {
            return null;
        }
        
        return '¥' . number_format($this->official_guide_price, 2);
    }

    /**
     * 检查官方指导价是否过期（超过一定天数未更新）
     */
    public function isOfficialGuidePriceStale(int $days = 90): bool
    {
        if (!$this->hasOfficialGuidePrice() || !$this->official_guide_price_set_at) {
            return true;
        }

        return $this->official_guide_price_set_at->diffInDays(now()) > $days;
    }

    /**
     * 按是否有官方指导价筛选
     */
    public function scopeWithOfficialGuidePrice($query)
    {
        return $query->whereNotNull('official_guide_price')
                    ->where('official_guide_price', '>', 0);
    }

    /**
     * 按官方指导价数据源筛选
     */
    public function scopeByGuidePriceSource($query, $source)
    {
        return $query->where('official_guide_price_source', $source);
    }

    /**
     * 过期的官方指导价记录
     */
    public function scopeStaleGuidePrice($query, int $days = 90)
    {
        return $query->where(function($q) use ($days) {
            $q->whereNull('official_guide_price_set_at')
              ->orWhere('official_guide_price_set_at', '<', now()->subDays($days));
        });
    }
}

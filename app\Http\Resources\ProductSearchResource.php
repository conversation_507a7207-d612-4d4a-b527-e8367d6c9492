<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'image_url' => $this->image_url,
            'shop' => [
                'id' => $this->shop_id, // Assuming a shop_id exists on the product
                'name' => $this->shop->name, // Assuming a shop relationship exists
            ],
            'skus' => ProductSkuResource::collection($this->whenLoaded('skus')),
        ];
    }
}

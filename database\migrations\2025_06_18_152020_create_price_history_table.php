<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('price_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sku_id')->constrained('product_skus')->onDelete('cascade')->comment('关联SKU ID');
            $table->decimal('price', 10, 2)->comment('价格');
            $table->decimal('sub_price', 10, 2)->nullable()->comment('副价格');
            $table->unsignedInteger('quantity')->default(0)->comment('库存数量');
            $table->json('promotion_info')->nullable()->comment('促销信息');
            $table->unsignedInteger('sales')->default(0)->comment('销量');
            $table->unsignedInteger('comment_count')->default(0)->comment('评价数量');
            $table->timestamp('timestamp')->comment('数据采集时间');
            $table->decimal('price_change', 10, 2)->default(0)->comment('价格变化量');
            $table->decimal('price_change_rate', 5, 2)->default(0)->comment('价格变化率(%)');
            $table->json('additional_data')->nullable()->comment('额外数据（折扣信息、活动信息等）');
            $table->string('data_source')->default('auto')->comment('数据来源（auto, manual, api）');
            $table->timestamps();
            
            // 索引 - 重要的查询优化
            $table->index(['sku_id', 'timestamp']);
            $table->index(['sku_id', 'created_at']);
            $table->index(['timestamp', 'price']);
            $table->index('price');
            $table->index('sales');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('price_history');
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class ImportLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'import_type',
        'data_type',
        'source_data',
        'status',
        'import_summary',
        'import_results',
        'error_message',
        'total_items',
        'success_count',
        'failed_count',
        'duplicate_count',
        'started_at',
        'completed_at',
        'is_undoable',
        'undone_at',
        'notes'
    ];

    protected $casts = [
        'import_summary' => 'array',
        'import_results' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'undone_at' => 'datetime',
        'is_undoable' => 'boolean'
    ];

    // 关联关系
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function dataSources(): HasMany
    {
        return $this->hasMany(DataSource::class);
    }

    // 作用域
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('import_type', $type);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    // 业务逻辑方法
    public function markAsStarted()
    {
        $this->update([
            'status' => 'processing',
            'started_at' => Carbon::now()
        ]);
    }

    public function markAsCompleted($summary = null, $results = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => Carbon::now(),
            'import_summary' => $summary,
            'import_results' => $results
        ]);
    }

    public function markAsFailed($errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => Carbon::now(),
            'error_message' => $errorMessage
        ]);
    }

    public function updateCounters($success = 0, $failed = 0, $duplicate = 0)
    {
        $this->increment('success_count', $success);
        $this->increment('failed_count', $failed);
        $this->increment('duplicate_count', $duplicate);
    }

    public function getSuccessRateAttribute()
    {
        if ($this->total_items == 0) {
            return 0;
        }
        return round(($this->success_count / $this->total_items) * 100, 2);
    }

    public function getDurationAttribute()
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }
        return $this->started_at->diffInSeconds($this->completed_at);
    }

    public function canUndo()
    {
        return $this->is_undoable && 
               $this->status === 'completed' && 
               !$this->undone_at;
    }

    public function undo()
    {
        if (!$this->canUndo()) {
            return false;
        }

        // 删除或标记相关的数据源
        $this->dataSources()->update(['is_active' => false]);
        
        // 标记为已撤销
        $this->update([
            'undone_at' => Carbon::now(),
            'is_undoable' => false
        ]);

        return true;
    }

    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];

        return $statusMap[$this->status] ?? $this->status;
    }

    public function getTypeTextAttribute()
    {
        $typeMap = [
            'manual' => '手动导入',
            'batch' => '批量导入'
        ];

        return $typeMap[$this->import_type] ?? $this->import_type;
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // 1. 创建基础权限
        $permissions = [
            // 用户管理权限
            ['name' => 'user.view', 'display_name' => '查看用户', 'description' => '查看用户列表和详情', 'module' => 'user', 'action' => 'view', 'resource' => 'user.*'],
            ['name' => 'user.create', 'display_name' => '创建用户', 'description' => '创建新用户账户', 'module' => 'user', 'action' => 'create', 'resource' => 'user.*'],
            ['name' => 'user.update', 'display_name' => '更新用户', 'description' => '修改用户信息', 'module' => 'user', 'action' => 'update', 'resource' => 'user.*'],
            ['name' => 'user.delete', 'display_name' => '删除用户', 'description' => '删除用户账户', 'module' => 'user', 'action' => 'delete', 'resource' => 'user.*'],
            
            // 角色管理权限
            ['name' => 'role.view', 'display_name' => '查看角色', 'description' => '查看角色列表和详情', 'module' => 'role', 'action' => 'view', 'resource' => 'role.*'],
            ['name' => 'role.create', 'display_name' => '创建角色', 'description' => '创建新角色', 'module' => 'role', 'action' => 'create', 'resource' => 'role.*'],
            ['name' => 'role.update', 'display_name' => '更新角色', 'description' => '修改角色信息', 'module' => 'role', 'action' => 'update', 'resource' => 'role.*'],
            ['name' => 'role.delete', 'display_name' => '删除角色', 'description' => '删除角色', 'module' => 'role', 'action' => 'delete', 'resource' => 'role.*'],
            
            // 监控权限
            ['name' => 'monitor.view', 'display_name' => '查看监控', 'description' => '查看监控数据', 'module' => 'monitor', 'action' => 'view', 'resource' => 'monitor.*'],
            ['name' => 'monitor.create', 'display_name' => '创建监控', 'description' => '创建监控任务', 'module' => 'monitor', 'action' => 'create', 'resource' => 'monitor.*'],
            ['name' => 'monitor.update', 'display_name' => '更新监控', 'description' => '修改监控配置', 'module' => 'monitor', 'action' => 'update', 'resource' => 'monitor.*'],
            ['name' => 'monitor.delete', 'display_name' => '删除监控', 'description' => '删除监控任务', 'module' => 'monitor', 'action' => 'delete', 'resource' => 'monitor.*'],
            
            // 系统设置权限
            ['name' => 'system.config', 'display_name' => '系统配置', 'description' => '修改系统配置', 'module' => 'system', 'action' => 'config', 'resource' => 'system.*'],
            ['name' => 'system.logs', 'display_name' => '系统日志', 'description' => '查看系统日志', 'module' => 'system', 'action' => 'logs', 'resource' => 'system.*'],
        ];

        // 添加时间戳
        foreach ($permissions as $key => $permission) {
            $permissions[$key]['created_at'] = $now;
            $permissions[$key]['updated_at'] = $now;
        }

        DB::table('permissions')->insert($permissions);

        // 2. 创建基础角色
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => '超级管理员',
                'description' => '拥有所有权限的超级管理员',
                'level' => 'admin',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'admin',
                'display_name' => '系统管理员',
                'description' => '系统管理员，拥有大部分权限',
                'level' => 'admin',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'operator',
                'display_name' => '运营人员',
                'description' => '运营人员，负责日常监控操作',
                'level' => 'advanced',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'viewer',
                'display_name' => '只读用户',
                'description' => '只读用户，只能查看数据',
                'level' => 'readonly',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('roles')->insert($roles);

        // 3. 创建默认用户
        $users = [
            [
                'name' => '系统管理员',
                'email' => '<EMAIL>',
                'username' => 'admin',
                'password' => Hash::make('password123'),
                'status' => 'active',
                'email_verified_at' => $now,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => '运营人员',
                'email' => '<EMAIL>',
                'username' => 'operator',
                'password' => Hash::make('password123'),
                'status' => 'active',
                'email_verified_at' => $now,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('users')->insert($users);

        // 4. 分配角色给用户
        $roleAssignments = [
            ['user_id' => 1, 'role_id' => 1, 'assigned_at' => $now, 'created_at' => $now, 'updated_at' => $now],
            ['user_id' => 2, 'role_id' => 3, 'assigned_at' => $now, 'created_at' => $now, 'updated_at' => $now],
        ];

        DB::table('role_user')->insert($roleAssignments);

        // 5. 分配权限给角色
        $permissionRoleAssignments = [];

        // 超级管理员拥有所有权限 (1-14)
        for ($i = 1; $i <= 14; $i++) {
            $permissionRoleAssignments[] = [
                'role_id' => 1,
                'permission_id' => $i,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // 系统管理员拥有大部分权限（除了删除用户）
        $adminPermissions = [1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];
        foreach ($adminPermissions as $permissionId) {
            $permissionRoleAssignments[] = [
                'role_id' => 2,
                'permission_id' => $permissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // 运营人员拥有监控相关权限
        $operatorPermissions = [1, 9, 10, 11, 12];
        foreach ($operatorPermissions as $permissionId) {
            $permissionRoleAssignments[] = [
                'role_id' => 3,
                'permission_id' => $permissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // 只读用户只有查看权限
        $viewerPermissions = [1, 5, 9, 14];
        foreach ($viewerPermissions as $permissionId) {
            $permissionRoleAssignments[] = [
                'role_id' => 4,
                'permission_id' => $permissionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('permission_role')->insert($permissionRoleAssignments);
    }
} 
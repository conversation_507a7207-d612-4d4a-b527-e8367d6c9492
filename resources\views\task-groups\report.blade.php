@extends('layouts.app')

@section('title', '任务分组报告 - ' . $taskGroup->name)

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('task-groups.index') }}">任务分组</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $taskGroup->name }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-title mb-0">
                        <i class="fas fa-chart-bar me-2" style="color: {{ $taskGroup->color }}"></i>
                        {{ $taskGroup->name }} - 分组报告
                    </h1>
                    @if($taskGroup->description)
                        <p class="text-muted mb-0">{{ $taskGroup->description }}</p>
                    @endif
                </div>
                <div>
                    <button type="button" class="btn btn-outline-primary me-2" onclick="exportReport('json')">
                        <i class="fas fa-download me-2"></i>导出JSON
                    </button>
                    <button type="button" class="btn btn-outline-success me-2" onclick="exportReport('csv')">
                        <i class="fas fa-file-csv me-2"></i>导出CSV
                    </button>
                    <button type="button" class="btn btn-primary" onclick="refreshReport()">
                        <i class="fas fa-sync-alt me-2"></i>刷新报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围选择器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="row g-2">
                                <div class="col-auto">
                                    <label class="form-label mb-0">时间范围:</label>
                                </div>
                                <div class="col-auto">
                                    <select class="form-select form-select-sm" id="dateRangeSelect" onchange="onDateRangeChange()">
                                        <option value="7d">最近7天</option>
                                        <option value="30d" selected>最近30天</option>
                                        <option value="90d">最近90天</option>
                                        <option value="1y">最近1年</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row g-2" id="customDateRange" style="display: none;">
                                <div class="col-auto">
                                    <input type="date" class="form-control form-control-sm" id="startDate">
                                </div>
                                <div class="col-auto">
                                    <span class="align-middle">至</span>
                                </div>
                                <div class="col-auto">
                                    <input type="date" class="form-control form-control-sm" id="endDate">
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="applyCustomDateRange()">
                                        应用
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告内容区域 -->
    <div id="reportContent">
        <!-- 加载中状态 -->
        <div class="text-center py-5" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在生成报告...</p>
        </div>
        
        <!-- 报告内容将通过JavaScript动态加载 -->
    </div>
</div>

<!-- 导出设置模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">导出格式</label>
                    <select class="form-select" id="exportFormat">
                        <option value="json">JSON格式</option>
                        <option value="csv">CSV格式</option>
                        <option value="excel">Excel格式</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">包含部分</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="group_info" id="section_group_info" checked>
                        <label class="form-check-label" for="section_group_info">分组信息</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="task_overview" id="section_task_overview" checked>
                        <label class="form-check-label" for="section_task_overview">任务概览</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="performance_metrics" id="section_performance_metrics" checked>
                        <label class="form-check-label" for="section_performance_metrics">性能指标</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="price_analytics" id="section_price_analytics" checked>
                        <label class="form-check-label" for="section_price_analytics">价格分析</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="alert_summary" id="section_alert_summary" checked>
                        <label class="form-check-label" for="section_alert_summary">告警汇总</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="trend_data" id="section_trend_data" checked>
                        <label class="form-check-label" for="section_trend_data">趋势数据</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeExport()">开始导出</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.stat-card {
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.chart-container {
    position: relative;
    height: 300px;
}

.metric-item {
    border-left: 4px solid var(--bs-primary);
    padding-left: 15px;
}

.alert-item {
    border-left: 3px solid;
    padding-left: 10px;
    margin-bottom: 10px;
}

.alert-high { border-left-color: #dc3545; }
.alert-medium { border-left-color: #ffc107; }
.alert-low { border-left-color: #28a745; }

.trend-up { color: #28a745; }
.trend-down { color: #dc3545; }
.trend-stable { color: #6c757d; }

.report-section {
    margin-bottom: 2rem;
}

.section-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 全局变量
const groupId = {{ $taskGroup->id }};
let currentReport = null;
let currentDateRange = '30d';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadReport();
});

/**
 * 加载报告数据
 */
async function loadReport() {
    try {
        showLoading(true);
        
        const params = new URLSearchParams();
        if (currentDateRange !== 'custom') {
            params.append('period', currentDateRange);
        } else {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            if (startDate && endDate) {
                params.append('start_date', startDate);
                params.append('end_date', endDate);
            }
        }
        
        const response = await fetch(`/api/task-groups/${groupId}/report?${params.toString()}`);
        const result = await response.json();
        
        if (result.success) {
            currentReport = result.data;
            renderReport(currentReport);
        } else {
            throw new Error(result.message || '加载报告失败');
        }
        
    } catch (error) {
        console.error('加载报告失败:', error);
        showAlert('加载报告失败，请重试', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * 渲染报告内容
 */
function renderReport(report) {
    const container = document.getElementById('reportContent');
    
    container.innerHTML = `
        <!-- 概览卡片 -->
        ${renderOverviewCards(report)}
        
        <!-- 任务概览 -->
        ${renderTaskOverview(report.task_overview)}
        
        <!-- 性能指标 -->
        ${renderPerformanceMetrics(report.performance_metrics)}
        
        <!-- 价格分析 -->
        ${renderPriceAnalytics(report.price_analytics)}
        
        <!-- 告警汇总 -->
        ${renderAlertSummary(report.alert_summary)}
        
        <!-- 趋势图表 -->
        ${renderTrendCharts(report.trend_data)}
    `;
    
    // 初始化图表
    initializeCharts(report);
}

/**
 * 渲染概览卡片
 */
function renderOverviewCards(report) {
    return `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">总任务数</h6>
                                <h3>${report.task_overview.total_tasks}</h3>
                            </div>
                            <i class="fas fa-tasks fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">活跃任务</h6>
                                <h3>${report.task_overview.active_tasks}</h3>
                            </div>
                            <i class="fas fa-play fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">健康度</h6>
                                <h3>${report.task_overview.health_score}%</h3>
                            </div>
                            <i class="fas fa-heartbeat fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">成功率</h6>
                                <h3>${report.performance_metrics.collection_stats.success_rate}%</h3>
                            </div>
                            <i class="fas fa-check-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染任务概览
 */
function renderTaskOverview(taskOverview) {
    return `
        <div class="report-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="section-header mb-0">
                        <i class="fas fa-list-ul me-2"></i>任务概览
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="taskStatusChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                ${Object.entries(taskOverview.status_distribution || {}).map(([status, count]) => `
                                    <div class="col-6 mb-3">
                                        <div class="metric-item">
                                            <h6 class="text-uppercase mb-1">${status}</h6>
                                            <h4 class="mb-0">${count}</h4>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染性能指标
 */
function renderPerformanceMetrics(performanceMetrics) {
    return `
        <div class="report-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="section-header mb-0">
                        <i class="fas fa-chart-line me-2"></i>性能指标
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric-item">
                                <h6>数据收集成功率</h6>
                                <h4>${performanceMetrics.collection_stats.success_rate}%</h4>
                                <small class="text-muted">共${performanceMetrics.collection_stats.total_attempts}次尝试</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-item">
                                <h6>平均响应时间</h6>
                                <h4>${performanceMetrics.response_time.average}ms</h4>
                                <small class="text-muted">最快${performanceMetrics.response_time.min}ms</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-item">
                                <h6>错误率</h6>
                                <h4>${performanceMetrics.collection_stats.error_rate}%</h4>
                                <small class="text-muted">${performanceMetrics.collection_stats.total_errors}个错误</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染价格分析
 */
function renderPriceAnalytics(priceAnalytics) {
    return `
        <div class="report-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="section-header mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>价格分析
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="priceChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-item mb-3">
                                <h6>平均价格</h6>
                                <h4>¥${priceAnalytics.price_statistics.average_price}</h4>
                            </div>
                            <div class="metric-item mb-3">
                                <h6>价格范围</h6>
                                <p class="mb-0">¥${priceAnalytics.price_statistics.min_price} - ¥${priceAnalytics.price_statistics.max_price}</p>
                            </div>
                            <div class="metric-item">
                                <h6>价格趋势</h6>
                                <span class="badge bg-${priceAnalytics.trend_analysis.direction === 'up' ? 'success' : priceAnalytics.trend_analysis.direction === 'down' ? 'danger' : 'secondary'}">
                                    <i class="fas fa-arrow-${priceAnalytics.trend_analysis.direction === 'up' ? 'up' : priceAnalytics.trend_analysis.direction === 'down' ? 'down' : 'right'} me-1"></i>
                                    ${priceAnalytics.trend_analysis.change_rate}%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染告警汇总
 */
function renderAlertSummary(alertSummary) {
    return `
        <div class="report-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="section-header mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>告警汇总
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            ${alertSummary.recent_alerts?.map(alert => `
                                <div class="alert-item alert-${alert.severity}">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="mb-1">${alert.rule_name}</h6>
                                            <small class="text-muted">${alert.message}</small>
                                        </div>
                                        <small class="text-muted">${new Date(alert.created_at).toLocaleString()}</small>
                                    </div>
                                </div>
                            `).join('') || '<p class="text-muted">暂无告警</p>'}
                        </div>
                        <div class="col-md-4">
                            <div class="metric-item mb-3">
                                <h6>总告警数</h6>
                                <h4>${alertSummary.alert_statistics.total_alerts}</h4>
                            </div>
                            <div class="metric-item mb-3">
                                <h6>未读告警</h6>
                                <h4 class="text-warning">${alertSummary.alert_statistics.unread_alerts}</h4>
                            </div>
                            <div class="metric-item">
                                <h6>严重程度分布</h6>
                                ${Object.entries(alertSummary.severity_distribution || {}).map(([severity, count]) => `
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="text-capitalize">${severity}:</span>
                                        <span class="fw-bold">${count}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 渲染趋势图表
 */
function renderTrendCharts(trendData) {
    return `
        <div class="report-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="section-header mb-0">
                        <i class="fas fa-chart-area me-2"></i>趋势分析
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <canvas id="trendChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 初始化图表
 */
function initializeCharts(report) {
    // 任务状态图表
    if (report.task_overview.status_distribution) {
        const statusCtx = document.getElementById('taskStatusChart');
        if (statusCtx) {
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(report.task_overview.status_distribution),
                    datasets: [{
                        data: Object.values(report.task_overview.status_distribution),
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }
    
    // 价格趋势图表
    if (report.price_analytics.trend_data) {
        const priceCtx = document.getElementById('priceChart');
        if (priceCtx) {
            new Chart(priceCtx, {
                type: 'line',
                data: {
                    labels: report.price_analytics.trend_data.map(item => item.date),
                    datasets: [{
                        label: '平均价格',
                        data: report.price_analytics.trend_data.map(item => item.average_price),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value;
                                }
                            }
                        }
                    }
                }
            });
        }
    }
    
    // 综合趋势图表
    if (report.trend_data) {
        const trendCtx = document.getElementById('trendChart');
        if (trendCtx) {
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: report.trend_data.dates || [],
                    datasets: [{
                        label: '数据收集次数',
                        data: report.trend_data.collection_counts || [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y'
                    }, {
                        label: '错误次数',
                        data: report.trend_data.error_counts || [],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    }
}

/**
 * 日期范围变更处理
 */
function onDateRangeChange() {
    const select = document.getElementById('dateRangeSelect');
    const customRange = document.getElementById('customDateRange');
    
    if (select.value === 'custom') {
        customRange.style.display = 'block';
    } else {
        customRange.style.display = 'none';
        currentDateRange = select.value;
        loadReport();
    }
}

/**
 * 应用自定义日期范围
 */
function applyCustomDateRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!startDate || !endDate) {
        showAlert('请选择开始和结束日期', 'warning');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        showAlert('开始日期不能晚于结束日期', 'warning');
        return;
    }
    
    currentDateRange = 'custom';
    loadReport();
}

/**
 * 刷新报告
 */
function refreshReport() {
    loadReport();
}

/**
 * 导出报告
 */
function exportReport(format) {
    const modal = new bootstrap.Modal(document.getElementById('exportModal'));
    document.getElementById('exportFormat').value = format;
    modal.show();
}

/**
 * 执行导出
 */
async function executeExport() {
    const format = document.getElementById('exportFormat').value;
    const sections = [];
    
    // 收集选中的部分
    document.querySelectorAll('input[id^="section_"]:checked').forEach(checkbox => {
        sections.push(checkbox.value);
    });
    
    try {
        const params = new URLSearchParams({
            format: format,
            sections: sections
        });
        
        if (currentDateRange !== 'custom') {
            params.append('period', currentDateRange);
        } else {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            if (startDate && endDate) {
                params.append('start_date', startDate);
                params.append('end_date', endDate);
            }
        }
        
        const response = await fetch(`/api/task-groups/${groupId}/export-report?${params.toString()}`);
        const result = await response.json();
        
        if (result.success) {
            // 根据格式处理下载
            const blob = new Blob([JSON.stringify(result.data, null, 2)], {
                type: format === 'json' ? 'application/json' : 'text/plain'
            });
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `task-group-report-${groupId}-${Date.now()}.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showAlert('报告导出成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
        } else {
            throw new Error(result.message || '导出失败');
        }
    } catch (error) {
        console.error('导出报告失败:', error);
        showAlert('导出报告失败，请重试', 'danger');
    }
}

/**
 * 显示加载状态
 */
function showLoading(show) {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.style.display = show ? 'block' : 'none';
    }
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建或获取提示容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script>
@endpush
</code_block_to_apply_changes_from>
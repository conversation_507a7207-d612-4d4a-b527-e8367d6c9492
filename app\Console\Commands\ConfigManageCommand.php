<?php

namespace App\Console\Commands;

use App\Services\ConfigurationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

/**
 * 配置管理命令
 * 
 * 提供配置查看、验证、导出等功能
 */
class ConfigManageCommand extends Command
{
    /**
     * 命令名称和签名
     *
     * @var string
     */
    protected $signature = 'config:manage 
                            {action : 操作类型 (show|validate|export|check|test)}
                            {--key= : 配置键}
                            {--platform= : 平台名称}
                            {--format=table : 输出格式 (table|json|yaml)}
                            {--output= : 输出文件路径}
                            {--include-sensitive : 包含敏感信息}
                            {--env-file=.env : 环境变量文件路径}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '配置管理工具 - 查看、验证和管理系统配置';

    /**
     * 配置服务
     *
     * @var ConfigurationService
     */
    private $configService;

    /**
     * 创建命令实例
     *
     * @param ConfigurationService $configService
     */
    public function __construct(ConfigurationService $configService)
    {
        parent::__construct();
        $this->configService = $configService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');

        try {
            switch ($action) {
                case 'show':
                    return $this->showConfiguration();
                case 'validate':
                    return $this->validateConfiguration();
                case 'export':
                    return $this->exportConfiguration();
                case 'check':
                    return $this->checkEnvironment();
                case 'test':
                    return $this->testConnections();
                default:
                    $this->error("未知的操作: {$action}");
                    $this->showHelp();
                    return 1;
            }
        } catch (\Exception $e) {
            $this->error("命令执行失败: {$e->getMessage()}");
            if ($this->option('verbose')) {
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * 显示配置信息
     *
     * @return int
     */
    private function showConfiguration(): int
    {
        $key = $this->option('key');
        $platform = $this->option('platform');
        $format = $this->option('format');
        $includeSensitive = $this->option('include-sensitive');

        if ($key) {
            // 显示特定配置
            $value = $this->configService->get($key);
            $this->displayValue($key, $value, $format);
        } elseif ($platform) {
            // 显示平台配置
            $config = $this->configService->getPlatformConfig($platform);
            $this->displayData("平台 {$platform} 配置", $config, $format);
        } else {
            // 显示所有配置
            $configs = $this->configService->getAllConfigs($includeSensitive);
            $this->displayData('系统配置', $configs, $format);
        }

        return 0;
    }

    /**
     * 验证配置
     *
     * @return int
     */
    private function validateConfiguration(): int
    {
        $this->info('🔍 正在验证系统配置...');
        
        $results = $this->configService->validateConfiguration();
        
        if ($results['valid']) {
            $this->info('✅ 配置验证通过！');
        } else {
            $this->error('❌ 配置验证失败！');
        }

        // 显示错误
        if (!empty($results['errors'])) {
            $this->newLine();
            $this->error('🚨 错误:');
            foreach ($results['errors'] as $error) {
                $this->line("  • {$error}");
            }
        }

        // 显示警告
        if (!empty($results['warnings'])) {
            $this->newLine();
            $this->warn('⚠️  警告:');
            foreach ($results['warnings'] as $warning) {
                $this->line("  • {$warning}");
            }
        }

        // 显示缺失的配置
        if (!empty($results['missing'])) {
            $this->newLine();
            $this->error('📋 缺失的环境变量:');
            foreach ($results['missing'] as $missing) {
                $this->line("  • {$missing}");
            }
        }

        return $results['valid'] ? 0 : 1;
    }

    /**
     * 导出配置
     *
     * @return int
     */
    private function exportConfiguration(): int
    {
        $format = $this->option('format');
        $output = $this->option('output');
        $includeSensitive = $this->option('include-sensitive');

        $configs = $this->configService->getAllConfigs($includeSensitive);

        if ($output) {
            // 导出到文件
            $content = $this->formatData($configs, $format);
            File::put($output, $content);
            $this->info("配置已导出到: {$output}");
        } else {
            // 输出到控制台
            $this->displayData('导出配置', $configs, $format);
        }

        return 0;
    }

    /**
     * 检查环境变量
     *
     * @return int
     */
    private function checkEnvironment(): int
    {
        $envFile = $this->option('env-file');
        
        if (!File::exists($envFile)) {
            $this->error("环境变量文件不存在: {$envFile}");
            return 1;
        }

        $this->info("🔍 检查环境变量文件: {$envFile}");
        
        $envContent = File::get($envFile);
        $envLines = explode("\n", $envContent);
        
        $envVars = [];
        $comments = [];
        $emptyVars = [];
        
        foreach ($envLines as $lineNum => $line) {
            $line = trim($line);
            
            if (empty($line)) {
                continue;
            }
            
            if (Str::startsWith($line, '#')) {
                $comments[] = $lineNum + 1;
                continue;
            }
            
            if (Str::contains($line, '=')) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                $envVars[$key] = $value;
                
                if (empty($value)) {
                    $emptyVars[] = $key;
                }
            }
        }

        // 显示统计信息
        $this->table(['项目', '数量'], [
            ['环境变量总数', count($envVars)],
            ['注释行数', count($comments)],
            ['空值变量', count($emptyVars)]
        ]);

        // 显示空值变量
        if (!empty($emptyVars)) {
            $this->newLine();
            $this->warn('⚠️  空值环境变量:');
            foreach ($emptyVars as $var) {
                $this->line("  • {$var}");
            }
        }

        return 0;
    }

    /**
     * 测试连接
     *
     * @return int
     */
    private function testConnections(): int
    {
        $this->info('🔗 测试系统连接...');
        
        $results = [];
        
        // 测试数据库连接
        try {
            \DB::connection()->getPdo();
            $results[] = ['数据库', '✅ 连接成功'];
        } catch (\Exception $e) {
            $results[] = ['数据库', "❌ 连接失败: {$e->getMessage()}"];
        }

        // 测试缓存连接
        try {
            \Cache::put('test_key', 'test_value', 10);
            \Cache::forget('test_key');
            $results[] = ['缓存', '✅ 连接成功'];
        } catch (\Exception $e) {
            $results[] = ['缓存', "❌ 连接失败: {$e->getMessage()}"];
        }

        // 测试队列连接
        try {
            $queueConfig = $this->configService->getQueueConfig();
            $connection = $queueConfig['connection'];
            
            // 简单的队列连接测试
            \Queue::connection($connection);
            $results[] = ['队列', '✅ 连接成功'];
        } catch (\Exception $e) {
            $results[] = ['队列', "❌ 连接失败: {$e->getMessage()}"];
        }

        // 测试平台API连接
        $platforms = $this->configService->get('datacollection.platforms', []);
        foreach ($platforms as $platform => $config) {
            if (!empty($config['base_url'])) {
                try {
                    $response = \Http::timeout(10)->get($config['base_url']);
                    if ($response->successful()) {
                        $results[] = ["{$platform} API", '✅ 连接成功'];
                    } else {
                        $results[] = ["{$platform} API", "⚠️  响应异常: {$response->status()}"];
                    }
                } catch (\Exception $e) {
                    $results[] = ["{$platform} API", "❌ 连接失败: {$e->getMessage()}"];
                }
            } else {
                $results[] = ["{$platform} API", '⏭️  未配置'];
            }
        }

        $this->table(['服务', '状态'], $results);

        return 0;
    }

    /**
     * 显示值
     *
     * @param string $key 键名
     * @param mixed $value 值
     * @param string $format 格式
     * @return void
     */
    private function displayValue(string $key, $value, string $format): void
    {
        switch ($format) {
            case 'json':
                $this->line(json_encode([$key => $value], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                break;
            case 'yaml':
                $this->line(yaml_emit([$key => $value]));
                break;
            default:
                $this->info("配置键: {$key}");
                $this->line("值: " . (is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value));
        }
    }

    /**
     * 显示数据
     *
     * @param string $title 标题
     * @param array $data 数据
     * @param string $format 格式
     * @return void
     */
    private function displayData(string $title, array $data, string $format): void
    {
        $this->info($title);
        $this->newLine();

        switch ($format) {
            case 'json':
                $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                break;
            case 'yaml':
                if (function_exists('yaml_emit')) {
                    $this->line(yaml_emit($data));
                } else {
                    $this->warn('YAML扩展未安装，使用JSON格式');
                    $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
                break;
            default:
                $this->displayArrayAsTable($data);
        }
    }

    /**
     * 格式化数据
     *
     * @param array $data 数据
     * @param string $format 格式
     * @return string
     */
    private function formatData(array $data, string $format): string
    {
        switch ($format) {
            case 'json':
                return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case 'yaml':
                if (function_exists('yaml_emit')) {
                    return yaml_emit($data);
                }
                // 回退到JSON
                return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            default:
                return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 将数组显示为表格
     *
     * @param array $data 数据
     * @param string $prefix 前缀
     * @return void
     */
    private function displayArrayAsTable(array $data, string $prefix = ''): void
    {
        $rows = [];
        
        foreach ($data as $key => $value) {
            $fullKey = $prefix ? "{$prefix}.{$key}" : $key;
            
            if (is_array($value)) {
                if (empty($value)) {
                    $rows[] = [$fullKey, '[]'];
                } else {
                    // 检查是否为简单数组
                    if (array_keys($value) === range(0, count($value) - 1)) {
                        $rows[] = [$fullKey, '[' . implode(', ', $value) . ']'];
                    } else {
                        $rows[] = [$fullKey, '[Object]'];
                        // 递归显示子项
                        foreach ($value as $subKey => $subValue) {
                            $subFullKey = "{$fullKey}.{$subKey}";
                            if (is_array($subValue)) {
                                $rows[] = [$subFullKey, is_array($subValue) ? '[Object]' : $subValue];
                            } else {
                                $rows[] = [$subFullKey, $subValue];
                            }
                        }
                    }
                }
            } else {
                $rows[] = [$fullKey, $value];
            }
        }

        $this->table(['配置键', '值'], $rows);
    }

    /**
     * 显示帮助信息
     *
     * @return void
     */
    private function showHelp(): void
    {
        $this->newLine();
        $this->info('可用操作:');
        $this->line('  show      - 显示配置信息');
        $this->line('  validate  - 验证配置完整性');
        $this->line('  export    - 导出配置');
        $this->line('  check     - 检查环境变量文件');
        $this->line('  test      - 测试系统连接');
        
        $this->newLine();
        $this->info('示例用法:');
        $this->line('  php artisan config:manage show');
        $this->line('  php artisan config:manage show --key=app.name');
        $this->line('  php artisan config:manage show --platform=taobao');
        $this->line('  php artisan config:manage validate');
        $this->line('  php artisan config:manage export --format=json --output=config.json');
        $this->line('  php artisan config:manage test');
    }
} 
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaskGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'color',
        'settings',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 分组创建者
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 分组下的监控任务
     */
    public function monitorTasks(): HasMany
    {
        return $this->hasMany(MonitorTask::class, 'task_group_id');
    }

    /**
     * 活跃的监控任务
     */
    public function activeMonitorTasks(): Has<PERSON>any
    {
        return $this->monitorTasks()->where('is_enabled', true)->where('status', 'active');
    }

    /**
     * 分组级别的告警规则
     */
    public function groupAlertRules(): HasMany
    {
        return $this->hasMany(GroupAlertRule::class);
    }

    /**
     * 分组统计信息
     */
    public function getStatsAttribute(): array
    {
        $tasks = $this->monitorTasks();
        
        return [
            'total_tasks' => $tasks->count(),
            'active_tasks' => $tasks->where('is_enabled', true)->where('status', 'active')->count(),
            'error_tasks' => $tasks->where('status', 'error')->count(),
            'paused_tasks' => $tasks->where('status', 'paused')->count(),
            'last_collection' => $tasks->max('last_collected_at'),
        ];
    }

    /**
     * 检查用户是否有权限访问这个分组
     */
    public function canAccess($userId): bool
    {
        return $this->user_id == $userId;
    }

    /**
     * 作用域：活跃的分组
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：用户的分组
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按排序顺序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}

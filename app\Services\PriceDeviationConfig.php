<?php

namespace App\Services;

/**
 * 价格偏差计算配置管理类
 * 
 * 统一管理价格偏差率计算相关的配置参数，确保整个应用中的一致性
 */
class PriceDeviationConfig
{
    /**
     * 计算精度 - 小数点后位数
     * 
     * @var int
     */
    public const PRECISION = 4;

    /**
     * 百分比转换常数
     * 
     * @var string
     */
    public const PERCENTAGE_MULTIPLIER = '100';

    /**
     * 最大允许的偏差率值（防止异常数据）
     * 
     * @var float
     */
    public const MAX_DEVIATION_RATE = 1000.0; // 1000%

    /**
     * 最小允许的偏差率值
     * 
     * @var float
     */
    public const MIN_DEVIATION_RATE = -100.0; // -100%

    /**
     * 价格的最小有效值
     * 
     * @var string
     */
    public const MIN_VALID_PRICE = '0.01';

    /**
     * 价格的最大有效值（防止异常输入）
     * 
     * @var string
     */
    public const MAX_VALID_PRICE = '999999999.99';

    /**
     * 批量处理的默认分块大小
     * 
     * @var int
     */
    public const DEFAULT_CHUNK_SIZE = 1000;

    /**
     * 批量处理的最大分块大小
     * 
     * @var int
     */
    public const MAX_CHUNK_SIZE = 10000;

    /**
     * 批量处理的最小分块大小
     * 
     * @var int
     */
    public const MIN_CHUNK_SIZE = 1;

    /**
     * 数据库查询超时时间（秒）
     * 
     * @var int
     */
    public const QUERY_TIMEOUT = 300; // 5分钟

    /**
     * 日志记录阈值配置
     */
    public const LOG_THRESHOLDS = [
        'batch_size_warning' => 5000,      // 批量处理记录数警告阈值
        'calculation_time_warning' => 60,  // 计算时间警告阈值（秒）
        'error_rate_warning' => 0.05,      // 错误率警告阈值（5%）
    ];

    /**
     * 异常值检测配置
     */
    public const ANOMALY_DETECTION = [
        'price_change_threshold' => 50.0,      // 价格变化超过50%视为异常
        'extreme_deviation_threshold' => 95.0, // 偏差率超过95%视为极端值
        'zero_price_warning' => true,          // 零价格警告开关
        'negative_deviation_warning' => true,  // 负偏差率警告开关
    ];

    /**
     * 性能监控配置
     */
    public const PERFORMANCE_CONFIG = [
        'enable_memory_monitoring' => true,    // 启用内存监控
        'memory_limit_warning' => 80,          // 内存使用率警告阈值（80%）
        'enable_time_tracking' => true,        // 启用时间跟踪
        'slow_calculation_threshold' => 1.0,   // 慢计算阈值（秒）
    ];

    /**
     * 数据验证规则
     */
    public const VALIDATION_RULES = [
        'price_fields' => ['price', 'sub_price', 'official_guide_price'],
        'required_fields' => ['sku_id', 'price', 'timestamp'],
        'nullable_fields' => ['sub_price', 'official_guide_price'],
        'numeric_fields' => ['price', 'sub_price', 'official_guide_price'],
    ];

    /**
     * 获取bcmath精度设置
     * 
     * @return int
     */
    public static function getPrecision(): int
    {
        return self::PRECISION;
    }

    /**
     * 初始化bcmath精度
     * 
     * @return void
     */
    public static function initializeBcmathPrecision(): void
    {
        bcscale(self::PRECISION);
    }

    /**
     * 验证偏差率是否在有效范围内
     * 
     * @param float|null $deviationRate
     * @return bool
     */
    public static function isValidDeviationRate(?float $deviationRate): bool
    {
        if ($deviationRate === null) {
            return true; // null值是有效的
        }

        return $deviationRate >= self::MIN_DEVIATION_RATE && 
               $deviationRate <= self::MAX_DEVIATION_RATE;
    }

    /**
     * 验证价格是否在有效范围内
     * 
     * @param string|float|null $price
     * @return bool
     */
    public static function isValidPrice($price): bool
    {
        if ($price === null) {
            return true; // null值是有效的
        }

        if (!is_numeric($price)) {
            return false;
        }

        $priceStr = (string) $price;
        
        return bccomp($priceStr, self::MIN_VALID_PRICE, self::PRECISION) >= 0 &&
               bccomp($priceStr, self::MAX_VALID_PRICE, self::PRECISION) <= 0;
    }

    /**
     * 检测是否为异常偏差率
     * 
     * @param float|null $deviationRate
     * @return bool
     */
    public static function isExtremeDeviation(?float $deviationRate): bool
    {
        if ($deviationRate === null) {
            return false;
        }

        return abs($deviationRate) > self::ANOMALY_DETECTION['extreme_deviation_threshold'];
    }

    /**
     * 获取推荐的分块大小
     * 
     * @param int $totalRecords
     * @param int $availableMemoryMB
     * @return int
     */
    public static function getRecommendedChunkSize(int $totalRecords, int $availableMemoryMB = 512): int
    {
        // 根据总记录数和可用内存计算推荐的分块大小
        $baseChunkSize = min($totalRecords, self::DEFAULT_CHUNK_SIZE);
        
        // 内存不足时减小分块大小
        if ($availableMemoryMB < 256) {
            $baseChunkSize = min($baseChunkSize, 500);
        } elseif ($availableMemoryMB > 1024) {
            $baseChunkSize = min($baseChunkSize, 2000);
        }

        return max(self::MIN_CHUNK_SIZE, min($baseChunkSize, self::MAX_CHUNK_SIZE));
    }

    /**
     * 获取配置的完整数组形式
     * 
     * @return array
     */
    public static function toArray(): array
    {
        return [
            'precision' => self::PRECISION,
            'percentage_multiplier' => self::PERCENTAGE_MULTIPLIER,
            'max_deviation_rate' => self::MAX_DEVIATION_RATE,
            'min_deviation_rate' => self::MIN_DEVIATION_RATE,
            'min_valid_price' => self::MIN_VALID_PRICE,
            'max_valid_price' => self::MAX_VALID_PRICE,
            'default_chunk_size' => self::DEFAULT_CHUNK_SIZE,
            'max_chunk_size' => self::MAX_CHUNK_SIZE,
            'min_chunk_size' => self::MIN_CHUNK_SIZE,
            'query_timeout' => self::QUERY_TIMEOUT,
            'log_thresholds' => self::LOG_THRESHOLDS,
            'anomaly_detection' => self::ANOMALY_DETECTION,
            'performance_config' => self::PERFORMANCE_CONFIG,
            'validation_rules' => self::VALIDATION_RULES,
        ];
    }
} 
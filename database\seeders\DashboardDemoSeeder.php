<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\MonitorTask;
use App\Models\PriceHistory;
use Carbon\Carbon;

class DashboardDemoSeeder extends Seeder
{
    public function run()
    {
        // 获取第一个用户ID（admin用户）
        $adminUserId = 1;

        // 创建示例产品
        $product1 = Product::create([
            'title' => 'iPhone 15 Pro',
            'description' => '苹果最新旗舰手机',
            'shop_name' => 'Apple官方旗舰店',
            'shop_type' => 'official',
            'source_platform' => 'taobao',
            'min_price' => 8999.00,
            'max_price' => 12999.00,
            'total_sales' => 15420,
            'rating' => 4.85,
            'review_count' => 3256
        ]);

        $product2 = Product::create([
            'title' => '华为 Mate 60 Pro', 
            'description' => '华为旗舰手机',
            'shop_name' => '华为官方旗舰店',
            'shop_type' => 'official',
            'source_platform' => 'jd',
            'min_price' => 6999.00,
            'max_price' => 8999.00,
            'total_sales' => 12300,
            'rating' => 4.78,
            'review_count' => 2845
        ]);

        $product3 = Product::create([
            'title' => '小米14 Ultra',
            'description' => '小米影像旗舰',
            'shop_name' => '小米官方旗舰店',
            'shop_type' => 'official',
            'source_platform' => 'jd',
            'min_price' => 5999.00,
            'max_price' => 7999.00,
            'total_sales' => 8950,
            'rating' => 4.72,
            'review_count' => 1987
        ]);

        // 创建监控任务
        $task1 = MonitorTask::create([
            'user_id' => $adminUserId,
            'product_id' => $product1->id,
            'task_name' => 'iPhone价格监控',
            'description' => '监控iPhone 15 Pro的价格变化',
            'platform' => 'taobao',
            'frequency' => '1hour',
            'monitor_type' => 'product',
            'status' => 'active',
            'next_collection_at' => now()->addHour()
        ]);

        $task2 = MonitorTask::create([
            'user_id' => $adminUserId,
            'product_id' => $product2->id,
            'task_name' => '华为价格监控',
            'description' => '监控华为 Mate 60 Pro的价格变化',
            'platform' => 'jd',
            'frequency' => '1hour',
            'monitor_type' => 'product',
            'status' => 'active',
            'next_collection_at' => now()->addHour()
        ]);

        $task3 = MonitorTask::create([
            'user_id' => $adminUserId,
            'product_id' => $product3->id,
            'task_name' => '小米价格监控',
            'description' => '监控小米14 Ultra的价格变化',
            'platform' => 'jd',
            'frequency' => '30min',
            'monitor_type' => 'product',
            'status' => 'active',
            'next_collection_at' => now()->addMinutes(30)
        ]);

        $this->command->info('仪表板演示数据创建完成！');
        $this->command->info('创建了 3 个产品和 3 个监控任务');
    }
} 
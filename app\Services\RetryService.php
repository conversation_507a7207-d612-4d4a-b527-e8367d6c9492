<?php

namespace App\Services;

use Closure;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\TooManyRedirectsException;
use Psr\Http\Message\ResponseInterface;

/**
 * 重试服务类
 * 
 * 提供指数退避和重试机制，用于处理临时性网络问题或API限流
 */
class RetryService
{
    /**
     * 默认最大重试次数
     */
    const DEFAULT_MAX_RETRIES = 3;
    
    /**
     * 默认基础延迟时间（毫秒）
     */
    const DEFAULT_BASE_DELAY = 1000;
    
    /**
     * 默认最大延迟时间（毫秒）
     */
    const DEFAULT_MAX_DELAY = 30000;
    
    /**
     * 默认退避倍数
     */
    const DEFAULT_BACKOFF_MULTIPLIER = 2;
    
    /**
     * 默认抖动因子（0-1之间）
     */
    const DEFAULT_JITTER_FACTOR = 0.1;

    /**
     * 可重试的HTTP状态码
     */
    const RETRYABLE_STATUS_CODES = [
        408, // Request Timeout
        429, // Too Many Requests
        500, // Internal Server Error
        502, // Bad Gateway
        503, // Service Unavailable
        504, // Gateway Timeout
        520, // Unknown Error
        521, // Web Server Is Down
        522, // Connection Timed Out
        523, // Origin Is Unreachable
        524, // A Timeout Occurred
    ];

    /**
     * 可重试的异常类型
     */
    const RETRYABLE_EXCEPTIONS = [
        ConnectException::class,
        TooManyRedirectsException::class,
    ];

    /**
     * 重试配置
     *
     * @var array
     */
    private $config;

    /**
     * 构造函数
     *
     * @param array $config 重试配置
     */
    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'max_retries' => self::DEFAULT_MAX_RETRIES,
            'base_delay' => self::DEFAULT_BASE_DELAY,
            'max_delay' => self::DEFAULT_MAX_DELAY,
            'backoff_multiplier' => self::DEFAULT_BACKOFF_MULTIPLIER,
            'jitter_factor' => self::DEFAULT_JITTER_FACTOR,
            'retryable_status_codes' => self::RETRYABLE_STATUS_CODES,
            'retryable_exceptions' => self::RETRYABLE_EXCEPTIONS,
        ], $config);
    }

    /**
     * 执行带重试的操作
     *
     * @param Closure $operation 要执行的操作
     * @param array $options 重试选项
     * @return mixed
     * @throws Exception
     */
    public function execute(Closure $operation, array $options = [])
    {
        $config = array_merge($this->config, $options);
        $maxRetries = $config['max_retries'];
        $attempt = 0;
        $lastException = null;

        while ($attempt <= $maxRetries) {
            try {
                $result = $operation();
                
                // 如果是HTTP响应，检查状态码
                if ($result instanceof ResponseInterface) {
                    if ($this->isRetryableResponse($result, $config)) {
                        throw new RequestException(
                            "HTTP {$result->getStatusCode()}: Retryable response",
                            new \GuzzleHttp\Psr7\Request('GET', ''),
                            $result
                        );
                    }
                }
                
                // 操作成功，记录成功日志
                if ($attempt > 0) {
                    Log::info('重试操作成功', [
                        'attempt' => $attempt + 1,
                        'total_attempts' => $attempt + 1
                    ]);
                }
                
                return $result;
                
            } catch (Exception $e) {
                $lastException = $e;
                
                // 检查是否应该重试
                if ($attempt >= $maxRetries || !$this->shouldRetry($e, $config)) {
                    break;
                }
                
                $delay = $this->calculateDelay($attempt, $config);
                
                Log::warning('操作失败，准备重试', [
                    'attempt' => $attempt + 1,
                    'max_retries' => $maxRetries,
                    'delay_ms' => $delay,
                    'exception' => $e->getMessage(),
                    'exception_type' => get_class($e)
                ]);
                
                // 等待指定时间后重试
                usleep($delay * 1000); // 转换为微秒
                $attempt++;
            }
        }

        // 所有重试都失败了
        Log::error('重试操作最终失败', [
            'total_attempts' => $attempt,
            'max_retries' => $maxRetries,
            'final_exception' => $lastException->getMessage(),
            'exception_type' => get_class($lastException)
        ]);

        throw $lastException;
    }

    /**
     * 异步执行带重试的操作（使用队列）
     *
     * @param string $jobClass 任务类名
     * @param array $jobData 任务数据
     * @param array $options 重试选项
     * @return void
     */
    public function executeAsync(string $jobClass, array $jobData, array $options = [])
    {
        $config = array_merge($this->config, $options);
        
        // 创建重试任务
        $job = new $jobClass($jobData, $config);
        
        // 设置重试次数和延迟
        $job->tries = $config['max_retries'] + 1; // +1 因为第一次不是重试
        $job->backoff = $this->generateBackoffSequence($config);
        
        dispatch($job);
    }

    /**
     * 检查响应是否应该重试
     *
     * @param ResponseInterface $response HTTP响应
     * @param array $config 配置
     * @return bool
     */
    private function isRetryableResponse(ResponseInterface $response, array $config): bool
    {
        $statusCode = $response->getStatusCode();
        return in_array($statusCode, $config['retryable_status_codes']);
    }

    /**
     * 检查异常是否应该重试
     *
     * @param Exception $exception 异常
     * @param array $config 配置
     * @return bool
     */
    private function shouldRetry(Exception $exception, array $config): bool
    {
        // 检查异常类型
        foreach ($config['retryable_exceptions'] as $retryableException) {
            if ($exception instanceof $retryableException) {
                return true;
            }
        }
        
        // 检查HTTP异常的状态码
        if ($exception instanceof RequestException) {
            $response = $exception->getResponse();
            if ($response) {
                return $this->isRetryableResponse($response, $config);
            }
        }
        
        return false;
    }

    /**
     * 计算延迟时间（指数退避 + 抖动）
     *
     * @param int $attempt 当前重试次数（从0开始）
     * @param array $config 配置
     * @return int 延迟时间（毫秒）
     */
    private function calculateDelay(int $attempt, array $config): int
    {
        $baseDelay = $config['base_delay'];
        $multiplier = $config['backoff_multiplier'];
        $maxDelay = $config['max_delay'];
        $jitterFactor = $config['jitter_factor'];
        
        // 计算指数退避延迟
        $exponentialDelay = $baseDelay * pow($multiplier, $attempt);
        
        // 限制最大延迟
        $delay = min($exponentialDelay, $maxDelay);
        
        // 添加抖动以避免雷群效应
        if ($jitterFactor > 0) {
            $jitter = $delay * $jitterFactor * (mt_rand() / mt_getrandmax());
            $delay = $delay + $jitter;
        }
        
        return (int) $delay;
    }

    /**
     * 生成退避序列（用于队列任务）
     *
     * @param array $config 配置
     * @return array
     */
    private function generateBackoffSequence(array $config): array
    {
        $sequence = [];
        $maxRetries = $config['max_retries'];
        
        for ($i = 0; $i < $maxRetries; $i++) {
            $delay = $this->calculateDelay($i, $config);
            $sequence[] = $delay / 1000; // 队列需要秒数
        }
        
        return $sequence;
    }

    /**
     * 创建HTTP客户端重试中间件（Guzzle）
     *
     * @param array $options 重试选项
     * @return callable
     */
    public function createGuzzleRetryMiddleware(array $options = []): callable
    {
        $config = array_merge($this->config, $options);
        
        return \GuzzleHttp\Middleware::retry(
            function ($retries, $request, $response = null, $exception = null) use ($config) {
                // 检查是否超过最大重试次数
                if ($retries >= $config['max_retries']) {
                    return false;
                }
                
                // 检查是否应该重试
                if ($exception) {
                    return $this->shouldRetry($exception, $config);
                }
                
                if ($response) {
                    return $this->isRetryableResponse($response, $config);
                }
                
                return false;
            },
            function ($retries) use ($config) {
                return $this->calculateDelay($retries - 1, $config);
            }
        );
    }

    /**
     * 获取重试统计信息
     *
     * @param string $key 统计键
     * @return array
     */
    public function getRetryStats(string $key): array
    {
        $cacheKey = "retry_stats:{$key}";
        return Cache::get($cacheKey, [
            'total_attempts' => 0,
            'successful_attempts' => 0,
            'failed_attempts' => 0,
            'average_attempts' => 0,
            'last_attempt_time' => null,
        ]);
    }

    /**
     * 记录重试统计信息
     *
     * @param string $key 统计键
     * @param int $attempts 尝试次数
     * @param bool $success 是否成功
     * @return void
     */
    public function recordRetryStats(string $key, int $attempts, bool $success): void
    {
        $cacheKey = "retry_stats:{$key}";
        $stats = $this->getRetryStats($key);
        
        $stats['total_attempts'] += $attempts;
        $stats['last_attempt_time'] = now()->toISOString();
        
        if ($success) {
            $stats['successful_attempts']++;
        } else {
            $stats['failed_attempts']++;
        }
        
        // 计算平均尝试次数
        $totalOperations = $stats['successful_attempts'] + $stats['failed_attempts'];
        if ($totalOperations > 0) {
            $stats['average_attempts'] = $stats['total_attempts'] / $totalOperations;
        }
        
        Cache::put($cacheKey, $stats, now()->addDays(7));
    }

    /**
     * 清除重试统计信息
     *
     * @param string $key 统计键
     * @return void
     */
    public function clearRetryStats(string $key): void
    {
        Cache::forget("retry_stats:{$key}");
    }

    /**
     * 创建重试配置
     *
     * @param array $overrides 覆盖配置
     * @return array
     */
    public static function createConfig(array $overrides = []): array
    {
        return array_merge([
            'max_retries' => self::DEFAULT_MAX_RETRIES,
            'base_delay' => self::DEFAULT_BASE_DELAY,
            'max_delay' => self::DEFAULT_MAX_DELAY,
            'backoff_multiplier' => self::DEFAULT_BACKOFF_MULTIPLIER,
            'jitter_factor' => self::DEFAULT_JITTER_FACTOR,
            'retryable_status_codes' => self::RETRYABLE_STATUS_CODES,
            'retryable_exceptions' => self::RETRYABLE_EXCEPTIONS,
        ], $overrides);
    }

    /**
     * 获取当前配置
     *
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * 更新配置
     *
     * @param array $config 新配置
     * @return void
     */
    public function updateConfig(array $config): void
    {
        $this->config = array_merge($this->config, $config);
    }
} 
<?php

namespace App\Http\Controllers;

use App\Models\AuditLog;
use App\Services\PerformanceMonitorService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class MonitoringController extends Controller
{
    private PerformanceMonitorService $performanceMonitor;

    public function __construct(PerformanceMonitorService $performanceMonitor)
    {
        $this->performanceMonitor = $performanceMonitor;
    }

    /**
     * 显示监控仪表板
     */
    public function dashboard()
    {
        $overview = $this->performanceMonitor->getSystemOverview();
        $recentLogs = AuditLog::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('monitoring.dashboard', compact('overview', 'recentLogs'));
    }

    /**
     * 获取系统性能概览
     */
    public function getSystemOverview(): JsonResponse
    {
        try {
            $overview = $this->performanceMonitor->getSystemOverview();

            return response()->json([
                'success' => true,
                'data' => $overview,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            Log::error('获取系统概览失败', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => '获取系统概览失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取响应时间统计
     */
    public function getResponseTimeStats(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);
            $stats = $this->performanceMonitor->getAverageResponseTime($hours);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取响应时间统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取请求统计
     */
    public function getRequestStats(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);
            $stats = $this->performanceMonitor->getRequestCount($hours);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取请求统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取错误率统计
     */
    public function getErrorRateStats(Request $request): JsonResponse
    {
        try {
            $hours = $request->input('hours', 24);
            $stats = $this->performanceMonitor->getErrorRate($hours);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取错误率统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取API端点性能排名
     */
    public function getApiPerformance(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 10);
            $hours = $request->input('hours', 24);
            $performance = $this->performanceMonitor->getApiEndpointPerformance($limit, $hours);

            return response()->json([
                'success' => true,
                'data' => $performance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取API性能数据失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取审计日志
     */
    public function getAuditLogs(Request $request): JsonResponse
    {
        try {
            $query = AuditLog::with('user')->orderBy('created_at', 'desc');

            // 过滤条件
            if ($request->has('user_id')) {
                $query->byUser($request->input('user_id'));
            }

            if ($request->has('event')) {
                $query->byEvent($request->input('event'));
            }

            if ($request->has('days')) {
                $query->recent($request->input('days'));
            }

            $logs = $query->paginate($request->input('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $logs,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取审计日志失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取用户活动统计
     */
    public function getUserActivityStats(Request $request): JsonResponse
    {
        try {
            $days = $request->input('days', 7);
            $since = now()->subDays($days);

            // 用户登录统计
            $loginStats = AuditLog::where('event', AuditLog::EVENT_LOGIN)
                ->where('created_at', '>=', $since)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // 活跃用户统计
            $activeUsers = AuditLog::where('created_at', '>=', $since)
                ->whereNotNull('user_id')
                ->distinct('user_id')
                ->count();

            // 操作类型统计
            $eventStats = AuditLog::where('created_at', '>=', $since)
                ->selectRaw('event, COUNT(*) as count')
                ->groupBy('event')
                ->orderByDesc('count')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'login_stats' => $loginStats,
                    'active_users' => $activeUsers,
                    'event_stats' => $eventStats,
                    'period_days' => $days,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取用户活动统计失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清除监控缓存
     */
    public function clearCache(): JsonResponse
    {
        try {
            $this->performanceMonitor->clearCache();

            return response()->json([
                'success' => true,
                'message' => '监控缓存已清除',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '清除缓存失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 健康检查
     */
    public function healthCheck(): JsonResponse
    {
        try {
            $checks = [
                'database' => $this->checkDatabase(),
                'cache' => $this->checkCache(),
                'storage' => $this->checkStorage(),
                'memory' => $this->checkMemory(),
            ];

            $allHealthy = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

            return response()->json([
                'success' => true,
                'overall_status' => $allHealthy ? 'healthy' : 'unhealthy',
                'checks' => $checks,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'overall_status' => 'error',
                'message' => '健康检查失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabase(): array
    {
        try {
            $startTime = microtime(true);
            \DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
                'message' => '数据库连接正常',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => '数据库连接失败: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 检查缓存系统
     */
    private function checkCache(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            \Cache::put($testKey, $testValue, 60);
            $retrieved = \Cache::get($testKey);
            \Cache::forget($testKey);

            if ($retrieved === $testValue) {
                return [
                    'status' => 'healthy',
                    'message' => '缓存系统正常',
                    'driver' => config('cache.default'),
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => '缓存读写测试失败',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => '缓存系统异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 检查存储系统
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'health check test';

            \Storage::put($testFile, $testContent);
            $retrieved = \Storage::get($testFile);
            \Storage::delete($testFile);

            if ($retrieved === $testContent) {
                return [
                    'status' => 'healthy',
                    'message' => '存储系统正常',
                    'driver' => config('filesystems.default'),
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => '存储读写测试失败',
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => '存储系统异常: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * 检查内存使用情况
     */
    private function checkMemory(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $usagePercentage = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;

        $status = $usagePercentage < 80 ? 'healthy' : ($usagePercentage < 95 ? 'warning' : 'critical');

        return [
            'status' => $status,
            'usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percentage' => round($usagePercentage, 2),
            'message' => "内存使用率: {$usagePercentage}%",
        ];
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompetitorMetric extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'sku_id',
        'own_sku_id',
        'date',
        'sales_volume',
        'sales_revenue',
        'market_share_volume',
        'market_share_revenue',
        'promotion_types',
        'total_promotions',
        'promotion_frequency',
        'avg_discount_rate',
        'max_discount_rate',
        'min_discount_rate',
        'promotion_duration_avg',
        'analysis_summary',
        'price_deviation_rate',
    ];

    protected $casts = [
        'promotion_types' => 'array',
        'date' => 'date',
    ];

    public function sku()
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }

    public function ownSku()
    {
        return $this->belongsTo(ProductSku::class, 'own_sku_id');
    }
} 
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('price_history', function (Blueprint $table) {
            // 官方指导价 - 用于渠道价格偏差率计算
            $table->decimal('official_guide_price', 10, 2)->nullable()->after('sub_price')
                  ->comment('官方指导价');
            
            // 促销价格偏差率 = (price - sub_price) / price × 100%
            $table->decimal('promotion_deviation_rate', 7, 4)->nullable()->after('official_guide_price')
                  ->comment('促销价格偏差率(%)');
            
            // 渠道价格偏差率 = (official_guide_price - sub_price) / official_guide_price × 100%
            $table->decimal('channel_deviation_rate', 7, 4)->nullable()->after('promotion_deviation_rate')
                  ->comment('渠道价格偏差率(%)');
            
            // 计算时间戳 - 用于跟踪何时计算的偏差率
            $table->timestamp('deviation_calculated_at')->nullable()->after('channel_deviation_rate')
                  ->comment('偏差率计算时间');
            
            // 计算状态 - 用于标记计算状态
            $table->enum('calculation_status', ['pending', 'calculated', 'failed'])->default('pending')
                  ->after('deviation_calculated_at')->comment('计算状态');
            
            // 添加索引用于查询优化
            $table->index(['promotion_deviation_rate']);
            $table->index(['channel_deviation_rate']);
            $table->index(['calculation_status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('price_history', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex(['promotion_deviation_rate']);
            $table->dropIndex(['channel_deviation_rate']);
            $table->dropIndex(['calculation_status', 'created_at']);
            
            // 删除字段
            $table->dropColumn([
                'official_guide_price',
                'promotion_deviation_rate',
                'channel_deviation_rate',
                'deviation_calculated_at',
                'calculation_status'
            ]);
        });
    }
};

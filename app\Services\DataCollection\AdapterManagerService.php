<?php

namespace App\Services\DataCollection;

use App\Models\ApiConfiguration;
use App\Services\DataCollection\Contracts\PlatformAdapterInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 适配器管理服务
 * 负责管理和协调所有平台适配器
 */
class AdapterManagerService
{
    private array $activeAdapters = [];
    private array $adapterCache = [];

    /**
     * 获取指定平台的适配器
     *
     * @param string $platform 平台名称
     * @return PlatformAdapterInterface|null
     */
    public function getAdapter(string $platform): ?PlatformAdapterInterface
    {
        // 先从缓存中查找
        if (isset($this->adapterCache[$platform])) {
            return $this->adapterCache[$platform];
        }

        // 从API配置中查找活跃的配置
        $apiConfig = ApiConfiguration::where('platform_type', $platform)
            ->where('is_active', true)
            ->first();

        if (!$apiConfig) {
            Log::warning("未找到平台 {$platform} 的活跃API配置");
            return null;
        }

        try {
            $adapter = PlatformAdapterFactory::createFromApiConfiguration($apiConfig);
            $this->adapterCache[$platform] = $adapter;
            return $adapter;
        } catch (Exception $e) {
            Log::error("创建平台 {$platform} 适配器失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取所有可用的适配器
     *
     * @return array 平台适配器数组
     */
    public function getAllAdapters(): array
    {
        $adapters = [];
        $supportedPlatforms = PlatformAdapterFactory::getSupportedPlatforms();

        foreach ($supportedPlatforms as $platform) {
            $adapter = $this->getAdapter($platform);
            if ($adapter) {
                $adapters[$platform] = $adapter;
            }
        }

        return $adapters;
    }

    /**
     * 执行所有适配器的健康检查
     *
     * @return array 健康检查结果
     */
    public function performHealthCheckAll(): array
    {
        $results = [];
        $adapters = $this->getAllAdapters();

        foreach ($adapters as $platform => $adapter) {
            try {
                $results[$platform] = $adapter->performHealthCheck();
            } catch (Exception $e) {
                $results[$platform] = [
                    'status' => 'error',
                    'platform' => $platform,
                    'error' => $e->getMessage(),
                    'timestamp' => now(),
                ];
            }
        }

        return $results;
    }

    /**
     * 获取所有适配器的统计信息
     *
     * @return array 统计信息
     */
    public function getStatisticsAll(): array
    {
        $statistics = [];
        $adapters = $this->getAllAdapters();

        foreach ($adapters as $platform => $adapter) {
            try {
                $statistics[$platform] = $adapter->getStatistics();
            } catch (Exception $e) {
                $statistics[$platform] = [
                    'platform' => $platform,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $statistics;
    }

    /**
     * 刷新适配器缓存
     *
     * @param string|null $platform 指定平台，null表示刷新所有
     * @return void
     */
    public function refreshCache(?string $platform = null): void
    {
        if ($platform) {
            unset($this->adapterCache[$platform]);
        } else {
            $this->adapterCache = [];
        }
    }

    /**
     * 测试指定平台的连接
     *
     * @param string $platform 平台名称
     * @return array 测试结果
     */
    public function testConnection(string $platform): array
    {
        $adapter = $this->getAdapter($platform);
        
        if (!$adapter) {
            return [
                'status' => 'error',
                'platform' => $platform,
                'error' => '无法获取适配器',
                'timestamp' => now(),
            ];
        }

        return $adapter->performHealthCheck();
    }

    /**
     * 获取平台支持的功能
     *
     * @param string $platform 平台名称
     * @return array 支持的功能列表
     */
    public function getSupportedFeatures(string $platform): array
    {
        $adapter = $this->getAdapter($platform);
        
        if (!$adapter) {
            return [];
        }

        return $adapter->getSupportedFeatures();
    }

    /**
     * 批量执行数据收集任务
     *
     * @param array $tasks 任务列表
     * @return array 执行结果
     */
    public function executeBatchTasks(array $tasks): array
    {
        $results = [];

        foreach ($tasks as $task) {
            $platform = $task['platform'] ?? '';
            $method = $task['method'] ?? '';
            $params = $task['params'] ?? [];

            $adapter = $this->getAdapter($platform);
            if (!$adapter) {
                $results[] = [
                    'task' => $task,
                    'status' => 'error',
                    'error' => "无法获取平台 {$platform} 的适配器",
                ];
                continue;
            }

            try {
                $result = $this->executeAdapterMethod($adapter, $method, $params);
                $results[] = [
                    'task' => $task,
                    'status' => 'success',
                    'data' => $result,
                ];
            } catch (Exception $e) {
                $results[] = [
                    'task' => $task,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * 执行适配器方法
     *
     * @param PlatformAdapterInterface $adapter 适配器实例
     * @param string $method 方法名
     * @param array $params 参数
     * @return mixed 执行结果
     * @throws Exception
     */
    private function executeAdapterMethod(PlatformAdapterInterface $adapter, string $method, array $params)
    {
        switch ($method) {
            case 'getItemDetail':
                return $adapter->getItemDetail($params['item_id'] ?? '', $params['options'] ?? []);
            
            case 'getSimilarProducts':
                return $adapter->getSimilarProducts($params['item_id'] ?? '', $params['options'] ?? []);
            
            case 'getShopItems':
                return $adapter->getShopItems(
                    $params['shop_id'] ?? '', 
                    $params['page'] ?? 1, 
                    $params['options'] ?? []
                );
            
            case 'searchItems':
                return $adapter->searchItems(
                    $params['keyword'] ?? '', 
                    $params['page'] ?? 1, 
                    $params['options'] ?? []
                );
            
            default:
                throw new Exception("不支持的方法: {$method}");
        }
    }

    /**
     * 获取适配器性能报告
     *
     * @return array 性能报告
     */
    public function getPerformanceReport(): array
    {
        $report = [
            'timestamp' => now(),
            'platforms' => [],
            'summary' => [
                'total_platforms' => 0,
                'healthy_platforms' => 0,
                'unhealthy_platforms' => 0,
                'total_requests' => 0,
                'successful_requests' => 0,
                'failed_requests' => 0,
            ],
        ];

        $adapters = $this->getAllAdapters();
        $report['summary']['total_platforms'] = count($adapters);

        foreach ($adapters as $platform => $adapter) {
            $healthCheck = $adapter->performHealthCheck();
            $statistics = $adapter->getStatistics();

            $report['platforms'][$platform] = [
                'health' => $healthCheck,
                'statistics' => $statistics,
                'features' => $adapter->getSupportedFeatures(),
                'api_version' => $adapter->getApiVersion(),
            ];

            // 更新汇总统计
            if ($healthCheck['status'] === 'healthy') {
                $report['summary']['healthy_platforms']++;
            } else {
                $report['summary']['unhealthy_platforms']++;
            }

            $report['summary']['total_requests'] += $statistics['requests_made'] ?? 0;
            $report['summary']['successful_requests'] += $statistics['successful_requests'] ?? 0;
            $report['summary']['failed_requests'] += $statistics['failed_requests'] ?? 0;
        }

        return $report;
    }
}

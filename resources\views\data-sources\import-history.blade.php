@extends('layouts.app')

@section('title', '导入历史')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('data-sources.index') }}">数据源管理</a></li>
    <li class="breadcrumb-item active">导入历史</li>
@endsection

@section('page-title', '导入历史')

@section('page-actions')
    <div class="btn-group">
        <button type="button" class="btn btn-primary" onclick="refreshHistory()">
            <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
        <button type="button" class="btn btn-success" onclick="exportHistory()">
            <i class="fas fa-download me-1"></i>导出记录
        </button>
        <button type="button" class="btn btn-danger" onclick="clearHistory()">
            <i class="fas fa-trash me-1"></i>清理历史
        </button>
    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-left-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">成功导入</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="successCount">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-danger">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">失败导入</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="failedCount">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-info">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">总记录数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalRecords">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-database fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-left-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">处理中</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="processingCount">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-spinner fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导入历史列表 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-history me-2"></i>导入历史记录
                </h6>
            </div>
            <div class="card-body">
                <!-- 筛选器 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" onchange="filterByStatus()">
                            <option value="">全部状态</option>
                            <option value="success">成功</option>
                            <option value="failed">失败</option>
                            <option value="processing">处理中</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="typeFilter" onchange="filterByType()">
                            <option value="">全部类型</option>
                            <option value="excel">Excel导入</option>
                            <option value="csv">CSV导入</option>
                            <option value="api">API同步</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="dateFilter" onchange="filterByDate()">
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索文件名...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchHistory()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 历史记录表格 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>导入时间</th>
                                <th>文件名/数据源</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>成功/总数</th>
                                <th>耗时</th>
                                <th>操作人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        显示 <span id="showingStart">1</span> 到 <span id="showingEnd">10</span> 条，共 <span id="totalHistoryRecords">0</span> 条记录
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                            <!-- 分页将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导入详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- 详情内容将通过JavaScript填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadErrorLog()">下载错误日志</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let currentPage = 1;
let itemsPerPage = 10;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadImportHistory();
    updateStatistics();
});

// 加载导入历史
function loadImportHistory() {
    // 模拟历史数据
    const mockHistory = [
        {
            id: 1,
            import_time: '2024-01-15 14:30:25',
            filename: '淘宝商品数据.xlsx',
            type: 'excel',
            status: 'success',
            success_count: 1250,
            total_count: 1250,
            duration: '2分35秒',
            operator: '张三',
            error_message: null
        },
        {
            id: 2,
            import_time: '2024-01-15 13:15:10',
            filename: '京东数码产品.csv',
            type: 'csv',
            status: 'failed',
            success_count: 0,
            total_count: 890,
            duration: '45秒',
            operator: '李四',
            error_message: '文件格式错误'
        },
        {
            id: 3,
            import_time: '2024-01-15 12:00:00',
            filename: 'API同步 - 天猫美妆',
            type: 'api',
            status: 'processing',
            success_count: 234,
            total_count: 567,
            duration: '进行中',
            operator: '系统',
            error_message: null
        }
    ];

    renderHistory(mockHistory);
    updatePagination(1, 10, mockHistory.length);
}

// 渲染历史记录
function renderHistory(history) {
    const tbody = document.getElementById('historyTableBody');
    
    if (history.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-history fa-2x mb-2"></i>
                    <div>暂无导入历史</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = history.map(record => `
        <tr>
            <td>
                <small class="text-muted">${formatDateTime(record.import_time)}</small>
            </td>
            <td>
                <div class="fw-bold">${record.filename}</div>
                <small class="text-muted">ID: ${record.id}</small>
            </td>
            <td>
                <span class="badge bg-${getTypeColor(record.type)}">${getTypeText(record.type)}</span>
            </td>
            <td>
                <span class="badge bg-${getStatusColor(record.status)}">${getStatusText(record.status)}</span>
                ${record.error_message ? `<br><small class="text-danger">${record.error_message}</small>` : ''}
            </td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar bg-${getProgressColor(record.status)}" 
                         style="width: ${(record.success_count / record.total_count * 100)}%">
                        ${record.success_count}/${record.total_count}
                    </div>
                </div>
            </td>
            <td>${record.duration}</td>
            <td>${record.operator}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewDetail(${record.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${record.status === 'failed' ? `
                        <button type="button" class="btn btn-outline-warning" onclick="retryImport(${record.id})" title="重试">
                            <i class="fas fa-redo"></i>
                        </button>
                    ` : ''}
                    <button type="button" class="btn btn-outline-danger" onclick="deleteRecord(${record.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 更新统计数据
function updateStatistics() {
    // 模拟统计数据
    document.getElementById('successCount').textContent = '1,250';
    document.getElementById('failedCount').textContent = '89';
    document.getElementById('totalRecords').textContent = '1,573';
    document.getElementById('processingCount').textContent = '3';
}

// 工具函数
function getTypeColor(type) {
    const colors = {
        'excel': 'success',
        'csv': 'warning',
        'api': 'info'
    };
    return colors[type] || 'secondary';
}

function getTypeText(type) {
    const texts = {
        'excel': 'Excel',
        'csv': 'CSV',
        'api': 'API'
    };
    return texts[type] || type;
}

function getStatusColor(status) {
    const colors = {
        'success': 'success',
        'failed': 'danger',
        'processing': 'warning'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'success': '成功',
        'failed': '失败',
        'processing': '处理中'
    };
    return texts[status] || status;
}

function getProgressColor(status) {
    const colors = {
        'success': 'success',
        'failed': 'danger',
        'processing': 'info'
    };
    return colors[status] || 'secondary';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 操作函数
function refreshHistory() {
    loadImportHistory();
    updateStatistics();
}

function exportHistory() {
    console.log('导出历史记录');
    alert('导出功能开发中...');
}

function clearHistory() {
    if (confirm('确定要清理历史记录吗？此操作不可恢复。')) {
        console.log('清理历史记录');
        alert('清理功能开发中...');
    }
}

function filterByStatus() {
    console.log('按状态筛选');
}

function filterByType() {
    console.log('按类型筛选');
}

function filterByDate() {
    console.log('按日期筛选');
}

function searchHistory() {
    console.log('搜索历史记录');
}

function viewDetail(id) {
    // 模拟详情数据
    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>导入ID</td><td>${id}</td></tr>
                    <tr><td>文件名</td><td>淘宝商品数据.xlsx</td></tr>
                    <tr><td>文件大小</td><td>2.5MB</td></tr>
                    <tr><td>导入时间</td><td>2024-01-15 14:30:25</td></tr>
                    <tr><td>完成时间</td><td>2024-01-15 14:33:00</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>处理结果</h6>
                <table class="table table-sm">
                    <tr><td>总记录数</td><td>1,250</td></tr>
                    <tr><td>成功导入</td><td>1,250</td></tr>
                    <tr><td>失败记录</td><td>0</td></tr>
                    <tr><td>重复记录</td><td>0</td></tr>
                    <tr><td>处理耗时</td><td>2分35秒</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>处理日志</h6>
                <div class="bg-light p-3" style="height: 200px; overflow-y: auto;">
                    <small>
                        [14:30:25] 开始处理文件: 淘宝商品数据.xlsx<br>
                        [14:30:26] 文件验证通过<br>
                        [14:30:27] 开始解析数据...<br>
                        [14:31:15] 数据解析完成，共1250条记录<br>
                        [14:31:16] 开始数据验证...<br>
                        [14:32:45] 数据验证完成<br>
                        [14:32:46] 开始导入数据库...<br>
                        [14:33:00] 导入完成，成功1250条，失败0条
                    </small>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('detailModalBody').innerHTML = detailHtml;
    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
    modal.show();
}

function retryImport(id) {
    if (confirm('确定要重试这个导入任务吗？')) {
        console.log('重试导入:', id);
        alert('重试功能开发中...');
    }
}

function deleteRecord(id) {
    if (confirm('确定要删除这条记录吗？')) {
        console.log('删除记录:', id);
        alert('删除功能开发中...');
    }
}

function downloadErrorLog() {
    console.log('下载错误日志');
    alert('下载功能开发中...');
}

function updatePagination(current, perPage, total) {
    // 更新分页信息
    document.getElementById('showingStart').textContent = ((current - 1) * perPage + 1);
    document.getElementById('showingEnd').textContent = Math.min(current * perPage, total);
    document.getElementById('totalHistoryRecords').textContent = total;
}
</script>
@endsection

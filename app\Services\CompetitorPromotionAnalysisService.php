<?php

namespace App\Services;

use App\Models\PriceHistory;
use App\Models\ProductSku;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class CompetitorPromotionAnalysisService
{
    /**
     * 分析指定SKU在给定时间范围内的竞争对手促销策略
     *
     * @param int $skuId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function analyze(int $skuId, Carbon $startDate, Carbon $endDate): array
    {
        $priceHistory = PriceHistory::where('sku_id', $skuId)
            ->whereBetween('timestamp', [$startDate, $endDate])
            ->whereNotNull('promotion_info')
            ->whereJsonLength('promotion_info', '>', 0)
            ->orderBy('timestamp', 'asc')
            ->get();

        if ($priceHistory->isEmpty()) {
            return $this->getEmptyAnalysisResult($startDate, $endDate);
        }

        $promotionTypes = $this->countPromotionTypes($priceHistory);
        $totalPromotions = array_sum($promotionTypes);
        $promotionFrequency = $this->calculatePromotionFrequency($priceHistory, $startDate, $endDate);
        $discountRates = $this->extractDiscountRates($priceHistory);
        
        $avgDiscountRate = !empty($discountRates) ? array_sum($discountRates) / count($discountRates) : 0;
        $maxDiscountRate = !empty($discountRates) ? max($discountRates) : 0;
        $minDiscountRate = !empty($discountRates) ? min($discountRates) : 0;
        
        // 促销平均持续时间（暂定，需要更精确的促销开始/结束时间数据）
        $avgDuration = $this->calculateAveragePromotionDuration($priceHistory);

        return [
            'promotion_types' => $promotionTypes,
            'total_promotions' => $totalPromotions,
            'promotion_frequency' => round($promotionFrequency, 2),
            'avg_discount_rate' => round($avgDiscountRate, 2),
            'max_discount_rate' => round($maxDiscountRate, 2),
            'min_discount_rate' => round($minDiscountRate, 2),
            'promotion_duration_avg' => $avgDuration,
            'data_period_start' => $startDate->toDateString(),
            'data_period_end' => $endDate->toDateString(),
            'data_points_count' => $priceHistory->count(),
        ];
    }

    /**
     * 统计不同促销类型的次数
     *
     * @param Collection $priceHistory
     * @return array
     */
    protected function countPromotionTypes(Collection $priceHistory): array
    {
        $types = [];
        $priceHistory->each(function ($record) use (&$types) {
            if (is_array($record->promotion_info)) {
                foreach ($record->promotion_info as $promotion) {
                    $type = $promotion['type'] ?? 'unknown';
                    if (!isset($types[$type])) {
                        $types[$type] = 0;
                    }
                    $types[$type]++;
                }
            }
        });
        return $types;
    }

    /**
     * 计算促销频率 (有促销活动的天数 / 总天数)
     *
     * @param Collection $priceHistory
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return float
     */
    protected function calculatePromotionFrequency(Collection $priceHistory, Carbon $startDate, Carbon $endDate): float
    {
        $totalDays = $endDate->diffInDays($startDate) + 1;
        if ($totalDays <= 0) {
            return 0;
        }

        $promotionDays = $priceHistory->map(function ($record) {
            return $record->timestamp->toDateString();
        })->unique()->count();

        return ($promotionDays / $totalDays) * 100;
    }

    /**
     * 从促销信息中提取折扣率
     *
     * @param Collection $priceHistory
     * @return array
     */
    protected function extractDiscountRates(Collection $priceHistory): array
    {
        $rates = [];
        $priceHistory->each(function ($record) use (&$rates) {
             if (is_array($record->promotion_info)) {
                foreach ($record->promotion_info as $promotion) {
                    if (isset($promotion['discount_rate']) && is_numeric($promotion['discount_rate'])) {
                        $rates[] = (float) $promotion['discount_rate'];
                    }
                }
            }
        });
        return $rates;
    }

    /**
     * 计算平均促销持续时间 (简易实现)
     * 这是一个简化的估算，假设连续有促销的记录视为同一次活动
     *
     * @param Collection $priceHistory
     * @return int
     */
    protected function calculateAveragePromotionDuration(Collection $priceHistory): int
    {
        if ($priceHistory->count() < 2) {
            return $priceHistory->count() > 0 ? 1 : 0;
        }
    
        $promotionPeriods = [];
        $currentPeriodStart = null;
        $lastPromotionDate = null;
    
        foreach ($priceHistory as $record) {
            $currentDate = $record->timestamp->startOfDay();
    
            if ($currentPeriodStart === null) {
                $currentPeriodStart = $currentDate;
            }
    
            if ($lastPromotionDate && $currentDate->diffInDays($lastPromotionDate) > 1) {
                // 如果当前促销日与上一个促销日不连续，则结束上一个周期
                $duration = $lastPromotionDate->diffInDays($currentPeriodStart) + 1;
                $promotionPeriods[] = $duration;
                $currentPeriodStart = $currentDate;
            }
    
            $lastPromotionDate = $currentDate;
        }
    
        // 处理最后一个周期
        if ($currentPeriodStart && $lastPromotionDate) {
            $duration = $lastPromotionDate->diffInDays($currentPeriodStart) + 1;
            $promotionPeriods[] = $duration;
        }
    
        if (empty($promotionPeriods)) {
            return 0;
        }
    
        return (int) round(array_sum($promotionPeriods) / count($promotionPeriods));
    }

    /**
     * 根据分析数据生成促销策略的定性总结
     *
     * @param int $skuId
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function summarizeStrategy(int $skuId, Carbon $startDate, Carbon $endDate): array
    {
        $analysis = $this->analyze($skuId, $startDate, $endDate);

        if ($analysis['total_promotions'] === 0) {
            return [
                'summary' => '在指定时期内无促销活动。',
                'descriptors' => [],
                'raw_data' => $analysis,
            ];
        }

        $descriptors = [];
        
        // 频率描述
        if ($analysis['promotion_frequency'] > 20) {
            $descriptors['frequency'] = '频繁';
        } elseif ($analysis['promotion_frequency'] < 5) {
            $descriptors['frequency'] = '罕见';
        } else {
            $descriptors['frequency'] = '中等';
        }

        // 持续时间描述
        if ($analysis['promotion_duration_avg'] > 10) {
            $descriptors['duration'] = '长期';
        } elseif ($analysis['promotion_duration_avg'] < 3) {
            $descriptors['duration'] = '短期';
        } else {
            $descriptors['duration'] = '中期';
        }

        // 折扣力度描述
        if ($analysis['avg_discount_rate'] > 25) {
            $descriptors['depth'] = '高折扣';
        } elseif ($analysis['avg_discount_rate'] < 10) {
            $descriptors['depth'] = '低折扣';
        } else {
            $descriptors['depth'] = '中等折扣';
        }
        
        // 主要促销类型
        $dominantType = 'N/A';
        if (!empty($analysis['promotion_types'])) {
            arsort($analysis['promotion_types']);
            $dominantType = key($analysis['promotion_types']);
        }
        $descriptors['dominant_type'] = $dominantType;

        // 生成总结陈述
        $summary = sprintf(
            '该竞争对手倾向于采用%s、%s的促销活动，提供%s。最主要的促销类型是"%s"。',
            $descriptors['frequency'],
            $descriptors['duration'],
            $descriptors['depth'],
            $descriptors['dominant_type']
        );

        return [
            'summary' => $summary,
            'descriptors' => $descriptors,
            'raw_data' => $analysis,
        ];
    }

    /**
     * 返回空的分析结果
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getEmptyAnalysisResult(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'promotion_types' => [],
            'total_promotions' => 0,
            'promotion_frequency' => 0.0,
            'avg_discount_rate' => 0.0,
            'max_discount_rate' => 0.0,
            'min_discount_rate' => 0.0,
            'promotion_duration_avg' => 0,
            'data_period_start' => $startDate->toDateString(),
            'data_period_end' => $endDate->toDateString(),
            'data_points_count' => 0,
        ];
    }
} 
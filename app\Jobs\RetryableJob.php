<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\RetryService;
use Exception;

/**
 * 可重试任务基类
 * 
 * 提供标准的重试机制和错误处理
 */
abstract class RetryableJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务数据
     *
     * @var array
     */
    protected $jobData;

    /**
     * 重试配置
     *
     * @var array
     */
    protected $retryConfig;

    /**
     * 任务可以尝试的次数
     *
     * @var int
     */
    public $tries = 4; // 默认3次重试 + 1次初始尝试

    /**
     * 任务运行的最大秒数
     *
     * @var int
     */
    public $timeout = 300; // 5分钟

    /**
     * 重试间隔（秒）
     *
     * @var array
     */
    public $backoff = [1, 2, 4]; // 默认退避序列

    /**
     * 构造函数
     *
     * @param array $jobData 任务数据
     * @param array $retryConfig 重试配置
     */
    public function __construct(array $jobData = [], array $retryConfig = [])
    {
        $this->jobData = $jobData;
        $this->retryConfig = array_merge(
            RetryService::createConfig(),
            $retryConfig
        );

        // 根据配置设置重试参数
        $this->tries = $this->retryConfig['max_retries'] + 1;
        $this->backoff = $this->generateBackoffSequence();
    }

    /**
     * 执行任务
     *
     * @return void
     */
    public function handle()
    {
        $attempt = $this->attempts();
        $maxAttempts = $this->tries;

        try {
            Log::info('开始执行可重试任务', [
                'job_class' => static::class,
                'attempt' => $attempt,
                'max_attempts' => $maxAttempts,
                'job_data' => $this->jobData
            ]);

            // 执行具体的任务逻辑
            $result = $this->executeJob();

            Log::info('可重试任务执行成功', [
                'job_class' => static::class,
                'attempt' => $attempt,
                'result' => $result
            ]);

            // 记录成功统计
            $this->recordStats($attempt, true);

        } catch (Exception $e) {
            Log::error('可重试任务执行失败', [
                'job_class' => static::class,
                'attempt' => $attempt,
                'max_attempts' => $maxAttempts,
                'exception' => $e->getMessage(),
                'exception_type' => get_class($e),
                'trace' => $e->getTraceAsString()
            ]);

            // 检查是否应该重试
            if ($this->shouldRetryException($e) && $attempt < $maxAttempts) {
                Log::warning('任务将重试', [
                    'job_class' => static::class,
                    'attempt' => $attempt,
                    'next_attempt' => $attempt + 1,
                    'delay' => $this->getNextDelay($attempt - 1)
                ]);

                // 重新抛出异常以触发重试
                throw $e;
            }

            // 记录失败统计
            $this->recordStats($attempt, false);

            // 处理最终失败
            $this->handleFinalFailure($e);

            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     *
     * @param Exception $exception 异常
     * @return void
     */
    public function failed(Exception $exception)
    {
        Log::error('可重试任务最终失败', [
            'job_class' => static::class,
            'total_attempts' => $this->attempts(),
            'exception' => $exception->getMessage(),
            'job_data' => $this->jobData
        ]);

        // 调用子类的失败处理方法
        $this->onFinalFailure($exception);
    }

    /**
     * 执行具体的任务逻辑（由子类实现）
     *
     * @return mixed
     */
    abstract protected function executeJob();

    /**
     * 检查异常是否应该重试（可由子类覆盖）
     *
     * @param Exception $exception 异常
     * @return bool
     */
    protected function shouldRetryException(Exception $exception): bool
    {
        // 检查异常类型
        foreach ($this->retryConfig['retryable_exceptions'] as $retryableException) {
            if ($exception instanceof $retryableException) {
                return true;
            }
        }
        
        // 检查HTTP异常的状态码
        if ($exception instanceof \GuzzleHttp\Exception\RequestException) {
            $response = $exception->getResponse();
            if ($response) {
                $statusCode = $response->getStatusCode();
                return in_array($statusCode, $this->retryConfig['retryable_status_codes']);
            }
        }
        
        return false;
    }

    /**
     * 处理最终失败（可由子类覆盖）
     *
     * @param Exception $exception 异常
     * @return void
     */
    protected function handleFinalFailure(Exception $exception): void
    {
        // 默认实现：记录错误日志
        Log::critical('任务重试耗尽，最终失败', [
            'job_class' => static::class,
            'job_data' => $this->jobData,
            'exception' => $exception->getMessage()
        ]);
    }

    /**
     * 最终失败时的回调（由子类实现）
     *
     * @param Exception $exception 异常
     * @return void
     */
    protected function onFinalFailure(Exception $exception): void
    {
        // 子类可以覆盖此方法来处理特定的失败逻辑
    }

    /**
     * 生成退避序列
     *
     * @return array
     */
    private function generateBackoffSequence(): array
    {
        $sequence = [];
        $maxRetries = $this->retryConfig['max_retries'];
        $baseDelay = $this->retryConfig['base_delay'] / 1000; // 转换为秒
        $multiplier = $this->retryConfig['backoff_multiplier'];
        $maxDelay = $this->retryConfig['max_delay'] / 1000; // 转换为秒

        for ($i = 0; $i < $maxRetries; $i++) {
            $delay = $baseDelay * pow($multiplier, $i);
            $delay = min($delay, $maxDelay);
            $sequence[] = (int) $delay;
        }

        return $sequence;
    }

    /**
     * 获取下次重试的延迟时间
     *
     * @param int $attempt 当前尝试次数
     * @return int
     */
    private function getNextDelay(int $attempt): int
    {
        return $this->backoff[$attempt] ?? end($this->backoff);
    }

    /**
     * 记录重试统计信息
     *
     * @param int $attempts 尝试次数
     * @param bool $success 是否成功
     * @return void
     */
    private function recordStats(int $attempts, bool $success): void
    {
        $retryService = new RetryService();
        $statsKey = static::class;
        $retryService->recordRetryStats($statsKey, $attempts, $success);
    }

    /**
     * 获取任务数据
     *
     * @return array
     */
    public function getJobData(): array
    {
        return $this->jobData;
    }

    /**
     * 获取重试配置
     *
     * @return array
     */
    public function getRetryConfig(): array
    {
        return $this->retryConfig;
    }

    /**
     * 设置任务数据
     *
     * @param array $jobData 任务数据
     * @return void
     */
    public function setJobData(array $jobData): void
    {
        $this->jobData = $jobData;
    }

    /**
     * 更新重试配置
     *
     * @param array $retryConfig 重试配置
     * @return void
     */
    public function updateRetryConfig(array $retryConfig): void
    {
        $this->retryConfig = array_merge($this->retryConfig, $retryConfig);
        $this->tries = $this->retryConfig['max_retries'] + 1;
        $this->backoff = $this->generateBackoffSequence();
    }
} 
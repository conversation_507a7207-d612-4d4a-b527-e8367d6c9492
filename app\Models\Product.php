<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Scout\Searchable;

class Product extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'title',
        'category_id',
        'category_path',
        'shop_id',
        'shop_name',
        'shop_type',
        'item_type',
        'state',
        'source_platform',
        'source_url',
        'source_id',
        'description',
        'image_url',
        'images',
        'min_price',
        'max_price',
        'total_sales',
        'rating',
        'review_count',
    ];

    protected $casts = [
        'images' => 'array',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
        'rating' => 'decimal:2',
        'total_sales' => 'integer',
        'review_count' => 'integer',
    ];

    /**
     * 产品所属分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 产品的所有SKU
     */
    public function skus(): HasMany
    {
        return $this->hasMany(ProductSku::class);
    }

    /**
     * 产品的监控任务
     */
    public function monitorTasks(): HasMany
    {
        return $this->hasMany(MonitorTask::class);
    }



    /**
     * 获取活跃的SKU
     */
    public function activeSkus(): HasMany
    {
        return $this->skus()->where('status', 'active');
    }

    /**
     * 获取最便宜的SKU价格
     */
    public function getMinSkuPriceAttribute()
    {
        return $this->skus()->min('price');
    }

    /**
     * 获取最贵的SKU价格
     */
    public function getMaxSkuPriceAttribute()
    {
        return $this->skus()->max('price');
    }

    /**
     * 检查产品是否有库存
     */
    public function hasStock(): bool
    {
        return $this->skus()->where('quantity', '>', 0)->exists();
    }

    /**
     * 获取产品总库存
     */
    public function getTotalStockAttribute(): int
    {
        return $this->skus()->sum('quantity');
    }

    /**
     * 按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('state', 'active');
    }

    /**
     * 按平台筛选
     */
    public function scopeFromPlatform($query, $platform)
    {
        return $query->where('source_platform', $platform);
    }

    /**
     * 按价格范围筛选
     */
    public function scopePriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('min_price', [$minPrice, $maxPrice]);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $array = $this->toArray();

        // 确保包含关联数据
        $array['category_name'] = $this->category?->name ?? '';
        $array['category_path'] = $this->category_path ?? '';
        
        // 移除不需要搜索的字段
        unset($array['images'], $array['created_at'], $array['updated_at']);
        
        return $array;
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->getKey();
    }

    /**
     * Get the key name used to index the model.
     *
     * @return mixed
     */
    public function getScoutKeyName()
    {
        return $this->getKeyName();
    }

    /**
     * Determine if the model should be searchable.
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        return $this->state === 'active';
    }
}

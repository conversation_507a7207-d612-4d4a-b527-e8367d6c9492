/**
 * API客户端 - 统一处理前端与后端的API通信
 */
class ApiClient {
    constructor() {
        this.baseURL = window.location.origin;
        this.csrfToken = this.getCSRFToken();
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
        
        if (this.csrfToken) {
            this.defaultHeaders['X-CSRF-TOKEN'] = this.csrfToken;
        }
    }

    /**
     * 获取CSRF令牌
     */
    getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }

    /**
     * 通用请求方法
     */
    async request(url, options = {}) {
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        // 如果是FormData，移除Content-Type让浏览器自动设置
        if (config.body instanceof FormData) {
            delete config.headers['Content-Type'];
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            // 处理HTTP错误状态
            if (!response.ok) {
                const errorData = await this.parseResponse(response);
                throw new ApiError(response.status, errorData.message || '请求失败', errorData);
            }

            return await this.parseResponse(response);
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            
            // 网络错误或其他错误
            throw new ApiError(0, '网络连接失败，请检查网络连接', { originalError: error });
        }
    }

    /**
     * 解析响应数据
     */
    async parseResponse(response) {
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        
        return await response.text();
    }

    /**
     * GET请求
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullUrl, {
            method: 'GET'
        });
    }

    /**
     * POST请求
     */
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: data instanceof FormData ? data : JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: data instanceof FormData ? data : JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }

    /**
     * 文件上传
     */
    async upload(url, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // 添加额外数据
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.post(url, formData);
    }
}

/**
 * API错误类
 */
class ApiError extends Error {
    constructor(status, message, data = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }

    /**
     * 是否为网络错误
     */
    isNetworkError() {
        return this.status === 0;
    }

    /**
     * 是否为客户端错误
     */
    isClientError() {
        return this.status >= 400 && this.status < 500;
    }

    /**
     * 是否为服务器错误
     */
    isServerError() {
        return this.status >= 500;
    }

    /**
     * 是否为认证错误
     */
    isAuthError() {
        return this.status === 401 || this.status === 403;
    }
}

/**
 * API服务类 - 具体的API接口封装
 */
class ApiService {
    constructor() {
        this.client = new ApiClient();
    }

    // ==================== 商品监控相关API ====================
    
    /**
     * 获取商品列表
     */
    async getProducts(params = {}) {
        return this.client.get('/api/products', params);
    }

    /**
     * 获取商品详情
     */
    async getProduct(id) {
        return this.client.get(`/api/products/${id}`);
    }

    /**
     * 创建商品监控
     */
    async createProduct(data) {
        return this.client.post('/api/products', data);
    }

    /**
     * 更新商品监控
     */
    async updateProduct(id, data) {
        return this.client.put(`/api/products/${id}`, data);
    }

    /**
     * 删除商品监控
     */
    async deleteProduct(id) {
        return this.client.delete(`/api/products/${id}`);
    }

    // ==================== 数据源相关API ====================
    
    /**
     * 获取数据源列表
     */
    async getDataSources(params = {}) {
        return this.client.get('/api/data-sources', params);
    }

    /**
     * 创建数据源
     */
    async createDataSource(data) {
        return this.client.post('/api/data-sources', data);
    }

    /**
     * Excel批量导入
     */
    async importExcel(file, options = {}) {
        return this.client.upload('/api/data-sources/excel-import', file, options);
    }

    /**
     * 获取导入历史
     */
    async getImportHistory(params = {}) {
        return this.client.get('/api/data-sources/import-history', params);
    }

    // ==================== 预警规则相关API ====================
    
    /**
     * 获取预警规则列表
     */
    async getAlertRules(params = {}) {
        return this.client.get('/api/alert-rules', params);
    }

    /**
     * 创建预警规则
     */
    async createAlertRule(data) {
        return this.client.post('/api/alert-rules', data);
    }

    /**
     * 更新预警规则
     */
    async updateAlertRule(id, data) {
        return this.client.put(`/api/alert-rules/${id}`, data);
    }

    /**
     * 删除预警规则
     */
    async deleteAlertRule(id) {
        return this.client.delete(`/api/alert-rules/${id}`);
    }

    // ==================== 竞品分析相关API ====================
    
    /**
     * 搜索竞品
     */
    async searchCompetitors(params) {
        return this.client.post('/api/competitors/search', params);
    }

    /**
     * 获取竞品分析报告
     */
    async getCompetitorAnalysis(params = {}) {
        return this.client.get('/api/competitors/analysis', params);
    }

    // ==================== 任务分组相关API ====================
    
    /**
     * 获取任务分组列表
     */
    async getTaskGroups(params = {}) {
        return this.client.get('/api/task-groups', params);
    }

    /**
     * 创建任务分组
     */
    async createTaskGroup(data) {
        return this.client.post('/api/task-groups', data);
    }

    /**
     * 更新任务分组
     */
    async updateTaskGroup(id, data) {
        return this.client.put(`/api/task-groups/${id}`, data);
    }

    /**
     * 删除任务分组
     */
    async deleteTaskGroup(id) {
        return this.client.delete(`/api/task-groups/${id}`);
    }

    /**
     * 获取未分组任务
     */
    async getUnassignedTasks() {
        return this.client.get('/api/task-groups/unassigned-tasks');
    }

    /**
     * 分配任务到分组
     */
    async assignTasksToGroup(groupId, taskIds) {
        return this.client.post(`/api/task-groups/${groupId}/assign`, { task_ids: taskIds });
    }

    // ==================== 仪表板相关API ====================
    
    /**
     * 获取仪表板统计数据
     */
    async getDashboardStats() {
        return this.client.get('/api/dashboard/stats');
    }

    /**
     * 获取图表数据
     */
    async getChartData(chartType, params = {}) {
        return this.client.get(`/api/dashboard/charts/${chartType}`, params);
    }

    // ==================== 用户相关API ====================
    
    /**
     * 获取当前用户信息
     */
    async getCurrentUser() {
        return this.client.get('/api/user/profile');
    }

    /**
     * 更新用户配置
     */
    async updateUserPreferences(data) {
        return this.client.put('/api/user/preferences', data);
    }

    // ==================== 数据源相关API ====================

    /**
     * 获取导入历史
     */
    async getImportHistory(params = {}) {
        return this.client.get('/api/data-sources/import-history', params);
    }

    /**
     * Excel导入
     */
    async importExcel(file, options = {}) {
        const formData = new FormData();
        formData.append('file', file);

        Object.keys(options).forEach(key => {
            formData.append(key, options[key]);
        });

        return this.client.post('/api/data-sources/import-excel', formData);
    }

    /**
     * 获取导入状态
     */
    async getImportStatus(id) {
        return this.client.get(`/api/data-sources/import-status/${id}`);
    }

    /**
     * 删除导入历史
     */
    async deleteImportHistory(id) {
        return this.client.delete(`/api/data-sources/import-history/${id}`);
    }
}

// 创建全局API服务实例
window.apiService = new ApiService();
window.ApiError = ApiError;

// 导出供模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, ApiService, ApiError };
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Database\Seeders\CompetitorMetricTestSeeder;

class CompetitorMetricsApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(CompetitorMetricTestSeeder::class);
        // Artisan command must be run to populate metrics
        $this->artisan('metrics:calculate-competitor');
    }

    /** @test */
    public function it_can_fetch_competitor_metrics()
    {
        $response = $this->getJson('/api/v1/competitor-metrics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'date',
                    'competitor_sku' => [
                        'id',
                        'name',
                        'product',
                    ],
                    'analysis' => [
                        'promotion_summary',
                        'price_deviation_rate',
                    ],
                    'metrics' => [
                        'promotion_frequency',
                        'avg_discount_rate',
                    ],
                    'calculated_at',
                ],
            ],
            'links',
            'meta',
        ]);
    }
} 
@extends('layouts.app')

@section('title', '商品详情')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
    <li class="breadcrumb-item"><a href="{{ route('products.index') }}">商品监控</a></li>
    <li class="breadcrumb-item active">商品详情</li>
@endsection

@section('page-title', '商品详情')

@section('page-actions')
    <div class="btn-group">
        <a href="{{ route('products.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
        <button type="button" class="btn btn-primary" onclick="updateProductData()">
            <i class="fas fa-sync-alt me-1"></i>更新数据
        </button>
        <button type="button" class="btn btn-success" onclick="exportProductData()">
            <i class="fas fa-download me-1"></i>导出数据
        </button>
    </div>
@endsection

@section('content')
<div class="row">
    <!-- 左侧商品基本信息 -->
    <div class="col-lg-8">
        <!-- 商品基本信息卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>商品基本信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <img src="https://via.placeholder.com/200x200" alt="商品图片" class="img-fluid rounded">
                    </div>
                    <div class="col-md-9">
                        <h5 class="mb-3" id="productTitle">商品标题加载中...</h5>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>商品ID:</strong></div>
                            <div class="col-sm-9"><code id="productId">{{ $product }}</code></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>平台:</strong></div>
                            <div class="col-sm-9"><span class="badge bg-info" id="platform">天猫</span></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>店铺:</strong></div>
                            <div class="col-sm-9" id="shopName">官方旗舰店</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>类目:</strong></div>
                            <div class="col-sm-9" id="categoryPath">汽车零部件->轮胎->乘用车轮胎</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>状态:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-success" id="status">已上架</span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>最后更新:</strong></div>
                            <div class="col-sm-9 text-muted" id="lastUpdate">2024-01-20 14:30:00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格趋势图表 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>价格趋势分析
                </h6>
            </div>
            <div class="card-body">
                <canvas id="priceTrendChart" height="100"></canvas>
            </div>
        </div>

        <!-- SKU信息表格 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list me-2"></i>SKU信息
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>SKU ID</th>
                                <th>规格</th>
                                <th>原价</th>
                                <th>券后价</th>
                                <th>库存</th>
                                <th>促销信息</th>
                            </tr>
                        </thead>
                        <tbody id="skuTableBody">
                            <!-- SKU数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧统计信息 -->
    <div class="col-lg-4">
        <!-- 价格统计 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-dollar-sign me-2"></i>价格统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <h4 class="text-success" id="minPrice">¥1,682</h4>
                        <small class="text-muted">最低价格</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger" id="maxPrice">¥2,464</h4>
                        <small class="text-muted">最高价格</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <h5 class="text-info" id="avgPrice">¥2,073</h5>
                        <small class="text-muted">平均价格</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning" id="priceDeviation">+5.2%</h5>
                        <small class="text-muted">价格偏离率</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存统计 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-warehouse me-2"></i>库存统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12">
                        <h3 class="text-primary" id="totalStock">1,979</h3>
                        <small class="text-muted">总库存</small>
                    </div>
                </div>
                <hr>
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%" id="stockProgress"></div>
                </div>
                <small class="text-muted">库存充足度: 75%</small>
            </div>
        </div>

        <!-- 销售统计 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-bar me-2"></i>销售统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <h4 class="text-success" id="salesCount">2,899</h4>
                        <small class="text-muted">月销量</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info" id="commentCount">1,682</h4>
                        <small class="text-muted">评论数</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控设置 -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cog me-2"></i>监控设置
                </h6>
            </div>
            <div class="card-body">
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="priceAlert" checked>
                    <label class="form-check-label" for="priceAlert">
                        价格变动预警
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="stockAlert" checked>
                    <label class="form-check-label" for="stockAlert">
                        库存不足预警
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="statusAlert">
                    <label class="form-check-label" for="statusAlert">
                        上下架状态预警
                    </label>
                </div>
                <button class="btn btn-primary btn-sm w-100" onclick="saveMonitorSettings()">
                    <i class="fas fa-save me-1"></i>保存设置
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProductDetail();
    initPriceTrendChart();
});

// 加载商品详情
function loadProductDetail() {
    const productId = '{{ $product }}';
    
    // 模拟加载商品详情数据
    setTimeout(() => {
        const mockData = {
            id: productId,
            title: '新款Bauer PROTO R轮胎 - 高性能运动轮胎 245/45R18',
            platform: '天猫',
            shop_name: '官方旗舰店',
            category_path: '汽车零部件->轮胎->乘用车轮胎',
            status: 1,
            last_update: '2024-01-20 14:30:00',
            min_price: 1682,
            max_price: 2464,
            avg_price: 2073,
            price_deviation: 5.2,
            total_stock: 1979,
            sales_count: 2899,
            comment_count: 1682,
            skus: [
                {
                    sku_id: '5187112947623',
                    name: '轮胎类型:普通胎',
                    price: 2464,
                    sub_price: 2073,
                    quantity: 1979,
                    promotion: '官方立折'
                }
            ]
        };
        
        renderProductDetail(mockData);
        renderSkuTable(mockData.skus);
    }, 500);
}

// 渲染商品详情
function renderProductDetail(product) {
    document.getElementById('productTitle').textContent = product.title;
    document.getElementById('platform').textContent = product.platform;
    document.getElementById('shopName').textContent = product.shop_name;
    document.getElementById('categoryPath').textContent = product.category_path;
    document.getElementById('status').textContent = product.status ? '已上架' : '已下架';
    document.getElementById('status').className = `badge ${product.status ? 'bg-success' : 'bg-secondary'}`;
    document.getElementById('lastUpdate').textContent = product.last_update;
    
    // 价格统计
    document.getElementById('minPrice').textContent = `¥${product.min_price}`;
    document.getElementById('maxPrice').textContent = `¥${product.max_price}`;
    document.getElementById('avgPrice').textContent = `¥${product.avg_price}`;
    document.getElementById('priceDeviation').textContent = `+${product.price_deviation}%`;
    
    // 库存统计
    document.getElementById('totalStock').textContent = product.total_stock.toLocaleString();
    
    // 销售统计
    document.getElementById('salesCount').textContent = product.sales_count.toLocaleString();
    document.getElementById('commentCount').textContent = product.comment_count.toLocaleString();
}

// 渲染SKU表格
function renderSkuTable(skus) {
    const tbody = document.getElementById('skuTableBody');
    tbody.innerHTML = skus.map(sku => `
        <tr>
            <td><code>${sku.sku_id}</code></td>
            <td>${sku.name}</td>
            <td><span class="text-muted">¥${sku.price}</span></td>
            <td><span class="text-success fw-bold">¥${sku.sub_price}</span></td>
            <td><span class="badge bg-success">${sku.quantity}</span></td>
            <td><small class="text-info">${sku.promotion}</small></td>
        </tr>
    `).join('');
}

// 初始化价格趋势图表
function initPriceTrendChart() {
    const ctx = document.getElementById('priceTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '平均价格',
                data: [2100, 2050, 2073, 2120, 2080, 2073],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '价格 (¥)'
                    }
                }
            }
        }
    });
}

// 更新商品数据
function updateProductData() {
    if (confirm('确定要更新商品数据吗？')) {
        console.log('更新商品数据');
        // 实际项目中调用API
    }
}

// 导出商品数据
function exportProductData() {
    console.log('导出商品数据');
    // 实际项目中调用导出API
}

// 保存监控设置
function saveMonitorSettings() {
    const settings = {
        price_alert: document.getElementById('priceAlert').checked,
        stock_alert: document.getElementById('stockAlert').checked,
        status_alert: document.getElementById('statusAlert').checked
    };
    
    console.log('保存监控设置:', settings);
    // 实际项目中调用API保存设置
    
    alert('监控设置已保存');
}
</script>
@endsection

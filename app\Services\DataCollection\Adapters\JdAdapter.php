<?php

namespace App\Services\DataCollection\Adapters;

use App\Services\DataCollection\AbstractPlatformAdapter;
use Exception;

/**
 * 京东平台适配器
 * 演示如何轻松扩展新的电商平台
 */
class JdAdapter extends AbstractPlatformAdapter
{
    /**
     * 获取平台名称
     *
     * @return string 平台名称
     */
    public function getPlatformName(): string
    {
        return 'jd';
    }

    /**
     * 获取商品详情
     * API: /jd/item/detail
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 商品详情数据
     * @throws Exception
     */
    public function getItemDetail(string $itemId, array $options = []): array
    {
        $endpoint = '/jd/item/detail';
        $params = array_merge(['sku_id' => $itemId], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        return $this->normalizeItemDetail($response['data'] ?? []);
    }

    /**
     * 获取同款商品
     * API: /jd/item/similar
     *
     * @param string $itemId 商品ID
     * @param array $options 额外选项
     * @return array 同款商品列表
     * @throws Exception
     */
    public function getSimilarProducts(string $itemId, array $options = []): array
    {
        $endpoint = '/jd/item/similar';
        $params = array_merge(['sku_id' => $itemId], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $items = $response['data'] ?? [];
        return [
            'items' => array_map([$this, 'normalizeSimilarProduct'], $items),
            'total' => count($items),
            'source_item_id' => $itemId,
        ];
    }

    /**
     * 获取店铺商品列表
     * API: /jd/shop/items
     *
     * @param string $shopId 店铺ID
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 店铺商品列表
     * @throws Exception
     */
    public function getShopItems(string $shopId, int $page = 1, array $options = []): array
    {
        $endpoint = '/jd/shop/items';
        $params = array_merge([
            'shop_id' => $shopId,
            'page' => $page,
            'page_size' => $options['page_size'] ?? 20
        ], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $items = $response['data'] ?? [];
        return [
            'items' => array_map([$this, 'normalizeShopItem'], $items),
            'pagination' => [
                'current_page' => $page,
                'has_more' => count($items) >= ($options['page_size'] ?? 20),
                'total' => $response['total'] ?? count($items),
            ]
        ];
    }

    /**
     * 搜索商品
     * API: /jd/search
     *
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @param array $options 额外选项
     * @return array 搜索结果
     * @throws Exception
     */
    public function searchItems(string $keyword, int $page = 1, array $options = []): array
    {
        $endpoint = '/jd/search';
        $params = array_merge([
            'keyword' => $keyword,
            'page' => $page,
            'page_size' => $options['page_size'] ?? 20
        ], $options);
        
        $url = $this->buildUrl($endpoint, $params);
        $response = $this->makeRequest('GET', $url);
        
        if (!$this->validateResponse($response)) {
            $this->handleApiError($response);
        }
        
        $items = $response['data'] ?? [];
        return [
            'items' => array_map([$this, 'normalizeSearchItem'], $items),
            'pagination' => [
                'current_page' => $page,
                'has_more' => count($items) >= ($options['page_size'] ?? 20),
                'total' => $response['total'] ?? count($items),
            ],
            'keyword' => $keyword,
        ];
    }

    /**
     * 获取平台支持的功能列表
     *
     * @return array 支持的功能列表
     */
    public function getSupportedFeatures(): array
    {
        return [
            'item_detail' => true,
            'similar_products' => true,
            'shop_items' => true,
            'search_items' => true,
            'health_check' => true,
            'price_history' => true,  // 京东特有功能
            'promotion_info' => true, // 京东特有功能
        ];
    }

    /**
     * 测试连接
     *
     * @return bool 连接是否成功
     */
    public function testConnection(): bool
    {
        try {
            // 使用京东的健康检查端点
            $testUrl = $this->baseUrl . '/jd/health';
            $result = $this->httpClient->testConnection($testUrl);
            
            return $result['status'] === 'success';
        } catch (Exception $e) {
            return false;
        }
    }

    // --- 数据标准化方法 ---

    /**
     * 标准化商品详情数据
     *
     * @param array $rawData 原始数据
     * @return array 标准化数据
     */
    protected function normalizeItemDetail(array $rawData): array
    {
        return [
            'platform' => 'jd',
            'item_id' => $rawData['sku_id'] ?? '',
            'title' => $rawData['name'] ?? '',
            'price' => $rawData['price'] ?? 0,
            'original_price' => $rawData['market_price'] ?? 0,
            'images' => $rawData['images'] ?? [],
            'shop_id' => $rawData['shop_id'] ?? '',
            'shop_name' => $rawData['shop_name'] ?? '',
            'category' => $rawData['category'] ?? '',
            'brand' => $rawData['brand'] ?? '',
            'sales_count' => $rawData['sales_count'] ?? 0,
            'comment_count' => $rawData['comment_count'] ?? 0,
            'rating' => $rawData['rating'] ?? 0,
            'stock_status' => $rawData['stock_status'] ?? 'unknown',
            'attributes' => $rawData['attributes'] ?? [],
            'description' => $rawData['description'] ?? '',
            'raw_data' => $rawData,
        ];
    }

    /**
     * 标准化同款商品数据
     *
     * @param array $rawData 原始数据
     * @return array 标准化数据
     */
    protected function normalizeSimilarProduct(array $rawData): array
    {
        return [
            'platform' => 'jd',
            'item_id' => $rawData['sku_id'] ?? '',
            'title' => $rawData['name'] ?? '',
            'price' => $rawData['price'] ?? 0,
            'image' => $rawData['image'] ?? '',
            'shop_name' => $rawData['shop_name'] ?? '',
            'similarity_score' => $rawData['similarity'] ?? 0,
        ];
    }

    /**
     * 标准化店铺商品数据
     *
     * @param array $rawData 原始数据
     * @return array 标准化数据
     */
    protected function normalizeShopItem(array $rawData): array
    {
        return [
            'platform' => 'jd',
            'item_id' => $rawData['sku_id'] ?? '',
            'title' => $rawData['name'] ?? '',
            'price' => $rawData['price'] ?? 0,
            'image' => $rawData['image'] ?? '',
            'sales_count' => $rawData['sales_count'] ?? 0,
            'stock_status' => $rawData['stock_status'] ?? 'unknown',
        ];
    }

    /**
     * 标准化搜索结果数据
     *
     * @param array $rawData 原始数据
     * @return array 标准化数据
     */
    protected function normalizeSearchItem(array $rawData): array
    {
        return [
            'platform' => 'jd',
            'item_id' => $rawData['sku_id'] ?? '',
            'title' => $rawData['name'] ?? '',
            'price' => $rawData['price'] ?? 0,
            'image' => $rawData['image'] ?? '',
            'shop_name' => $rawData['shop_name'] ?? '',
            'sales_count' => $rawData['sales_count'] ?? 0,
            'rating' => $rawData['rating'] ?? 0,
            'promotion' => $rawData['promotion'] ?? '',
        ];
    }
}

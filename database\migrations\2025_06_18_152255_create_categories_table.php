<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('分类名称');
            $table->string('slug')->unique()->comment('分类标识符');
            $table->text('description')->nullable()->comment('分类描述');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('父分类ID');
            $table->string('path')->nullable()->comment('分类路径（如：电子产品/手机/智能手机）');
            $table->unsignedInteger('level')->default(1)->comment('分类层级');
            $table->unsignedInteger('sort_order')->default(0)->comment('排序顺序');
            $table->string('image')->nullable()->comment('分类图片');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->json('metadata')->nullable()->comment('分类元数据');
            $table->timestamps();
            
            // 索引
            $table->index(['parent_id', 'is_active']);
            $table->index(['level', 'sort_order']);
            $table->index(['is_active', 'sort_order']);
            $table->index('path');
            
            // 外键约束（自引用）
            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('用户姓名');
            $table->string('email', 191)->unique()->comment('邮箱地址');
            $table->string('phone_number', 20)->nullable()->unique()->comment('手机号码');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->timestamp('email_verified_at')->nullable()->comment('邮箱验证时间');
            $table->timestamp('phone_verified_at')->nullable()->comment('手机验证时间');
            $table->string('password')->comment('密码哈希');
            $table->string('avatar')->nullable()->comment('头像路径');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->comment('用户状态');
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            $table->string('last_login_ip', 45)->nullable()->comment('最后登录IP');
            $table->integer('login_attempts')->default(0)->comment('登录尝试次数');
            $table->timestamp('locked_until')->nullable()->comment('账户锁定至');
            $table->rememberToken();
            $table->timestamps();
            
            // 索引
            $table->index(['email', 'status']);
            $table->index(['phone_number', 'status']);
            $table->index(['username', 'status']);
            $table->index('last_login_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
}; 
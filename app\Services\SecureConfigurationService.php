<?php

namespace App\Services;

use App\Models\ApiConfiguration;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 安全配置管理服务
 * 负责API配置的安全存储、加密和环境变量集成
 */
class SecureConfigurationService
{
    /**
     * 环境变量前缀
     */
    private const ENV_PREFIX = 'API_CONFIG_';

    /**
     * 敏感字段列表
     */
    private const SENSITIVE_FIELDS = [
        'api_key',
        'secret_key',
        'access_token',
        'refresh_token',
        'private_key',
        'password',
        'client_secret',
        'webhook_secret',
    ];

    /**
     * 安全存储API配置
     *
     * @param array $configData 配置数据
     * @return ApiConfiguration
     * @throws Exception
     */
    public function storeSecureConfiguration(array $configData): ApiConfiguration
    {
        // 处理认证凭据的安全存储
        if (isset($configData['auth_credentials'])) {
            $configData['auth_credentials'] = $this->encryptSensitiveData($configData['auth_credentials']);
        }

        // 处理元数据中的敏感信息
        if (isset($configData['metadata'])) {
            $configData['metadata'] = $this->processSensitiveMetadata($configData['metadata']);
        }

        // 创建配置
        $configuration = ApiConfiguration::create($configData);

        Log::info('安全API配置已创建', [
            'id' => $configuration->id,
            'name' => $configuration->name,
            'platform_type' => $configuration->platform_type,
        ]);

        return $configuration;
    }

    /**
     * 更新安全配置
     *
     * @param ApiConfiguration $configuration 配置实例
     * @param array $updateData 更新数据
     * @return ApiConfiguration
     * @throws Exception
     */
    public function updateSecureConfiguration(ApiConfiguration $configuration, array $updateData): ApiConfiguration
    {
        // 处理认证凭据的安全更新
        if (isset($updateData['auth_credentials'])) {
            $updateData['auth_credentials'] = $this->encryptSensitiveData($updateData['auth_credentials']);
        }

        // 处理元数据中的敏感信息
        if (isset($updateData['metadata'])) {
            $updateData['metadata'] = $this->processSensitiveMetadata($updateData['metadata']);
        }

        $configuration->update($updateData);

        Log::info('安全API配置已更新', [
            'id' => $configuration->id,
            'name' => $configuration->name,
        ]);

        return $configuration;
    }

    /**
     * 获取解密的配置数据
     *
     * @param ApiConfiguration $configuration 配置实例
     * @return array 解密后的配置数据
     */
    public function getDecryptedConfiguration(ApiConfiguration $configuration): array
    {
        $configData = $configuration->toArray();

        // 解密认证凭据
        if (isset($configData['auth_credentials']) && $configData['auth_credentials']) {
            $configData['auth_credentials'] = $this->decryptSensitiveData($configData['auth_credentials']);
        } else {
            $configData['auth_credentials'] = [];
        }

        // 解密元数据中的敏感信息
        if (isset($configData['metadata']) && $configData['metadata']) {
            $configData['metadata'] = $this->processDecryptMetadata($configData['metadata']);
        } else {
            $configData['metadata'] = [];
        }

        return $configData;
    }

    /**
     * 从环境变量获取配置
     *
     * @param string $configKey 配置键名
     * @return mixed 配置值
     */
    public function getFromEnvironment(string $configKey)
    {
        $envKey = self::ENV_PREFIX . strtoupper($configKey);
        return env($envKey);
    }

    /**
     * 检查配置是否应该从环境变量获取
     *
     * @param string $configKey 配置键名
     * @return bool 是否从环境变量获取
     */
    public function shouldUseEnvironment(string $configKey): bool
    {
        $envKey = self::ENV_PREFIX . strtoupper($configKey);
        return env($envKey) !== null;
    }

    /**
     * 获取有效的配置值（优先使用环境变量）
     *
     * @param ApiConfiguration $configuration 配置实例
     * @param string $field 字段名
     * @return mixed 配置值
     */
    public function getEffectiveValue(ApiConfiguration $configuration, string $field)
    {
        // 构建环境变量键名
        $envKey = self::ENV_PREFIX . strtoupper($configuration->platform_type) . '_' . strtoupper($field);
        
        // 优先使用环境变量
        $envValue = env($envKey);
        if ($envValue !== null) {
            return $envValue;
        }

        // 回退到数据库配置
        return $configuration->$field;
    }

    /**
     * 获取有效的认证凭据（优先使用环境变量）
     *
     * @param ApiConfiguration $configuration 配置实例
     * @return array 认证凭据
     */
    public function getEffectiveCredentials(ApiConfiguration $configuration): array
    {
        $credentials = $configuration->auth_credentials ?? [];
        
        // 检查每个凭据字段是否有环境变量覆盖
        foreach ($credentials as $key => $value) {
            $envKey = self::ENV_PREFIX . strtoupper($configuration->platform_type) . '_' . strtoupper($key);
            $envValue = env($envKey);
            
            if ($envValue !== null) {
                $credentials[$key] = $envValue;
            }
        }

        return $credentials;
    }

    /**
     * 验证配置的安全性
     *
     * @param array $configData 配置数据
     * @return array 验证结果
     */
    public function validateSecurity(array $configData): array
    {
        $issues = [];

        // 检查是否使用HTTPS
        if (isset($configData['base_url']) && !str_starts_with($configData['base_url'], 'https://')) {
            $issues[] = 'API端点应使用HTTPS协议以确保数据传输安全';
        }

        // 检查认证凭据是否为空
        if (empty($configData['auth_credentials'])) {
            $issues[] = '缺少认证凭据，这可能导致API调用失败';
        }

        // 检查敏感信息是否暴露在日志中
        foreach (self::SENSITIVE_FIELDS as $field) {
            if (isset($configData['auth_credentials'][$field])) {
                $value = $configData['auth_credentials'][$field];
                if (strlen($value) < 8) {
                    $issues[] = "字段 {$field} 的值过短，可能不够安全";
                }
            }
        }

        return [
            'is_secure' => empty($issues),
            'issues' => $issues,
            'recommendations' => $this->getSecurityRecommendations($configData),
        ];
    }

    /**
     * 加密敏感数据
     *
     * @param array $data 原始数据
     * @return array 加密后的数据
     */
    private function encryptSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            if (in_array($key, self::SENSITIVE_FIELDS) && !empty($value)) {
                // 只加密非空的敏感字段
                $data[$key] = Crypt::encryptString($value);
            }
        }

        return $data;
    }

    /**
     * 解密敏感数据
     *
     * @param array $data 加密的数据
     * @return array 解密后的数据
     */
    private function decryptSensitiveData(array $data): array
    {
        foreach ($data as $key => $value) {
            if (in_array($key, self::SENSITIVE_FIELDS) && !empty($value)) {
                try {
                    $data[$key] = Crypt::decryptString($value);
                } catch (Exception $e) {
                    Log::warning("解密字段 {$key} 失败", ['error' => $e->getMessage()]);
                    // 如果解密失败，保持原值（可能已经是明文）
                }
            }
        }

        return $data;
    }

    /**
     * 处理元数据中的敏感信息
     *
     * @param array $metadata 元数据
     * @return array 处理后的元数据
     */
    private function processSensitiveMetadata(array $metadata): array
    {
        // 递归处理嵌套的敏感数据
        foreach ($metadata as $key => $value) {
            if (is_array($value)) {
                $metadata[$key] = $this->processSensitiveMetadata($value);
            } elseif (in_array($key, self::SENSITIVE_FIELDS) && !empty($value)) {
                $metadata[$key] = Crypt::encryptString($value);
            }
        }

        return $metadata;
    }

    /**
     * 解密元数据中的敏感信息
     *
     * @param array $metadata 加密的元数据
     * @return array 解密后的元数据
     */
    private function processDecryptMetadata(array $metadata): array
    {
        foreach ($metadata as $key => $value) {
            if (is_array($value)) {
                $metadata[$key] = $this->processDecryptMetadata($value);
            } elseif (in_array($key, self::SENSITIVE_FIELDS) && !empty($value)) {
                try {
                    $metadata[$key] = Crypt::decryptString($value);
                } catch (Exception $e) {
                    Log::warning("解密元数据字段 {$key} 失败", ['error' => $e->getMessage()]);
                }
            }
        }

        return $metadata;
    }

    /**
     * 获取安全建议
     *
     * @param array $configData 配置数据
     * @return array 安全建议
     */
    private function getSecurityRecommendations(array $configData): array
    {
        $recommendations = [];

        if (!str_starts_with($configData['base_url'] ?? '', 'https://')) {
            $recommendations[] = '使用HTTPS协议保护API通信';
        }

        $recommendations[] = '定期轮换API密钥和访问令牌';
        $recommendations[] = '使用环境变量存储敏感配置，避免硬编码';
        $recommendations[] = '启用API访问日志监控异常活动';
        $recommendations[] = '设置适当的速率限制防止滥用';

        return $recommendations;
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->boolean('is_enabled')->default(true)->comment('是否启用任务')->after('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->dropColumn('is_enabled');
        });
    }
};

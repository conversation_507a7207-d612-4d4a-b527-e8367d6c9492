#!/bin/bash

# MeiliSearch 安装和配置脚本
# 用于快速设置竞争对手搜索功能所需的MeiliSearch服务

echo "=== MeiliSearch 安装和配置脚本 ==="

# 检查操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "检测到 Linux 系统"
    
    # 下载并安装 MeiliSearch
    echo "正在下载 MeiliSearch..."
    curl -L https://install.meilisearch.com | sh
    
    # 移动到系统路径
    sudo mv ./meilisearch /usr/local/bin/
    
    echo "MeiliSearch 安装完成"
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "检测到 macOS 系统"
    
    # 使用 Homebrew 安装
    if command -v brew &> /dev/null; then
        echo "使用 Homebrew 安装 MeiliSearch..."
        brew install meilisearch
    else
        echo "未找到 Homebrew，请先安装 Homebrew 或手动安装 MeiliSearch"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "检测到 Windows 系统"
    echo "请手动下载 MeiliSearch Windows 版本："
    echo "https://github.com/meilisearch/meilisearch/releases"
    echo "下载后将 meilisearch.exe 放置在 PATH 中"
    
else
    echo "不支持的操作系统: $OSTYPE"
    exit 1
fi

# 创建 MeiliSearch 配置目录
echo "创建配置目录..."
mkdir -p storage/meilisearch

# 创建 MeiliSearch 配置文件
echo "创建配置文件..."
cat > storage/meilisearch/config.toml << EOF
# MeiliSearch 配置文件

# 数据库路径
db_path = "./storage/meilisearch/data.ms"

# HTTP 地址和端口
http_addr = "127.0.0.1:7700"

# 主密钥（生产环境请修改）
master_key = "your-master-key-here"

# 日志级别
log_level = "INFO"

# 最大索引大小 (100MB)
max_indexing_memory = "100Mb"

# 最大索引线程数
max_indexing_threads = 4

# 启用仪表板
no_analytics = false
EOF

# 创建启动脚本
echo "创建启动脚本..."
cat > scripts/start-meilisearch.sh << 'EOF'
#!/bin/bash

# MeiliSearch 启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/storage/meilisearch/config.toml"

echo "启动 MeiliSearch..."
echo "配置文件: $CONFIG_FILE"
echo "访问地址: http://127.0.0.1:7700"
echo "按 Ctrl+C 停止服务"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件不存在: $CONFIG_FILE"
    echo "请先运行 setup-meilisearch.sh 脚本"
    exit 1
fi

# 启动 MeiliSearch
meilisearch --config-file-path "$CONFIG_FILE"
EOF

# 创建停止脚本
cat > scripts/stop-meilisearch.sh << 'EOF'
#!/bin/bash

# MeiliSearch 停止脚本

echo "正在停止 MeiliSearch..."

# 查找 MeiliSearch 进程
PIDS=$(pgrep -f "meilisearch")

if [ -z "$PIDS" ]; then
    echo "未找到运行中的 MeiliSearch 进程"
else
    echo "找到 MeiliSearch 进程: $PIDS"
    
    # 优雅停止
    for PID in $PIDS; do
        echo "正在停止进程 $PID..."
        kill -TERM $PID
        
        # 等待进程结束
        sleep 2
        
        # 检查进程是否仍在运行
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程 $PID..."
            kill -KILL $PID
        fi
    done
    
    echo "MeiliSearch 已停止"
fi
EOF

# 创建索引管理脚本
cat > scripts/manage-search-index.php << 'EOF'
<?php

/**
 * MeiliSearch 索引管理脚本
 * 用于创建和管理产品搜索索引
 */

require_once __DIR__ . '/../vendor/autoload.php';

use MeiliSearch\Client;

// 配置
$host = 'http://127.0.0.1:7700';
$masterKey = 'your-master-key-here'; // 请修改为实际的主密钥

try {
    // 创建客户端
    $client = new Client($host, $masterKey);
    
    // 检查连接
    echo "正在连接到 MeiliSearch...\n";
    $health = $client->health();
    echo "连接成功！状态: " . $health['status'] . "\n";
    
    // 创建产品索引
    echo "创建产品索引...\n";
    $index = $client->index('products');
    
    // 配置可搜索属性
    $searchableAttributes = [
        'title',
        'description', 
        'category_path',
        'shop_name',
        'brand'
    ];
    
    $index->updateSearchableAttributes($searchableAttributes);
    echo "已设置可搜索属性: " . implode(', ', $searchableAttributes) . "\n";
    
    // 配置可过滤属性
    $filterableAttributes = [
        'source_platform',
        'category_path',
        'min_price',
        'max_price',
        'total_sales',
        'rating',
        'shop_id',
        'shop_name'
    ];
    
    $index->updateFilterableAttributes($filterableAttributes);
    echo "已设置可过滤属性: " . implode(', ', $filterableAttributes) . "\n";
    
    // 配置可排序属性
    $sortableAttributes = [
        'min_price',
        'max_price',
        'total_sales',
        'rating',
        'created_at',
        'updated_at'
    ];
    
    $index->updateSortableAttributes($sortableAttributes);
    echo "已设置可排序属性: " . implode(', ', $sortableAttributes) . "\n";
    
    // 配置同义词（可选）
    $synonyms = [
        'phone' => ['手机', 'mobile', 'smartphone'],
        'laptop' => ['笔记本', 'notebook', '电脑'],
        'tablet' => ['平板', 'pad']
    ];
    
    $index->updateSynonyms($synonyms);
    echo "已设置同义词\n";
    
    // 配置停用词（可选）
    $stopWords = ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个'];
    $index->updateStopWords($stopWords);
    echo "已设置停用词\n";
    
    echo "索引配置完成！\n";
    echo "现在可以使用 Laravel Scout 进行产品搜索了。\n";
    echo "\n使用方法:\n";
    echo "1. 在 Laravel 中运行: php artisan scout:import \"App\\Models\\Product\"\n";
    echo "2. 或者使用模型方法: Product::all()->searchable()\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "请检查 MeiliSearch 是否正在运行，以及配置是否正确。\n";
    exit(1);
}
EOF

# 设置脚本权限
chmod +x scripts/start-meilisearch.sh
chmod +x scripts/stop-meilisearch.sh

echo ""
echo "=== 安装完成 ==="
echo ""
echo "接下来的步骤:"
echo "1. 修改 storage/meilisearch/config.toml 中的 master_key"
echo "2. 修改 .env 文件，添加以下配置:"
echo "   SCOUT_DRIVER=meilisearch"
echo "   MEILISEARCH_HOST=http://127.0.0.1:7700"
echo "   MEILISEARCH_KEY=your-master-key-here"
echo ""
echo "3. 启动 MeiliSearch 服务:"
echo "   ./scripts/start-meilisearch.sh"
echo ""
echo "4. 配置搜索索引:"
echo "   php scripts/manage-search-index.php"
echo ""
echo "5. 导入现有产品数据:"
echo "   php artisan scout:import \"App\\Models\\Product\""
echo ""
echo "6. 访问 MeiliSearch 仪表板:"
echo "   http://127.0.0.1:7700"
echo ""
echo "注意: 请在生产环境中修改默认的 master_key！" 
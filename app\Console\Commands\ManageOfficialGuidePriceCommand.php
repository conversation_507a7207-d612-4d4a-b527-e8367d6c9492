<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OfficialGuidePriceService;
use App\Models\ProductSku;

/**
 * 官方指导价管理命令
 */
class ManageOfficialGuidePriceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'guide-price:manage 
                           {action : Action to perform (stats|set|auto-calculate|import|clear)}
                           {--sku-id= : SKU ID for single operations}
                           {--price= : Guide price to set}
                           {--source=manual : Price source (manual|api|import|auto)}
                           {--days=90 : Days to analyze for auto calculation}
                           {--method=max : Calculation method (max|avg|percentile)}
                           {--percentile=95 : Percentile for percentile method}
                           {--file= : CSV file path for import}
                           {--dry-run : Show what would be done without making changes}
                           {--limit=100 : Limit number of records to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理商品SKU的官方指导价';

    private OfficialGuidePriceService $guidePriceService;

    public function __construct(OfficialGuidePriceService $guidePriceService)
    {
        parent::__construct();
        $this->guidePriceService = $guidePriceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        try {
            switch ($action) {
                case 'stats':
                    $this->showStats();
                    break;

                case 'set':
                    $this->setSingle();
                    break;

                case 'auto-calculate':
                    $this->autoCalculate();
                    break;

                case 'import':
                    $this->importFromCsv();
                    break;

                case 'clear':
                    $this->clearGuidePrices();
                    break;

                default:
                    $this->error("Invalid action: {$action}");
                    $this->info('Available actions: stats, set, auto-calculate, import, clear');
                    return 1;
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Command failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * 显示官方指导价统计信息
     */
    private function showStats(): void
    {
        $this->info('获取官方指导价统计信息...');
        
        $stats = $this->guidePriceService->getGuidePriceStats();

        $this->table(['指标', '数值'], [
            ['活跃SKU总数', number_format($stats['total_skus'])],
            ['已设置指导价', number_format($stats['coverage']['with_guide_price'])],
            ['未设置指导价', number_format($stats['coverage']['without_guide_price'])],
            ['覆盖率', $stats['coverage']['percentage'] . '%'],
            ['过期数量', number_format($stats['stale_count'])]
        ]);

        if (!empty($stats['source_distribution'])) {
            $this->info("\n按数据源分布:");
            $sourceData = [];
            foreach ($stats['source_distribution'] as $source => $count) {
                $sourceData[] = [$source, number_format($count)];
            }
            $this->table(['数据源', '数量'], $sourceData);
        }
    }

    /**
     * 为单个SKU设置官方指导价
     */
    private function setSingle(): void
    {
        $skuId = $this->option('sku-id');
        $price = $this->option('price');
        $source = $this->option('source');

        if (!$skuId || !$price) {
            $this->error('需要提供 --sku-id 和 --price 参数');
            return;
        }

        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将为SKU {$skuId} 设置官方指导价 {$price} (来源: {$source})");
            return;
        }

        $this->info("为SKU {$skuId} 设置官方指导价...");

        $result = $this->guidePriceService->setGuidePriceForSku(
            (int) $skuId,
            (float) $price,
            $source
        );

        if ($result['success']) {
            $this->info("✅ 成功设置官方指导价");
            $this->line("   旧价格: " . ($result['old_price'] ?? '未设置'));
            $this->line("   新价格: {$result['new_price']}");
            $this->line("   数据源: {$result['source']}");
        } else {
            $this->error("❌ 设置失败: " . $result['error']);
        }
    }

    /**
     * 自动计算官方指导价
     */
    private function autoCalculate(): void
    {
        $skuId = $this->option('sku-id');
        $days = (int) $this->option('days');
        $method = $this->option('method');
        $percentile = (float) $this->option('percentile');

        if (!$skuId) {
            // 批量处理所有需要的SKU
            $this->batchAutoCalculate($days, $method, $percentile);
            return;
        }

        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将为SKU {$skuId} 自动计算官方指导价 (方法: {$method}, 天数: {$days})");
            return;
        }

        $this->info("为SKU {$skuId} 自动计算官方指导价...");

        $result = $this->guidePriceService->calculateAutoGuidePrice(
            (int) $skuId,
            $days,
            $method,
            $percentile
        );

        if ($result['success']) {
            $this->info("✅ 成功计算并设置官方指导价");
            $this->line("   计算价格: {$result['new_price']}");
            $this->line("   计算方法: {$method}");
            $this->line("   分析天数: {$days}");
            if (isset($result['calculation_details'])) {
                $this->line("   数据点数: " . $result['calculation_details']['price_points']);
            }
        } else {
            $this->error("❌ 计算失败: " . $result['error']);
        }
    }

    /**
     * 批量自动计算
     */
    private function batchAutoCalculate(int $days, string $method, float $percentile): void
    {
        $limit = (int) $this->option('limit');
        
        $this->info("获取需要自动计算官方指导价的SKU...");
        
        $skus = ProductSku::active()
                         ->whereNull('official_guide_price')
                         ->limit($limit)
                         ->get(['id', 'sku_code']);

        if ($skus->isEmpty()) {
            $this->info("没有找到需要计算官方指导价的SKU");
            return;
        }

        $this->info("找到 {$skus->count()} 个SKU需要计算官方指导价");

        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将为 {$skus->count()} 个SKU 自动计算官方指导价");
            return;
        }

        $bar = $this->output->createProgressBar($skus->count());
        $bar->start();

        $success = 0;
        $failed = 0;

        foreach ($skus as $sku) {
            $result = $this->guidePriceService->calculateAutoGuidePrice(
                $sku->id,
                $days,
                $method,
                $percentile
            );

            if ($result['success']) {
                $success++;
            } else {
                $failed++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("批量计算完成:");
        $this->line("  ✅ 成功: {$success}");
        $this->line("  ❌ 失败: {$failed}");
    }

    /**
     * 从CSV文件导入
     */
    private function importFromCsv(): void
    {
        $filePath = $this->option('file');

        if (!$filePath) {
            $this->error('需要提供 --file 参数指定CSV文件路径');
            return;
        }

        if (!file_exists($filePath)) {
            $this->error("文件不存在: {$filePath}");
            return;
        }

        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将从文件 {$filePath} 导入官方指导价");
            return;
        }

        $this->info("从CSV文件导入官方指导价...");

        $result = $this->guidePriceService->importFromCsv($filePath);

        if ($result['success']) {
            $this->info("✅ 导入完成:");
            $this->line("   总计: {$result['total']}");
            $this->line("   成功: {$result['success']}");
            $this->line("   失败: {$result['failed']}");

            if (!empty($result['errors'])) {
                $this->warn("\n错误信息:");
                foreach (array_slice($result['errors'], 0, 5) as $error) {
                    $this->line("  - {$error}");
                }
                if (count($result['errors']) > 5) {
                    $this->line("  ... 还有 " . (count($result['errors']) - 5) . " 个错误");
                }
            }
        } else {
            $this->error("❌ 导入失败: " . $result['error']);
        }
    }

    /**
     * 清除官方指导价
     */
    private function clearGuidePrices(): void
    {
        $skuId = $this->option('sku-id');

        if ($skuId) {
            // 清除单个SKU
            $this->clearSingleSku((int) $skuId);
        } else {
            // 清除所有过期的官方指导价
            $this->clearStaleGuidePrices();
        }
    }

    /**
     * 清除单个SKU的官方指导价
     */
    private function clearSingleSku(int $skuId): void
    {
        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将清除SKU {$skuId} 的官方指导价");
            return;
        }

        $sku = ProductSku::find($skuId);
        if (!$sku) {
            $this->error("SKU不存在: {$skuId}");
            return;
        }

        if (!$sku->official_guide_price) {
            $this->info("SKU {$skuId} 没有设置官方指导价");
            return;
        }

        $sku->official_guide_price = null;
        $sku->official_guide_price_set_at = null;
        $sku->official_guide_price_source = null;
        $sku->save();

        $this->info("✅ 已清除SKU {$skuId} 的官方指导价");
    }

    /**
     * 清除过期的官方指导价
     */
    private function clearStaleGuidePrices(): void
    {
        $limit = (int) $this->option('limit');

        $staleSkus = ProductSku::active()
                              ->withOfficialGuidePrice()
                              ->staleGuidePrice()
                              ->limit($limit)
                              ->get(['id', 'sku_code', 'official_guide_price_set_at']);

        if ($staleSkus->isEmpty()) {
            $this->info("没有找到过期的官方指导价");
            return;
        }

        $this->info("找到 {$staleSkus->count()} 个过期的官方指导价");

        if ($this->option('dry-run')) {
            $this->info("模拟运行: 将清除 {$staleSkus->count()} 个过期的官方指导价");
            return;
        }

        if (!$this->confirm("确定要清除这些过期的官方指导价吗?")) {
            $this->info("操作已取消");
            return;
        }

        $updated = ProductSku::whereIn('id', $staleSkus->pluck('id'))
                            ->update([
                                'official_guide_price' => null,
                                'official_guide_price_set_at' => null,
                                'official_guide_price_source' => null
                            ]);

        $this->info("✅ 已清除 {$updated} 个过期的官方指导价");
    }
} 
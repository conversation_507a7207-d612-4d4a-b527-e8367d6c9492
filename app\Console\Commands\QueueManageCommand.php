<?php

namespace App\Console\Commands;

use App\Services\QueueManagementService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;

class QueueManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:manage 
                            {action : 操作类型 (start|stop|restart|status|pause|resume|flush)}
                            {--queue= : 指定队列名称}
                            {--workers=3 : 工作进程数量}
                            {--timeout=60 : 任务超时时间（秒）}
                            {--tries=3 : 最大重试次数}
                            {--sleep=3 : 空闲时睡眠时间（秒）}
                            {--memory=128 : 内存限制（MB）}
                            {--daemon : 以守护进程模式运行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '管理Laravel队列工作进程的启动、停止和配置';

    /**
     * Queue management service instance
     */
    protected $queueService;

    /**
     * Create a new command instance.
     */
    public function __construct(QueueManagementService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'start':
                return $this->startWorkers();
            case 'stop':
                return $this->stopWorkers();
            case 'restart':
                return $this->restartWorkers();
            case 'status':
                return $this->showStatus();
            case 'pause':
                return $this->pauseQueue();
            case 'resume':
                return $this->resumeQueue();
            case 'flush':
                return $this->flushQueue();
            default:
                $this->error("未知操作: {$action}");
                $this->line('可用操作: start, stop, restart, status, pause, resume, flush');
                return 1;
        }
    }

    /**
     * 启动队列工作进程
     */
    protected function startWorkers()
    {
        $this->info('正在启动队列工作进程...');

        $queues = $this->getQueueNames();
        $workers = (int) $this->option('workers');
        $timeout = (int) $this->option('timeout');
        $tries = (int) $this->option('tries');
        $sleep = (int) $this->option('sleep');
        $memory = (int) $this->option('memory');

        $startedWorkers = 0;

        foreach ($queues as $queueName => $queueConfig) {
            $queueWorkers = $queueConfig['workers'] ?? 1;
            
            for ($i = 1; $i <= $queueWorkers; $i++) {
                $success = $this->startWorker($queueName, [
                    'timeout' => $timeout,
                    'tries' => $tries,
                    'sleep' => $sleep,
                    'memory' => $memory,
                    'daemon' => $this->option('daemon')
                ]);

                if ($success) {
                    $startedWorkers++;
                    $this->line("✓ 启动队列 '{$queueName}' 工作进程 #{$i}");
                } else {
                    $this->error("✗ 启动队列 '{$queueName}' 工作进程 #{$i} 失败");
                }
            }
        }

        $this->info("成功启动 {$startedWorkers} 个工作进程");
        
        // 创建PID文件记录
        $this->createPidFile($startedWorkers);

        return 0;
    }

    /**
     * 停止队列工作进程
     */
    protected function stopWorkers()
    {
        $this->info('正在停止队列工作进程...');

        // 发送停止信号到所有工作进程
        Artisan::call('queue:restart');
        
        $this->line('已发送重启信号到所有工作进程');
        $this->line('工作进程将在完成当前任务后优雅退出');

        // 清理PID文件
        $this->removePidFile();

        return 0;
    }

    /**
     * 重启队列工作进程
     */
    protected function restartWorkers()
    {
        $this->info('正在重启队列工作进程...');

        // 先停止
        $this->stopWorkers();
        
        // 等待一段时间让进程优雅退出
        $this->line('等待工作进程退出...');
        sleep(5);
        
        // 再启动
        return $this->startWorkers();
    }

    /**
     * 显示队列状态
     */
    protected function showStatus()
    {
        $this->info('=== 队列工作进程状态 ===');

        // 显示队列统计
        $result = $this->queueService->getQueueStats();
        
        if ($result['success']) {
            $stats = $result['stats'];
            
            $this->table(['指标', '数值'], [
                ['总待处理任务', $stats['total_pending']],
                ['正在处理任务', $stats['processing'] ?? 0],
                ['失败任务', $stats['failed_jobs']],
            ]);

            // 显示各队列状态
            if (!empty($stats['queues'])) {
                $this->newLine();
                $queueData = [];
                foreach ($stats['queues'] as $queueName => $queueInfo) {
                    $queueData[] = [
                        $queueName,
                        $queueInfo['size'],
                        $this->getQueueStatus($queueName)
                    ];
                }
                $this->table(['队列名称', '待处理任务', '状态'], $queueData);
            }
        } else {
            $this->error('获取队列状态失败: ' . $result['error']);
        }

        // 显示工作进程信息
        $this->displayWorkerProcesses();

        return 0;
    }

    /**
     * 暂停队列
     */
    protected function pauseQueue()
    {
        $queueName = $this->option('queue');
        
        $result = $this->queueService->pauseQueue($queueName);
        
        if ($result['success']) {
            $message = $queueName 
                ? "队列 '{$queueName}' 已暂停" 
                : "所有队列已暂停";
            $this->info($message);
        } else {
            $this->error('暂停队列失败: ' . $result['error']);
        }

        return 0;
    }

    /**
     * 恢复队列
     */
    protected function resumeQueue()
    {
        $queueName = $this->option('queue');
        
        $result = $this->queueService->resumeQueue($queueName);
        
        if ($result['success']) {
            $message = $queueName 
                ? "队列 '{$queueName}' 已恢复" 
                : "所有队列已恢复";
            $this->info($message);
        } else {
            $this->error('恢复队列失败: ' . $result['error']);
        }

        return 0;
    }

    /**
     * 清空队列
     */
    protected function flushQueue()
    {
        $queueName = $this->option('queue') ?? 'default';
        
        if ($this->confirm("确定要清空队列 '{$queueName}' 中的所有任务吗？")) {
            try {
                Artisan::call('queue:flush', ['--queue' => $queueName]);
                $this->info("队列 '{$queueName}' 已清空");
            } catch (\Exception $e) {
                $this->error('清空队列失败: ' . $e->getMessage());
            }
        }

        return 0;
    }

    /**
     * 启动单个工作进程
     */
    protected function startWorker($queueName, array $options = [])
    {
        try {
            $command = [
                'queue:work',
                '--queue' => $queueName,
                '--timeout' => $options['timeout'] ?? 60,
                '--tries' => $options['tries'] ?? 3,
                '--sleep' => $options['sleep'] ?? 3,
                '--memory' => $options['memory'] ?? 128,
            ];

            if ($options['daemon'] ?? false) {
                $command['--daemon'] = true;
            }

            // 在后台启动工作进程
            if (PHP_OS_FAMILY === 'Windows') {
                $cmd = 'start /B php artisan ' . $this->buildCommandString($command);
                popen($cmd, 'r');
            } else {
                $cmd = 'php artisan ' . $this->buildCommandString($command) . ' > /dev/null 2>&1 &';
                exec($cmd);
            }

            return true;

        } catch (\Exception $e) {
            $this->error("启动工作进程失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 构建命令字符串
     */
    protected function buildCommandString(array $command)
    {
        $cmd = array_shift($command);
        
        foreach ($command as $key => $value) {
            if (is_bool($value) && $value) {
                $cmd .= " {$key}";
            } else {
                $cmd .= " {$key}={$value}";
            }
        }

        return $cmd;
    }

    /**
     * 获取队列配置
     */
    protected function getQueueNames()
    {
        $specificQueue = $this->option('queue');
        
        if ($specificQueue) {
            return [$specificQueue => ['workers' => $this->option('workers')]];
        }

        // 从配置文件获取队列设置
        $queueConfig = config('datacollection.queue.queues', [
            'data-collection-high' => ['workers' => 2],
            'data-collection' => ['workers' => 3],
            'data-collection-low' => ['workers' => 1],
        ]);

        return $queueConfig;
    }

    /**
     * 获取队列状态
     */
    protected function getQueueStatus($queueName)
    {
        // 这里可以实现更复杂的状态检测
        return '运行中';
    }

    /**
     * 显示工作进程信息
     */
    protected function displayWorkerProcesses()
    {
        $this->newLine();
        $this->info('=== 活跃工作进程 ===');

        try {
            $processes = $this->getWorkerProcesses();
            
            if (empty($processes)) {
                $this->warn('未检测到活跃的队列工作进程');
                $this->line('使用 php artisan queue:manage start 启动工作进程');
            } else {
                $this->table(['进程ID', '命令', '状态'], $processes);
            }

        } catch (\Exception $e) {
            $this->error('获取进程信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取工作进程列表
     */
    protected function getWorkerProcesses()
    {
        $processes = [];

        try {
            if (PHP_OS_FAMILY === 'Windows') {
                $output = shell_exec('wmic process where "CommandLine like \'%queue:work%\'" get ProcessId,CommandLine /format:csv 2>NUL');
            } else {
                $output = shell_exec('ps aux | grep "queue:work" | grep -v grep');
            }

            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (empty(trim($line))) continue;
                    
                    // 简化的进程信息解析
                    if (strpos($line, 'queue:work') !== false) {
                        $processes[] = [
                            '检测到',
                            'queue:work',
                            '运行中'
                        ];
                    }
                }
            }

        } catch (\Exception $e) {
            // 忽略错误，返回空数组
        }

        return $processes;
    }

    /**
     * 创建PID文件
     */
    protected function createPidFile($workerCount)
    {
        $pidData = [
            'workers' => $workerCount,
            'started_at' => now()->toISOString(),
            'pid' => getmypid()
        ];

        $pidFile = storage_path('app/queue_workers.json');
        File::put($pidFile, json_encode($pidData, JSON_PRETTY_PRINT));
    }

    /**
     * 删除PID文件
     */
    protected function removePidFile()
    {
        $pidFile = storage_path('app/queue_workers.json');
        if (File::exists($pidFile)) {
            File::delete($pidFile);
        }
    }
} 
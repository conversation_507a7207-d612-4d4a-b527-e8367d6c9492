<?php

namespace Tests\Feature;

use App\Models\AuditLog;
use App\Models\Role;
use App\Models\User;
use App\Services\PerformanceMonitorService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class LoggingAndMonitoringTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $regularUser;
    protected PerformanceMonitorService $performanceMonitor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建角色和用户
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->roles()->attach($adminRole->id);
        
        $this->regularUser = User::factory()->create();
        
        $this->performanceMonitor = new PerformanceMonitorService();
    }

    public function test_can_create_audit_log()
    {
        $log = AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_LOGIN,
            'url' => '/login',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Browser',
            'metadata' => ['test' => 'data'],
        ]);

        $this->assertInstanceOf(AuditLog::class, $log);
        $this->assertEquals(AuditLog::EVENT_LOGIN, $log->event);
        $this->assertEquals($this->adminUser->id, $log->user_id);
    }

    public function test_audit_log_static_methods()
    {
        // 测试登录日志
        $loginLog = AuditLog::logLogin($this->adminUser, ['source' => 'web']);
        $this->assertEquals(AuditLog::EVENT_LOGIN, $loginLog->event);
        $this->assertEquals($this->adminUser->id, $loginLog->user_id);

        // 测试登出日志
        $logoutLog = AuditLog::logLogout($this->adminUser);
        $this->assertEquals(AuditLog::EVENT_LOGOUT, $logoutLog->event);

        // 测试API调用日志
        $this->actingAs($this->adminUser);
        $apiLog = AuditLog::logApiCall('/api/test', ['response_time' => 150]);
        $this->assertEquals(AuditLog::EVENT_API_CALL, $apiLog->event);
        $this->assertEquals(150, $apiLog->metadata['response_time']);

        // 测试数据收集日志
        $dataLog = AuditLog::logDataCollection('taobao', ['products_count' => 100]);
        $this->assertEquals(AuditLog::EVENT_DATA_COLLECTION, $dataLog->event);
        $this->assertEquals('taobao', $dataLog->metadata['platform']);
    }

    public function test_audit_log_scopes()
    {
        // 创建测试数据
        AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_LOGIN,
            'created_at' => now(),
        ]);

        AuditLog::create([
            'user_id' => $this->regularUser->id,
            'event' => AuditLog::EVENT_LOGOUT,
            'created_at' => now()->subDays(2),
        ]);

        // 测试用户过滤
        $adminLogs = AuditLog::byUser($this->adminUser->id)->get();
        $this->assertCount(1, $adminLogs);

        // 测试事件过滤
        $loginLogs = AuditLog::byEvent(AuditLog::EVENT_LOGIN)->get();
        $this->assertCount(1, $loginLogs);

        // 测试最近日志
        $recentLogs = AuditLog::recent(3)->get(); // 扩大范围以包含所有日志
        $this->assertCount(2, $recentLogs);

        // 测试今日日志
        $todayLogs = AuditLog::today()->get();
        $this->assertGreaterThanOrEqual(1, $todayLogs->count());
    }

    public function test_audit_log_helper_methods()
    {
        $log = AuditLog::create([
            'event' => AuditLog::EVENT_UPDATED,
            'auditable_type' => User::class,
            'old_values' => ['name' => 'Old Name'],
            'new_values' => ['name' => 'New Name'],
        ]);

        $this->assertEquals('更新', $log->getEventDisplayName());
        $this->assertEquals('用户', $log->getModelDisplayName());
        $this->assertTrue($log->hasValueChanges());
        $this->assertEquals(['name'], $log->getChangedFields());
    }

    public function test_performance_monitor_service()
    {
        // 创建一些测试数据
        AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_API_CALL,
            'metadata' => [
                'response_time' => 150.5,
                'success' => true,
                'status_code' => 200,
            ],
            'created_at' => now(),
        ]);

        AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_API_CALL,
            'metadata' => [
                'response_time' => 250.0,
                'success' => false,
                'status_code' => 500,
            ],
            'created_at' => now(),
        ]);

        // 测试响应时间统计
        $responseTime = $this->performanceMonitor->getAverageResponseTime(24);
        $this->assertArrayHasKey('average', $responseTime);
        $this->assertArrayHasKey('minimum', $responseTime);
        $this->assertArrayHasKey('maximum', $responseTime);
        $this->assertEquals(2, $responseTime['total_requests']);

        // 测试请求统计
        $requestStats = $this->performanceMonitor->getRequestCount(24);
        $this->assertEquals(2, $requestStats['total']);
        $this->assertEquals(1, $requestStats['successful']);
        $this->assertEquals(1, $requestStats['failed']);

        // 测试错误率
        $errorRate = $this->performanceMonitor->getErrorRate(24);
        $this->assertEquals(50.0, $errorRate['error_rate_percentage']);

        // 测试数据库性能
        $dbPerformance = $this->performanceMonitor->getDatabasePerformance();
        $this->assertArrayHasKey('connection_time_ms', $dbPerformance);
        $this->assertArrayHasKey('driver', $dbPerformance);

        // 测试内存使用
        $memoryUsage = $this->performanceMonitor->getMemoryUsage();
        $this->assertArrayHasKey('current_mb', $memoryUsage);
        $this->assertArrayHasKey('peak_mb', $memoryUsage);
        $this->assertArrayHasKey('usage_percentage', $memoryUsage);

        // 测试活跃用户数
        $activeUsers = $this->performanceMonitor->getActiveUsersCount(30);
        $this->assertIsInt($activeUsers);
        $this->assertEquals(1, $activeUsers); // 只有一个用户有活动

        // 测试系统负载
        $systemLoad = $this->performanceMonitor->getSystemLoad();
        $this->assertArrayHasKey('php_version', $systemLoad);
        $this->assertArrayHasKey('cpu_count', $systemLoad);
    }

    public function test_monitoring_controller_dashboard()
    {
        $response = $this->actingAs($this->adminUser)
            ->get('/monitoring/');

        $response->assertStatus(200);
    }

    public function test_monitoring_controller_system_overview()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/monitoring/system-overview');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'response_time',
                    'request_count',
                    'error_rate',
                    'database_performance',
                    'memory_usage',
                    'cache_hit_rate',
                    'active_users',
                    'system_load',
                ],
                'timestamp',
            ]);
    }

    public function test_monitoring_controller_response_time_stats()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/monitoring/response-time?hours=24');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'average',
                    'minimum',
                    'maximum',
                    'total_requests',
                    'period_hours',
                ],
            ]);
    }

    public function test_monitoring_controller_audit_logs()
    {
        // 创建一些测试日志
        AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_LOGIN,
            'created_at' => now(),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->getJson('/monitoring/audit-logs');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data',
                    'current_page',
                    'total',
                ],
            ]);
    }

    public function test_monitoring_controller_health_check()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/monitoring/health-check');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'overall_status',
                'checks' => [
                    'database',
                    'cache',
                    'storage',
                    'memory',
                ],
                'timestamp',
            ]);
    }

    public function test_monitoring_controller_clear_cache()
    {
        $response = $this->actingAs($this->adminUser)
            ->postJson('/monitoring/clear-cache');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '监控缓存已清除',
            ]);
    }

    public function test_monitoring_controller_user_activity_stats()
    {
        // 创建一些用户活动数据
        AuditLog::create([
            'user_id' => $this->adminUser->id,
            'event' => AuditLog::EVENT_LOGIN,
            'created_at' => now(),
        ]);

        AuditLog::create([
            'user_id' => $this->regularUser->id,
            'event' => AuditLog::EVENT_UPDATED,
            'created_at' => now(),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->getJson('/monitoring/user-activity?days=7');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'login_stats',
                    'active_users',
                    'event_stats',
                    'period_days',
                ],
            ]);
    }

    public function test_performance_monitor_caching()
    {
        // 清除缓存
        Cache::flush();

        // 第一次调用应该从数据库获取并缓存
        $overview1 = $this->performanceMonitor->getSystemOverview();
        $this->assertIsArray($overview1);

        // 验证缓存中有数据
        $cacheKey = 'performance_metrics_overview';
        $this->assertTrue(Cache::has($cacheKey));

        // 第二次调用应该从缓存获取
        $overview2 = $this->performanceMonitor->getSystemOverview();
        $this->assertEquals($overview1, $overview2);

        // 清除缓存
        $this->performanceMonitor->clearCache();
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_requires_admin_role_for_monitoring()
    {
        $response = $this->actingAs($this->regularUser)
            ->get('/monitoring/');

        $response->assertStatus(403);
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}

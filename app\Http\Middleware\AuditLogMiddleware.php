<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    /**
     * 需要记录审计日志的路由模式
     */
    private array $auditableRoutes = [
        'api-configurations.*',
        'system-configurations.*',
        'users.*',
        'roles.*',
        'permissions.*',
    ];

    /**
     * 不需要记录的HTTP方法
     */
    private array $excludedMethods = ['GET', 'HEAD', 'OPTIONS'];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        $response = $next($request);

        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000, 2); // 毫秒

        // 记录审计日志
        $this->logRequest($request, $response, $responseTime);

        return $response;
    }

    /**
     * 记录请求日志
     */
    private function logRequest(Request $request, Response $response, float $responseTime): void
    {
        try {
            // 检查是否需要记录此请求
            if (!$this->shouldLogRequest($request)) {
                return;
            }

            $routeName = $request->route()?->getName() ?? '';
            $statusCode = $response->getStatusCode();

            // 记录API调用日志
            AuditLog::logApiCall($request->path(), [
                'route_name' => $routeName,
                'method' => $request->method(),
                'parameters' => $this->sanitizeParameters($request->all()),
                'response_time' => $responseTime,
                'status_code' => $statusCode,
                'success' => $statusCode < 400,
            ]);

            // 如果是重要操作，记录详细日志
            if ($this->isImportantOperation($request, $statusCode)) {
                Log::info('重要操作执行', [
                    'user_id' => auth()->id(),
                    'route' => $routeName,
                    'method' => $request->method(),
                    'path' => $request->path(),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'response_time' => $responseTime,
                    'status_code' => $statusCode,
                ]);
            }

        } catch (\Exception $e) {
            // 记录日志失败不应该影响正常请求
            Log::error('审计日志记录失败', [
                'error' => $e->getMessage(),
                'request_path' => $request->path(),
            ]);
        }
    }

    /**
     * 判断是否应该记录此请求
     */
    private function shouldLogRequest(Request $request): bool
    {
        // 排除特定HTTP方法
        if (in_array($request->method(), $this->excludedMethods)) {
            return false;
        }

        // 只有认证用户的操作才记录
        if (!auth()->check()) {
            return false;
        }

        $routeName = $request->route()?->getName() ?? '';

        // 检查路由是否在需要审计的列表中
        foreach ($this->auditableRoutes as $pattern) {
            if (fnmatch($pattern, $routeName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否为重要操作
     */
    private function isImportantOperation(Request $request, int $statusCode): bool
    {
        // 成功的写操作被认为是重要操作
        if ($statusCode >= 200 && $statusCode < 300) {
            return in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE']);
        }

        return false;
    }

    /**
     * 清理敏感参数
     */
    private function sanitizeParameters(array $parameters): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'api_key',
            'secret_key',
            'access_token',
            'refresh_token',
            'private_key',
            'client_secret',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($parameters[$field])) {
                $parameters[$field] = '***';
            }
        }

        // 递归处理嵌套数组
        foreach ($parameters as $key => $value) {
            if (is_array($value)) {
                $parameters[$key] = $this->sanitizeParameters($value);
            }
        }

        return $parameters;
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            // 添加调度相关字段
            $table->string('platform')->default('taobao')->comment('平台名称')->after('task_name');
            $table->string('cron_expression')->nullable()->comment('自定义cron表达式')->after('frequency');
            
            // 添加执行统计字段
            $table->timestamp('last_executed_at')->nullable()->comment('最后执行时间')->after('last_collected_at');
            $table->unsignedInteger('execution_count')->default(0)->comment('执行次数')->after('collection_count');
            $table->unsignedInteger('success_count')->default(0)->comment('成功次数')->after('execution_count');
            $table->unsignedInteger('failure_count')->default(0)->comment('失败次数')->after('success_count');
            
            // 添加调度和执行控制字段
            $table->unsignedInteger('min_interval')->default(300)->comment('最小执行间隔(秒)')->after('frequency');
            $table->json('dependencies')->nullable()->comment('任务依赖关系')->after('monitor_settings');
            $table->json('retry_config')->nullable()->comment('重试配置')->after('dependencies');
            $table->json('parameters')->nullable()->comment('任务参数')->after('retry_config');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monitor_tasks', function (Blueprint $table) {
            $table->dropColumn([
                'platform',
                'cron_expression',
                'last_executed_at',
                'execution_count',
                'success_count',
                'failure_count',
                'min_interval',
                'dependencies',
                'retry_config',
                'parameters'
            ]);
        });
    }
};

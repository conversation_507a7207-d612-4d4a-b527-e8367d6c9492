/**
 * 商品监控管理JavaScript功能
 */

// 全局变量
let currentPage = 1;
let itemsPerPage = 10;
let currentSearch = '';
let currentPlatform = '';
let currentStatus = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeProducts();
    bindEvents();
});

// 初始化商品监控功能
function initializeProducts() {
    loadProducts();
    loadPlatforms();
}

// 绑定事件
function bindEvents() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // 筛选功能
    const platformFilter = document.getElementById('platformFilter');
    if (platformFilter) {
        platformFilter.addEventListener('change', handlePlatformFilter);
    }

    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }

    // 添加商品表单
    const addProductForm = document.getElementById('addProductForm');
    if (addProductForm) {
        addProductForm.addEventListener('submit', handleAddProduct);
    }

    // 编辑商品表单
    const editProductForm = document.getElementById('editProductForm');
    if (editProductForm) {
        editProductForm.addEventListener('submit', handleEditProduct);
    }
}

// 加载商品列表
async function loadProducts() {
    try {
        showLoading(true);
        
        const params = {
            page: currentPage,
            per_page: itemsPerPage,
            search: currentSearch,
            platform: currentPlatform,
            status: currentStatus
        };
        
        const response = await apiService.getProducts(params);
        
        if (response.success) {
            renderProducts(response.data.data);
            updatePagination(response.data.current_page, response.data.per_page, response.data.total);
        } else {
            throw new Error(response.message || '获取商品列表失败');
        }
        
    } catch (error) {
        console.error('加载商品列表失败:', error);
        showAlert('获取商品列表失败，显示模拟数据', 'warning');
        
        // 使用模拟数据作为后备
        const mockProducts = [
            {
                id: 1,
                name: 'iPhone 15 Pro Max',
                platform: '淘宝',
                current_price: 9999.00,
                target_price: 9500.00,
                status: 'active',
                last_updated: '2024-01-15 10:30:00',
                price_change: -2.5
            },
            {
                id: 2,
                name: '华为Mate60 Pro',
                platform: '京东',
                current_price: 6999.00,
                target_price: 6500.00,
                status: 'active',
                last_updated: '2024-01-15 09:45:00',
                price_change: 1.2
            },
            {
                id: 3,
                name: '小米14 Ultra',
                platform: '天猫',
                current_price: 5999.00,
                target_price: 5800.00,
                status: 'paused',
                last_updated: '2024-01-15 08:20:00',
                price_change: 0
            }
        ];
        
        renderProducts(mockProducts);
        updatePagination(1, 10, 100);
        
    } finally {
        showLoading(false);
    }
}

// 渲染商品列表
function renderProducts(products) {
    const tbody = document.getElementById('productsTableBody');
    if (!tbody) return;

    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <div>暂无商品数据</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = products.map(product => `
        <tr>
            <td>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${product.id}">
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${product.image || '/images/default-product.png'}" 
                         alt="${product.name}" 
                         class="product-image me-2"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                    <div>
                        <div class="fw-bold">${product.name}</div>
                        <small class="text-muted">${product.sku || 'N/A'}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-${getPlatformColor(product.platform)}">${product.platform}</span>
            </td>
            <td>
                <div class="price-info">
                    <div class="fw-bold">¥${product.current_price.toFixed(2)}</div>
                    ${product.price_change !== 0 ? `
                        <small class="text-${product.price_change > 0 ? 'danger' : 'success'}">
                            ${product.price_change > 0 ? '+' : ''}${product.price_change}%
                        </small>
                    ` : ''}
                </div>
            </td>
            <td>¥${product.target_price.toFixed(2)}</td>
            <td>
                <span class="badge bg-${getStatusColor(product.status)}">${getStatusText(product.status)}</span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(product.last_updated)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewProduct(${product.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="editProduct(${product.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteProduct(${product.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 加载平台列表
async function loadPlatforms() {
    try {
        const response = await apiService.get('/api/platforms');
        if (response.success) {
            updatePlatformFilter(response.data);
        }
    } catch (error) {
        console.error('加载平台列表失败:', error);
        // 使用默认平台列表
        updatePlatformFilter(['淘宝', '京东', '天猫', '拼多多']);
    }
}

// 更新平台筛选器
function updatePlatformFilter(platforms) {
    const select = document.getElementById('platformFilter');
    if (!select) return;

    const currentValue = select.value;
    select.innerHTML = '<option value="">全部平台</option>';
    
    platforms.forEach(platform => {
        const option = document.createElement('option');
        option.value = platform;
        option.textContent = platform;
        if (platform === currentValue) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// 事件处理函数
function handleSearch(event) {
    currentSearch = event.target.value;
    currentPage = 1;
    loadProducts();
}

function handlePlatformFilter(event) {
    currentPlatform = event.target.value;
    currentPage = 1;
    loadProducts();
}

function handleStatusFilter(event) {
    currentStatus = event.target.value;
    currentPage = 1;
    loadProducts();
}

// 添加商品
async function handleAddProduct(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        
        const response = await apiService.createProduct(data);
        
        if (response.success) {
            showAlert('商品添加成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            modal.hide();
            
            // 重置表单
            event.target.reset();
            
            // 重新加载列表
            loadProducts();
        } else {
            throw new Error(response.message || '添加商品失败');
        }
        
    } catch (error) {
        console.error('添加商品失败:', error);
        showAlert('添加商品失败：' + error.message, 'danger');
    }
}

// 编辑商品
async function editProduct(id) {
    try {
        const response = await apiService.getProduct(id);
        
        if (response.success) {
            const product = response.data;
            
            // 填充编辑表单
            document.getElementById('editProductId').value = product.id;
            document.getElementById('editProductName').value = product.name;
            document.getElementById('editProductUrl').value = product.url;
            document.getElementById('editPlatform').value = product.platform;
            document.getElementById('editTargetPrice').value = product.target_price;
            document.getElementById('editMonitorFrequency').value = product.monitor_frequency;
            
            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
            modal.show();
        } else {
            throw new Error(response.message || '获取商品信息失败');
        }
        
    } catch (error) {
        console.error('获取商品信息失败:', error);
        showAlert('获取商品信息失败：' + error.message, 'danger');
    }
}

// 处理编辑商品提交
async function handleEditProduct(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        const productId = data.id;
        
        delete data.id; // 移除ID字段
        
        const response = await apiService.updateProduct(productId, data);
        
        if (response.success) {
            showAlert('商品更新成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
            modal.hide();
            
            // 重新加载列表
            loadProducts();
        } else {
            throw new Error(response.message || '更新商品失败');
        }
        
    } catch (error) {
        console.error('更新商品失败:', error);
        showAlert('更新商品失败：' + error.message, 'danger');
    }
}

// 删除商品
async function deleteProduct(id) {
    if (!confirm('确定要删除这个商品监控吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await apiService.deleteProduct(id);
        
        if (response.success) {
            showAlert('商品删除成功！', 'success');
            loadProducts();
        } else {
            throw new Error(response.message || '删除商品失败');
        }
        
    } catch (error) {
        console.error('删除商品失败:', error);
        showAlert('删除商品失败：' + error.message, 'danger');
    }
}

// 查看商品详情
function viewProduct(id) {
    window.location.href = `/products/${id}`;
}

// 更新分页
function updatePagination(currentPage, perPage, total) {
    const totalPages = Math.ceil(total / perPage);
    const paginationContainer = document.getElementById('pagination');
    
    if (!paginationContainer || totalPages <= 1) {
        if (paginationContainer) paginationContainer.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<nav><ul class="pagination justify-content-center">';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    paginationHTML += '</ul></nav>';
    paginationContainer.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadProducts();
}

// 工具函数
function getPlatformColor(platform) {
    const colors = {
        '淘宝': 'warning',
        '京东': 'danger',
        '天猫': 'primary',
        '拼多多': 'success'
    };
    return colors[platform] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'paused': 'warning',
        'stopped': 'secondary',
        'error': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '运行中',
        'paused': '已暂停',
        'stopped': '已停止',
        'error': '错误'
    };
    return texts[status] || status;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(message, type = 'info') {
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function showLoading(show) {
    const existingSpinner = document.querySelector('.products-loading');
    
    if (show && !existingSpinner) {
        const spinner = document.createElement('div');
        spinner.className = 'products-loading position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        spinner.style.backgroundColor = 'rgba(255,255,255,0.8)';
        spinner.style.zIndex = '9998';
        spinner.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="text-muted">正在加载商品数据...</div>
            </div>
        `;
        document.body.appendChild(spinner);
    } else if (!show && existingSpinner) {
        existingSpinner.remove();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->comment('配置键名');
            $table->string('category')->default('general')->comment('配置分类');
            $table->text('value')->nullable()->comment('配置值');
            $table->string('type')->default('string')->comment('数据类型: string, integer, boolean, json, file');
            $table->string('label')->comment('显示标签');
            $table->text('description')->nullable()->comment('配置描述');
            $table->json('validation_rules')->nullable()->comment('验证规则');
            $table->json('options')->nullable()->comment('选项列表(用于select类型)');
            $table->boolean('is_encrypted')->default(false)->comment('是否加密存储');
            $table->boolean('is_public')->default(false)->comment('是否公开(前端可访问)');
            $table->boolean('requires_restart')->default(false)->comment('是否需要重启应用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();

            // 索引
            $table->index(['category', 'sort_order']);
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_configurations');
    }
};

<?php

namespace App\Services;

use App\Models\AlertRule;
use App\Models\PriceHistory;
use App\Models\ProductSku;

class AlertSeverityEvaluator
{
    /**
     * 评估警报的严重程度
     */
    public function evaluateSeverity(AlertRule $alertRule, array $alertData): string
    {
        $severity = 'general'; // 默认严重程度

        // 根据警报规则类型进行评估
        switch ($alertRule->alert_type) {
            case 'price_change':
                $severity = $this->evaluatePriceChangeSeverity($alertRule, $alertData);
                break;
            
            case 'stock_change':
                $severity = $this->evaluateStockChangeSeverity($alertRule, $alertData);
                break;
            
            case 'competitor_analysis':
                $severity = $this->evaluateCompetitorSeverity($alertRule, $alertData);
                break;
            
            case 'system_error':
                $severity = $this->evaluateSystemErrorSeverity($alertRule, $alertData);
                break;
            
            case 'data_quality':
                $severity = $this->evaluateDataQualitySeverity($alertRule, $alertData);
                break;
            
            default:
                $severity = $this->evaluateGenericSeverity($alertRule, $alertData);
        }

        return $severity;
    }

    /**
     * 评估价格变化的严重程度
     */
    private function evaluatePriceChangeSeverity(AlertRule $alertRule, array $alertData): string
    {
        $priceChange = $alertData['price_change_percentage'] ?? 0;
        $currentPrice = $alertData['current_price'] ?? 0;
        $previousPrice = $alertData['previous_price'] ?? 0;

        // 紧急情况：价格变化超过30%或价格异常（比如变为0或负数）
        if (abs($priceChange) >= 30 || $currentPrice <= 0) {
            return 'emergency';
        }

        // 重要情况：价格变化超过15%
        if (abs($priceChange) >= 15) {
            return 'important';
        }

        // 重要情况：高价商品的小幅变化也可能很重要
        if ($currentPrice > 1000 && abs($priceChange) >= 5) {
            return 'important';
        }

        // 检查是否为连续的价格变化趋势
        if ($this->isConsecutivePriceChange($alertData)) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 评估库存变化的严重程度
     */
    private function evaluateStockChangeSeverity(AlertRule $alertRule, array $alertData): string
    {
        $stockLevel = $alertData['current_stock'] ?? 0;
        $stockChange = $alertData['stock_change'] ?? 0;
        $isPopularProduct = $alertData['is_popular_product'] ?? false;

        // 紧急情况：库存为0或负数
        if ($stockLevel <= 0) {
            return 'emergency';
        }

        // 重要情况：库存低于10且是热门产品
        if ($stockLevel < 10 && $isPopularProduct) {
            return 'important';
        }

        // 重要情况：库存下降超过50%
        if ($stockChange < 0 && abs($stockChange) > ($stockLevel * 0.5)) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 评估竞争对手分析的严重程度
     */
    private function evaluateCompetitorSeverity(AlertRule $alertRule, array $alertData): string
    {
        $competitorPriceDiff = $alertData['competitor_price_difference'] ?? 0;
        $marketShareImpact = $alertData['market_share_impact'] ?? 0;

        // 紧急情况：竞争对手价格低于我们50%以上
        if ($competitorPriceDiff < -50) {
            return 'emergency';
        }

        // 重要情况：竞争对手价格优势明显（20%以上）
        if ($competitorPriceDiff < -20) {
            return 'important';
        }

        // 重要情况：市场份额受到明显影响
        if ($marketShareImpact < -10) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 评估系统错误的严重程度
     */
    private function evaluateSystemErrorSeverity(AlertRule $alertRule, array $alertData): string
    {
        $errorType = $alertData['error_type'] ?? '';
        $affectedServices = $alertData['affected_services'] ?? [];
        $errorCount = $alertData['error_count'] ?? 1;

        // 紧急情况：关键服务错误或大量错误
        $criticalErrors = ['database_connection', 'payment_failure', 'security_breach'];
        if (in_array($errorType, $criticalErrors) || $errorCount > 100) {
            return 'emergency';
        }

        // 重要情况：多个服务受影响或中等数量错误
        if (count($affectedServices) > 2 || $errorCount > 10) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 评估数据质量的严重程度
     */
    private function evaluateDataQualitySeverity(AlertRule $alertRule, array $alertData): string
    {
        $dataAccuracy = $alertData['data_accuracy'] ?? 100;
        $missingDataPercentage = $alertData['missing_data_percentage'] ?? 0;
        $dataFreshness = $alertData['data_freshness_hours'] ?? 0;

        // 紧急情况：数据准确性很低或大量数据缺失
        if ($dataAccuracy < 70 || $missingDataPercentage > 50) {
            return 'emergency';
        }

        // 重要情况：数据质量有明显问题
        if ($dataAccuracy < 90 || $missingDataPercentage > 20 || $dataFreshness > 24) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 评估通用警报的严重程度
     */
    private function evaluateGenericSeverity(AlertRule $alertRule, array $alertData): string
    {
        // 根据配置的阈值进行评估
        $config = $alertRule->config ?? [];
        
        if (isset($config['severity_thresholds'])) {
            $thresholds = $config['severity_thresholds'];
            $value = $alertData['trigger_value'] ?? 0;

            if (isset($thresholds['emergency']) && $value >= $thresholds['emergency']) {
                return 'emergency';
            }

            if (isset($thresholds['important']) && $value >= $thresholds['important']) {
                return 'important';
            }
        }

        // 基于时间因素的评估
        if ($this->isBusinessHours() && isset($alertData['business_critical']) && $alertData['business_critical']) {
            return 'important';
        }

        return 'general';
    }

    /**
     * 检查是否为连续的价格变化趋势
     */
    private function isConsecutivePriceChange(array $alertData): bool
    {
        $skuId = $alertData['sku_id'] ?? null;
        if (!$skuId) {
            return false;
        }

        // 查看过去7天的价格变化
        $recentChanges = PriceHistory::where('sku_id', $skuId)
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        if ($recentChanges->count() < 3) {
            return false;
        }

        // 检查是否有连续的变化趋势
        $consecutiveChanges = 0;
        $lastDirection = null;

        for ($i = 0; $i < $recentChanges->count() - 1; $i++) {
            $current = $recentChanges[$i];
            $next = $recentChanges[$i + 1];

            $direction = $current->price > $next->price ? 'up' : 'down';

            if ($lastDirection === null || $direction === $lastDirection) {
                $consecutiveChanges++;
                $lastDirection = $direction;
            } else {
                $consecutiveChanges = 1;
                $lastDirection = $direction;
            }
        }

        // 如果连续3次以上同方向变化，认为是趋势
        return $consecutiveChanges >= 3;
    }

    /**
     * 检查当前是否为工作时间
     */
    private function isBusinessHours(): bool
    {
        $now = now();
        $hour = $now->hour;
        $isWeekday = $now->isWeekday();

        // 工作日的9:00-18:00认为是工作时间
        return $isWeekday && $hour >= 9 && $hour < 18;
    }

    /**
     * 根据历史数据调整严重程度
     */
    public function adjustSeverityBasedOnHistory(string $baseSeverity, array $alertData): string
    {
        $userId = $alertData['user_id'] ?? null;
        $ruleId = $alertData['rule_id'] ?? null;

        if (!$userId || !$ruleId) {
            return $baseSeverity;
        }

        // 检查最近的警报频率
        $recentAlertsCount = \App\Models\AlertLog::where('user_id', $userId)
            ->where('rule_id', $ruleId)
            ->where('created_at', '>=', now()->subHours(24))
            ->count();

        // 如果24小时内同类警报过多，降低严重程度避免骚扰
        if ($recentAlertsCount > 10) {
            if ($baseSeverity === 'emergency') {
                return 'important';
            } elseif ($baseSeverity === 'important') {
                return 'general';
            }
        }

        // 如果是第一次出现的新类型警报，可能需要提高关注度
        $historicalCount = \App\Models\AlertLog::where('user_id', $userId)
            ->where('rule_id', $ruleId)
            ->count();

        if ($historicalCount === 0 && $baseSeverity === 'general') {
            return 'important';
        }

        return $baseSeverity;
    }
} 
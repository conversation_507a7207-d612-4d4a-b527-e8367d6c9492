<?php

namespace App\Services\DataCollection;

use App\Models\ApiConfiguration;
use App\Services\DataCollection\Contracts\PlatformAdapterInterface;
use App\Services\DataCollection\HttpClientService;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 抽象平台适配器基类
 * 提供通用的HTTP客户端和错误处理功能
 */
abstract class AbstractPlatformAdapter implements PlatformAdapterInterface
{
    protected HttpClientService $httpClient;
    protected string $baseUrl;
    protected string $token;
    protected array $config;
    protected ?ApiConfiguration $apiConfiguration = null;
    protected array $statistics = [
        'requests_made' => 0,
        'successful_requests' => 0,
        'failed_requests' => 0,
        'last_request_time' => null,
        'average_response_time' => 0,
    ];

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->baseUrl = $config['base_url'] ?? '';
        $this->token = $config['token'] ?? '';

        $this->initializeHttpClient();
    }

    /**
     * 初始化HTTP客户端
     */
    protected function initializeHttpClient(): void
    {
        $this->httpClient = new HttpClientService($this->config);
    }

    /**
     * 发送HTTP请求
     *
     * @param string $method HTTP方法
     * @param string $url 完整URL
     * @param array $options 请求选项
     * @return array 响应数据
     * @throws Exception
     */
    protected function makeRequest(string $method, string $url, array $options = []): array
    {
        $startTime = microtime(true);

        try {
            Log::info("API请求开始", [
                'platform' => $this->getPlatformName(),
                'method' => $method,
                'url' => $url,
            ]);

            $response = $this->httpClient->request($method, $url, $options);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("API请求成功", [
                'platform' => $this->getPlatformName(),
                'method' => $method,
                'url' => $url,
                'response_code' => $response['code'] ?? 'unknown',
                'response_time' => $responseTime,
            ]);

            // 更新统计信息
            $this->updateStatistics(true, $responseTime);

            // 更新API配置的请求计数
            if ($this->apiConfiguration) {
                $this->apiConfiguration->incrementRequestCount();
            }

            return $response;

        } catch (Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error("API请求失败", [
                'platform' => $this->getPlatformName(),
                'method' => $method,
                'url' => $url,
                'error' => $e->getMessage(),
                'response_time' => $responseTime,
            ]);

            // 更新统计信息
            $this->updateStatistics(false, $responseTime);

            // 更新API配置的错误计数
            if ($this->apiConfiguration) {
                $this->apiConfiguration->incrementErrorCount();
            }

            throw $e;
        }
    }

    /**
     * 构建请求URL（带token）
     *
     * @param string $endpoint 端点
     * @param array $params 查询参数
     * @return string 完整URL
     */
    protected function buildUrl(string $endpoint, array $params = []): string
    {
        // 添加token到查询参数
        if ($this->token) {
            $params['token'] = $this->token;
        }
        
        $queryString = http_build_query($params);
        $url = $this->baseUrl . $endpoint;
        
        return $url . ($queryString ? '?' . $queryString : '');
    }

    /**
     * 验证API响应
     *
     * @param array $response API响应
     * @return bool 响应是否有效
     */
    protected function validateResponse(array $response): bool
    {
        return isset($response['code']) && 
               ($response['code'] === 200 || $response['code'] === '0' || $response['code'] === 0);
    }

    /**
     * 处理API错误
     *
     * @param array $response API响应
     * @throws Exception
     */
    protected function handleApiError(array $response): void
    {
        $code = $response['code'] ?? 'unknown';
        $message = $response['message'] ?? $response['msg'] ?? 'API调用失败';
        
        Log::error("API返回错误", [
            'platform' => $this->getPlatformName(),
            'code' => $code,
            'message' => $message,
            'response' => $response,
        ]);

        throw new Exception("API错误: {$message} (代码: {$code})");
    }

    /**
     * 获取平台名称
     *
     * @return string 平台名称
     */
    abstract public function getPlatformName(): string;

    /**
     * 测试连接
     *
     * @return bool 连接是否成功
     */
    public function testConnection(): bool
    {
        try {
            // 使用一个简单的测试端点
            $testUrl = $this->baseUrl . '/test';
            $result = $this->httpClient->testConnection($testUrl);
            
            return $result['status'] === 'success';
        } catch (Exception $e) {
            Log::warning("连接测试失败", [
                'platform' => $this->getPlatformName(),
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * 获取HTTP客户端性能指标
     *
     * @return array 性能指标
     */
    public function getMetrics(): array
    {
        return $this->httpClient->getMetrics();
    }

    /**
     * 获取平台支持的功能列表
     *
     * @return array 支持的功能列表
     */
    public function getSupportedFeatures(): array
    {
        return [
            'item_detail' => true,
            'similar_products' => true,
            'shop_items' => true,
            'search_items' => true,
            'health_check' => true,
        ];
    }

    /**
     * 获取平台的API版本信息
     *
     * @return string API版本
     */
    public function getApiVersion(): string
    {
        return $this->config['api_version'] ?? '1.0';
    }

    /**
     * 设置API配置
     *
     * @param ApiConfiguration $config API配置对象
     * @return void
     */
    public function setApiConfiguration(ApiConfiguration $config): void
    {
        $this->apiConfiguration = $config;

        // 更新内部配置
        $this->baseUrl = $config->base_url;
        $authData = $config->auth_credentials ?? [];
        $this->token = $authData['token'] ?? $authData['api_key'] ?? '';

        // 重新初始化HTTP客户端
        $this->config = array_merge($this->config, [
            'base_url' => $config->base_url,
            'token' => $this->token,
            'timeout' => $config->metadata['timeout'] ?? 30,
            'retry_attempts' => $config->metadata['retry_attempts'] ?? 3,
        ]);

        $this->initializeHttpClient();
    }

    /**
     * 获取当前API配置
     *
     * @return ApiConfiguration|null 当前API配置
     */
    public function getApiConfiguration(): ?ApiConfiguration
    {
        return $this->apiConfiguration;
    }

    /**
     * 执行健康检查
     *
     * @return array 健康检查结果
     */
    public function performHealthCheck(): array
    {
        $startTime = microtime(true);

        try {
            $isHealthy = $this->testConnection();
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            $result = [
                'status' => $isHealthy ? 'healthy' : 'unhealthy',
                'response_time' => $responseTime,
                'timestamp' => now(),
                'platform' => $this->getPlatformName(),
                'api_version' => $this->getApiVersion(),
            ];

            if ($this->apiConfiguration) {
                $this->apiConfiguration->updateHealthStatus($result['status'], $responseTime);
            }

            return $result;

        } catch (Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            $result = [
                'status' => 'unhealthy',
                'response_time' => $responseTime,
                'timestamp' => now(),
                'platform' => $this->getPlatformName(),
                'error' => $e->getMessage(),
            ];

            if ($this->apiConfiguration) {
                $this->apiConfiguration->updateHealthStatus('unhealthy', $responseTime);
            }

            return $result;
        }
    }

    /**
     * 获取适配器的统计信息
     *
     * @return array 统计信息
     */
    public function getStatistics(): array
    {
        return array_merge($this->statistics, [
            'platform' => $this->getPlatformName(),
            'api_version' => $this->getApiVersion(),
            'supported_features' => $this->getSupportedFeatures(),
            'configuration_id' => $this->apiConfiguration?->id,
        ]);
    }

    /**
     * 更新统计信息
     *
     * @param bool $success 请求是否成功
     * @param float $responseTime 响应时间（毫秒）
     * @return void
     */
    protected function updateStatistics(bool $success, float $responseTime): void
    {
        $this->statistics['requests_made']++;
        $this->statistics['last_request_time'] = now();

        if ($success) {
            $this->statistics['successful_requests']++;
        } else {
            $this->statistics['failed_requests']++;
        }

        // 计算平均响应时间
        $totalRequests = $this->statistics['requests_made'];
        $currentAvg = $this->statistics['average_response_time'];
        $this->statistics['average_response_time'] =
            (($currentAvg * ($totalRequests - 1)) + $responseTime) / $totalRequests;
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_skus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade')->comment('关联产品ID');
            $table->string('sku_code')->nullable()->comment('SKU编码');
            $table->decimal('price', 10, 2)->comment('当前价格');
            $table->decimal('sub_price', 10, 2)->nullable()->comment('副价格（如原价、活动价等）');
            $table->string('sub_price_title')->nullable()->comment('副价格标题说明');
            $table->unsignedInteger('quantity')->default(0)->comment('库存数量');
            $table->boolean('is_sku')->default(true)->comment('是否为SKU（区分单品和多规格）');
            $table->json('props')->nullable()->comment('SKU属性（颜色、尺寸等）');
            $table->json('delivery')->nullable()->comment('配送信息');
            $table->string('sku_image')->nullable()->comment('SKU专属图片');
            $table->decimal('original_price', 10, 2)->nullable()->comment('原价');
            $table->decimal('discount_price', 10, 2)->nullable()->comment('折扣价');
            $table->string('promotion_type')->nullable()->comment('促销类型');
            $table->text('promotion_info')->nullable()->comment('促销信息');
            $table->unsignedInteger('sales_count')->default(0)->comment('销量');
            $table->enum('status', ['active', 'inactive', 'out_of_stock'])->default('active')->comment('SKU状态');
            $table->timestamps();
            
            // 索引
            $table->index(['product_id', 'status']);
            $table->index(['price', 'status']);
            $table->index(['quantity', 'status']);
            $table->index('sku_code');
            $table->index('is_sku');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_skus');
    }
};

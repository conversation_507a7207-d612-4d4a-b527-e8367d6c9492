<?php

namespace App\Services;

use App\Models\ProductSku;
use App\Models\PriceHistory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Exception;

/**
 * 官方指导价管理服务
 * 
 * 提供官方指导价的设置、更新、验证和管理功能
 */
class OfficialGuidePriceService
{
    /**
     * 数据源常量
     */
    const SOURCE_MANUAL = 'manual';     // 手动输入
    const SOURCE_API = 'api';           // API接口
    const SOURCE_IMPORT = 'import';     // 批量导入
    const SOURCE_AUTO = 'auto';         // 自动计算

    /**
     * 为单个SKU设置官方指导价
     * 
     * @param int $skuId SKU ID
     * @param float $guidePrice 官方指导价
     * @param string $source 数据源
     * @return array 设置结果
     */
    public function setGuidePriceForSku(int $skuId, float $guidePrice, string $source = self::SOURCE_MANUAL): array
    {
        try {
            // 验证输入
            $validation = $this->validateGuidePrice($guidePrice, $source);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            $sku = ProductSku::find($skuId);
            if (!$sku) {
                return [
                    'success' => false,
                    'error' => "SKU not found: {$skuId}"
                ];
            }

            DB::beginTransaction();

            // 记录旧值用于日志
            $oldPrice = $sku->official_guide_price;
            $oldSource = $sku->official_guide_price_source;

            // 更新SKU的官方指导价
            $sku->official_guide_price = $guidePrice;
            $sku->official_guide_price_set_at = Carbon::now();
            $sku->official_guide_price_source = $source;
            $sku->save();

            // 触发相关价格历史记录的偏差率重新计算
            $this->triggerDeviationRecalculation($skuId);

            DB::commit();

            Log::info('官方指导价设置成功', [
                'sku_id' => $skuId,
                'old_price' => $oldPrice,
                'new_price' => $guidePrice,
                'source' => $source,
                'old_source' => $oldSource
            ]);

            return [
                'success' => true,
                'sku_id' => $skuId,
                'old_price' => $oldPrice,
                'new_price' => $guidePrice,
                'source' => $source,
                'set_at' => $sku->official_guide_price_set_at
            ];

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('官方指导价设置失败', [
                'sku_id' => $skuId,
                'price' => $guidePrice,
                'source' => $source,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '设置失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量设置官方指导价
     * 
     * @param array $priceData 价格数据数组 [['sku_id' => 1, 'price' => 100.00], ...]
     * @param string $source 数据源
     * @return array 处理结果
     */
    public function setBatchGuidePrices(array $priceData, string $source = self::SOURCE_IMPORT): array
    {
        $results = [
            'total' => count($priceData),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'processed_items' => []
        ];

        foreach ($priceData as $index => $item) {
            try {
                // 验证单项数据
                if (!isset($item['sku_id']) || !isset($item['price'])) {
                    $results['failed']++;
                    $results['errors'][] = "Item {$index}: Missing sku_id or price";
                    continue;
                }

                $result = $this->setGuidePriceForSku($item['sku_id'], $item['price'], $source);
                
                if ($result['success']) {
                    $results['success']++;
                    $results['processed_items'][] = [
                        'sku_id' => $item['sku_id'],
                        'price' => $item['price'],
                        'status' => 'success'
                    ];
                } else {
                    $results['failed']++;
                    $results['errors'][] = "SKU {$item['sku_id']}: {$result['error']}";
                    $results['processed_items'][] = [
                        'sku_id' => $item['sku_id'],
                        'price' => $item['price'],
                        'status' => 'failed',
                        'error' => $result['error']
                    ];
                }

            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Item {$index}: " . $e->getMessage();
            }
        }

        Log::info('批量设置官方指导价完成', [
            'total' => $results['total'],
            'success' => $results['success'],
            'failed' => $results['failed'],
            'source' => $source
        ]);

        // 只有当所有项目都成功时，顶层success才为true
        $results['success'] = $results['failed'] === 0;

        return $results;
    }

    /**
     * 从CSV文件导入官方指导价
     * 
     * @param string $filePath CSV文件路径
     * @param array $mapping 列映射 ['sku_id' => 0, 'price' => 1]
     * @return array 导入结果
     */
    public function importFromCsv(string $filePath, array $mapping = ['sku_id' => 0, 'price' => 1]): array
    {
        try {
            if (!file_exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'File not found: ' . $filePath
                ];
            }

            $priceData = [];
            $row = 0;
            
            if (($handle = fopen($filePath, "r")) !== FALSE) {
                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $row++;
                    
                    // 跳过标题行
                    if ($row === 1) {
                        continue;
                    }

                    // 验证列数
                    if (count($data) <= max($mapping)) {
                        Log::warning("CSV行 {$row}: 列数不足", ['data' => $data]);
                        continue;
                    }

                    $skuId = isset($mapping['sku_id']) ? trim($data[$mapping['sku_id']]) : null;
                    $price = isset($mapping['price']) ? trim($data[$mapping['price']]) : null;

                    if (!$skuId || !is_numeric($price)) {
                        Log::warning("CSV行 {$row}: 数据无效", [
                            'sku_id' => $skuId,
                            'price' => $price
                        ]);
                        continue;
                    }

                    $priceData[] = [
                        'sku_id' => (int) $skuId,
                        'price' => (float) $price
                    ];
                }
                fclose($handle);
            }

            if (empty($priceData)) {
                return [
                    'success' => false,
                    'error' => 'No valid price data found in CSV file'
                ];
            }

            // 批量处理
            return $this->setBatchGuidePrices($priceData, self::SOURCE_IMPORT);

        } catch (Exception $e) {
            Log::error('CSV导入失败', [
                'file' => $filePath,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Import failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 自动基于历史价格计算官方指导价
     * 
     * @param int $skuId SKU ID
     * @param int $days 分析天数
     * @param string $method 计算方法 (max|avg|percentile)
     * @param float $percentile 百分位数（仅当method为percentile时使用）
     * @return array 计算结果
     */
    public function calculateAutoGuidePrice(int $skuId, int $days = 90, string $method = 'max', float $percentile = 95.0): array
    {
        try {
            $sku = ProductSku::find($skuId);
            if (!$sku) {
                return [
                    'success' => false,
                    'error' => "SKU not found: {$skuId}"
                ];
            }

            // 获取历史价格数据
            $priceHistory = PriceHistory::forSku($skuId)
                                      ->recent($days)
                                      ->whereNotNull('price')
                                      ->where('price', '>', 0)
                                      ->pluck('price')
                                      ->toArray();

            if (empty($priceHistory)) {
                return [
                    'success' => false,
                    'error' => "No price history found for SKU {$skuId} in the last {$days} days"
                ];
            }

            // 根据方法计算指导价
            $guidePrice = null;
            switch ($method) {
                case 'max':
                    $guidePrice = max($priceHistory);
                    break;
                    
                case 'avg':
                    $guidePrice = array_sum($priceHistory) / count($priceHistory);
                    break;
                    
                case 'percentile':
                    sort($priceHistory);
                    $index = ceil(($percentile / 100) * count($priceHistory)) - 1;
                    $guidePrice = $priceHistory[$index];
                    break;
                    
                default:
                    return [
                        'success' => false,
                        'error' => "Invalid calculation method: {$method}"
                    ];
            }

            $guidePrice = round($guidePrice, 2);

            // 设置自动计算的指导价
            $result = $this->setGuidePriceForSku($skuId, $guidePrice, self::SOURCE_AUTO);

            if ($result['success']) {
                $result['calculation_details'] = [
                    'method' => $method,
                    'days_analyzed' => $days,
                    'price_points' => count($priceHistory),
                    'percentile' => $method === 'percentile' ? $percentile : null
                ];
            }

            return $result;

        } catch (Exception $e) {
            Log::error('自动计算官方指导价失败', [
                'sku_id' => $skuId,
                'method' => $method,
                'days' => $days,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Auto calculation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取需要设置官方指导价的SKU列表
     * 
     * @param array $filters 过滤条件
     * @return array SKU列表
     */
    public function getSkusNeedingGuidePrice(array $filters = []): array
    {
        $query = ProductSku::active()
                          ->whereNull('official_guide_price');

        // 应用过滤条件
        if (!empty($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (!empty($filters['price_min'])) {
            $query->where('price', '>=', $filters['price_min']);
        }

        if (!empty($filters['price_max'])) {
            $query->where('price', '<=', $filters['price_max']);
        }

        $skus = $query->with('product:id,title')
                     ->get(['id', 'product_id', 'sku_code', 'price', 'sub_price']);

        return $skus->map(function($sku) {
            return [
                'sku_id' => $sku->id,
                'sku_code' => $sku->sku_code,
                'product_title' => $sku->product->title,
                'current_price' => $sku->price,
                'sub_price' => $sku->sub_price,
                'suggested_guide_price' => $sku->price * 1.2 // 建议价格（比当前价格高20%）
            ];
        })->toArray();
    }

    /**
     * 验证官方指导价
     * 
     * @param float $price 价格
     * @param string $source 数据源
     * @return array 验证结果
     */
    private function validateGuidePrice(float $price, string $source): array
    {
        $validator = Validator::make([
            'price' => $price,
            'source' => $source
        ], [
            'price' => 'required|numeric|min:0.01|max:999999.99',
            'source' => 'required|in:' . implode(',', [
                self::SOURCE_MANUAL,
                self::SOURCE_API,
                self::SOURCE_IMPORT,
                self::SOURCE_AUTO
            ])
        ]);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'error' => $validator->errors()->first()
            ];
        }

        return [
            'valid' => true
        ];
    }

    /**
     * 触发偏差率重新计算
     * 
     * @param int $skuId SKU ID
     */
    private function triggerDeviationRecalculation(int $skuId): void
    {
        // 更新相关价格历史记录的计算状态为待处理
        PriceHistory::forSku($skuId)
                   ->where('calculation_status', 'calculated')
                   ->update(['calculation_status' => 'pending']);

        Log::info('触发偏差率重新计算', ['sku_id' => $skuId]);
    }

    /**
     * 获取官方指导价统计信息
     * 
     * @return array 统计信息
     */
    public function getGuidePriceStats(): array
    {
        $totalSkus = ProductSku::active()->count();
        $withGuidePrice = ProductSku::active()->withOfficialGuidePrice()->count();
        $bySource = ProductSku::active()
                             ->withOfficialGuidePrice()
                             ->groupBy('official_guide_price_source')
                             ->selectRaw('official_guide_price_source as source, count(*) as count')
                             ->get()
                             ->keyBy('source')
                             ->toArray();

        $staleCount = ProductSku::active()
                               ->withOfficialGuidePrice()
                               ->staleGuidePrice()
                               ->count();

        $totalActiveSkus = ProductSku::where('status', 'active')->count();

        return [
            'total_skus' => $totalSkus,
            'total_active_skus' => $totalActiveSkus,
            'with_guide_price' => $withGuidePrice,
            'without_guide_price' => $totalSkus - $withGuidePrice,
            'coverage' => [
                'with_guide_price' => $withGuidePrice,
                'without_guide_price' => $totalSkus - $withGuidePrice,
                'percentage' => $totalSkus > 0 ? round(($withGuidePrice / $totalSkus) * 100, 2) : 0,
            ],
            'source_distribution' => $bySource,
            'stale_count' => $staleCount
        ];
    }

    /**
     * 清除指定SKU的官方指导价
     * 
     * @param array $skuIds SKU ID数组
     * @return array 清除结果
     */
    public function clearGuidePrices(array $skuIds): array
    {
        try {
            $cleared = ProductSku::whereIn('id', $skuIds)
                                ->update([
                                    'official_guide_price' => null,
                                    'official_guide_price_set_at' => null,
                                    'official_guide_price_source' => null
                                ]);

            Log::info('清除官方指导价', [
                'sku_ids' => $skuIds,
                'cleared_count' => $cleared
            ]);

            return [
                'success' => true,
                'cleared' => $cleared
            ];

        } catch (Exception $e) {
            Log::error('清除官方指导价失败', [
                'sku_ids' => $skuIds,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    // ============ 以下为公共辅助方法，用于单元测试 ============

    /**
     * 验证价格是否有效
     * 
     * @param float $price 价格
     * @return bool
     */
    public function isValidPrice(float $price): bool
    {
        return $price > 0 && $price <= 999999.99;
    }

    /**
     * 验证数据源是否有效
     * 
     * @param string|null $source 数据源
     * @return bool
     */
    public function isValidSource(?string $source): bool
    {
        if (empty($source)) {
            return false;
        }

        $validSources = [
            self::SOURCE_MANUAL,
            self::SOURCE_API,
            self::SOURCE_IMPORT,
            self::SOURCE_AUTO
        ];

        return in_array($source, $validSources);
    }

    /**
     * 计算百分位数
     * 
     * @param array $values 数值数组
     * @param float $percentile 百分位数 (0-100)
     * @return float
     */
    public function calculatePercentile(array $values, float $percentile): float
    {
        sort($values);
        $count = count($values);
        
        if ($count === 0) {
            return 0;
        }
        
        if ($count === 1) {
            return $values[0];
        }
        
        $index = ($percentile / 100) * ($count - 1);
        
        if ($index == intval($index)) {
            return $values[$index];
        }
        
        $lower = floor($index);
        $upper = ceil($index);
        $weight = $index - $lower;
        
        return $values[$lower] * (1 - $weight) + $values[$upper] * $weight;
    }

    /**
     * 计算平均值
     * 
     * @param array $values 数值数组
     * @return float
     */
    public function calculateAverage(array $values): float
    {
        if (empty($values)) {
            return 0;
        }
        
        return array_sum($values) / count($values);
    }

    /**
     * 计算最大值
     * 
     * @param array $values 数值数组
     * @return float
     */
    public function calculateMax(array $values): float
    {
        if (empty($values)) {
            return 0;
        }
        
        return max($values);
    }
} 
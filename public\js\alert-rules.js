/**
 * 预警规则管理JavaScript功能
 */

// 全局变量
let currentPage = 1;
let itemsPerPage = 10;
let currentSearch = '';
let currentStatus = '';
let conditionCounter = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAlertRules();
    bindEvents();
});

// 初始化预警规则功能
function initializeAlertRules() {
    loadAlertRules();
    setupConditionTemplates();
}

// 绑定事件
function bindEvents() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // 状态筛选
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }

    // 创建规则表单
    const createRuleForm = document.getElementById('createRuleForm');
    if (createRuleForm) {
        createRuleForm.addEventListener('submit', handleCreateRule);
    }

    // 编辑规则表单
    const editRuleForm = document.getElementById('editRuleForm');
    if (editRuleForm) {
        editRuleForm.addEventListener('submit', handleEditRule);
    }

    // 触发类型变化
    const triggerTypeSelect = document.getElementById('triggerType');
    if (triggerTypeSelect) {
        triggerTypeSelect.addEventListener('change', handleTriggerTypeChange);
    }
}

// 加载预警规则列表
async function loadAlertRules() {
    try {
        showLoading(true);
        
        const params = {
            page: currentPage,
            per_page: itemsPerPage,
            search: currentSearch,
            status: currentStatus
        };
        
        const response = await apiService.getAlertRules(params);
        
        if (response.success) {
            renderAlertRules(response.data.data);
            updatePagination(response.data.current_page, response.data.per_page, response.data.total);
        } else {
            throw new Error(response.message || '获取预警规则失败');
        }
        
    } catch (error) {
        console.error('加载预警规则失败:', error);
        showAlert('获取预警规则失败，显示模拟数据', 'warning');
        
        // 使用模拟数据作为后备
        const mockRules = [
            {
                id: 1,
                name: '价格下降预警',
                description: '当商品价格下降超过10%时触发预警',
                trigger_type: 'price_change',
                conditions: [
                    { field: 'price_change_percent', operator: '<=', value: -10 }
                ],
                actions: ['email', 'sms'],
                is_active: true,
                created_at: '2024-01-15 10:30:00'
            },
            {
                id: 2,
                name: '库存不足预警',
                description: '当商品库存低于100时触发预警',
                trigger_type: 'stock_change',
                conditions: [
                    { field: 'stock_quantity', operator: '<', value: 100 }
                ],
                actions: ['email'],
                is_active: true,
                created_at: '2024-01-14 15:20:00'
            }
        ];
        
        renderAlertRules(mockRules);
        updatePagination(1, 10, 50);
        
    } finally {
        showLoading(false);
    }
}

// 渲染预警规则列表
function renderAlertRules(rules) {
    const tbody = document.getElementById('rulesTableBody');
    if (!tbody) return;

    if (rules.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-bell fa-2x mb-2"></i>
                    <div>暂无预警规则</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = rules.map(rule => `
        <tr>
            <td>
                <div class="fw-bold">${rule.name}</div>
                <small class="text-muted">${rule.description}</small>
            </td>
            <td>
                <span class="badge bg-info">${getTriggerTypeText(rule.trigger_type)}</span>
            </td>
            <td>
                <div class="conditions-summary">
                    ${rule.conditions.map(condition => `
                        <small class="d-block">
                            ${getFieldText(condition.field)} ${getOperatorText(condition.operator)} ${condition.value}
                        </small>
                    `).join('')}
                </div>
            </td>
            <td>
                <div class="actions-summary">
                    ${rule.actions.map(action => `
                        <span class="badge bg-secondary me-1">${getActionText(action)}</span>
                    `).join('')}
                </div>
            </td>
            <td>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" ${rule.is_active ? 'checked' : ''} 
                           onchange="toggleRuleStatus(${rule.id}, this.checked)">
                </div>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="editRule(${rule.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="testRule(${rule.id})" title="测试">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteRule(${rule.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 处理创建规则
async function handleCreateRule(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const ruleData = {
            name: formData.get('name'),
            description: formData.get('description'),
            trigger_type: formData.get('trigger_type'),
            conditions: collectConditions(),
            actions: collectActions(),
            is_active: formData.get('is_active') === 'on'
        };
        
        // 验证数据
        if (!ruleData.name || !ruleData.trigger_type) {
            showAlert('请填写规则名称和触发类型', 'warning');
            return;
        }
        
        if (ruleData.conditions.length === 0) {
            showAlert('请至少添加一个触发条件', 'warning');
            return;
        }
        
        if (ruleData.actions.length === 0) {
            showAlert('请至少选择一个预警动作', 'warning');
            return;
        }
        
        const response = await apiService.createAlertRule(ruleData);
        
        if (response.success) {
            showAlert('预警规则创建成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('createRuleModal'));
            modal.hide();
            
            // 重置表单
            event.target.reset();
            resetConditions();
            resetActions();
            
            // 重新加载列表
            loadAlertRules();
        } else {
            throw new Error(response.message || '创建预警规则失败');
        }
        
    } catch (error) {
        console.error('创建预警规则失败:', error);
        showAlert('创建预警规则失败：' + error.message, 'danger');
    }
}

// 编辑规则
async function editRule(id) {
    try {
        const response = await apiService.getAlertRule(id);
        
        if (response.success) {
            const rule = response.data;
            
            // 填充编辑表单
            document.getElementById('editRuleId').value = rule.id;
            document.getElementById('editRuleName').value = rule.name;
            document.getElementById('editRuleDescription').value = rule.description;
            document.getElementById('editTriggerType').value = rule.trigger_type;
            document.getElementById('editIsActive').checked = rule.is_active;
            
            // 填充条件和动作
            populateEditConditions(rule.conditions);
            populateEditActions(rule.actions);
            
            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editRuleModal'));
            modal.show();
        } else {
            throw new Error(response.message || '获取规则信息失败');
        }
        
    } catch (error) {
        console.error('获取规则信息失败:', error);
        showAlert('获取规则信息失败：' + error.message, 'danger');
    }
}

// 处理编辑规则提交
async function handleEditRule(event) {
    event.preventDefault();
    
    try {
        const formData = new FormData(event.target);
        const ruleData = {
            name: formData.get('name'),
            description: formData.get('description'),
            trigger_type: formData.get('trigger_type'),
            conditions: collectEditConditions(),
            actions: collectEditActions(),
            is_active: formData.get('is_active') === 'on'
        };
        
        const ruleId = formData.get('id');
        
        const response = await apiService.updateAlertRule(ruleId, ruleData);
        
        if (response.success) {
            showAlert('预警规则更新成功！', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editRuleModal'));
            modal.hide();
            
            // 重新加载列表
            loadAlertRules();
        } else {
            throw new Error(response.message || '更新预警规则失败');
        }
        
    } catch (error) {
        console.error('更新预警规则失败:', error);
        showAlert('更新预警规则失败：' + error.message, 'danger');
    }
}

// 删除规则
async function deleteRule(id) {
    if (!confirm('确定要删除这个预警规则吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await apiService.deleteAlertRule(id);
        
        if (response.success) {
            showAlert('预警规则删除成功！', 'success');
            loadAlertRules();
        } else {
            throw new Error(response.message || '删除预警规则失败');
        }
        
    } catch (error) {
        console.error('删除预警规则失败:', error);
        showAlert('删除预警规则失败：' + error.message, 'danger');
    }
}

// 切换规则状态
async function toggleRuleStatus(id, isActive) {
    try {
        const response = await apiService.updateAlertRule(id, { is_active: isActive });
        
        if (response.success) {
            showAlert(`规则已${isActive ? '启用' : '禁用'}`, 'success');
        } else {
            throw new Error(response.message || '更新规则状态失败');
        }
        
    } catch (error) {
        console.error('更新规则状态失败:', error);
        showAlert('更新规则状态失败：' + error.message, 'danger');
        
        // 恢复开关状态
        const checkbox = document.querySelector(`input[onchange*="${id}"]`);
        if (checkbox) {
            checkbox.checked = !isActive;
        }
    }
}

// 测试规则
function testRule(id) {
    // 实现规则测试功能
    console.log('测试规则:', id);
    showAlert('规则测试功能开发中...', 'info');
}

// 工具函数
function getTriggerTypeText(type) {
    const types = {
        'price_change': '价格变化',
        'stock_change': '库存变化',
        'promotion_start': '促销开始',
        'promotion_end': '促销结束',
        'competitor_change': '竞品变化'
    };
    return types[type] || type;
}

function getFieldText(field) {
    const fields = {
        'price_change_percent': '价格变化率',
        'current_price': '当前价格',
        'stock_quantity': '库存数量',
        'competitor_price': '竞品价格'
    };
    return fields[field] || field;
}

function getOperatorText(operator) {
    const operators = {
        '>': '大于',
        '>=': '大于等于',
        '<': '小于',
        '<=': '小于等于',
        '=': '等于',
        '!=': '不等于'
    };
    return operators[operator] || operator;
}

function getActionText(action) {
    const actions = {
        'email': '邮件通知',
        'sms': '短信通知',
        'webhook': 'Webhook',
        'system': '系统通知'
    };
    return actions[action] || action;
}

// 条件管理函数
function addCondition() {
    // 实现添加条件功能
    console.log('添加条件');
}

function removeCondition(index) {
    // 实现移除条件功能
    console.log('移除条件:', index);
}

function collectConditions() {
    // 收集表单中的条件数据
    const conditions = [];
    // 实现条件收集逻辑
    return conditions;
}

function resetConditions() {
    // 重置条件表单
    console.log('重置条件');
}

function collectActions() {
    // 收集表单中的动作数据
    const actions = [];
    // 实现动作收集逻辑
    return actions;
}

function resetActions() {
    // 重置动作表单
    console.log('重置动作');
}

// 事件处理函数
function handleSearch(event) {
    currentSearch = event.target.value;
    currentPage = 1;
    loadAlertRules();
}

function handleStatusFilter(event) {
    currentStatus = event.target.value;
    currentPage = 1;
    loadAlertRules();
}

function handleTriggerTypeChange(event) {
    // 根据触发类型更新可用字段
    console.log('触发类型变化:', event.target.value);
}

// 分页函数
function updatePagination(currentPage, perPage, total) {
    // 实现分页更新逻辑
    console.log('更新分页:', currentPage, perPage, total);
}

function changePage(page) {
    currentPage = page;
    loadAlertRules();
}

// 工具函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(message, type = 'info') {
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function showLoading(show) {
    const existingSpinner = document.querySelector('.alert-rules-loading');
    
    if (show && !existingSpinner) {
        const spinner = document.createElement('div');
        spinner.className = 'alert-rules-loading position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center';
        spinner.style.backgroundColor = 'rgba(255,255,255,0.8)';
        spinner.style.zIndex = '9998';
        spinner.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="text-muted">正在加载预警规则...</div>
            </div>
        `;
        document.body.appendChild(spinner);
    } else if (!show && existingSpinner) {
        existingSpinner.remove();
    }
}

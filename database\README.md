# 电商市场动态监测系统 - 用户管理数据库架构

## 数据库架构概览

本系统采用Laravel框架的数据库架构设计，使用MySQL作为主数据库，设计了完整的用户权限管理体系。

## 核心数据表

### 1. 用户表 (users)
存储系统所有用户的基本信息和状态。

**字段说明：**
- `id`: 主键，自增ID
- `name`: 用户姓名（最长100字符）
- `email`: 邮箱地址（最长191字符，唯一）
- `phone_number`: 手机号码（最长20字符，可选，唯一）
- `username`: 用户名（最长50字符，唯一）
- `email_verified_at`: 邮箱验证时间
- `phone_verified_at`: 手机验证时间
- `password`: 密码哈希值
- `avatar`: 头像路径
- `status`: 用户状态（active/inactive/suspended）
- `last_login_at`: 最后登录时间
- `last_login_ip`: 最后登录IP地址
- `login_attempts`: 登录尝试次数
- `locked_until`: 账户锁定至某时间
- `remember_token`: 记住登录令牌
- `created_at`, `updated_at`: 时间戳

**索引：**
- 复合索引：email+status, phone_number+status, username+status
- 单字段索引：last_login_at

### 2. 角色表 (roles)
定义系统中的不同角色类型。

**字段说明：**
- `id`: 主键，自增ID
- `name`: 角色名称（最长50字符，唯一）
- `display_name`: 角色显示名称（最长100字符）
- `description`: 角色描述
- `level`: 角色级别（admin/advanced/normal/readonly）
- `is_active`: 是否激活
- `permissions_config`: 权限配置（JSON格式）
- `created_at`, `updated_at`: 时间戳

**索引：**
- 复合索引：name+is_active
- 单字段索引：level

### 3. 权限表 (permissions)
定义系统中的所有权限项。

**字段说明：**
- `id`: 主键，自增ID
- `name`: 权限名称（最长100字符，唯一）
- `display_name`: 权限显示名称（最长150字符）
- `description`: 权限描述
- `module`: 所属模块（最长50字符）
- `action`: 操作类型（最长50字符）
- `resource`: 资源标识（最长100字符）
- `is_active`: 是否激活
- `created_at`, `updated_at`: 时间戳

**索引：**
- 复合索引：name+is_active, module+action
- 单字段索引：resource

### 4. 用户角色关联表 (role_user)
多对多关系表，关联用户和角色。

**字段说明：**
- `id`: 主键，自增ID
- `user_id`: 用户ID（外键）
- `role_id`: 角色ID（外键）
- `assigned_at`: 分配时间
- `assigned_by`: 分配者ID（外键，可为空）
- `created_at`, `updated_at`: 时间戳

**索引：**
- 唯一索引：user_id+role_id
- 单字段索引：user_id, role_id, assigned_at

### 5. 角色权限关联表 (permission_role)
多对多关系表，关联角色和权限。

**字段说明：**
- `id`: 主键，自增ID
- `role_id`: 角色ID（外键）
- `permission_id`: 权限ID（外键）
- `created_at`, `updated_at`: 时间戳

**索引：**
- 唯一索引：role_id+permission_id
- 单字段索引：role_id, permission_id

### 6. 用户操作日志表 (user_action_logs)
记录用户的所有重要操作。

**字段说明：**
- `id`: 主键，自增ID
- `user_id`: 用户ID（外键，可为空）
- `action`: 操作类型（最长100字符）
- `module`: 操作模块（最长50字符）
- `description`: 操作描述
- `data_before`: 操作前数据（JSON格式）
- `data_after`: 操作后数据（JSON格式）
- `ip_address`: IP地址（最长45字符）
- `user_agent`: 用户代理信息
- `request_id`: 请求ID（最长100字符）
- `status`: 操作状态（success/failed/error）
- `error_message`: 错误信息
- `created_at`: 创建时间

**索引：**
- 复合索引：user_id+created_at, action+module
- 单字段索引：created_at, ip_address, status

## 权限体系设计

### 权限模块
1. **用户管理 (user)**：用户的增删改查操作
2. **角色管理 (role)**：角色的增删改查操作
3. **监控管理 (monitor)**：监控任务的增删改查操作
4. **系统设置 (system)**：系统配置和日志查看

### 角色级别
1. **超级管理员 (super_admin)**：拥有所有权限
2. **系统管理员 (admin)**：拥有大部分权限（除删除用户外）
3. **运营人员 (operator)**：拥有监控相关权限
4. **只读用户 (viewer)**：只有查看权限

### 默认账户
- **管理员账户**：admin / admin123（超级管理员权限）
- **运营账户**：operator / operator123（运营人员权限）

## 数据库配置

### 连接配置
- **默认连接**: MySQL
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **默认数据库名**: ecommerce_monitor

### 环境变量
请在 `.env` 文件中配置以下数据库连接信息：
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ecommerce_monitor
DB_USERNAME=root
DB_PASSWORD=
```

## 迁移文件执行顺序

1. `2025_06_18_000001_create_users_table.php`
2. `2025_06_18_000002_create_roles_table.php`
3. `2025_06_18_000003_create_permissions_table.php`
4. `2025_06_18_000004_create_role_user_table.php`
5. `2025_06_18_000005_create_permission_role_table.php`
6. `2025_06_18_000006_create_user_action_logs_table.php`

## 数据种子文件

运行 `UserManagementSeeder` 可以初始化：
- 14个基础权限
- 4个默认角色
- 2个默认用户
- 完整的角色权限分配

## 安全特性

1. **密码加密**: 使用Laravel的Hash facade进行密码加密
2. **外键约束**: 所有关联表都设置了外键约束
3. **软删除**: 关键操作使用软删除机制
4. **操作日志**: 完整记录用户操作轨迹
5. **登录保护**: 登录失败次数限制和账户锁定
6. **权限验证**: 基于角色的权限控制(RBAC)

## 扩展性考虑

1. **权限配置**: 支持JSON格式的动态权限配置
2. **多角色支持**: 用户可以分配多个角色
3. **日志完整性**: 详细的操作前后数据对比
4. **索引优化**: 根据查询模式优化了索引策略
5. **数据分区**: 日志表支持按时间分区（未来扩展） 
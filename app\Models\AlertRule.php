<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AlertRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'task_id',
        'product_id',
        'product_sku_id',
        'category_id',
        'channel_id',
        'rule_name',
        'rule_title',
        'description',
        'type',
        'threshold',
        'official_price',
        'percentage_threshold',
        'promotion_deviation_threshold',
        'channel_deviation_threshold',
        'inventory_threshold',
        'data_update_hours_threshold',
        'status_change_type',
        'conditions',
        'alert_parameters',
        'scope',
        'comparison',
        'is_active',
        'notification_method',
        'notification_settings',
        'trigger_count',
        'last_triggered_at',
        'cooldown_minutes',
    ];

    protected $casts = [
        'threshold' => 'decimal:2',
        'official_price' => 'decimal:2',
        'percentage_threshold' => 'decimal:2',
        'promotion_deviation_threshold' => 'decimal:2',
        'channel_deviation_threshold' => 'decimal:2',
        'conditions' => 'array',
        'alert_parameters' => 'array',
        'notification_settings' => 'array',
        'is_active' => 'boolean',
        'last_triggered_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 警报类型常量
    const TYPE_PROMOTION_PRICE_DEVIATION = 'promotion_price_deviation';
    const TYPE_CHANNEL_PRICE_DEVIATION = 'channel_price_deviation';
    const TYPE_PRODUCT_STATUS_CHANGE = 'product_status_change';
    const TYPE_INVENTORY_ANOMALY = 'inventory_anomaly';
    const TYPE_DATA_UPDATE_ANOMALY = 'data_update_anomaly';
    const TYPE_PRICE_DROP = 'price_drop';
    const TYPE_PRICE_RISE = 'price_rise';
    const TYPE_STOCK_CHANGE = 'stock_change';
    const TYPE_CUSTOM = 'custom';

    // 比较操作符常量
    const COMPARISON_GREATER_THAN = 'greater_than';
    const COMPARISON_LESS_THAN = 'less_than';
    const COMPARISON_EQUAL = 'equal';
    const COMPARISON_GREATER_EQUAL = 'greater_equal';
    const COMPARISON_LESS_EQUAL = 'less_equal';

    // 适用范围常量
    const SCOPE_GLOBAL = 'global';
    const SCOPE_PRODUCT = 'product';
    const SCOPE_SKU = 'sku';
    const SCOPE_CATEGORY = 'category';
    const SCOPE_BRAND = 'brand';

    // 状态变化类型常量
    const STATUS_CHANGE_ON_SHELF = 'on_shelf';
    const STATUS_CHANGE_OFF_SHELF = 'off_shelf';
    const STATUS_CHANGE_ANY = 'any';

    // 通知方式常量
    const NOTIFICATION_EMAIL = 'email';
    const NOTIFICATION_SMS = 'sms';
    const NOTIFICATION_PUSH = 'push';
    const NOTIFICATION_ALL = 'all';

    /**
     * 获取所有警报类型选项
     */
    public static function getAlertTypes(): array
    {
        return [
            self::TYPE_PROMOTION_PRICE_DEVIATION => '促销价偏差率警报',
            self::TYPE_CHANNEL_PRICE_DEVIATION => '渠道价偏差率警报',
            self::TYPE_PRODUCT_STATUS_CHANGE => '商品状态变化警报',
            self::TYPE_INVENTORY_ANOMALY => '库存异常警报',
            self::TYPE_DATA_UPDATE_ANOMALY => '数据更新异常警报',
            self::TYPE_PRICE_DROP => '价格下降警报',
            self::TYPE_PRICE_RISE => '价格上涨警报',
            self::TYPE_STOCK_CHANGE => '库存变化警报',
            self::TYPE_CUSTOM => '自定义警报',
        ];
    }

    /**
     * 获取比较操作符选项
     */
    public static function getComparisonOptions(): array
    {
        return [
            self::COMPARISON_GREATER_THAN => '大于',
            self::COMPARISON_LESS_THAN => '小于',
            self::COMPARISON_EQUAL => '等于',
            self::COMPARISON_GREATER_EQUAL => '大于等于',
            self::COMPARISON_LESS_EQUAL => '小于等于',
        ];
    }

    /**
     * 获取适用范围选项
     */
    public static function getScopeOptions(): array
    {
        return [
            self::SCOPE_GLOBAL => '全局',
            self::SCOPE_PRODUCT => '商品',
            self::SCOPE_SKU => 'SKU',
            self::SCOPE_CATEGORY => '分类',
            self::SCOPE_BRAND => '品牌',
        ];
    }

    /**
     * 获取通知方式选项
     */
    public static function getNotificationMethods(): array
    {
        return [
            self::NOTIFICATION_EMAIL => '邮件',
            self::NOTIFICATION_SMS => '短信',
            self::NOTIFICATION_PUSH => '推送',
            self::NOTIFICATION_ALL => '全部',
        ];
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联监控任务
     */
    public function monitorTask(): BelongsTo
    {
        return $this->belongsTo(MonitorTask::class, 'task_id');
    }

    /**
     * 关联产品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联产品SKU
     */
    public function productSku(): BelongsTo
    {
        return $this->belongsTo(ProductSku::class);
    }

    /**
     * 告警日志
     */
    public function alertLogs(): HasMany
    {
        return $this->hasMany(AlertLog::class, 'rule_id');
    }

    /**
     * 关联分类（用于基于分类的警报规则）
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * 获取最近的警报日志
     */
    public function recentAlertLogs(int $limit = 10): HasMany
    {
        return $this->alertLogs()->orderBy('created_at', 'desc')->limit($limit);
    }

    /**
     * 获取未读的警报日志
     */
    public function unreadAlertLogs(): HasMany
    {
        return $this->alertLogs()->where('status', 'unread');
    }

    /**
     * 获取已解决的警报日志
     */
    public function resolvedAlertLogs(): HasMany
    {
        return $this->alertLogs()->where('status', 'resolved');
    }

    /**
     * 检查价格是否触发告警
     */
    public function shouldTrigger($currentPrice, $previousPrice = null): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // 检查冷却时间
        if ($this->inCooldown()) {
            return false;
        }

        return $this->checkCondition($currentPrice, $previousPrice);
    }

    /**
     * 检查是否在冷却期
     */
    public function inCooldown(): bool
    {
        if (!$this->last_triggered_at) {
            return false;
        }

        return $this->last_triggered_at->addMinutes($this->cooldown_minutes) > now();
    }

    /**
     * 检查条件是否满足
     */
    protected function checkCondition($currentPrice, $previousPrice = null): bool
    {
        switch ($this->type) {
            case 'price_drop':
                return $this->checkPriceDrop($currentPrice, $previousPrice);
            case 'price_rise':
                return $this->checkPriceRise($currentPrice, $previousPrice);
            case 'stock_change':
                // 库存变化检查需要额外逻辑
                return false;
            case 'custom':
                return $this->checkCustomCondition($currentPrice, $previousPrice);
            default:
                return false;
        }
    }

    /**
     * 检查价格下降
     */
    protected function checkPriceDrop($currentPrice, $previousPrice = null): bool
    {
        // 如果设置了百分比阈值
        if ($this->percentage_threshold && $previousPrice) {
            $dropRate = (($previousPrice - $currentPrice) / $previousPrice) * 100;
            return $dropRate >= $this->percentage_threshold;
        }

        // 如果设置了绝对价格阈值
        if ($this->threshold) {
            return $this->compareValues($currentPrice, $this->threshold);
        }

        // 如果设置了官方价格参考
        if ($this->official_price) {
            $dropRate = (($this->official_price - $currentPrice) / $this->official_price) * 100;
            return $dropRate >= ($this->percentage_threshold ?? 5); // 默认5%
        }

        return false;
    }

    /**
     * 检查价格上涨
     */
    protected function checkPriceRise($currentPrice, $previousPrice = null): bool
    {
        // 如果设置了百分比阈值
        if ($this->percentage_threshold && $previousPrice) {
            $riseRate = (($currentPrice - $previousPrice) / $previousPrice) * 100;
            return $riseRate >= $this->percentage_threshold;
        }

        // 如果设置了绝对价格阈值
        if ($this->threshold) {
            return $this->compareValues($currentPrice, $this->threshold);
        }

        return false;
    }

    /**
     * 检查自定义条件
     */
    protected function checkCustomCondition($currentPrice, $previousPrice = null): bool
    {
        if (empty($this->conditions)) {
            return false;
        }

        // 可以扩展为更复杂的条件检查逻辑
        return false;
    }

    /**
     * 比较数值
     */
    protected function compareValues($value1, $value2): bool
    {
        switch ($this->comparison) {
            case 'greater_than':
                return $value1 > $value2;
            case 'less_than':
                return $value1 < $value2;
            case 'equal':
                return abs($value1 - $value2) < 0.01; // 浮点数比较
            case 'greater_equal':
                return $value1 >= $value2;
            case 'less_equal':
                return $value1 <= $value2;
            default:
                return false;
        }
    }

    /**
     * 记录触发
     */
    public function recordTrigger(): void
    {
        $this->trigger_count++;
        $this->last_triggered_at = now();
        $this->save();
    }

    /**
     * 获取类型中文
     */
    public function getTypeTextAttribute(): string
    {
        $typeMap = [
            'price_drop' => '价格下降',
            'price_rise' => '价格上涨',
            'stock_change' => '库存变化',
            'custom' => '自定义条件'
        ];

        return $typeMap[$this->type] ?? $this->type;
    }

    /**
     * 获取比较操作符中文
     */
    public function getComparisonTextAttribute(): string
    {
        $comparisonMap = [
            'greater_than' => '大于',
            'less_than' => '小于',
            'equal' => '等于',
            'greater_equal' => '大于等于',
            'less_equal' => '小于等于'
        ];

        return $comparisonMap[$this->comparison] ?? $this->comparison;
    }

    /**
     * 按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 按用户筛选
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按类型筛选
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 可以触发的规则（不在冷却期）
     */
    public function scopeCanTrigger($query)
    {
        return $query->where('is_active', true)
                    ->where(function($q) {
                        $q->whereNull('last_triggered_at')
                          ->orWhereRaw('last_triggered_at + INTERVAL cooldown_minutes MINUTE <= NOW()');
                    });
    }

    /**
     * 检查规则是否在冷却期内
     */
    public function isInCooldown(): bool
    {
        if (!$this->last_triggered_at) {
            return false;
        }

        $cooldownEndsAt = $this->last_triggered_at->addMinutes($this->cooldown_minutes);
        return now()->lt($cooldownEndsAt);
    }

    /**
     * 获取冷却期剩余时间（分钟）
     */
    public function getCooldownRemainingMinutes(): int
    {
        if (!$this->isInCooldown()) {
            return 0;
        }

        $cooldownEndsAt = $this->last_triggered_at->addMinutes($this->cooldown_minutes);
        return now()->diffInMinutes($cooldownEndsAt);
    }

    /**
     * 触发警报（更新触发次数和时间）
     */
    public function trigger(): void
    {
        $this->increment('trigger_count');
        $this->update(['last_triggered_at' => now()]);
    }

    /**
     * 获取类型描述
     */
    public function getTypeDescriptionAttribute(): string
    {
        return self::getAlertTypes()[$this->type] ?? $this->type;
    }

    /**
     * 获取比较操作符描述
     */
    public function getComparisonDescriptionAttribute(): string
    {
        return self::getComparisonOptions()[$this->comparison] ?? $this->comparison;
    }

    /**
     * 获取适用范围描述
     */
    public function getScopeDescriptionAttribute(): string
    {
        return self::getScopeOptions()[$this->scope] ?? $this->scope;
    }

    /**
     * 获取通知方式描述
     */
    public function getNotificationMethodDescriptionAttribute(): string
    {
        return self::getNotificationMethods()[$this->notification_method] ?? $this->notification_method;
    }

    /**
     * 验证规则配置是否有效
     */
    public function isValidConfiguration(): bool
    {
        switch ($this->type) {
            case self::TYPE_PROMOTION_PRICE_DEVIATION:
                return $this->promotion_deviation_threshold !== null && $this->promotion_deviation_threshold > 0;
            
            case self::TYPE_CHANNEL_PRICE_DEVIATION:
                return $this->channel_deviation_threshold !== null && $this->channel_deviation_threshold > 0;
            
            case self::TYPE_PRODUCT_STATUS_CHANGE:
                return $this->product_id !== null && in_array($this->status_change_type, [
                    self::STATUS_CHANGE_ON_SHELF, 
                    self::STATUS_CHANGE_OFF_SHELF, 
                    self::STATUS_CHANGE_ANY
                ]);
            
            case self::TYPE_INVENTORY_ANOMALY:
                return $this->product_id !== null || $this->product_sku_id !== null;
            
            case self::TYPE_DATA_UPDATE_ANOMALY:
                return $this->data_update_hours_threshold !== null && $this->data_update_hours_threshold > 0;
            
            default:
                return true; // 对于其他类型，暂时返回true
        }
    }

    /**
     * 检查促销价偏差率警报
     */
    public function checkPromotionPriceDeviation($currentPrice, $originalPrice): bool
    {
        if ($this->type !== self::TYPE_PROMOTION_PRICE_DEVIATION) {
            return false;
        }

        if (!$this->promotion_deviation_threshold || !$originalPrice) {
            return false;
        }

        $deviationRate = abs(($currentPrice - $originalPrice) / $originalPrice) * 100;
        return $deviationRate >= $this->promotion_deviation_threshold;
    }

    /**
     * 检查渠道价偏差率警报
     */
    public function checkChannelPriceDeviation($channelPrices): bool
    {
        if ($this->type !== self::TYPE_CHANNEL_PRICE_DEVIATION) {
            return false;
        }

        if (!$this->channel_deviation_threshold || count($channelPrices) < 2) {
            return false;
        }

        $avgPrice = array_sum($channelPrices) / count($channelPrices);
        
        foreach ($channelPrices as $price) {
            $deviationRate = abs(($price - $avgPrice) / $avgPrice) * 100;
            if ($deviationRate >= $this->channel_deviation_threshold) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查库存异常警报
     */
    public function checkInventoryAnomaly($currentStock, $previousStock = null): bool
    {
        if ($this->type !== self::TYPE_INVENTORY_ANOMALY) {
            return false;
        }

        // 库存低于阈值
        if ($this->inventory_threshold && $currentStock <= $this->inventory_threshold) {
            return true;
        }

        // 库存异常变动（如果有历史数据）
        if ($previousStock && $previousStock > 0) {
            $changeRate = abs(($currentStock - $previousStock) / $previousStock) * 100;
            // 如果库存变动超过50%可能是异常
            if ($changeRate >= 50) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查数据更新异常警报
     */
    public function checkDataUpdateAnomaly($lastUpdateTime): bool
    {
        if ($this->type !== self::TYPE_DATA_UPDATE_ANOMALY) {
            return false;
        }

        if (!$this->data_update_hours_threshold || !$lastUpdateTime) {
            return false;
        }

        $hoursSinceUpdate = now()->diffInHours($lastUpdateTime);
        return $hoursSinceUpdate >= $this->data_update_hours_threshold;
    }

    /**
     * 检查商品状态变化警报
     */
    public function checkProductStatusChange($newStatus, $oldStatus): bool
    {
        if ($this->type !== self::TYPE_PRODUCT_STATUS_CHANGE) {
            return false;
        }

        if ($newStatus === $oldStatus) {
            return false;
        }

        switch ($this->status_change_type) {
            case self::STATUS_CHANGE_ON_SHELF:
                return $newStatus === 'on_shelf' && $oldStatus !== 'on_shelf';
            case self::STATUS_CHANGE_OFF_SHELF:
                return $newStatus === 'off_shelf' && $oldStatus !== 'off_shelf';
            case self::STATUS_CHANGE_ANY:
                return true;
            default:
                return false;
        }
    }

    /**
     * 获取警报规则的完整描述
     */
    public function getFullDescription(): string
    {
        $description = $this->rule_title ?: $this->rule_name;
        
        switch ($this->type) {
            case self::TYPE_PROMOTION_PRICE_DEVIATION:
                $description .= " (促销价偏差率 >= {$this->promotion_deviation_threshold}%)";
                break;
            case self::TYPE_CHANNEL_PRICE_DEVIATION:
                $description .= " (渠道价偏差率 >= {$this->channel_deviation_threshold}%)";
                break;
            case self::TYPE_INVENTORY_ANOMALY:
                $description .= " (库存 <= {$this->inventory_threshold})";
                break;
            case self::TYPE_DATA_UPDATE_ANOMALY:
                $description .= " (数据更新超过 {$this->data_update_hours_threshold} 小时)";
                break;
            case self::TYPE_PRODUCT_STATUS_CHANGE:
                $description .= " (状态变化: {$this->status_change_type})";
                break;
        }

        return $description;
    }

    /**
     * 获取警报统计信息
     */
    public function getStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $totalAlerts = $this->alertLogs()->where('created_at', '>=', $startDate)->count();
        $unreadAlerts = $this->alertLogs()->where('created_at', '>=', $startDate)->where('status', 'unread')->count();
        $resolvedAlerts = $this->alertLogs()->where('created_at', '>=', $startDate)->where('status', 'resolved')->count();
        
        return [
            'total_alerts' => $totalAlerts,
            'unread_alerts' => $unreadAlerts,
            'resolved_alerts' => $resolvedAlerts,
            'resolution_rate' => $totalAlerts > 0 ? round(($resolvedAlerts / $totalAlerts) * 100, 2) : 0,
            'trigger_frequency' => $totalAlerts > 0 ? round($totalAlerts / $days, 2) : 0,
        ];
    }

    /**
     * 重置触发统计
     */
    public function resetTriggerStats(): void
    {
        $this->update([
            'trigger_count' => 0,
            'last_triggered_at' => null
        ]);
    }

    /**
     * 获取相关的产品信息（用于显示）
     */
    public function getRelatedProductInfo(): array
    {
        $info = [];
        
        if ($this->product) {
            $info['product'] = [
                'id' => $this->product->id,
                'name' => $this->product->name,
                'brand' => $this->product->brand ?? 'N/A'
            ];
        }
        
        if ($this->productSku) {
            $info['sku'] = [
                'id' => $this->productSku->id,
                'sku_code' => $this->productSku->sku_code,
                'title' => $this->productSku->title ?? 'N/A'
            ];
        }
        
        if ($this->monitorTask) {
            $info['task'] = [
                'id' => $this->monitorTask->id,
                'task_name' => $this->monitorTask->task_name,
                'platform' => $this->monitorTask->platform ?? 'N/A'
            ];
        }
        
        return $info;
    }

    /**
     * 按严重程度筛选（基于触发频率）
     */
    public function scopeBySeverity($query, $severity = 'high')
    {
        $triggers = [
            'low' => 5,
            'medium' => 10,
            'high' => 20,
            'critical' => 50
        ];
        
        $minTriggers = $triggers[$severity] ?? 10;
        return $query->where('trigger_count', '>=', $minTriggers);
    }

    /**
     * 按产品筛选
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * 按SKU筛选
     */
    public function scopeForSku($query, $skuId)
    {
        return $query->where('product_sku_id', $skuId);
    }

    /**
     * 按分类筛选
     */
    public function scopeForCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 筛选有效配置的规则
     */
    public function scopeValidConfiguration($query)
    {
        return $query->whereNotNull('type')
                    ->where(function($q) {
                        $q->whereNotNull('threshold')
                          ->orWhereNotNull('percentage_threshold')
                          ->orWhereNotNull('promotion_deviation_threshold')
                          ->orWhereNotNull('channel_deviation_threshold');
                    });
    }

    /**
     * 筛选最近触发的规则
     */
    public function scopeRecentlyTriggered($query, $hours = 24)
    {
        return $query->where('last_triggered_at', '>=', now()->subHours($hours));
    }

    /**
     * 筛选频繁触发的规则
     */
    public function scopeFrequentlyTriggered($query, $minCount = 10)
    {
        return $query->where('trigger_count', '>=', $minCount);
    }
}

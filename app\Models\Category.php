<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'path',
        'level',
        'sort_order',
        'image',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'level' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * 父分类
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * 子分类
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    /**
     * 所有子分类（递归）
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 分类下的产品
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'category_id');
    }

    /**
     * 活跃的产品
     */
    public function activeProducts(): HasMany
    {
        return $this->products()->where('state', 'active');
    }

    /**
     * 检查是否为根分类
     */
    public function isRoot(): bool
    {
        return $this->parent_id === null;
    }

    /**
     * 检查是否为叶子分类（无子分类）
     */
    public function isLeaf(): bool
    {
        return $this->children()->count() === 0;
    }

    /**
     * 检查是否有子分类
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * 获取祖先分类路径
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $category = $this->parent;
        
        while ($category) {
            $ancestors->prepend($category);
            $category = $category->parent;
        }
        
        return $ancestors;
    }

    /**
     * 获取所有后代分类ID
     */
    public function getDescendantIds(): array
    {
        $descendants = [];
        
        foreach ($this->children as $child) {
            $descendants[] = $child->id;
            $descendants = array_merge($descendants, $child->getDescendantIds());
        }
        
        return $descendants;
    }

    /**
     * 获取分类面包屑导航
     */
    public function getBreadcrumbAttribute(): string
    {
        if ($this->path) {
            return $this->path;
        }
        
        $ancestors = $this->getAncestors();
        $breadcrumb = $ancestors->pluck('name')->push($this->name);
        
        return $breadcrumb->implode(' > ');
    }

    /**
     * 更新分类路径
     */
    public function updatePath(): void
    {
        $ancestors = $this->getAncestors();
        $path = $ancestors->pluck('name')->push($this->name)->implode('/');
        
        $this->update(['path' => $path]);
        
        // 更新所有子分类的路径
        foreach ($this->children as $child) {
            $child->updatePath();
        }
    }

    /**
     * 获取产品数量（包括子分类）
     */
    public function getTotalProductCountAttribute(): int
    {
        $count = $this->products()->count();
        
        foreach ($this->children as $child) {
            $count += $child->total_product_count;
        }
        
        return $count;
    }

    /**
     * 按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 根分类
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 按层级筛选
     */
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 按排序顺序排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * 搜索分类
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%")
              ->orWhere('path', 'like', "%{$keyword}%");
        });
    }

    /**
     * 获取分类树结构
     */
    public static function getTree($parentId = null, $level = 1)
    {
        return static::where('parent_id', $parentId)
                    ->active()
                    ->ordered()
                    ->with(['children' => function($query) use ($level) {
                        if ($level < 5) { // 限制递归深度
                            $query->with(['children']);
                        }
                    }])
                    ->get();
    }

    /**
     * 获取扁平化的分类列表（带层级缩进）
     */
    public static function getFlatList($parentId = null, $level = 0, &$result = [])
    {
        $categories = static::where('parent_id', $parentId)
                           ->active()
                           ->ordered()
                           ->get();
        
        foreach ($categories as $category) {
            $category->indent_level = $level;
            $category->display_name = str_repeat('　', $level) . $category->name;
            $result[] = $category;
            
            static::getFlatList($category->id, $level + 1, $result);
        }
        
        return $result;
    }

    /**
     * 保存时自动更新路径和层级
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($category) {
            // 自动计算层级
            if ($category->parent_id) {
                $parent = static::find($category->parent_id);
                $category->level = $parent ? $parent->level + 1 : 1;
            } else {
                $category->level = 1;
            }
        });
    }
}

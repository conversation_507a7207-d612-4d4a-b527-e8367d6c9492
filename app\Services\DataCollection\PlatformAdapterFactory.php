<?php

namespace App\Services\DataCollection;

use App\Models\ApiConfiguration;
use App\Services\DataCollection\Contracts\PlatformAdapterInterface;
use App\Services\DataCollection\Adapters\TaobaoAdapter;
use App\Services\DataCollection\Adapters\JdAdapter;
use InvalidArgumentException;

/**
 * 平台适配器工厂类
 * 根据平台名称创建相应的适配器实例
 * 支持从API配置管理模块创建适配器
 */
class PlatformAdapterFactory
{
    /**
     * 支持的平台配置
     */
    private static array $platformConfigs = [
        'taobao' => [
            'adapter_class' => TaobaoAdapter::class,
            'base_url' => 'http://60.247.148.208:5001',
            'token_env_key' => 'TAOBAO_API_TOKEN',
        ],
        'jd' => [
            'adapter_class' => JdAdapter::class,
            'base_url' => 'http://jd-api.example.com',
            'token_env_key' => 'JD_API_TOKEN',
        ],
        // 可以在这里添加更多平台
        // 'tmall' => [
        //     'adapter_class' => TmallAdapter::class,
        //     'base_url' => 'http://tmall-api.example.com',
        //     'token_env_key' => 'TMALL_API_TOKEN',
        // ],
    ];

    /**
     * 创建平台适配器实例
     *
     * @param string $platform 平台名称
     * @param array $customConfig 自定义配置（可选）
     * @return PlatformAdapterInterface
     * @throws InvalidArgumentException
     */
    public static function create(string $platform, array $customConfig = []): PlatformAdapterInterface
    {
        if (!isset(self::$platformConfigs[$platform])) {
            throw new InvalidArgumentException("不支持的平台: {$platform}");
        }

        $config = self::$platformConfigs[$platform];
        $adapterClass = $config['adapter_class'];

        if (!class_exists($adapterClass)) {
            throw new InvalidArgumentException("适配器类不存在: {$adapterClass}");
        }

        // 合并默认配置和自定义配置
        $adapterConfig = array_merge([
            'base_url' => $config['base_url'],
            'token' => env($config['token_env_key'], ''),
            'timeout' => env('API_TIMEOUT', 30),
            'retry_attempts' => env('API_RETRY_ATTEMPTS', 3),
            'retry_delay' => [1, 2, 4], // 指数退避延迟
        ], $customConfig);

        return new $adapterClass($adapterConfig);
    }

    /**
     * 获取所有支持的平台
     *
     * @return array 平台名称列表
     */
    public static function getSupportedPlatforms(): array
    {
        return array_keys(self::$platformConfigs);
    }

    /**
     * 检查平台是否支持
     *
     * @param string $platform 平台名称
     * @return bool
     */
    public static function isSupported(string $platform): bool
    {
        return isset(self::$platformConfigs[$platform]);
    }

    /**
     * 创建所有支持平台的适配器实例
     *
     * @param array $customConfigs 按平台分组的自定义配置
     * @return array 平台适配器实例数组
     */
    public static function createAll(array $customConfigs = []): array
    {
        $adapters = [];
        
        foreach (self::$platformConfigs as $platform => $config) {
            try {
                $platformConfig = $customConfigs[$platform] ?? [];
                $adapters[$platform] = self::create($platform, $platformConfig);
            } catch (\Exception $e) {
                // 记录错误但继续创建其他适配器
                \Log::warning("创建{$platform}适配器失败: " . $e->getMessage());
            }
        }

        return $adapters;
    }

    /**
     * 从API配置创建适配器实例
     *
     * @param ApiConfiguration $apiConfig API配置对象
     * @return PlatformAdapterInterface
     * @throws InvalidArgumentException
     */
    public static function createFromApiConfiguration(ApiConfiguration $apiConfig): PlatformAdapterInterface
    {
        $platform = $apiConfig->platform_type;

        if (!isset(self::$platformConfigs[$platform])) {
            throw new InvalidArgumentException("不支持的平台: {$platform}");
        }

        $config = self::$platformConfigs[$platform];
        $adapterClass = $config['adapter_class'];

        if (!class_exists($adapterClass)) {
            throw new InvalidArgumentException("适配器类不存在: {$adapterClass}");
        }

        // 从API配置构建适配器配置
        $authData = $apiConfig->auth_credentials ?? [];
        $metadata = $apiConfig->metadata ?? [];

        $adapterConfig = [
            'base_url' => $apiConfig->base_url,
            'token' => $authData['token'] ?? $authData['api_key'] ?? '',
            'timeout' => $metadata['timeout'] ?? 30,
            'retry_attempts' => $metadata['retry_attempts'] ?? 3,
            'retry_delay' => $metadata['retry_delay'] ?? [1, 2, 4],
            'api_version' => $apiConfig->version ?? '1.0',
        ];

        $adapter = new $adapterClass($adapterConfig);

        // 设置API配置到适配器
        $adapter->setApiConfiguration($apiConfig);

        return $adapter;
    }

    /**
     * 获取平台的默认配置
     *
     * @param string $platform 平台名称
     * @return array 默认配置
     * @throws InvalidArgumentException
     */
    public static function getPlatformConfig(string $platform): array
    {
        if (!isset(self::$platformConfigs[$platform])) {
            throw new InvalidArgumentException("不支持的平台: {$platform}");
        }

        $config = self::$platformConfigs[$platform];

        return [
            'adapter_class' => $config['adapter_class'],
            'base_url' => $config['base_url'],
            'token' => env($config['token_env_key'], ''),
            'timeout' => env('API_TIMEOUT', 30),
            'retry_attempts' => env('API_RETRY_ATTEMPTS', 3),
            'retry_delay' => [1, 2, 4],
        ];
    }

    /**
     * 添加新的平台配置
     *
     * @param string $platform 平台名称
     * @param array $config 平台配置
     */
    public static function addPlatform(string $platform, array $config): void
    {
        if (!isset($config['adapter_class']) || !isset($config['base_url'])) {
            throw new InvalidArgumentException("平台配置必须包含 adapter_class 和 base_url");
        }

        self::$platformConfigs[$platform] = $config;
    }

    /**
     * 测试所有平台的连接
     *
     * @return array 每个平台的连接测试结果
     */
    public static function testAllConnections(): array
    {
        $results = [];
        
        foreach (self::$platformConfigs as $platform => $config) {
            try {
                $adapter = self::create($platform);
                $results[$platform] = [
                    'status' => $adapter->testConnection() ? 'success' : 'failed',
                    'error' => null,
                ];
            } catch (\Exception $e) {
                $results[$platform] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }
} 
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductSkuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sku_name' => $this->sku_name,
            'props' => $this->props,
            'official_guide_price' => $this->official_guide_price,
            'latest_price' => $this->priceHistory->first()->price ?? null,
        ];
    }
}

<?php

namespace App\Jobs;

use App\Models\MonitorTask;
use App\Services\DataCollection\DataCollectionService;
use App\Services\PerformanceMonitoringService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * 数据收集队列作业
 * 
 * 处理监控任务的数据收集，支持：
 * - 多平台数据收集
 * - 错误处理和重试
 * - 进度跟踪
 * - 结果保存
 * - 性能监控
 * - 并发控制
 */
class DataCollectionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 监控任务实例
     *
     * @var MonitorTask
     */
    protected $monitorTask;

    /**
     * 作业选项
     *
     * @var array
     */
    protected $options;

    /**
     * 性能监控服务
     *
     * @var PerformanceMonitoringService|null
     */
    protected $performanceMonitor;

    /**
     * 并发控制配置
     *
     * @var array
     */
    protected $concurrencyConfig;

    /**
     * 任务超时时间（秒）
     *
     * @var int
     */
    public $timeout = 300; // 5分钟

    /**
     * 最大重试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 重试延迟（秒）
     *
     * @var int
     */
    public $retryAfter = 60;

    /**
     * 创建新的作业实例
     *
     * @param MonitorTask $monitorTask
     * @param array $options
     */
    public function __construct(MonitorTask $monitorTask, array $options = [])
    {
        $this->monitorTask = $monitorTask;
        $this->options = $options;
        
        // 初始化并发控制配置
        $this->concurrencyConfig = config('datacollection.collection', []);
        
        // 设置队列
        $this->onQueue($monitorTask->queue_name ?? 'data_collection');
        
        // 设置延迟
        if (isset($options['delay'])) {
            $this->delay($options['delay']);
        }
    }

    /**
     * 执行作业
     *
     * @param DataCollectionService $dataCollectionService
     * @param PerformanceMonitoringService $performanceMonitor
     */
    public function handle(DataCollectionService $dataCollectionService, PerformanceMonitoringService $performanceMonitor)
    {
        $this->performanceMonitor = $performanceMonitor;
        $startTime = microtime(true);
        $jobId = $this->job->getJobId();
        
        // 记录作业开始
        $this->recordJobStart($jobId, $startTime);
        
        Log::info('开始执行数据收集作业', [
            'task_id' => $this->monitorTask->id,
            'task_name' => $this->monitorTask->task_name,
            'platform' => $this->monitorTask->platform,
            'attempt' => $this->attempts(),
            'job_id' => $jobId,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ]);

        try {
            // 检查并发限制
            $this->checkConcurrencyLimits();
            
            // 检查任务状态
            $this->validateTask();

            // 更新任务状态为执行中
            $this->updateTaskStatus('running', [
                'started_at' => now(),
                'job_id' => $jobId,
                'attempt' => $this->attempts(),
                'memory_start' => memory_get_usage(true)
            ]);

            // 执行数据收集
            $result = $this->performDataCollection($dataCollectionService);

            // 处理收集结果
            $this->processResult($result);

            // 计算执行时间和性能指标
            $executionTime = round(microtime(true) - $startTime, 2);
            $memoryUsage = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);

            // 记录性能指标
            $this->recordPerformanceMetrics($executionTime, $memoryUsage, $peakMemory, true);

            // 更新任务状态为成功
            $this->updateTaskStatus('completed', [
                'completed_at' => now(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage,
                'peak_memory' => $peakMemory,
                'result_summary' => $this->getResultSummary($result)
            ]);

            // 释放并发控制
            $this->releaseConcurrencyControl();

            Log::info('数据收集作业执行成功', [
                'task_id' => $this->monitorTask->id,
                'execution_time' => $executionTime,
                'memory_usage' => $this->formatBytes($memoryUsage),
                'peak_memory' => $this->formatBytes($peakMemory),
                'collected_items' => count($result['data'] ?? [])
            ]);

        } catch (\Exception $e) {
            $executionTime = round(microtime(true) - $startTime, 2);
            $this->recordPerformanceMetrics($executionTime, memory_get_usage(true), memory_get_peak_usage(true), false);
            $this->releaseConcurrencyControl();
            $this->handleFailure($e, $startTime);
            throw $e; // 重新抛出异常以触发重试机制
        }
    }

    /**
     * 检查并发限制
     *
     * @throws \Exception
     */
    protected function checkConcurrencyLimits()
    {
        $platform = $this->monitorTask->platform;
        $maxConcurrency = $this->concurrencyConfig['concurrency'] ?? 10;
        
        // 检查全局并发限制
        $globalKey = 'concurrency:global';
        $currentGlobal = Redis::scard($globalKey);
        
        if ($currentGlobal >= $maxConcurrency) {
            throw new \Exception("全局并发限制达到上限: {$currentGlobal}/{$maxConcurrency}");
        }
        
        // 检查平台特定并发限制
        $platformKey = "concurrency:platform:{$platform}";
        $platformMaxConcurrency = $this->getPlatformConcurrencyLimit($platform);
        $currentPlatform = Redis::scard($platformKey);
        
        if ($currentPlatform >= $platformMaxConcurrency) {
            throw new \Exception("平台 {$platform} 并发限制达到上限: {$currentPlatform}/{$platformMaxConcurrency}");
        }

        // 获取并发控制锁
        $this->acquireConcurrencyControl();
        
        Log::debug('并发检查通过', [
            'task_id' => $this->monitorTask->id,
            'platform' => $platform,
            'global_concurrency' => $currentGlobal,
            'platform_concurrency' => $currentPlatform,
            'max_global' => $maxConcurrency,
            'max_platform' => $platformMaxConcurrency
        ]);
    }

    /**
     * 获取平台并发限制
     *
     * @param string $platform
     * @return int
     */
    protected function getPlatformConcurrencyLimit(string $platform): int
    {
        $platformLimits = [
            'taobao' => 5,
            'tmall' => 5,
            'jd' => 3,
            'douyin' => 3,
            'kuaishou' => 2,
        ];
        
        return $platformLimits[$platform] ?? 3; // 默认限制3个并发
    }

    /**
     * 获取并发控制锁
     */
    protected function acquireConcurrencyControl()
    {
        $jobId = $this->job->getJobId();
        $platform = $this->monitorTask->platform;
        
        // 添加到全局并发控制集合
        Redis::sadd('concurrency:global', $jobId);
        Redis::expire('concurrency:global', 600); // 10分钟过期
        
        // 添加到平台并发控制集合
        Redis::sadd("concurrency:platform:{$platform}", $jobId);
        Redis::expire("concurrency:platform:{$platform}", 600);
        
        // 记录作业开始时间（用于监控长时间运行的作业）
        Redis::hset('concurrency:jobs', $jobId, json_encode([
            'task_id' => $this->monitorTask->id,
            'platform' => $platform,
            'started_at' => time(),
            'queue' => $this->queue
        ]));
        Redis::expire('concurrency:jobs', 600);
    }

    /**
     * 释放并发控制锁
     */
    protected function releaseConcurrencyControl()
    {
        $jobId = $this->job->getJobId();
        $platform = $this->monitorTask->platform;
        
        // 从并发控制集合中移除
        Redis::srem('concurrency:global', $jobId);
        Redis::srem("concurrency:platform:{$platform}", $jobId);
        Redis::hdel('concurrency:jobs', $jobId);
    }

    /**
     * 记录作业开始
     *
     * @param string $jobId
     * @param float $startTime
     */
    protected function recordJobStart($jobId, $startTime)
    {
        if ($this->performanceMonitor) {
            $this->performanceMonitor->recordJobStart([
                'job_id' => $jobId,
                'task_id' => $this->monitorTask->id,
                'platform' => $this->monitorTask->platform,
                'queue' => $this->queue,
                'started_at' => $startTime,
                'memory_start' => memory_get_usage(true)
            ]);
        }
    }

    /**
     * 记录性能指标
     *
     * @param float $executionTime
     * @param int $memoryUsage
     * @param int $peakMemory
     * @param bool $success
     */
    protected function recordPerformanceMetrics($executionTime, $memoryUsage, $peakMemory, $success)
    {
        if (!$this->performanceMonitor) {
            return;
        }

        $metrics = [
            'job_class' => self::class,
            'task_id' => $this->monitorTask->id,
            'platform' => $this->monitorTask->platform,
            'queue' => $this->queue,
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'peak_memory' => $peakMemory,
            'success' => $success,
            'attempt' => $this->attempts(),
            'timestamp' => time()
        ];

        $this->performanceMonitor->recordJobMetrics($metrics);

        // 如果执行时间过长，记录警告
        if ($executionTime > 180) { // 3分钟
            $this->performanceMonitor->recordAlert([
                'type' => 'long_running_job',
                'severity' => 'warning',
                'job_id' => $this->job->getJobId(),
                'task_id' => $this->monitorTask->id,
                'execution_time' => $executionTime,
                'message' => "作业执行时间过长: {$executionTime}秒"
            ]);
        }

        // 如果内存使用过高，记录警告  
        if ($peakMemory > 100 * 1024 * 1024) { // 100MB
            $this->performanceMonitor->recordAlert([
                'type' => 'high_memory_usage',
                'severity' => 'warning',
                'job_id' => $this->job->getJobId(),
                'task_id' => $this->monitorTask->id,
                'peak_memory' => $peakMemory,
                'message' => "作业内存使用过高: " . $this->formatBytes($peakMemory)
            ]);
        }
    }

    /**
     * 验证任务状态
     *
     * @throws \Exception
     */
    protected function validateTask()
    {
        // 刷新任务数据
        $this->monitorTask->refresh();

        if (!$this->monitorTask->is_enabled) {
            throw new \Exception('监控任务已被禁用');
        }

        if ($this->monitorTask->status !== 'active') {
            throw new \Exception('监控任务状态不是活跃状态');
        }

        if (empty($this->monitorTask->platform)) {
            throw new \Exception('监控任务未指定平台');
        }
    }

    /**
     * 执行数据收集
     *
     * @param DataCollectionService $dataCollectionService
     * @return array
     */
    protected function performDataCollection(DataCollectionService $dataCollectionService)
    {
        $platform = $this->monitorTask->platform;
        $parameters = $this->prepareCollectionParameters();

        Log::debug('开始执行平台数据收集', [
            'task_id' => $this->monitorTask->id,
            'platform' => $platform,
            'parameters' => $parameters
        ]);

        // 根据监控任务类型选择收集方法
        switch ($this->monitorTask->collection_type ?? 'product_details') {
            case 'product_details':
                return $dataCollectionService->collectProductDetails($platform, $parameters);
                
            case 'similar_products':
                return $dataCollectionService->collectSimilarProducts($platform, $parameters);
                
            case 'shop_products':
                return $dataCollectionService->collectShopProducts($platform, $parameters);
                
            case 'search_products':
                return $dataCollectionService->collectSearchResults($platform, $parameters);
                
            default:
                return $dataCollectionService->collectProductDetails($platform, $parameters);
        }
    }

    /**
     * 准备收集参数
     *
     * @return array
     */
    protected function prepareCollectionParameters()
    {
        // 从任务配置中获取参数
        $taskParameters = json_decode($this->monitorTask->parameters, true) ?? [];
        
        // 合并默认参数
        $defaultParameters = [
            'collect_reviews' => true,
            'collect_images' => false,
            'max_items' => 100,
            'timeout' => 30
        ];

        // 合并作业选项
        $parameters = array_merge($defaultParameters, $taskParameters, $this->options);

        // 添加任务元信息
        $parameters['_meta'] = [
            'task_id' => $this->monitorTask->id,
            'execution_id' => uniqid('exec_'),
            'execution_time' => now()->toISOString(),
            'job_id' => $this->job->getJobId() ?? null
        ];

        return $parameters;
    }

    /**
     * 处理收集结果
     *
     * @param array $result
     */
    protected function processResult(array $result)
    {
        if (empty($result['data'])) {
            Log::warning('数据收集结果为空', [
                'task_id' => $this->monitorTask->id
            ]);
            return;
        }

        $collectedData = $result['data'];
        $processedCount = 0;

        foreach ($collectedData as $item) {
            try {
                $this->saveCollectedItem($item);
                $processedCount++;
            } catch (\Exception $e) {
                Log::error('保存收集数据项失败', [
                    'task_id' => $this->monitorTask->id,
                    'item_id' => $item['id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('数据收集结果处理完成', [
            'task_id' => $this->monitorTask->id,
            'total_items' => count($collectedData),
            'processed_items' => $processedCount
        ]);
    }

    /**
     * 保存收集的数据项
     *
     * @param array $item
     */
    protected function saveCollectedItem(array $item)
    {
        // 这里可以根据具体需求保存到不同的表
        // 例如：products, price_history 等
        
        // 示例：保存价格历史
        if (isset($item['price']) && isset($item['product_id'])) {
            \App\Models\PriceHistory::create([
                'monitor_task_id' => $this->monitorTask->id,
                'product_id' => $item['product_id'],
                'platform' => $this->monitorTask->platform,
                'price' => $item['price'],
                'original_price' => $item['original_price'] ?? null,
                'discount_price' => $item['discount_price'] ?? null,
                'currency' => $item['currency'] ?? 'CNY',
                'stock_status' => $item['stock_status'] ?? 'unknown',
                'collected_at' => now(),
                'raw_data' => json_encode($item)
            ]);
        }
    }

    /**
     * 获取结果摘要
     *
     * @param array $result
     * @return array
     */
    protected function getResultSummary(array $result)
    {
        return [
            'collected_items' => count($result['data'] ?? []),
            'success' => $result['success'] ?? true,
            'platform' => $this->monitorTask->platform,
            'collection_type' => $this->monitorTask->collection_type ?? 'product_details',
            'execution_id' => $result['execution_id'] ?? null
        ];
    }

    /**
     * 更新任务状态
     *
     * @param string $status
     * @param array $data
     */
    protected function updateTaskStatus($status, array $data = [])
    {
        $updateData = [
            'last_executed_at' => now()
        ];

        if ($status === 'running') {
            $updateData['execution_count'] = $this->monitorTask->execution_count + 1;
        } elseif ($status === 'completed') {
            $updateData['success_count'] = $this->monitorTask->success_count + 1;
            $updateData['consecutive_failures'] = 0; // 重置连续失败计数
        } elseif ($status === 'failed') {
            $updateData['failure_count'] = $this->monitorTask->failure_count + 1;
            $updateData['consecutive_failures'] = $this->monitorTask->consecutive_failures + 1;
        }

        // 合并附加数据
        $updateData = array_merge($updateData, $data);

        $this->monitorTask->update($updateData);
    }

    /**
     * 处理作业失败
     *
     * @param \Exception $exception
     * @param float $startTime
     */
    protected function handleFailure(\Exception $exception, $startTime)
    {
        $executionTime = round(microtime(true) - $startTime, 2);
        
        Log::error('数据收集作业执行失败', [
            'task_id' => $this->monitorTask->id,
            'attempt' => $this->attempts(),
            'max_attempts' => $this->tries,
            'execution_time' => $executionTime,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // 更新任务失败状态
        $this->updateTaskStatus('failed', [
            'failed_at' => now(),
            'execution_time' => $executionTime,
            'last_error' => $exception->getMessage(),
            'last_error_trace' => $exception->getTraceAsString()
        ]);

        // 检查是否需要禁用任务
        $this->checkForTaskDisabling();
    }

    /**
     * 检查是否需要禁用任务
     */
    protected function checkForTaskDisabling()
    {
        $maxConsecutiveFailures = $this->monitorTask->max_consecutive_failures ?? 5;
        
        if ($this->monitorTask->consecutive_failures >= $maxConsecutiveFailures) {
            $this->monitorTask->update([
                'is_enabled' => false,
                'status' => 'disabled',
                'disabled_reason' => "连续失败{$this->monitorTask->consecutive_failures}次，自动禁用",
                'disabled_at' => now()
            ]);
            
            Log::warning('监控任务因连续失败过多被自动禁用', [
                'task_id' => $this->monitorTask->id,
                'consecutive_failures' => $this->monitorTask->consecutive_failures
            ]);
        }
    }

    /**
     * 作业失败时的处理
     *
     * @param \Exception $exception
     */
    public function failed(\Exception $exception)
    {
        Log::error('数据收集作业最终失败', [
            'task_id' => $this->monitorTask->id,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // 更新最终失败状态
        $this->updateTaskStatus('failed', [
            'finally_failed_at' => now(),
            'final_error' => $exception->getMessage()
        ]);
    }

    /**
     * 获取重试延迟时间
     *
     * @return int
     */
    public function retryUntil()
    {
        return now()->addMinutes(30); // 30分钟后不再重试
    }

    /**
     * 确定作业应延迟多长时间
     *
     * @return int
     */
    public function backoff()
    {
        // 指数退避：第1次重试等待60秒，第2次等待120秒，第3次等待240秒
        return [60, 120, 240];
    }

    /**
     * 格式化字节
     *
     * @param int $bytes
     * @return string
     */
    protected function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));
        return round($bytes, 2) . ' ' . $units[$pow];
    }
} 
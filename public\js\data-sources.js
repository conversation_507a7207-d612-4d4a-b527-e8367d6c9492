/**
 * 数据源管理JavaScript功能
 */

// 全局变量
let uploadProgress = 0;
let currentUploadFile = null;
let uploadAbortController = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDataSources();
    bindEvents();
});

// 初始化数据源功能
function initializeDataSources() {
    loadImportHistory();
    setupDropZone();
}

// 绑定事件
function bindEvents() {
    // 文件选择
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileUpload);
    }

    // 上传按钮
    const uploadBtn = document.getElementById('uploadBtn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', startUpload);
    }

    // 取消上传按钮
    const cancelBtn = document.getElementById('cancelUploadBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', cancelUpload);
    }

    // 表单提交
    const uploadForm = document.getElementById('uploadForm');
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFormSubmit);
    }
}

// 设置拖拽上传区域
function setupDropZone() {
    const dropZone = document.getElementById('dropZone');
    if (!dropZone) return;

    // 防止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // 高亮拖拽区域
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    // 处理文件拖拽
    dropZone.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    e.currentTarget.classList.add('drag-over');
}

function unhighlight(e) {
    e.currentTarget.classList.remove('drag-over');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.files = files;
            handleFileUpload({ target: fileInput });
        }
    }
}

// 处理文件上传
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // 验证文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
    ];
    
    if (!allowedTypes.includes(file.type)) {
        showAlert('请选择Excel文件(.xlsx, .xls)或CSV文件', 'danger');
        event.target.value = '';
        return;
    }
    
    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showAlert('文件大小不能超过10MB', 'danger');
        event.target.value = '';
        return;
    }
    
    // 显示文件信息
    updateFileInfo(file);
    currentUploadFile = file;
    
    // 启用上传按钮
    const uploadBtn = document.getElementById('uploadBtn');
    if (uploadBtn) {
        uploadBtn.disabled = false;
    }
    
    // 自动开始上传（可选）
    if (document.getElementById('autoUpload')?.checked) {
        await startUpload();
    }
}

// 更新文件信息显示
function updateFileInfo(file) {
    const fileInfoDiv = document.getElementById('fileInfo');
    if (!fileInfoDiv) return;

    fileInfoDiv.innerHTML = `
        <div class="alert alert-info">
            <div class="d-flex align-items-center">
                <i class="fas fa-file-excel fa-2x me-3 text-success"></i>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${file.name}</h6>
                    <small class="text-muted">
                        大小: ${formatFileSize(file.size)} | 
                        类型: ${file.type} | 
                        修改时间: ${new Date(file.lastModified).toLocaleString()}
                    </small>
                </div>
            </div>
        </div>
    `;
    fileInfoDiv.style.display = 'block';
}

// 开始上传
async function startUpload() {
    if (!currentUploadFile) {
        showAlert('请先选择文件', 'warning');
        return;
    }

    try {
        // 显示上传进度
        showUploadProgress();
        
        // 获取上传选项
        const options = {
            platform: document.getElementById('platform')?.value || '',
            category: document.getElementById('category')?.value || '',
            overwrite: document.getElementById('overwrite')?.checked || false,
            validate_only: document.getElementById('validateOnly')?.checked || false
        };

        // 创建AbortController用于取消上传
        uploadAbortController = new AbortController();

        // 开始上传
        const response = await apiService.importExcel(currentUploadFile, options);

        if (response.success) {
            handleUploadSuccess(response.data);
        } else {
            throw new Error(response.message || '上传失败');
        }

    } catch (error) {
        if (error.name === 'AbortError') {
            showAlert('上传已取消', 'info');
        } else {
            console.error('上传失败:', error);
            showAlert('上传失败：' + error.message, 'danger');
        }
        hideUploadProgress();
    } finally {
        uploadAbortController = null;
    }
}

// 取消上传
function cancelUpload() {
    if (uploadAbortController) {
        uploadAbortController.abort();
        hideUploadProgress();
        showAlert('上传已取消', 'info');
    }
}

// 显示上传进度
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        updateProgress(0);
    }

    // 禁用上传按钮，启用取消按钮
    const uploadBtn = document.getElementById('uploadBtn');
    const cancelBtn = document.getElementById('cancelUploadBtn');
    
    if (uploadBtn) uploadBtn.disabled = true;
    if (cancelBtn) {
        cancelBtn.disabled = false;
        cancelBtn.style.display = 'inline-block';
    }
}

// 隐藏上传进度
function hideUploadProgress() {
    const progressContainer = document.getElementById('uploadProgress');
    if (progressContainer) {
        progressContainer.style.display = 'none';
    }

    // 恢复按钮状态
    const uploadBtn = document.getElementById('uploadBtn');
    const cancelBtn = document.getElementById('cancelUploadBtn');
    
    if (uploadBtn) uploadBtn.disabled = false;
    if (cancelBtn) {
        cancelBtn.disabled = true;
        cancelBtn.style.display = 'none';
    }
}

// 更新进度条
function updateProgress(percent) {
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    const progressText = document.querySelector('#uploadProgress .progress-text');
    
    if (progressBar) {
        progressBar.style.width = percent + '%';
        progressBar.setAttribute('aria-valuenow', percent);
    }
    
    if (progressText) {
        progressText.textContent = `${Math.round(percent)}%`;
    }
}

// 处理上传成功
function handleUploadSuccess(data) {
    hideUploadProgress();
    
    // 显示结果
    const resultDiv = document.getElementById('uploadResult');
    if (resultDiv) {
        resultDiv.innerHTML = `
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>上传成功！</h5>
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">${data.total_rows || 0}</div>
                            <small class="text-muted">总行数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success">${data.success_count || 0}</div>
                            <small class="text-muted">成功导入</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">${data.error_count || 0}</div>
                            <small class="text-muted">导入失败</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">${data.duplicate_count || 0}</div>
                            <small class="text-muted">重复数据</small>
                        </div>
                    </div>
                </div>
                ${data.errors && data.errors.length > 0 ? `
                    <div class="mt-3">
                        <h6>错误详情：</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>行号</th>
                                        <th>错误信息</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.errors.map(error => `
                                        <tr>
                                            <td>${error.row}</td>
                                            <td>${error.message}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        resultDiv.style.display = 'block';
    }
    
    // 重新加载导入历史
    loadImportHistory();
    
    // 清空文件选择
    resetUploadForm();
}

// 重置上传表单
function resetUploadForm() {
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const uploadBtn = document.getElementById('uploadBtn');
    
    if (fileInput) fileInput.value = '';
    if (fileInfo) fileInfo.style.display = 'none';
    if (uploadBtn) uploadBtn.disabled = true;
    
    currentUploadFile = null;
}

// 加载导入历史
async function loadImportHistory() {
    try {
        const response = await apiService.getImportHistory();
        
        if (response.success) {
            renderImportHistory(response.data.data);
        } else {
            throw new Error(response.message || '获取导入历史失败');
        }
        
    } catch (error) {
        console.error('加载导入历史失败:', error);
        
        // 使用模拟数据
        const mockHistory = [
            {
                id: 1,
                filename: 'products_2024_01_15.xlsx',
                total_rows: 1000,
                success_count: 950,
                error_count: 50,
                status: 'completed',
                created_at: '2024-01-15 10:30:00'
            },
            {
                id: 2,
                filename: 'inventory_update.csv',
                total_rows: 500,
                success_count: 500,
                error_count: 0,
                status: 'completed',
                created_at: '2024-01-14 15:20:00'
            }
        ];
        
        renderImportHistory(mockHistory);
    }
}

// 渲染导入历史
function renderImportHistory(history) {
    const tbody = document.getElementById('historyTableBody');
    if (!tbody) return;

    if (history.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-history fa-2x mb-2"></i>
                    <div>暂无导入历史</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = history.map(item => `
        <tr>
            <td>${item.filename}</td>
            <td>${item.total_rows}</td>
            <td><span class="text-success">${item.success_count}</span></td>
            <td><span class="text-danger">${item.error_count}</span></td>
            <td>
                <span class="badge bg-${getStatusColor(item.status)}">${getStatusText(item.status)}</span>
            </td>
            <td>
                <div>${formatDateTime(item.created_at)}</div>
                <div class="btn-group btn-group-sm mt-1">
                    <button type="button" class="btn btn-outline-primary" onclick="viewImportDetail(${item.id})" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${item.error_count > 0 ? `
                        <button type="button" class="btn btn-outline-warning" onclick="downloadErrorReport(${item.id})" title="下载错误报告">
                            <i class="fas fa-download"></i>
                        </button>
                    ` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

// 查看导入详情
function viewImportDetail(id) {
    // 实现查看详情功能
    console.log('查看导入详情:', id);
}

// 下载错误报告
function downloadErrorReport(id) {
    // 实现下载错误报告功能
    console.log('下载错误报告:', id);
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getStatusColor(status) {
    const colors = {
        'completed': 'success',
        'processing': 'warning',
        'failed': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'completed': '已完成',
        'processing': '处理中',
        'failed': '失败',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showAlert(message, type = 'info') {
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// 处理表单提交
function handleFormSubmit(event) {
    event.preventDefault();
    startUpload();
}

<?php

namespace App\Services;

use App\Jobs\DataCollectionJob;
use App\Jobs\BatchDataCollectionJob;
use App\Models\Product;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;

class QueueManagementService
{
    /**
     * 调度单个产品数据收集任务
     */
    public function scheduleDataCollection($productUrl, array $options = [], $productId = null, $delay = 0)
    {
        try {
            $job = new DataCollectionJob($productUrl, $options, $productId);
            
            if ($delay > 0) {
                $job->delay(now()->addSeconds($delay));
            }

            $jobId = dispatch($job);

            Log::info('数据收集任务已调度', [
                'job_id' => $jobId,
                'product_url' => $productUrl,
                'product_id' => $productId,
                'delay' => $delay,
                'priority' => $options['priority'] ?? 'normal'
            ]);

            return [
                'success' => true,
                'job_id' => $jobId,
                'message' => '任务已成功调度'
            ];

        } catch (\Exception $e) {
            Log::error('调度数据收集任务失败', [
                'product_url' => $productUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 批量调度数据收集任务
     */
    public function scheduleBatchDataCollection(array $productIds, array $options = [], int $batchSize = 10)
    {
        try {
            $job = new BatchDataCollectionJob($productIds, $options, $batchSize);
            $jobId = dispatch($job);

            Log::info('批量数据收集任务已调度', [
                'job_id' => $jobId,
                'product_count' => count($productIds),
                'batch_size' => $batchSize
            ]);

            return [
                'success' => true,
                'job_id' => $jobId,
                'message' => '批量任务已成功调度'
            ];

        } catch (\Exception $e) {
            Log::error('调度批量数据收集任务失败', [
                'product_ids' => $productIds,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 为所有待收集的产品调度任务
     */
    public function scheduleAllPendingProducts(array $options = [])
    {
        try {
            $products = Product::where('collection_status', '!=', 'success')
                ->orWhere('last_collection_at', '<', now()->subHours(24))
                ->orWhereNull('last_collection_at')
                ->whereNotNull('url')
                ->where('url', '!=', '')
                ->get();

            if ($products->isEmpty()) {
                return [
                    'success' => true,
                    'message' => '没有待收集的产品',
                    'scheduled_count' => 0
                ];
            }

            $scheduledCount = 0;
            $failedCount = 0;
            $delay = 0;

            foreach ($products as $product) {
                $result = $this->scheduleDataCollection(
                    $product->url, 
                    array_merge($options, ['priority' => $this->determineProductPriority($product)]),
                    $product->id,
                    $delay
                );

                if ($result['success']) {
                    $scheduledCount++;
                    // 为下一个任务增加延迟以避免过于频繁的请求
                    $delay += rand(5, 15); // 5-15秒随机延迟
                } else {
                    $failedCount++;
                }
            }

            Log::info('批量调度完成', [
                'total_products' => $products->count(),
                'scheduled_count' => $scheduledCount,
                'failed_count' => $failedCount
            ]);

            return [
                'success' => true,
                'message' => "成功调度 {$scheduledCount} 个任务，失败 {$failedCount} 个",
                'scheduled_count' => $scheduledCount,
                'failed_count' => $failedCount
            ];

        } catch (\Exception $e) {
            Log::error('批量调度待收集产品失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats()
    {
        try {
            $stats = [
                'queues' => [],
                'failed_jobs' => 0,
                'total_pending' => 0
            ];

            // 获取各个队列的统计信息
            $queueNames = ['data-collection-high', 'data-collection', 'data-collection-low'];
            
            foreach ($queueNames as $queueName) {
                $queueSize = $this->getQueueSize($queueName);
                $stats['queues'][$queueName] = [
                    'size' => $queueSize,
                    'name' => $queueName
                ];
                $stats['total_pending'] += $queueSize;
            }

            // 获取失败任务数量
            $stats['failed_jobs'] = DB::table('failed_jobs')->count();

            // 获取正在处理的任务数量
            $stats['processing'] = $this->getProcessingJobsCount();

            return [
                'success' => true,
                'stats' => $stats
            ];

        } catch (\Exception $e) {
            Log::error('获取队列统计信息失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 清理失败的任务
     */
    public function clearFailedJobs()
    {
        try {
            $count = DB::table('failed_jobs')->count();
            DB::table('failed_jobs')->truncate();

            Log::info('清理失败任务完成', ['cleared_count' => $count]);

            return [
                'success' => true,
                'message' => "已清理 {$count} 个失败任务"
            ];

        } catch (\Exception $e) {
            Log::error('清理失败任务失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 重试失败的任务
     */
    public function retryFailedJobs($limit = null)
    {
        try {
            $failedJobs = DB::table('failed_jobs');
            
            if ($limit) {
                $failedJobs = $failedJobs->limit($limit);
            }
            
            $jobs = $failedJobs->get();
            $retryCount = 0;

            foreach ($jobs as $job) {
                try {
                    Queue::retry($job->id);
                    $retryCount++;
                } catch (\Exception $e) {
                    Log::error('重试失败任务出错', [
                        'job_id' => $job->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('重试失败任务完成', [
                'total_failed' => $jobs->count(),
                'retry_count' => $retryCount
            ]);

            return [
                'success' => true,
                'message' => "成功重试 {$retryCount} 个任务"
            ];

        } catch (\Exception $e) {
            Log::error('重试失败任务失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 暂停队列处理
     */
    public function pauseQueue($queueName = null)
    {
        try {
            if ($queueName) {
                $this->setQueuePaused($queueName, true);
                $message = "队列 {$queueName} 已暂停";
            } else {
                // 暂停所有数据收集队列
                $queues = ['data-collection-high', 'data-collection', 'data-collection-low'];
                foreach ($queues as $queue) {
                    $this->setQueuePaused($queue, true);
                }
                $message = "所有数据收集队列已暂停";
            }

            Log::info('暂停队列', ['queue' => $queueName ?? 'all']);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (\Exception $e) {
            Log::error('暂停队列失败', [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 恢复队列处理
     */
    public function resumeQueue($queueName = null)
    {
        try {
            if ($queueName) {
                $this->setQueuePaused($queueName, false);
                $message = "队列 {$queueName} 已恢复";
            } else {
                // 恢复所有数据收集队列
                $queues = ['data-collection-high', 'data-collection', 'data-collection-low'];
                foreach ($queues as $queue) {
                    $this->setQueuePaused($queue, false);
                }
                $message = "所有数据收集队列已恢复";
            }

            Log::info('恢复队列', ['queue' => $queueName ?? 'all']);

            return [
                'success' => true,
                'message' => $message
            ];

        } catch (\Exception $e) {
            Log::error('恢复队列失败', [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 确定产品优先级
     */
    protected function determineProductPriority(Product $product)
    {
        // 可以根据产品特征确定优先级
        if ($product->is_monitored) {
            return 'high';
        }
        
        if ($product->last_collection_at && $product->last_collection_at < now()->subDays(7)) {
            return 'low';
        }

        return 'normal';
    }

    /**
     * 获取队列大小
     */
    protected function getQueueSize($queueName)
    {
        try {
            if (config('queue.default') === 'redis') {
                return Redis::llen("queues:{$queueName}");
            } else {
                return DB::table('jobs')->where('queue', $queueName)->count();
            }
        } catch (\Exception $e) {
            Log::error('获取队列大小失败', [
                'queue' => $queueName,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 获取正在处理的任务数量
     */
    protected function getProcessingJobsCount()
    {
        try {
            if (config('queue.default') === 'redis') {
                // Redis队列中正在处理的任务通常存储在不同的键中
                return 0; // 这里需要根据具体的Redis队列实现来获取
            } else {
                return DB::table('jobs')->where('reserved_at', '!=', null)->count();
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 设置队列暂停状态
     */
    protected function setQueuePaused($queueName, $paused)
    {
        // 这里可以实现队列暂停逻辑
        // 比如在Redis中设置一个标志，然后在Worker中检查这个标志
        try {
            if (config('queue.default') === 'redis') {
                $key = "queue:paused:{$queueName}";
                if ($paused) {
                    Redis::set($key, '1');
                } else {
                    Redis::del($key);
                }
            }
        } catch (\Exception $e) {
            Log::error('设置队列暂停状态失败', [
                'queue' => $queueName,
                'paused' => $paused,
                'error' => $e->getMessage()
            ]);
        }
    }
} 
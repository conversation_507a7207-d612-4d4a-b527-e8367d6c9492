<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PriceDeviationCalculator;
use App\Services\PriceDeviationMonitor;
use App\Services\PriceDeviationConfig;
use App\Models\PriceHistory;
use App\Models\ProductSku;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class CalculatePriceDeviationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'price:calculate-deviations
                            {--product-id= : Filter by specific product ID}
                            {--sku-id= : Filter by specific SKU ID}
                            {--start-date= : Start date for filtering (Y-m-d format)}
                            {--end-date= : End date for filtering (Y-m-d format)}
                            {--chunk-size=1000 : Number of records to process in each chunk}
                            {--only-missing : Only calculate for records without existing deviation rates}
                            {--dry-run : Preview what would be calculated without making changes}
                            {--force : Force recalculation even for records with existing rates}
                            {--stats : Show detailed statistics after completion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Batch calculate promotion and channel price deviation rates for historical price data';

    /**
     * 价格偏差计算器
     *
     * @var PriceDeviationCalculator
     */
    private PriceDeviationCalculator $calculator;

    /**
     * 性能监控器
     *
     * @var PriceDeviationMonitor
     */
    private PriceDeviationMonitor $monitor;

    /**
     * 处理统计
     *
     * @var array
     */
    private array $stats = [
        'total_records' => 0,
        'processed' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => 0,
        'promotion_calculated' => 0,
        'channel_calculated' => 0,
        'start_time' => null,
        'end_time' => null
    ];

    /**
     * Create a new command instance.
     */
    public function __construct(PriceDeviationCalculator $calculator)
    {
        parent::__construct();
        $this->calculator = $calculator;
        $this->monitor = new PriceDeviationMonitor();
        $this->calculator->setMonitor($this->monitor);
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->stats['start_time'] = Carbon::now();
        
        // 启动监控会话
        $this->monitor->startSession();
        
        $this->info('🚀 开始批量计算价格偏差率...');
        $this->newLine();

        try {
            // 验证输入参数
            if (!$this->validateOptions()) {
                return Command::FAILURE;
            }

            // 检查和显示系统资源
            $this->displayResourceInfo();

            // 构建查询
            $query = $this->buildQuery();
            
            // 获取总记录数
            $this->stats['total_records'] = $query->count();
            
            if ($this->stats['total_records'] === 0) {
                $this->warn('没有找到符合条件的价格历史记录');
                return Command::SUCCESS;
            }

            $this->info("找到 {$this->stats['total_records']} 条记录需要处理");
            
            // 推荐分块大小
            $recommendedChunkSize = PriceDeviationConfig::getRecommendedChunkSize(
                $this->stats['total_records']
            );
            $actualChunkSize = (int) $this->option('chunk-size');
            
            if ($actualChunkSize !== $recommendedChunkSize && $this->stats['total_records'] > 1000) {
                $this->comment("💡 建议分块大小: {$recommendedChunkSize}，当前: {$actualChunkSize}");
            }
            
            if ($this->option('dry-run')) {
                $this->info('🔍 预览模式 - 不会实际修改数据');
                $this->previewCalculation($query);
                return Command::SUCCESS;
            }

            // 确认执行
            if (!$this->option('force') && !$this->confirm('确认开始批量计算？')) {
                $this->info('操作已取消');
                return Command::SUCCESS;
            }

            // 执行批量计算
            $this->performBatchCalculation($query);

            // 显示结果
            $this->displayResults();

            return Command::SUCCESS;

        } catch (Exception $e) {
            $this->error('批量计算失败: ' . $e->getMessage());
            Log::error('批量价格偏差率计算失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        } finally {
            // 结束监控会话并显示报告
            $report = $this->monitor->endSession();
            if ($this->option('stats')) {
                $this->displayMonitoringReport($report);
            }
        }
    }

    /**
     * 验证命令选项
     */
    private function validateOptions(): bool
    {
        // 验证日期格式
        if ($this->option('start-date')) {
            try {
                Carbon::createFromFormat('Y-m-d', $this->option('start-date'));
            } catch (Exception $e) {
                $this->error('开始日期格式错误，请使用 Y-m-d 格式 (如: 2024-01-01)');
                return false;
            }
        }

        if ($this->option('end-date')) {
            try {
                Carbon::createFromFormat('Y-m-d', $this->option('end-date'));
            } catch (Exception $e) {
                $this->error('结束日期格式错误，请使用 Y-m-d 格式 (如: 2024-12-31)');
                return false;
            }
        }

        // 验证chunk-size
        $chunkSize = (int) $this->option('chunk-size');
        if ($chunkSize < 1 || $chunkSize > 10000) {
            $this->error('chunk-size 必须在 1-10000 之间');
            return false;
        }

        return true;
    }

    /**
     * 构建查询
     */
    private function buildQuery()
    {
        $query = PriceHistory::query()
            ->with(['sku:id,product_id,official_guide_price'])
            ->select([
                'id', 'sku_id', 'price', 'sub_price', 
                'promotion_deviation_rate', 'channel_deviation_rate',
                'calculation_status', 'timestamp'
            ]);

        // 应用过滤条件
        if ($this->option('product-id')) {
            $query->whereHas('sku', function($q) {
                $q->where('product_id', $this->option('product-id'));
            });
        }

        if ($this->option('sku-id')) {
            $query->where('sku_id', $this->option('sku-id'));
        }

        if ($this->option('start-date')) {
            $startDate = Carbon::createFromFormat('Y-m-d', $this->option('start-date'))->startOfDay();
            $query->where('timestamp', '>=', $startDate);
        }

        if ($this->option('end-date')) {
            $endDate = Carbon::createFromFormat('Y-m-d', $this->option('end-date'))->endOfDay();
            $query->where('timestamp', '<=', $endDate);
        }

        // 只处理缺失偏差率的记录
        if ($this->option('only-missing')) {
            $query->where(function($q) {
                $q->whereNull('promotion_deviation_rate')
                  ->orWhereNull('channel_deviation_rate')
                  ->orWhere('calculation_status', '!=', 'calculated');
            });
        }

        return $query;
    }

    /**
     * 预览计算
     */
    private function previewCalculation($query): void
    {
        $sampleRecords = $query->limit(5)->get();
        
        $this->info('📋 预览前5条记录的计算结果:');
        $this->newLine();

        $headers = ['ID', 'SKU ID', '价格', '促销价', '指导价', '促销偏差率', '渠道偏差率', '状态'];
        $rows = [];

        foreach ($sampleRecords as $record) {
            $promotionRate = $this->calculator->calculatePromotionDeviation(
                $record->price, 
                $record->sub_price
            );
            
            $channelRate = $this->calculator->calculateChannelDeviation(
                $record->sku->official_guide_price ?? null,
                $record->sub_price
            );

            $rows[] = [
                $record->id,
                $record->sku_id,
                number_format($record->price, 2),
                $record->sub_price ? number_format($record->sub_price, 2) : 'N/A',
                $record->sku->official_guide_price ? number_format($record->sku->official_guide_price, 2) : 'N/A',
                $promotionRate !== null ? number_format($promotionRate, 2) . '%' : 'N/A',
                $channelRate !== null ? number_format($channelRate, 2) . '%' : 'N/A',
                $this->getRecordStatus($record, $promotionRate, $channelRate)
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * 执行批量计算
     */
    private function performBatchCalculation($query): void
    {
        $chunkSize = (int) $this->option('chunk-size');
        $progressBar = $this->output->createProgressBar($this->stats['total_records']);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        $query->chunk($chunkSize, function ($records) use ($progressBar) {
            $this->processChunk($records, $progressBar);
        });

        $progressBar->finish();
        $this->newLine(2);

        $this->stats['end_time'] = Carbon::now();
    }

    /**
     * 处理数据块
     */
    private function processChunk($records, $progressBar): void
    {
        DB::beginTransaction();

        try {
            foreach ($records as $record) {
                $this->processRecord($record);
                $progressBar->advance();
            }

            DB::commit();

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('处理数据块时发生错误', [
                'error' => $e->getMessage(),
                'chunk_size' => $records->count()
            ]);

            // 记录错误但继续处理其他块
            $this->stats['errors'] += $records->count();
        }
    }

    /**
     * 处理单条记录
     */
    private function processRecord($record): void
    {
        try {
            $this->stats['processed']++;
            $updated = false;

            // 检查是否需要重新计算
            if (!$this->shouldRecalculate($record)) {
                $this->stats['skipped']++;
                return;
            }

            // 计算促销价格偏差率
            $promotionRate = $this->calculator->calculatePromotionDeviation(
                $record->price,
                $record->sub_price
            );

            // 计算渠道价格偏差率
            $channelRate = $this->calculator->calculateChannelDeviation(
                $record->sku->official_guide_price ?? null,
                $record->sub_price
            );

            // 更新记录
            $updateData = [];

            if ($promotionRate !== null) {
                $updateData['promotion_deviation_rate'] = $promotionRate;
                $this->stats['promotion_calculated']++;
                $updated = true;
            }

            if ($channelRate !== null) {
                $updateData['channel_deviation_rate'] = $channelRate;
                $this->stats['channel_calculated']++;
                $updated = true;
            }

            if ($updated) {
                $updateData['calculation_status'] = 'calculated';
                $updateData['deviation_calculated_at'] = Carbon::now();
                
                PriceHistory::where('id', $record->id)->update($updateData);
                $this->stats['updated']++;
            }

        } catch (Exception $e) {
            $this->stats['errors']++;
            
            Log::error('处理单条记录失败', [
                'record_id' => $record->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否需要重新计算
     */
    private function shouldRecalculate($record): bool
    {
        // 强制模式下总是重新计算
        if ($this->option('force')) {
            return true;
        }

        // 只处理缺失的模式
        if ($this->option('only-missing')) {
            return $record->promotion_deviation_rate === null ||
                   $record->channel_deviation_rate === null ||
                   $record->calculation_status !== 'calculated';
        }

        // 默认：如果状态不是已计算，则重新计算
        return $record->calculation_status !== 'calculated';
    }

    /**
     * 获取记录状态
     */
    private function getRecordStatus($record, $promotionRate, $channelRate): string
    {
        if ($promotionRate !== null && $channelRate !== null) {
            return '完整';
        } elseif ($promotionRate !== null || $channelRate !== null) {
            return '部分';
        } else {
            return '无法计算';
        }
    }

    /**
     * 显示结果统计
     */
    private function displayResults(): void
    {
        $duration = $this->stats['end_time']->diffInSeconds($this->stats['start_time']);
        
        $this->info('✅ 批量计算完成！');
        $this->newLine();

        // 基本统计
        $this->table(['统计项', '数值'], [
            ['总记录数', number_format($this->stats['total_records'])],
            ['已处理', number_format($this->stats['processed'])],
            ['已更新', number_format($this->stats['updated'])],
            ['已跳过', number_format($this->stats['skipped'])],
            ['错误数', number_format($this->stats['errors'])],
            ['促销偏差率计算', number_format($this->stats['promotion_calculated'])],
            ['渠道偏差率计算', number_format($this->stats['channel_calculated'])],
            ['处理时间', $duration . ' 秒'],
            ['处理速度', $duration > 0 ? number_format($this->stats['processed'] / $duration, 2) . ' 条/秒' : 'N/A']
        ]);

        // 详细统计
        if ($this->option('stats')) {
            $this->displayDetailedStats();
        }

        // 记录日志
        Log::info('批量价格偏差率计算完成', $this->stats);
    }

    /**
     * 显示详细统计
     */
    private function displayDetailedStats(): void
    {
        $this->newLine();
        $this->info('📊 详细统计信息:');

        // 获取最新的偏差率分布
        $promotionStats = PriceHistory::whereNotNull('promotion_deviation_rate')
            ->selectRaw('
                MIN(promotion_deviation_rate) as min_rate,
                MAX(promotion_deviation_rate) as max_rate,
                AVG(promotion_deviation_rate) as avg_rate,
                COUNT(*) as count
            ')
            ->first();

        $channelStats = PriceHistory::whereNotNull('channel_deviation_rate')
            ->selectRaw('
                MIN(channel_deviation_rate) as min_rate,
                MAX(channel_deviation_rate) as max_rate,
                AVG(channel_deviation_rate) as avg_rate,
                COUNT(*) as count
            ')
            ->first();

        if ($promotionStats && $promotionStats->count > 0) {
            $this->line('促销偏差率统计:');
            $this->line('  最小值: ' . number_format($promotionStats->min_rate, 2) . '%');
            $this->line('  最大值: ' . number_format($promotionStats->max_rate, 2) . '%');
            $this->line('  平均值: ' . number_format($promotionStats->avg_rate, 2) . '%');
            $this->line('  记录数: ' . number_format($promotionStats->count));
        }

        if ($channelStats && $channelStats->count > 0) {
            $this->line('渠道偏差率统计:');
            $this->line('  最小值: ' . number_format($channelStats->min_rate, 2) . '%');
            $this->line('  最大值: ' . number_format($channelStats->max_rate, 2) . '%');
            $this->line('  平均值: ' . number_format($channelStats->avg_rate, 2) . '%');
            $this->line('  记录数: ' . number_format($channelStats->count));
        }
    }

    /**
     * 显示系统资源信息
     */
    private function displayResourceInfo(): void
    {
        $resources = $this->monitor->monitorResources();
        
        $this->comment('💻 系统资源状态:');
        $this->line('  内存使用: ' . $resources['memory']['current_formatted'] . 
                   ' / ' . $resources['memory']['limit_formatted'] . 
                   ' (' . number_format($resources['memory']['usage_percentage'], 1) . '%)');
        
        if ($resources['memory']['usage_percentage'] > 70) {
            $this->warn('  ⚠️  内存使用率较高，建议减小分块大小');
        }
        
        $this->newLine();
    }

    /**
     * 显示监控报告
     */
    private function displayMonitoringReport(array $report): void
    {
        $this->newLine();
        $this->info('📈 性能监控报告:');
        
        $session = $report['session_summary'];
        $quality = $report['quality_assessment'];
        
        // 会话摘要
        $this->table(['监控项', '数值'], [
            ['会话时长', $session['duration_formatted']],
            ['计算次数', number_format($session['calculations_count'])],
            ['内存使用', $session['memory_used_formatted']],
            ['峰值内存', $session['peak_memory_formatted']],
            ['异常检测', $report['anomalies_count'] . ' 个异常'],
            ['性能警告', $report['performance_warnings_count'] . ' 个警告'],
            ['错误数量', $report['errors_count'] . ' 个错误'],
        ]);

        // 质量评估
        $this->newLine();
        $this->info('🎯 计算质量评估:');
        $this->table(['评估项', '分数/比率'], [
            ['总体评分', $quality['overall_score'] . ' 分'],
            ['质量等级', $quality['quality_grade']],
            ['成功率', $quality['success_rate'] . '%'],
            ['异常率', $quality['anomaly_rate'] . '%'],
            ['性能分数', $quality['performance_score'] . ' 分'],
            ['平均计算时间', number_format($quality['average_calculation_time'] * 1000, 2) . ' 毫秒'],
        ]);

        // 改进建议
        if (!empty($quality['recommendations'])) {
            $this->newLine();
            $this->info('💡 改进建议:');
            foreach ($quality['recommendations'] as $recommendation) {
                $this->line('  • ' . $recommendation);
            }
        }
    }
} 
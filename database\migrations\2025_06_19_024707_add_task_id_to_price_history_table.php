<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('price_history', function (Blueprint $table) {
            $table->foreignId('task_id')->nullable()->after('sku_id')
                  ->constrained('monitor_tasks')->onDelete('cascade')
                  ->comment('关联监控任务ID');
            
            // 添加索引
            $table->index(['task_id', 'timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('price_history', function (Blueprint $table) {
            $table->dropForeign(['task_id']);
            $table->dropIndex(['task_id', 'timestamp']);
            $table->dropColumn('task_id');
        });
    }
};

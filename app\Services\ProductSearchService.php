<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Http\Request;

class ProductSearchService
{
    public function search(Request $request)
    {
        $query = Product::query();

        // Keyword search (delegated to <PERSON>)
        if ($request->filled('keyword')) {
            // This will be a simple database LIKE query until <PERSON> is fully configured
            $query->where('title', 'like', '%' . $request->input('keyword') . '%');
        }

        // ID search
        if ($request->filled('product_id')) {
            $query->where('id', $request->input('product_id'));
        }

        // TODO: Implement other search dimensions

        return $query->paginate(20);
    }
} 
<?php

namespace Tests\Feature;

use App\Models\ApiConfiguration;
use App\Models\Role;
use App\Models\User;
use App\Services\ApiVersionManagerService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiVersionManagementTest extends TestCase
{
    use RefreshDatabase;

    protected ApiVersionManagerService $versionManagerService;
    protected User $adminUser;
    protected ApiConfiguration $baseConfig;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->versionManagerService = new ApiVersionManagerService();
        
        // 创建管理员角色和用户
        $adminRole = Role::create([
            'name' => 'admin',
            'display_name' => '管理员',
            'description' => '系统管理员',
            'level' => 'admin',
            'is_active' => true,
        ]);
        
        $this->adminUser = User::factory()->create();
        $this->adminUser->roles()->attach($adminRole->id);
        
        // 创建基础API配置
        $this->baseConfig = ApiConfiguration::create([
            'name' => 'Test API',
            'slug' => 'test-api',
            'platform_type' => 'taobao',
            'base_url' => 'https://api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'test-key'],
            'version' => '1.0.0',
            'is_active' => true,
        ]);
    }

    public function test_can_create_new_version()
    {
        $newVersion = $this->versionManagerService->createNewVersion(
            $this->baseConfig,
            '1.1.0',
            ['base_url' => 'https://api-v2.example.com']
        );

        $this->assertInstanceOf(ApiConfiguration::class, $newVersion);
        $this->assertEquals('1.1.0', $newVersion->version);
        $this->assertEquals('https://api-v2.example.com', $newVersion->base_url);
        $this->assertFalse($newVersion->is_deprecated);
    }

    public function test_cannot_create_duplicate_version()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('版本 1.0.0 已存在');

        $this->versionManagerService->createNewVersion(
            $this->baseConfig,
            '1.0.0'
        );
    }

    public function test_can_upgrade_to_version()
    {
        // 创建新版本
        $newVersion = $this->versionManagerService->createNewVersion(
            $this->baseConfig,
            '1.1.0'
        );

        // 升级到新版本
        $upgradedConfig = $this->versionManagerService->upgradeToVersion(
            'taobao',
            'Test API v1.1.0', // 使用新的名称格式
            '1.1.0'
        );

        $this->assertNotNull($upgradedConfig);
        $this->assertEquals('1.1.0', $upgradedConfig->version);
        $this->assertTrue($upgradedConfig->is_active);

        // 检查旧版本是否被停用
        $this->baseConfig->refresh();
        $this->assertFalse($this->baseConfig->is_active);
    }

    public function test_can_deprecate_version()
    {
        $reason = 'Security vulnerability fixed in newer version';
        
        $this->versionManagerService->deprecateVersion($this->baseConfig, $reason);
        
        $this->baseConfig->refresh();
        $this->assertTrue($this->baseConfig->is_deprecated);
        
        $metadata = $this->baseConfig->metadata;
        $this->assertNotNull($metadata);
        $this->assertArrayHasKey('deprecation', $metadata);
        $this->assertEquals($reason, $metadata['deprecation']['reason']);
    }

    public function test_can_get_compatibility_info()
    {
        // 创建多个版本
        $v110 = $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');
        $v120 = $this->versionManagerService->createNewVersion($this->baseConfig, '1.2.0');

        $compatibilityInfo = $this->versionManagerService->getCompatibilityInfo($v110);

        $this->assertArrayHasKey('current_version', $compatibilityInfo);
        $this->assertArrayHasKey('backward_compatible', $compatibilityInfo);
        $this->assertArrayHasKey('forward_compatible', $compatibilityInfo);
        $this->assertEquals('1.1.0', $compatibilityInfo['current_version']);
    }

    public function test_can_get_migration_path()
    {
        // 创建多个版本
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.2.0');
        $this->versionManagerService->createNewVersion($this->baseConfig, '2.0.0');

        // 由于版本管理的名称格式问题，我们需要使用基础名称
        $migrationPath = $this->versionManagerService->getMigrationPath(
            'taobao',
            'Test API', // 使用基础名称
            '1.0.0',
            '2.0.0'
        );

        $this->assertArrayHasKey('from', $migrationPath);
        $this->assertArrayHasKey('to', $migrationPath);
        $this->assertArrayHasKey('path', $migrationPath);
        $this->assertArrayHasKey('direction', $migrationPath);
        $this->assertEquals('1.0.0', $migrationPath['from']);
        $this->assertEquals('2.0.0', $migrationPath['to']);
        $this->assertEquals('upgrade', $migrationPath['direction']);
    }

    public function test_can_get_version_statistics()
    {
        // 创建多个版本和平台
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.2.0');

        ApiConfiguration::create([
            'name' => 'JD API',
            'slug' => 'jd-api',
            'platform_type' => 'jd',
            'base_url' => 'https://jd-api.example.com',
            'auth_type' => 'api_key',
            'auth_credentials' => ['api_key' => 'jd-key'],
            'version' => '1.0.0',
            'is_active' => true,
        ]);

        $statistics = $this->versionManagerService->getVersionStatistics();

        $this->assertArrayHasKey('total_configurations', $statistics);
        $this->assertArrayHasKey('active_configurations', $statistics);
        $this->assertArrayHasKey('deprecated_configurations', $statistics);
        $this->assertArrayHasKey('version_distribution', $statistics);
        $this->assertEquals(4, $statistics['total_configurations']); // 3 taobao + 1 jd
    }

    public function test_api_configuration_version_methods()
    {
        // 创建新版本
        $newVersion = $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');

        // 测试版本比较方法
        $this->assertTrue($newVersion->isVersionGreaterThan('1.0.0'));
        $this->assertFalse($newVersion->isVersionLessThan('1.0.0'));
        $this->assertTrue($newVersion->isLatestVersion());
        $this->assertFalse($this->baseConfig->isLatestVersion());

        // 测试获取其他版本
        $otherVersions = $newVersion->getOtherVersions();
        $this->assertCount(1, $otherVersions);
        $this->assertEquals('1.0.0', $otherVersions->first()->version);

        // 测试版本信息
        $versionInfo = $newVersion->getVersionInfo();
        $this->assertArrayHasKey('current_version', $versionInfo);
        $this->assertArrayHasKey('is_latest', $versionInfo);
        $this->assertArrayHasKey('other_versions', $versionInfo);
        $this->assertTrue($versionInfo['is_latest']);
    }

    public function test_controller_get_version_info()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson(route('api-configurations.version-info', $this->baseConfig));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'configuration',
                'version_info',
                'compatibility',
            ]);
    }

    public function test_controller_create_version()
    {
        $response = $this->actingAs($this->adminUser)
            ->postJson(route('api-configurations.create-version', $this->baseConfig), [
                'version' => '1.1.0',
                'changes' => [
                    'base_url' => 'https://api-v2.example.com'
                ]
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'configuration',
            ]);

        $this->assertDatabaseHas('api_configurations', [
            'version' => '1.1.0',
            'base_url' => 'https://api-v2.example.com'
        ]);
    }

    public function test_controller_upgrade_version()
    {
        // 先创建新版本
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');

        $response = $this->actingAs($this->adminUser)
            ->postJson(route('api-configurations.upgrade-version'), [
                'platform_type' => 'taobao',
                'name' => 'Test API v1.1.0', // 使用新的名称格式
                'target_version' => '1.1.0'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'configuration',
            ]);
    }

    public function test_controller_deprecate_version()
    {
        $response = $this->actingAs($this->adminUser)
            ->postJson(route('api-configurations.deprecate', $this->baseConfig), [
                'reason' => 'Security vulnerability'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'configuration',
            ]);

        $this->baseConfig->refresh();
        $this->assertTrue($this->baseConfig->is_deprecated);
    }

    public function test_controller_get_migration_path()
    {
        // 创建多个版本
        $this->versionManagerService->createNewVersion($this->baseConfig, '1.1.0');
        $this->versionManagerService->createNewVersion($this->baseConfig, '2.0.0');

        $response = $this->actingAs($this->adminUser)
            ->getJson('/api-configurations/migration-path?' . http_build_query([
                'platform_type' => 'taobao',
                'name' => 'Test API',
                'from_version' => '1.0.0',
                'to_version' => '2.0.0'
            ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'from',
                'to',
                'path',
                'steps',
                'direction',
            ]);
    }

    public function test_controller_get_version_statistics()
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/api-configurations/version-statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'total_configurations',
                'active_configurations',
                'deprecated_configurations',
                'version_distribution',
            ]);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UserManagementController extends Controller
{
    /**
     * 显示用户管理主页
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = User::with(['roles' => function($q) {
            $q->where('is_active', true);
        }]);

        // 搜索过滤
        if ($search = $request->get('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // 角色过滤
        if ($roleFilter = $request->get('role')) {
            $query->whereHas('roles', function($q) use ($roleFilter) {
                $q->where('name', $roleFilter);
            });
        }

        // 状态过滤
        if ($statusFilter = $request->get('status')) {
            $query->where('status', $statusFilter);
        }

        $users = $query->paginate(15);
        $roles = Role::where('is_active', true)->get();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'users' => $users,
                    'roles' => $roles,
                ],
            ]);
        }

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * 显示用户详情
     */
    public function show(User $user): View|JsonResponse
    {
        $user->load(['roles.permissions', 'actionLogs' => function($q) {
            $q->latest()->limit(10);
        }]);

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'permissions' => $user->getAllPermissions(),
                ],
            ]);
        }

        return view('admin.users.show', compact('user'));
    }

    /**
     * 显示角色分配页面
     */
    public function assignRoleForm(User $user): View|JsonResponse
    {
        $roles = Role::where('is_active', true)->with('permissions')->get();
        $userRoles = $user->roles()->where('is_active', true)->pluck('id')->toArray();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'roles' => $roles,
                    'userRoles' => $userRoles,
                ],
            ]);
        }

        return view('admin.users.assign-roles', compact('user', 'roles', 'userRoles'));
    }

    /**
     * 分配角色给用户
     */
    public function assignRoles(Request $request, User $user): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ], [
            'roles.required' => '请选择至少一个角色',
            'roles.*.exists' => '选择的角色不存在',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // 获取角色对象
            $roles = Role::whereIn('id', $request->roles)
                         ->where('is_active', true)
                         ->get();

            if ($roles->count() !== count($request->roles)) {
                throw new \Exception('部分角色不存在或已被禁用');
            }

            // 同步用户角色
            $user->syncRoles($roles->toArray(), Auth::id());

            // 记录操作日志
            $user->actionLogs()->create([
                'action' => 'assign_roles',
                'description' => '管理员分配角色：' . $roles->pluck('display_name')->join(', '),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'performed_by' => Auth::id(),
            ]);

            DB::commit();

            $message = "成功为用户 {$user->display_name} 分配角色";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'data' => [
                        'user' => $user->fresh(['roles']),
                    ],
                ]);
            }

            return redirect()->route('admin.users.show', $user)
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '角色分配失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '角色分配失败：' . $e->getMessage());
        }
    }

    /**
     * 撤销用户角色
     */
    public function revokeRole(Request $request, User $user, Role $role): JsonResponse|RedirectResponse
    {
        try {
            $user->removeRole($role);

            // 记录操作日志
            $user->actionLogs()->create([
                'action' => 'revoke_role',
                'description' => "管理员撤销角色：{$role->display_name}",
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'performed_by' => Auth::id(),
            ]);

            $message = "成功撤销用户 {$user->display_name} 的角色 {$role->display_name}";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                ]);
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '角色撤销失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '角色撤销失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    public function updateStatus(Request $request, User $user): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,inactive,suspended',
        ], [
            'status.required' => '请选择用户状态',
            'status.in' => '无效的用户状态',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator);
        }

        try {
            $oldStatus = $user->status;
            $user->update(['status' => $request->status]);

            // 记录操作日志
            $statusMap = [
                'active' => '启用',
                'inactive' => '禁用',
                'suspended' => '暂停',
            ];

            $user->actionLogs()->create([
                'action' => 'update_status',
                'description' => "管理员更改用户状态：{$statusMap[$oldStatus]} → {$statusMap[$request->status]}",
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'performed_by' => Auth::id(),
            ]);

            $message = "成功更新用户 {$user->display_name} 的状态为{$statusMap[$request->status]}";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'data' => ['user' => $user->fresh()],
                ]);
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '状态更新失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '状态更新失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户权限列表
     */
    public function getUserPermissions(User $user): JsonResponse
    {
        $permissions = $user->getAllPermissions();
        $roles = $user->roles()->where('is_active', true)->with('permissions')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user->only(['id', 'name', 'email', 'display_name']),
                'roles' => $roles,
                'permissions' => $permissions,
                'permission_names' => $permissions->pluck('name')->toArray(),
            ],
        ]);
    }

    /**
     * 批量操作用户
     */
    public function batchAction(Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:assign_role,revoke_role,update_status,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'role_id' => 'required_if:action,assign_role,revoke_role|exists:roles,id',
            'status' => 'required_if:action,update_status|in:active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors(),
                ], 422);
            }
            return back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            $users = User::whereIn('id', $request->user_ids)->get();
            $affectedCount = 0;

            foreach ($users as $user) {
                switch ($request->action) {
                    case 'assign_role':
                        $role = Role::find($request->role_id);
                        $user->assignRole($role, Auth::id());
                        $affectedCount++;
                        break;

                    case 'revoke_role':
                        $role = Role::find($request->role_id);
                        $user->removeRole($role);
                        $affectedCount++;
                        break;

                    case 'update_status':
                        $user->update(['status' => $request->status]);
                        $affectedCount++;
                        break;

                    case 'delete':
                        // 注意：实际项目中可能需要软删除
                        $user->roles()->detach();
                        $user->delete();
                        $affectedCount++;
                        break;
                }
            }

            DB::commit();

            $message = "批量操作完成，影响 {$affectedCount} 个用户";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'affected_count' => $affectedCount,
                ]);
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => '批量操作失败：' . $e->getMessage(),
                ], 500);
            }

            return back()->with('error', '批量操作失败：' . $e->getMessage());
        }
    }
} 
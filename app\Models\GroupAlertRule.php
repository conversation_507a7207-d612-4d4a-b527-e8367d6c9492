<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class GroupAlertRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_group_id',
        'name',
        'description',
        'rule_type',
        'conditions',
        'operator',
        'severity',
        'priority',
        'notification_settings',
        'is_active',
        'override_individual',
        'cooldown_minutes',
        'last_triggered_at',
        'trigger_count',
    ];

    protected $casts = [
        'conditions' => 'array',
        'notification_settings' => 'array',
        'is_active' => 'boolean',
        'override_individual' => 'boolean',
        'cooldown_minutes' => 'integer',
        'trigger_count' => 'integer',
        'last_triggered_at' => 'datetime',
    ];

    /**
     * 所属任务分组
     */
    public function taskGroup(): BelongsTo
    {
        return $this->belongsTo(TaskGroup::class, 'task_group_id');
    }

    /**
     * 告警日志
     */
    public function alertLogs(): HasMany
    {
        return $this->hasMany(AlertLog::class, 'group_alert_rule_id');
    }

    /**
     * 检查是否在冷却期内
     */
    public function isInCooldown(): bool
    {
        if (!$this->last_triggered_at) {
            return false;
        }

        $cooldownEnd = $this->last_triggered_at->addMinutes($this->cooldown_minutes);
        return Carbon::now() < $cooldownEnd;
    }

    /**
     * 记录规则触发
     */
    public function recordTrigger(): void
    {
        $this->increment('trigger_count');
        $this->update(['last_triggered_at' => Carbon::now()]);
    }

    /**
     * 获取规则类型的显示名称
     */
    public function getRuleTypeTextAttribute(): string
    {
        $types = [
            'price_change' => '价格变化',
            'price_threshold' => '价格阈值',
            'availability' => '可用性',
            'custom' => '自定义',
        ];

        return $types[$this->rule_type] ?? $this->rule_type;
    }

    /**
     * 获取严重程度的显示名称
     */
    public function getSeverityTextAttribute(): string
    {
        $severities = [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'critical' => '严重',
        ];

        return $severities[$this->severity] ?? $this->severity;
    }

    /**
     * 获取优先级的显示名称
     */
    public function getPriorityTextAttribute(): string
    {
        $priorities = [
            'low' => '低',
            'medium' => '中',
            'high' => '高',
        ];

        return $priorities[$this->priority] ?? $this->priority;
    }

    /**
     * 检查规则是否适用于指定的任务
     */
    public function appliesToTask(MonitorTask $task): bool
    {
        // 检查任务是否属于此分组
        if ($task->task_group_id !== $this->task_group_id) {
            return false;
        }

        // 检查规则是否启用
        if (!$this->is_active) {
            return false;
        }

        // 如果设置了覆盖个人规则，则适用
        if ($this->override_individual) {
            return true;
        }

        // 检查任务是否有个人规则
        $hasIndividualRules = $task->alertRules()->where('is_active', true)->exists();
        
        // 如果没有个人规则，则适用分组规则
        return !$hasIndividualRules;
    }

    /**
     * 评估规则条件
     */
    public function evaluateConditions(array $data): bool
    {
        if (empty($this->conditions)) {
            return false;
        }

        $results = [];
        
        foreach ($this->conditions as $condition) {
            $results[] = $this->evaluateSingleCondition($condition, $data);
        }

        // 根据操作符组合结果
        if ($this->operator === 'or') {
            return in_array(true, $results);
        }

        return !in_array(false, $results);
    }

    /**
     * 评估单个条件
     */
    private function evaluateSingleCondition(array $condition, array $data): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!isset($data[$field])) {
            return false;
        }

        $actualValue = $data[$field];

        switch ($operator) {
            case '=':
                return $actualValue == $value;
            case '!=':
                return $actualValue != $value;
            case '>':
                return $actualValue > $value;
            case '>=':
                return $actualValue >= $value;
            case '<':
                return $actualValue < $value;
            case '<=':
                return $actualValue <= $value;
            case 'contains':
                return str_contains(strtolower($actualValue), strtolower($value));
            case 'between':
                return $actualValue >= $value['min'] && $actualValue <= $value['max'];
            case 'in':
                return in_array($actualValue, (array) $value);
            default:
                return false;
        }
    }

    /**
     * 作用域：活跃的规则
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：指定分组的规则
     */
    public function scopeForGroup($query, $groupId)
    {
        return $query->where('task_group_id', $groupId);
    }

    /**
     * 作用域：指定严重程度的规则
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * 作用域：非冷却期的规则
     */
    public function scopeNotInCooldown($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_triggered_at')
              ->orWhere('last_triggered_at', '<=', Carbon::now()->subMinutes(60));
        });
    }
}

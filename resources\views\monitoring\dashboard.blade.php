@extends('layouts.app')

@section('title', '系统监控')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">系统监控仪表板</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">首页</a></li>
                        <li class="breadcrumb-item active">系统监控</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="平均响应时间">平均响应时间</h5>
                            <h3 class="my-2 py-1">{{ $overview['response_time']['average'] ?? 0 }}ms</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                过去24小时
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="response-time-chart" data-colors="#00acc1"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="请求总数">请求总数</h5>
                            <h3 class="my-2 py-1">{{ $overview['request_count']['total'] ?? 0 }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                成功率: {{ $overview['request_count']['total'] > 0 ? round(($overview['request_count']['successful'] / $overview['request_count']['total']) * 100, 1) : 0 }}%
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="request-count-chart" data-colors="#5b73e8"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="错误率">错误率</h5>
                            <h3 class="my-2 py-1">{{ $overview['error_rate']['error_rate_percentage'] ?? 0 }}%</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-danger me-2"><i class="mdi mdi-arrow-down-bold"></i></span>
                                错误数: {{ $overview['error_rate']['total_errors'] ?? 0 }}
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="error-rate-chart" data-colors="#f1556c"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="活跃用户">活跃用户</h5>
                            <h3 class="my-2 py-1">{{ $overview['active_users'] ?? 0 }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-info me-2"><i class="mdi mdi-account-multiple"></i></span>
                                过去30分钟
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="active-users-chart" data-colors="#f7b84b"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细监控信息 -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <div class="dropdown float-end">
                        <a href="#" class="dropdown-toggle arrow-none card-drop" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="mdi mdi-dots-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a href="javascript:void(0);" class="dropdown-item">刷新数据</a>
                            <a href="javascript:void(0);" class="dropdown-item">导出报告</a>
                        </div>
                    </div>
                    <h4 class="header-title mb-3">系统性能趋势</h4>
                    <div id="performance-chart" class="apex-charts" data-colors="#727cf5"></div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">系统状态</h4>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-1">内存使用率</p>
                            <p class="text-muted mb-1">{{ $overview['memory_usage']['usage_percentage'] ?? 0 }}%</p>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar" role="progressbar" style="width: {{ $overview['memory_usage']['usage_percentage'] ?? 0 }}%" aria-valuenow="{{ $overview['memory_usage']['usage_percentage'] ?? 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-1">缓存命中率</p>
                            <p class="text-muted mb-1">{{ $overview['cache_hit_rate']['hit_rate_percentage'] ?? 0 }}%</p>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ $overview['cache_hit_rate']['hit_rate_percentage'] ?? 0 }}%" aria-valuenow="{{ $overview['cache_hit_rate']['hit_rate_percentage'] ?? 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <p class="text-muted mb-1">数据库连接时间</p>
                            <p class="text-muted mb-1">{{ $overview['database_performance']['connection_time_ms'] ?? 0 }}ms</p>
                        </div>
                        <div class="progress progress-sm">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ min(($overview['database_performance']['connection_time_ms'] ?? 0) / 10, 100) }}%" aria-valuenow="{{ $overview['database_performance']['connection_time_ms'] ?? 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshSystemStatus()">
                            <i class="mdi mdi-refresh me-1"></i>刷新状态
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近活动日志 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">最近活动日志</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>事件</th>
                                    <th>详情</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentLogs as $log)
                                <tr>
                                    <td>{{ $log->created_at->format('Y-m-d H:i:s') }}</td>
                                    <td>{{ $log->user->name ?? '系统' }}</td>
                                    <td>
                                        <span class="badge badge-soft-primary">{{ $log->getEventDisplayName() }}</span>
                                    </td>
                                    <td>{{ $log->getModelDisplayName() }}</td>
                                    <td>{{ $log->ip_address }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted">暂无活动日志</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshSystemStatus() {
    // 刷新系统状态的JavaScript代码
    location.reload();
}
</script>
@endsection

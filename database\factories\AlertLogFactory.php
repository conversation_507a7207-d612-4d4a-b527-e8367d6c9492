<?php

namespace Database\Factories;

use App\Models\AlertLog;
use App\Models\User;
use App\Models\AlertRule;
use App\Models\MonitorTask;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AlertLog>
 */
class AlertLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AlertLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'rule_id' => AlertRule::factory(),
            'task_id' => MonitorTask::factory(),
            'alert_title' => $this->faker->sentence(4),
            'message' => $this->faker->paragraph,
            'severity' => $this->faker->randomElement(['low', 'medium', 'high', 'critical']),
            'status' => 'unread',
            'notification_method' => 'email',
        ];
    }
} 
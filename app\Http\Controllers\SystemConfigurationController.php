<?php

namespace App\Http\Controllers;

use App\Models\SystemConfiguration;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SystemConfigurationController extends Controller
{
    /**
     * 显示系统配置页面
     */
    public function index()
    {
        $configurations = SystemConfiguration::ordered()->get()->groupBy('category');

        return view('system-configurations.index', compact('configurations'));
    }

    /**
     * 获取指定分类的配置
     */
    public function getByCategory(string $category): JsonResponse
    {
        $configurations = SystemConfiguration::byCategory($category)
            ->ordered()
            ->get();

        return response()->json($configurations);
    }

    /**
     * 获取公开配置（前端可访问）
     */
    public function getPublicConfigurations(): JsonResponse
    {
        $configurations = SystemConfiguration::getAllPublic();

        return response()->json($configurations);
    }

    /**
     * 批量更新配置
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        try {
            $configurations = $request->input('configurations', []);
            $updated = [];
            $errors = [];

            foreach ($configurations as $key => $value) {
                $config = SystemConfiguration::where('key', $key)->first();

                if (!$config) {
                    $errors[$key] = '配置项不存在';
                    continue;
                }

                // 验证配置值
                $validation = $this->validateConfigValue($config, $value);
                if (!$validation['valid']) {
                    $errors[$key] = $validation['message'];
                    continue;
                }

                // 处理文件上传
                if ($config->type === SystemConfiguration::TYPE_FILE && $request->hasFile($key)) {
                    $value = $this->handleFileUpload($request->file($key), $key);
                }

                $config->update(['value' => $value]);
                $updated[$key] = $value;

                Log::info('系统配置已更新', [
                    'key' => $key,
                    'old_value' => $config->getOriginal('value'),
                    'new_value' => $value,
                    'user_id' => auth()->id(),
                ]);
            }

            // 清除缓存
            SystemConfiguration::clearCache();

            return response()->json([
                'message' => '配置更新成功',
                'updated' => $updated,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            Log::error('批量更新系统配置失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => '配置更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新单个配置
     */
    public function update(Request $request, string $key): JsonResponse
    {
        try {
            $config = SystemConfiguration::where('key', $key)->firstOrFail();
            $value = $request->input('value');

            // 验证配置值
            $validation = $this->validateConfigValue($config, $value);
            if (!$validation['valid']) {
                return response()->json([
                    'message' => '验证失败',
                    'error' => $validation['message']
                ], 422);
            }

            // 处理文件上传
            if ($config->type === SystemConfiguration::TYPE_FILE && $request->hasFile('value')) {
                $value = $this->handleFileUpload($request->file('value'), $key);
            }

            $oldValue = $config->value;
            $config->update(['value' => $value]);

            // 清除缓存
            SystemConfiguration::clearCache($key);

            Log::info('系统配置已更新', [
                'key' => $key,
                'old_value' => $oldValue,
                'new_value' => $value,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => '配置更新成功',
                'configuration' => $config->fresh(),
            ]);

        } catch (\Exception $e) {
            Log::error('更新系统配置失败', [
                'key' => $key,
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => '配置更新失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试邮件配置
     */
    public function testEmailConfiguration(Request $request): JsonResponse
    {
        try {
            $testEmail = $request->input('test_email', auth()->user()->email);

            // 获取邮件配置
            $emailConfigs = SystemConfiguration::getByCategory('email');

            if (empty($emailConfigs)) {
                return response()->json([
                    'message' => '邮件配置不完整',
                    'success' => false
                ], 400);
            }

            // 临时设置邮件配置
            config([
                'mail.mailers.smtp.host' => $emailConfigs['smtp_host'] ?? '',
                'mail.mailers.smtp.port' => $emailConfigs['smtp_port'] ?? 587,
                'mail.mailers.smtp.username' => $emailConfigs['smtp_username'] ?? '',
                'mail.mailers.smtp.password' => $emailConfigs['smtp_password'] ?? '',
                'mail.mailers.smtp.encryption' => $emailConfigs['smtp_encryption'] ?? 'tls',
                'mail.from.address' => $emailConfigs['mail_from_address'] ?? '',
                'mail.from.name' => $emailConfigs['mail_from_name'] ?? '',
            ]);

            // 发送测试邮件
            Mail::raw('这是一封测试邮件，用于验证邮件服务器配置是否正确。', function ($message) use ($testEmail) {
                $message->to($testEmail)
                    ->subject('系统邮件配置测试');
            });

            return response()->json([
                'message' => '测试邮件发送成功',
                'success' => true,
                'test_email' => $testEmail
            ]);

        } catch (\Exception $e) {
            Log::error('邮件配置测试失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'message' => '邮件配置测试失败',
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重置配置为默认值
     */
    public function resetToDefault(string $key): JsonResponse
    {
        try {
            $config = SystemConfiguration::where('key', $key)->firstOrFail();

            // 获取默认值（这里可以根据需要实现默认值逻辑）
            $defaultValue = $this->getDefaultValue($key);

            $config->update(['value' => $defaultValue]);
            SystemConfiguration::clearCache($key);

            return response()->json([
                'message' => '配置已重置为默认值',
                'configuration' => $config->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '重置配置失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出配置
     */
    public function exportConfigurations(): JsonResponse
    {
        try {
            $configurations = SystemConfiguration::all()
                ->map(function ($config) {
                    return [
                        'key' => $config->key,
                        'category' => $config->category,
                        'value' => $config->is_encrypted ? '***' : $config->value,
                        'type' => $config->type,
                        'label' => $config->label,
                        'description' => $config->description,
                    ];
                });

            return response()->json([
                'configurations' => $configurations,
                'exported_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => '导出配置失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证配置值
     */
    private function validateConfigValue(SystemConfiguration $config, $value): array
    {
        $rules = $config->validation_rules ?? [];

        if (empty($rules)) {
            return ['valid' => true];
        }

        $validator = Validator::make(
            ['value' => $value],
            ['value' => $rules]
        );

        if ($validator->fails()) {
            return [
                'valid' => false,
                'message' => $validator->errors()->first('value')
            ];
        }

        return ['valid' => true];
    }

    /**
     * 处理文件上传
     */
    private function handleFileUpload($file, string $configKey): string
    {
        $path = $file->store('system-configs', 'public');

        // 删除旧文件
        $oldPath = SystemConfiguration::getValue($configKey);
        if ($oldPath && Storage::disk('public')->exists($oldPath)) {
            Storage::disk('public')->delete($oldPath);
        }

        return $path;
    }

    /**
     * 获取默认值
     */
    private function getDefaultValue(string $key)
    {
        $defaults = [
            'system_name' => '电商市场动态监控系统',
            'default_monitoring_frequency' => 60,
            'data_retention_period' => 365,
            'concurrent_worker_count' => 4,
            'memory_limit' => '512M',
            'database_connection_pool_size' => 10,
            'cache_ttl' => 3600,
            'smtp_port' => 587,
            'smtp_encryption' => 'tls',
        ];

        return $defaults[$key] ?? null;
    }
}

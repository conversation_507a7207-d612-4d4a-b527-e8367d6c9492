<?php

namespace App\Services;

use App\Models\ApiConfiguration;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Carbon\Carbon;

class ApiHealthMonitorService
{
    protected $timeout = 10; // seconds
    protected $retryAttempts = 3;
    
    /**
     * Perform health check for a single API configuration
     *
     * @param ApiConfiguration $config
     * @return array
     */
    public function performHealthCheck(ApiConfiguration $config): array
    {
        if (!$config->health_check_endpoint) {
            return [
                'status' => 'unknown',
                'message' => 'No health check endpoint configured',
                'response_time' => null,
                'timestamp' => now()
            ];
        }
        
        $startTime = microtime(true);
        $attempts = 0;
        $lastError = null;
        
        while ($attempts < $this->retryAttempts) {
            $attempts++;
            
            try {
                $response = $this->makeHealthCheckRequest($config);
                $responseTime = round((microtime(true) - $startTime) * 1000); // milliseconds
                
                $result = [
                    'status' => $response->successful() ? 'healthy' : 'unhealthy',
                    'message' => $response->successful() 
                        ? 'API endpoint is responding normally' 
                        : "HTTP {$response->status()}: {$response->reason()}",
                    'response_time' => $responseTime,
                    'http_status' => $response->status(),
                    'timestamp' => now(),
                    'attempts' => $attempts
                ];
                
                // Update the configuration record
                $this->updateConfigurationHealth($config, $result);
                
                Log::info("Health check completed for API configuration", [
                    'config_id' => $config->id,
                    'config_name' => $config->name,
                    'status' => $result['status'],
                    'response_time' => $responseTime,
                    'attempts' => $attempts
                ]);
                
                return $result;
                
            } catch (ConnectionException $e) {
                $lastError = "Connection failed: " . $e->getMessage();
                Log::warning("Health check connection failed", [
                    'config_id' => $config->id,
                    'config_name' => $config->name,
                    'attempt' => $attempts,
                    'error' => $lastError
                ]);
                
                if ($attempts < $this->retryAttempts) {
                    sleep(1); // Wait 1 second before retry
                }
                
            } catch (RequestException $e) {
                $lastError = "Request failed: " . $e->getMessage();
                Log::warning("Health check request failed", [
                    'config_id' => $config->id,
                    'config_name' => $config->name,
                    'attempt' => $attempts,
                    'error' => $lastError
                ]);
                break; // Don't retry on request exceptions
                
            } catch (\Exception $e) {
                $lastError = "Unexpected error: " . $e->getMessage();
                Log::error("Health check unexpected error", [
                    'config_id' => $config->id,
                    'config_name' => $config->name,
                    'attempt' => $attempts,
                    'error' => $lastError
                ]);
                break;
            }
        }
        
        // All attempts failed
        $responseTime = round((microtime(true) - $startTime) * 1000);
        $result = [
            'status' => 'unhealthy',
            'message' => $lastError ?: 'Health check failed after multiple attempts',
            'response_time' => $responseTime,
            'timestamp' => now(),
            'attempts' => $attempts
        ];
        
        $this->updateConfigurationHealth($config, $result);
        
        return $result;
    }
    
    /**
     * Make HTTP request for health check
     *
     * @param ApiConfiguration $config
     * @return \Illuminate\Http\Client\Response
     */
    protected function makeHealthCheckRequest(ApiConfiguration $config)
    {
        $http = Http::timeout($this->timeout);
        
        // Add authentication if configured
        if ($config->auth_type && $config->decryptedAuthData) {
            $authData = $config->decryptedAuthData;
            
            switch ($config->auth_type) {
                case 'bearer_token':
                    if (isset($authData['token'])) {
                        $http = $http->withToken($authData['token']);
                    }
                    break;
                    
                case 'api_key':
                    if (isset($authData['key']) && isset($authData['header'])) {
                        $http = $http->withHeaders([
                            $authData['header'] => $authData['key']
                        ]);
                    }
                    break;
                    
                case 'basic_auth':
                    if (isset($authData['username']) && isset($authData['password'])) {
                        $http = $http->withBasicAuth($authData['username'], $authData['password']);
                    }
                    break;
            }
        }
        
        // Add custom headers if specified
        if ($config->metadata && isset($config->metadata['health_check_headers'])) {
            $http = $http->withHeaders($config->metadata['health_check_headers']);
        }
        
        return $http->get($config->health_check_endpoint);
    }
    
    /**
     * Update configuration with health check results
     *
     * @param ApiConfiguration $config
     * @param array $result
     * @return void
     */
    protected function updateConfigurationHealth(ApiConfiguration $config, array $result): void
    {
        $config->update([
            'health_status' => $result['status'],
            'last_health_check_at' => $result['timestamp'],
            'response_time_avg' => $this->calculateAverageResponseTime($config, $result['response_time'] ?? null)
        ]);
    }
    
    /**
     * Calculate average response time
     *
     * @param ApiConfiguration $config
     * @param int|null $newResponseTime
     * @return int|null
     */
    protected function calculateAverageResponseTime(ApiConfiguration $config, ?int $newResponseTime): ?int
    {
        if ($newResponseTime === null) {
            return $config->response_time_avg;
        }
        
        if ($config->response_time_avg === null) {
            return $newResponseTime;
        }
        
        // Simple moving average (could be enhanced with more sophisticated algorithms)
        return round(($config->response_time_avg + $newResponseTime) / 2);
    }
    
    /**
     * Perform bulk health checks for multiple configurations
     *
     * @param \Illuminate\Support\Collection $configurations
     * @return array
     */
    public function performBulkHealthCheck($configurations): array
    {
        $results = [];
        $totalChecked = 0;
        $healthyCount = 0;
        $unhealthyCount = 0;
        
        foreach ($configurations as $config) {
            if (!$config->health_check_endpoint || !$config->is_active) {
                continue;
            }
            
            $result = $this->performHealthCheck($config);
            $results[] = array_merge($result, [
                'config_id' => $config->id,
                'config_name' => $config->name
            ]);
            
            $totalChecked++;
            
            if ($result['status'] === 'healthy') {
                $healthyCount++;
            } else {
                $unhealthyCount++;
            }
        }
        
        Log::info("Bulk health check completed", [
            'total_checked' => $totalChecked,
            'healthy' => $healthyCount,
            'unhealthy' => $unhealthyCount
        ]);
        
        return [
            'summary' => [
                'total_checked' => $totalChecked,
                'healthy' => $healthyCount,
                'unhealthy' => $unhealthyCount,
                'timestamp' => now()
            ],
            'results' => $results
        ];
    }
    
    /**
     * Get health statistics for dashboard
     *
     * @return array
     */
    public function getHealthStatistics(): array
    {
        $totalConfigs = ApiConfiguration::count();
        $activeConfigs = ApiConfiguration::active()->count();
        $healthyConfigs = ApiConfiguration::where('health_status', 'healthy')->count();
        $unhealthyConfigs = ApiConfiguration::where('health_status', 'unhealthy')->count();
        $unknownConfigs = ApiConfiguration::where('health_status', 'unknown')->count();
        
        // Recent health checks (last 24 hours)
        $recentHealthChecks = ApiConfiguration::where('last_health_check_at', '>=', now()->subDay())
            ->count();
        
        // Average response time
        $avgResponseTime = ApiConfiguration::whereNotNull('response_time_avg')
            ->avg('response_time_avg');
        
        return [
            'total_configurations' => $totalConfigs,
            'active_configurations' => $activeConfigs,
            'health_status' => [
                'healthy' => $healthyConfigs,
                'unhealthy' => $unhealthyConfigs,
                'unknown' => $unknownConfigs
            ],
            'recent_health_checks_24h' => $recentHealthChecks,
            'average_response_time_ms' => $avgResponseTime ? round($avgResponseTime) : null,
            'health_percentage' => $totalConfigs > 0 ? round(($healthyConfigs / $totalConfigs) * 100, 1) : 0
        ];
    }
    
    /**
     * Set timeout for health checks
     *
     * @param int $seconds
     * @return self
     */
    public function setTimeout(int $seconds): self
    {
        $this->timeout = $seconds;
        return $this;
    }
    
    /**
     * Set retry attempts for health checks
     *
     * @param int $attempts
     * @return self
     */
    public function setRetryAttempts(int $attempts): self
    {
        $this->retryAttempts = $attempts;
        return $this;
    }

    /**
     * Check health for a single configuration (alias for performHealthCheck)
     *
     * @param ApiConfiguration $config
     * @return array
     */
    public function checkHealth(ApiConfiguration $config): array
    {
        if (!$config->health_check_endpoint) {
            return [
                'status' => 'unknown',
                'message' => 'No health check endpoint configured',
                'response_time' => null,
                'http_status' => null
            ];
        }

        $url = rtrim($config->base_url, '/') . '/' . ltrim($config->health_check_endpoint, '/');
        
        try {
            $startTime = microtime(true);
            $response = Http::timeout(30)->get($url);
            $responseTime = round((microtime(true) - $startTime) * 1000);
            
            $status = $response->successful() ? 'healthy' : 'unhealthy';
            
            // Update the configuration
            $config->update([
                'health_status' => $status,
                'last_health_check_at' => now()
            ]);
            
            return [
                'status' => $status,
                'response_time' => $responseTime,
                'http_status' => $response->status(),
                'message' => $response->successful() ? 'API is healthy' : 'API is unhealthy'
            ];
            
        } catch (\Exception $e) {
            $config->update([
                'health_status' => 'unhealthy',
                'last_health_check_at' => now()
            ]);
            
            return [
                'status' => 'unhealthy',
                'response_time' => null,
                'http_status' => null,
                'message' => 'Health check failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Bulk health check for multiple configurations
     *
     * @param \Illuminate\Support\Collection $configurations
     * @return array
     */
    public function bulkHealthCheck($configurations): array
    {
        $results = [];
        
        foreach ($configurations as $config) {
            $result = $this->checkHealth($config);
            $results[] = [
                'id' => $config->id,
                'name' => $config->name,
                'status' => $result['status'],
                'response_time' => $result['response_time'],
                'http_status' => $result['http_status'],
                'message' => $result['message']
            ];
        }
        
        return $results;
    }
} 
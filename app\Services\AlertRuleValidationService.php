<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductSku;
use App\Models\MonitorTask;
use App\Models\AlertRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

/**
 * 警报规则验证服务
 * 
 * 提供全面的服务器端验证逻辑，确保警报规则参数的数据完整性和一致性
 */
class AlertRuleValidationService
{
    /**
     * 验证警报规则的完整性
     */
    public function validateAlertRule(array $data, ?int $ruleId = null): array
    {
        $errors = [];

        // 基础字段验证
        $errors = array_merge($errors, $this->validateBasicFields($data));

        // 类型特定验证
        if (isset($data['type'])) {
            $errors = array_merge($errors, $this->validateTypeSpecificFields($data));
        }

        // 关联资源验证
        $errors = array_merge($errors, $this->validateRelatedResources($data));

        // 业务逻辑验证
        $errors = array_merge($errors, $this->validateBusinessLogic($data, $ruleId));

        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

        return $data;
    }

    /**
     * 验证基础字段
     */
    private function validateBasicFields(array $data): array
    {
        $errors = [];

        // 规则名称验证
        if (empty($data['rule_name'])) {
            $errors['rule_name'][] = '规则名称不能为空';
        } elseif (strlen($data['rule_name']) > 255) {
            $errors['rule_name'][] = '规则名称长度不能超过255个字符';
        }

        // 规则类型验证
        if (empty($data['type'])) {
            $errors['type'][] = '警报类型不能为空';
        } elseif (!in_array($data['type'], array_keys(AlertRule::getAlertTypes()))) {
            $errors['type'][] = '无效的警报类型';
        }

        // 范围验证
        if (empty($data['scope'])) {
            $errors['scope'][] = '适用范围不能为空';
        } elseif (!in_array($data['scope'], array_keys(AlertRule::getScopeOptions()))) {
            $errors['scope'][] = '无效的适用范围';
        }

        // 比较操作符验证
        if (empty($data['comparison'])) {
            $errors['comparison'][] = '比较操作符不能为空';
        } elseif (!in_array($data['comparison'], array_keys(AlertRule::getComparisonOptions()))) {
            $errors['comparison'][] = '无效的比较操作符';
        }

        // 通知方式验证
        if (empty($data['notification_method'])) {
            $errors['notification_method'][] = '通知方式不能为空';
        } elseif (!in_array($data['notification_method'], array_keys(AlertRule::getNotificationMethods()))) {
            $errors['notification_method'][] = '无效的通知方式';
        }

        // 冷却时间验证
        if (isset($data['cooldown_minutes'])) {
            if (!is_numeric($data['cooldown_minutes']) || $data['cooldown_minutes'] < 1 || $data['cooldown_minutes'] > 10080) {
                $errors['cooldown_minutes'][] = '冷却时间必须在1-10080分钟之间';
            }
        }

        return $errors;
    }

    /**
     * 验证类型特定字段
     */
    private function validateTypeSpecificFields(array $data): array
    {
        $errors = [];
        $type = $data['type'];

        switch ($type) {
            case AlertRule::TYPE_PROMOTION_PRICE_DEVIATION:
                $errors = array_merge($errors, $this->validatePromotionPriceDeviation($data));
                break;

            case AlertRule::TYPE_CHANNEL_PRICE_DEVIATION:
                $errors = array_merge($errors, $this->validateChannelPriceDeviation($data));
                break;

            case AlertRule::TYPE_PRODUCT_STATUS_CHANGE:
                $errors = array_merge($errors, $this->validateProductStatusChange($data));
                break;

            case AlertRule::TYPE_INVENTORY_ANOMALY:
                $errors = array_merge($errors, $this->validateInventoryAnomaly($data));
                break;

            case AlertRule::TYPE_DATA_UPDATE_ANOMALY:
                $errors = array_merge($errors, $this->validateDataUpdateAnomaly($data));
                break;

            case AlertRule::TYPE_PRICE_DROP:
            case AlertRule::TYPE_PRICE_RISE:
                $errors = array_merge($errors, $this->validatePriceChange($data));
                break;

            case AlertRule::TYPE_STOCK_CHANGE:
                $errors = array_merge($errors, $this->validateStockChange($data));
                break;

            case AlertRule::TYPE_CUSTOM:
                $errors = array_merge($errors, $this->validateCustomRule($data));
                break;
        }

        return $errors;
    }

    /**
     * 验证促销价偏差率警报
     */
    private function validatePromotionPriceDeviation(array $data): array
    {
        $errors = [];

        if (empty($data['promotion_deviation_threshold'])) {
            $errors['promotion_deviation_threshold'][] = '促销价偏差率阈值不能为空';
        } elseif (!is_numeric($data['promotion_deviation_threshold']) || 
                   $data['promotion_deviation_threshold'] < 0 || 
                   $data['promotion_deviation_threshold'] > 100) {
            $errors['promotion_deviation_threshold'][] = '促销价偏差率阈值必须在0-100%之间';
        }

        return $errors;
    }

    /**
     * 验证渠道价偏差率警报
     */
    private function validateChannelPriceDeviation(array $data): array
    {
        $errors = [];

        if (empty($data['channel_deviation_threshold'])) {
            $errors['channel_deviation_threshold'][] = '渠道价偏差率阈值不能为空';
        } elseif (!is_numeric($data['channel_deviation_threshold']) || 
                   $data['channel_deviation_threshold'] < 0 || 
                   $data['channel_deviation_threshold'] > 100) {
            $errors['channel_deviation_threshold'][] = '渠道价偏差率阈值必须在0-100%之间';
        }

        if (empty($data['official_price'])) {
            $errors['official_price'][] = '官方指导价不能为空';
        } elseif (!is_numeric($data['official_price']) || $data['official_price'] < 0) {
            $errors['official_price'][] = '官方指导价必须大于等于0';
        }

        return $errors;
    }

    /**
     * 验证商品状态变化警报
     */
    private function validateProductStatusChange(array $data): array
    {
        $errors = [];

        if (empty($data['product_id'])) {
            $errors['product_id'][] = '商品ID不能为空';
        }

        if (empty($data['status_change_type'])) {
            $errors['status_change_type'][] = '状态变化类型不能为空';
        } elseif (!in_array($data['status_change_type'], ['on_shelf', 'off_shelf', 'any'])) {
            $errors['status_change_type'][] = '无效的状态变化类型';
        }

        return $errors;
    }

    /**
     * 验证库存异常警报
     */
    private function validateInventoryAnomaly(array $data): array
    {
        $errors = [];

        if (empty($data['product_id'])) {
            $errors['product_id'][] = '商品ID不能为空';
        }

        if (!isset($data['inventory_threshold'])) {
            $errors['inventory_threshold'][] = '库存阈值不能为空';
        } elseif (!is_numeric($data['inventory_threshold']) || $data['inventory_threshold'] < 0) {
            $errors['inventory_threshold'][] = '库存阈值必须大于等于0';
        }

        return $errors;
    }

    /**
     * 验证数据更新异常警报
     */
    private function validateDataUpdateAnomaly(array $data): array
    {
        $errors = [];

        if (empty($data['data_update_hours_threshold'])) {
            $errors['data_update_hours_threshold'][] = '数据更新异常阈值不能为空';
        } elseif (!is_numeric($data['data_update_hours_threshold']) || 
                   $data['data_update_hours_threshold'] < 1 || 
                   $data['data_update_hours_threshold'] > 168) {
            $errors['data_update_hours_threshold'][] = '数据更新异常阈值必须在1-168小时之间';
        }

        return $errors;
    }

    /**
     * 验证价格变化警报
     */
    private function validatePriceChange(array $data): array
    {
        $errors = [];

        if (empty($data['threshold'])) {
            $errors['threshold'][] = '价格阈值不能为空';
        } elseif (!is_numeric($data['threshold']) || $data['threshold'] < 0) {
            $errors['threshold'][] = '价格阈值必须大于等于0';
        }

        if (isset($data['percentage_threshold'])) {
            if (!is_numeric($data['percentage_threshold']) || 
                $data['percentage_threshold'] < 0 || 
                $data['percentage_threshold'] > 100) {
                $errors['percentage_threshold'][] = '百分比阈值必须在0-100%之间';
            }
        }

        return $errors;
    }

    /**
     * 验证库存变化警报
     */
    private function validateStockChange(array $data): array
    {
        $errors = [];

        if (!isset($data['inventory_threshold'])) {
            $errors['inventory_threshold'][] = '库存变化阈值不能为空';
        } elseif (!is_numeric($data['inventory_threshold']) || $data['inventory_threshold'] < 0) {
            $errors['inventory_threshold'][] = '库存变化阈值必须大于等于0';
        }

        return $errors;
    }

    /**
     * 验证自定义警报
     */
    private function validateCustomRule(array $data): array
    {
        $errors = [];

        if (empty($data['conditions']) && empty($data['alert_parameters'])) {
            $errors['conditions'][] = '自定义警报必须设置条件或参数';
        }

        if (isset($data['conditions']) && !is_array($data['conditions'])) {
            $errors['conditions'][] = '条件配置必须是数组格式';
        }

        if (isset($data['alert_parameters']) && !is_array($data['alert_parameters'])) {
            $errors['alert_parameters'][] = '警报参数必须是数组格式';
        }

        return $errors;
    }

    /**
     * 验证关联资源
     */
    private function validateRelatedResources(array $data): array
    {
        $errors = [];

        // 验证监控任务
        if (!empty($data['task_id'])) {
            $task = MonitorTask::find($data['task_id']);
            if (!$task) {
                $errors['task_id'][] = '指定的监控任务不存在';
            } elseif (!$task->is_enabled) {
                $errors['task_id'][] = '指定的监控任务已禁用，无法创建警报规则';
            }
        }

        // 验证产品
        if (!empty($data['product_id'])) {
            $product = Product::find($data['product_id']);
            if (!$product) {
                $errors['product_id'][] = '指定的产品不存在';
            } else {
                // 验证产品状态（如果有状态字段）
                if (isset($product->status) && $product->status !== 'active') {
                    $errors['product_id'][] = '指定的产品状态异常，无法创建警报规则';
                }
            }
        }

        // 验证产品SKU
        if (!empty($data['product_sku_id'])) {
            $sku = ProductSku::find($data['product_sku_id']);
            if (!$sku) {
                $errors['product_sku_id'][] = '指定的产品SKU不存在';
            } else {
                // 验证SKU与产品的关系
                if (!empty($data['product_id']) && $sku->product_id !== (int)$data['product_id']) {
                    $errors['product_sku_id'][] = '指定的SKU不属于该产品';
                }

                // 验证SKU状态（如果有状态字段）
                if (isset($sku->status) && $sku->status !== 'active') {
                    $errors['product_sku_id'][] = '指定的产品SKU状态异常，无法创建警报规则';
                }
            }
        }

        // 验证渠道ID
        if (!empty($data['channel_id'])) {
            // 这里可以添加渠道验证逻辑，比如检查渠道是否在允许的列表中
            $allowedChannels = $this->getAllowedChannels();
            if (!in_array($data['channel_id'], $allowedChannels)) {
                $errors['channel_id'][] = '指定的渠道ID无效';
            }
        }

        return $errors;
    }

    /**
     * 验证业务逻辑
     */
    private function validateBusinessLogic(array $data, ?int $ruleId = null): array
    {
        $errors = [];

        // 检查重复规则
        $errors = array_merge($errors, $this->checkDuplicateRules($data, $ruleId));

        // 检查规则冲突
        $errors = array_merge($errors, $this->checkRuleConflicts($data, $ruleId));

        // 检查资源限制
        $errors = array_merge($errors, $this->checkResourceLimits($data));

        return $errors;
    }

    /**
     * 检查重复规则
     */
    private function checkDuplicateRules(array $data, ?int $ruleId = null): array
    {
        $errors = [];

        // 只有在必要字段存在时才进行重复检查
        if (!isset($data['rule_name']) || !isset($data['type'])) {
            return $errors;
        }

        $query = AlertRule::where('rule_name', $data['rule_name'])
                          ->where('type', $data['type'])
                          ->where('user_id', auth()->id());

        if ($ruleId) {
            $query->where('id', '!=', $ruleId);
        }

        // 根据类型添加额外的重复检查条件
        if (isset($data['product_id'])) {
            $query->where('product_id', $data['product_id']);
        }

        if (isset($data['product_sku_id'])) {
            $query->where('product_sku_id', $data['product_sku_id']);
        }

        if ($query->exists()) {
            $errors['rule_name'][] = '已存在相同的警报规则';
        }

        return $errors;
    }

    /**
     * 检查规则冲突
     */
    private function checkRuleConflicts(array $data, ?int $ruleId = null): array
    {
        $errors = [];

        // 只有在type字段存在时才进行冲突检查
        if (!isset($data['type'])) {
            return $errors;
        }

        // 检查价格相关规则的冲突
        if (in_array($data['type'], [AlertRule::TYPE_PRICE_DROP, AlertRule::TYPE_PRICE_RISE])) {
            $conflictQuery = AlertRule::where('user_id', auth()->id())
                                   ->where('is_active', true)
                                   ->whereIn('type', [AlertRule::TYPE_PRICE_DROP, AlertRule::TYPE_PRICE_RISE]);

            if ($ruleId) {
                $conflictQuery->where('id', '!=', $ruleId);
            }

            if (isset($data['product_id'])) {
                $conflictQuery->where('product_id', $data['product_id']);
            }

            if ($conflictQuery->exists()) {
                $conflictRule = $conflictQuery->first();
                if ($conflictRule->type !== $data['type']) {
                    $errors['type'][] = "与现有的{$conflictRule->rule_name}规则冲突";
                }
            }
        }

        return $errors;
    }

    /**
     * 检查资源限制
     */
    private function checkResourceLimits(array $data): array
    {
        $errors = [];

        // 检查用户警报规则数量限制
        $userRuleCount = AlertRule::where('user_id', auth()->id())->count();
        $maxRulesPerUser = config('alert.max_rules_per_user', 50);

        if ($userRuleCount >= $maxRulesPerUser) {
            $errors['user_limit'][] = "警报规则数量已达到限制({$maxRulesPerUser}条)";
        }

        // 检查单个产品的警报规则数量限制
        if (!empty($data['product_id'])) {
            $productRuleCount = AlertRule::where('user_id', auth()->id())
                                        ->where('product_id', $data['product_id'])
                                        ->count();
            $maxRulesPerProduct = config('alert.max_rules_per_product', 10);

            if ($productRuleCount >= $maxRulesPerProduct) {
                $errors['product_id'][] = "该产品的警报规则数量已达到限制({$maxRulesPerProduct}条)";
            }
        }

        return $errors;
    }

    /**
     * 获取允许的渠道列表
     */
    private function getAllowedChannels(): array
    {
        // 这里可以从配置文件或数据库中获取允许的渠道列表
        return [
            'taobao',
            'tmall',
            'jd',
            'pdd',
            'vip',
            'suning',
            'amazon',
            'official'
        ];
    }

    /**
     * 验证JSON字段格式
     */
    public function validateJsonField(array $data, string $field): array
    {
        $errors = [];

        if (isset($data[$field])) {
            if (is_string($data[$field])) {
                $decoded = json_decode($data[$field], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $errors[$field][] = "{$field}字段JSON格式无效";
                }
            } elseif (!is_array($data[$field])) {
                $errors[$field][] = "{$field}字段必须是数组或有效的JSON字符串";
            }
        }

        return $errors;
    }

    /**
     * 验证通知设置
     */
    public function validateNotificationSettings(array $data): array
    {
        $errors = [];

        if (isset($data['notification_settings']) && is_array($data['notification_settings'])) {
            $settings = $data['notification_settings'];

            // 验证邮件设置
            if (isset($settings['email'])) {
                if (isset($settings['email']['recipients']) && !is_array($settings['email']['recipients'])) {
                    $errors['notification_settings.email.recipients'][] = '邮件接收者列表必须是数组';
                }

                if (isset($settings['email']['recipients'])) {
                    foreach ($settings['email']['recipients'] as $email) {
                        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            $errors['notification_settings.email.recipients'][] = "无效的邮件地址：{$email}";
                        }
                    }
                }
            }

            // 验证短信设置
            if (isset($settings['sms'])) {
                if (isset($settings['sms']['phones']) && !is_array($settings['sms']['phones'])) {
                    $errors['notification_settings.sms.phones'][] = '短信接收者列表必须是数组';
                }

                if (isset($settings['sms']['phones'])) {
                    foreach ($settings['sms']['phones'] as $phone) {
                        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                            $errors['notification_settings.sms.phones'][] = "无效的手机号码：{$phone}";
                        }
                    }
                }
            }
        }

        return $errors;
    }
} 
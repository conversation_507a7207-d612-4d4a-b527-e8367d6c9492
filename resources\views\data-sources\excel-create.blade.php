@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-file-excel text-success me-2"></i>
                        Excel批量导入数据源
                    </h4>
                    <a href="{{ route('data-sources.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>

                <div class="card-body">
                    {{-- 使用说明 --}}
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>使用说明
                        </h5>
                        <ul class="mb-0">
                            <li>支持 <code>.xlsx</code>、<code>.xls</code>、<code>.csv</code> 格式文件</li>
                            <li>文件大小限制：10MB</li>
                            <li>第一行为表头，从第二行开始为数据</li>
                            <li>支持的平台：淘宝、天猫、京东、拼多多</li>
                            <li><strong>建议先下载模板，按照格式填写数据</strong></li>
                        </ul>
                    </div>

                    {{-- 下载模板 --}}
                    <div class="text-center mb-4">
                        <a href="{{ route('data-sources.download-template') }}" 
                           class="btn btn-success btn-lg">
                            <i class="fas fa-download me-2"></i>
                            下载Excel模板
                        </a>
                        <p class="text-muted mt-2">建议先下载模板，了解格式要求</p>
                    </div>

                    <hr class="my-4">

                    {{-- 上传表单 --}}
                    <form action="{{ route('data-sources.excel-store') }}" 
                          method="POST" 
                          enctype="multipart/form-data" 
                          id="excelForm">
                        @csrf

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="excel_file" class="form-label">
                                        <i class="fas fa-file-upload text-primary me-1"></i>
                                        选择Excel文件 <span class="text-danger">*</span>
                                    </label>

                                    <!-- 拖拽上传区域 -->
                                    <div class="upload-area" id="uploadArea">
                                        <div class="upload-content">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">拖拽文件到此处或点击选择</h5>
                                            <p class="text-muted mb-3">支持 .xlsx, .xls, .csv 格式，最大10MB</p>
                                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('excel_file').click()">
                                                <i class="fas fa-folder-open me-1"></i>选择文件
                                            </button>
                                        </div>
                                        <input type="file"
                                               class="d-none @error('excel_file') is-invalid @enderror"
                                               id="excel_file"
                                               name="excel_file"
                                               accept=".xlsx,.xls,.csv"
                                               required>
                                    </div>

                                    @error('excel_file')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>

                                {{-- 文件信息显示 --}}
                                <div id="fileInfo" class="alert alert-success d-none">
                                    <h6><i class="fas fa-check-circle me-1"></i>文件已选择：</h6>
                                    <div id="fileDetails"></div>
                                    <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="clearFile()">
                                        <i class="fas fa-times me-1"></i>重新选择
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-cogs me-1"></i>导入选项
                                        </h6>
                                        
                                        <div class="form-check">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="auto_monitor" 
                                                   name="auto_monitor" 
                                                   value="1">
                                            <label class="form-check-label" for="auto_monitor">
                                                自动监控价格变化
                                            </label>
                                            <div class="form-text">
                                                勾选后系统将定期监控商品价格变化
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- 格式要求 --}}
                                <div class="card bg-light mt-3">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-list-alt me-1"></i>Excel格式要求
                                        </h6>
                                        <div class="small">
                                            <strong>第一列：</strong>商品URL或ID<br>
                                            <strong>第二列：</strong>平台（可选）<br>
                                            <strong>第三列：</strong>备注（可选）<br>
                                            <strong>第四列：</strong>自动监控（可选）
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- 提交按钮 --}}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-upload me-1"></i>
                                <span class="btn-text">开始导入</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {{-- 导入历史快捷链接 --}}
            <div class="text-center mt-3">
                <a href="{{ route('data-sources.import-history') }}" class="btn btn-outline-primary">
                    <i class="fas fa-history me-1"></i>查看导入历史
                </a>
            </div>
        </div>
    </div>
</div>

{{-- 进度模态框 --}}
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>
                    <span id="progressTitle">正在处理Excel文件...</span>
                </h5>
            </div>
            <div class="modal-body">
                <!-- 总体进度 -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="fw-bold">总体进度</span>
                        <span id="overallProgress">0%</span>
                    </div>
                    <div class="progress mb-2" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="overallProgressBar"
                             role="progressbar"
                             style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="progressStatus">准备开始处理...</small>
                </div>

                <!-- 详细进度信息 -->
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1" id="totalRows">0</h4>
                            <small class="text-muted">总行数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1" id="processedRows">0</h4>
                            <small class="text-muted">已处理</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1" id="successRows">0</h4>
                            <small class="text-muted">成功导入</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <h4 class="text-danger mb-1" id="errorRows">0</h4>
                            <small class="text-muted">失败</small>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div id="errorSection" class="mt-4 d-none">
                    <h6 class="text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>错误信息
                    </h6>
                    <div class="alert alert-danger">
                        <div id="errorList"></div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="text-center mt-4">
                    <button type="button" class="btn btn-secondary d-none" id="cancelBtn" onclick="cancelImport()">
                        <i class="fas fa-times me-1"></i>取消导入
                    </button>
                    <button type="button" class="btn btn-primary d-none" id="completeBtn" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i>完成
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('excel_file');
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    const form = document.getElementById('excelForm');
    const submitBtn = document.getElementById('submitBtn');
    const uploadArea = document.getElementById('uploadArea');

    // 拖拽上传功能
    setupDragAndDrop();

    // 文件选择处理
    fileInput.addEventListener('change', function(e) {
        handleFileSelect(e.target.files[0]);
    });

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!fileInput.files[0]) {
            alert('请先选择要导入的Excel文件！');
            return;
        }

        startImport();
    });

    // 设置拖拽上传
    function setupDragAndDrop() {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        uploadArea.addEventListener('drop', handleDrop, false);
        uploadArea.addEventListener('click', () => fileInput.click());
    }

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        uploadArea.classList.add('drag-over');
    }

    function unhighlight() {
        uploadArea.classList.remove('drag-over');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    }

    // 处理文件选择
    function handleFileSelect(file) {
        if (!file) {
            fileInfo.classList.add('d-none');
            uploadArea.classList.remove('file-selected');
            return;
        }

        // 验证文件大小
        if (file.size > 10 * 1024 * 1024) { // 10MB
            alert('文件大小超过10MB限制！');
            clearFile();
            return;
        }

        // 验证文件类型
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'text/csv'
        ];

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
            alert('请选择有效的Excel或CSV文件！');
            clearFile();
            return;
        }

        // 显示文件信息
        const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
        fileDetails.innerHTML = `
            <div class="row">
                <div class="col-sm-4"><strong>文件名：</strong></div>
                <div class="col-sm-8">${file.name}</div>
            </div>
            <div class="row">
                <div class="col-sm-4"><strong>文件大小：</strong></div>
                <div class="col-sm-8">${sizeInMB} MB</div>
            </div>
            <div class="row">
                <div class="col-sm-4"><strong>文件类型：</strong></div>
                <div class="col-sm-8">${getFileTypeText(file.name)}</div>
            </div>
            <div class="row">
                <div class="col-sm-4"><strong>最后修改：</strong></div>
                <div class="col-sm-8">${new Date(file.lastModified).toLocaleString()}</div>
            </div>
        `;

        fileInfo.classList.remove('d-none');
        uploadArea.classList.add('file-selected');
    }

    // 获取文件类型文本
    function getFileTypeText(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const types = {
            'xlsx': 'Excel 工作簿 (.xlsx)',
            'xls': 'Excel 97-2003 工作簿 (.xls)',
            'csv': '逗号分隔值文件 (.csv)'
        };
        return types[ext] || '未知格式';
    }

    // 清除文件
    window.clearFile = function() {
        fileInput.value = '';
        fileInfo.classList.add('d-none');
        uploadArea.classList.remove('file-selected');
    }

    // 开始导入
    function startImport() {
        // 显示进度模态框
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        progressModal.show();

        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.querySelector('.btn-text').textContent = '正在导入...';
        submitBtn.querySelector('.spinner-border').classList.remove('d-none');

        // 模拟导入进度（实际项目中应该通过WebSocket或轮询获取真实进度）
        simulateImportProgress();
    }

    // 模拟导入进度
    function simulateImportProgress() {
        const totalRows = Math.floor(Math.random() * 1000) + 100; // 模拟100-1100行数据
        let processedRows = 0;
        let successRows = 0;
        let errorRows = 0;
        const errors = [];

        // 更新总行数
        document.getElementById('totalRows').textContent = totalRows;

        const interval = setInterval(() => {
            // 模拟处理进度
            const increment = Math.floor(Math.random() * 10) + 1;
            processedRows = Math.min(processedRows + increment, totalRows);

            // 模拟成功和失败
            const newSuccess = Math.floor(increment * 0.9); // 90%成功率
            const newErrors = increment - newSuccess;

            successRows += newSuccess;
            errorRows += newErrors;

            // 模拟错误信息
            if (newErrors > 0) {
                for (let i = 0; i < newErrors; i++) {
                    errors.push(`第${processedRows - newErrors + i + 1}行：商品URL格式不正确`);
                }
            }

            // 更新进度显示
            updateProgress(processedRows, totalRows, successRows, errorRows, errors);

            // 检查是否完成
            if (processedRows >= totalRows) {
                clearInterval(interval);
                completeImport(successRows, errorRows, errors);
            }
        }, 200);
    }

    // 更新进度显示
    function updateProgress(processed, total, success, errors, errorList) {
        const percentage = Math.round((processed / total) * 100);

        document.getElementById('overallProgress').textContent = percentage + '%';
        document.getElementById('overallProgressBar').style.width = percentage + '%';
        document.getElementById('processedRows').textContent = processed;
        document.getElementById('successRows').textContent = success;
        document.getElementById('errorRows').textContent = errors;

        if (processed < total) {
            document.getElementById('progressStatus').textContent = `正在处理第 ${processed} 行，共 ${total} 行...`;
        } else {
            document.getElementById('progressStatus').textContent = '处理完成！';
        }

        // 显示错误信息
        if (errorList.length > 0) {
            const errorSection = document.getElementById('errorSection');
            const errorListEl = document.getElementById('errorList');

            errorListEl.innerHTML = errorList.slice(-5).map(error => `<div>• ${error}</div>`).join('');
            if (errorList.length > 5) {
                errorListEl.innerHTML += `<div class="text-muted">... 还有 ${errorList.length - 5} 个错误</div>`;
            }

            errorSection.classList.remove('d-none');
        }
    }

    // 完成导入
    function completeImport(success, errors, errorList) {
        document.getElementById('progressTitle').innerHTML =
            '<i class="fas fa-check-circle text-success me-2"></i>导入完成！';

        document.getElementById('overallProgressBar').classList.remove('progress-bar-animated');
        document.getElementById('overallProgressBar').classList.add('bg-success');

        document.getElementById('completeBtn').classList.remove('d-none');

        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.querySelector('.btn-text').textContent = '开始导入';
        submitBtn.querySelector('.spinner-border').classList.add('d-none');

        // 显示结果消息
        setTimeout(() => {
            if (errors === 0) {
                alert(`导入成功！共导入 ${success} 条数据。`);
            } else {
                alert(`导入完成！成功 ${success} 条，失败 ${errors} 条。请查看错误详情。`);
            }
        }, 1000);
    }

    // 取消导入
    window.cancelImport = function() {
        if (confirm('确定要取消导入吗？已处理的数据将会丢失。')) {
            location.reload();
        }
    }
});
</script>
@endpush

@push('styles')
<style>
.alert-light {
    border-left: 4px solid #0d6efd;
}

.form-check-label {
    cursor: pointer;
}

.btn:disabled {
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 拖拽上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #e7f1ff;
}

.upload-area.drag-over {
    border-color: #0d6efd;
    background-color: #e7f1ff;
    transform: scale(1.02);
}

.upload-area.file-selected {
    border-color: #198754;
    background-color: #d1e7dd;
}

.upload-area.file-selected:hover {
    border-color: #198754;
    background-color: #d1e7dd;
}

.upload-content {
    pointer-events: none;
}

.upload-area i {
    transition: transform 0.3s ease;
}

.upload-area:hover i {
    transform: translateY(-5px);
}

/* 进度条样式 */
.progress {
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 统计卡片样式 */
.border.rounded {
    transition: all 0.3s ease;
}

.border.rounded:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

/* 错误列表样式 */
#errorList {
    max-height: 150px;
    overflow-y: auto;
    font-size: 0.9rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1rem;
        min-height: 150px;
    }

    .upload-area h5 {
        font-size: 1rem;
    }

    .upload-area p {
        font-size: 0.9rem;
    }
}
</style>
@endpush
@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('data_sources', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade')->comment('添加用户ID');
            $table->foreignId('import_log_id')->nullable()->constrained('import_logs')->onDelete('set null')->comment('关联导入记录ID');
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('set null')->comment('关联产品ID');
            $table->enum('source_type', ['url', 'id'])->comment('数据源类型');
            $table->string('platform')->comment('平台标识（taobao, tmall, jd等）');
            $table->text('source_url')->nullable()->comment('原始URL');
            $table->string('source_id')->comment('平台商品ID');
            $table->string('normalized_url')->nullable()->comment('标准化URL');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'duplicate'])->default('pending')->comment('处理状态');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->json('extracted_data')->nullable()->comment('提取的商品数据');
            $table->timestamp('processed_at')->nullable()->comment('处理完成时间');
            $table->timestamp('last_fetched_at')->nullable()->comment('最后抓取时间');
            $table->unsignedInteger('fetch_attempts')->default(0)->comment('抓取尝试次数');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('auto_monitor')->default(false)->comment('是否自动创建监控任务');
            $table->json('metadata')->nullable()->comment('额外元数据');
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'status']);
            $table->index(['platform', 'source_id']);
            $table->index(['source_type', 'status']);
            $table->index(['import_log_id']);
            $table->index(['product_id']);
            $table->index(['is_active', 'status']);
            $table->index('processed_at');
            
            // 唯一约束：同一用户不能重复添加相同的数据源
            $table->unique(['user_id', 'platform', 'source_id'], 'unique_user_platform_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('data_sources');
    }
};
